import { 
    testNotLoggedOnUserBuyingFurniture, 
    testNotLoggedOnUserBuyingSample, 
    testNotLoggedOnUserBuyingFurnitureAndSample, 
    testNotLoggedOnUserBuyingFurnitureOnMobile,
    testNotLoggedOnUserBuyingSampleOnMobile,
    testNotLoggedOnUserBuyingFurnitureAndSampleOnMobile,
} from "../../test_modules/goldenpath_tests"

const mobileDevices = ['iphone-se2']
const desktopDevices = ['macbook-15']

describe('Goldenpath on mobile', () => {
    mobileDevices.forEach(device => 
        context(`${device} resolution`, () => {
            beforeEach(() => {             
                cy.viewport(device)
            })
            
            const test1Params = [
                {region: 'pl', language: 'pl', category: 'sofa'},
                {region: 'fr', language: 'en', category: 'bookcases'},
                {region: 'fr', language: 'en', category: 'bookcases'},
                {region: 'de', language: 'de', category: 'wardrobes'},
                {region: 'nl', language: 'nl', category: 'sideboards'},
                {region: 'no', language: 'en', category: 'desks'},
                {region: 'ch', language: 'de', category: 'bookcases'},
                {region: 'it', language: 'en', category: 'chestofdrawers'},
                {region: 'es', language: 'es', category: 'tvstands'},
            ]
            test1Params.forEach(param => testNotLoggedOnUserBuyingFurnitureOnMobile(param.region, param.category, param.language, param.payment_method))

            const test2Params = [
                {region: 'be', language: 'de'}, //all types of samples //change payment on bancontact when new test card available
                {region: 'ro', language: 'en'}, //not all types of samples
            ]
            test2Params.forEach(param => testNotLoggedOnUserBuyingSampleOnMobile(param.region, param.language, param.payment_method))

            const test3Params = [
                {region: 'ch', language: 'de', category: 'vinylstorage'},
                {region: 'uk', language: 'en', category: 'bedsidetables'},
            ]
            test3Params.forEach(param => testNotLoggedOnUserBuyingFurnitureAndSampleOnMobile(param.region, param.category, param.language, param.payment_method))
        })
    )
})

describe('Goldenpath on desktop', () => {
    desktopDevices.forEach(device => 
        context(`${device} resolution`, () => {
            beforeEach(() => {
                cy.viewport(device)
                cy.setCookie('popup-popup-product_save_nouser1-done', 'ok')    
            })
            
            const test1Params = [
                {region: 'nl', language: 'nl', category: 'sofa'},
                {region: 'it', language: 'it', category: 'sideboards'},
                {region: 'de', language: 'de', category: 'wardrobes'},
                {region: 'es', language: 'es', category: 'desks'},
                {region: 'fr', language: 'fr', category: 'bookcases'},
                {region: 'gr', language: 'en', category: 'wallstorage'},
                {region: 'at', language: 'de', category: 'bookcases'},
                {region: 'bg', language: 'en', category: 'allshelves'},
            ]
            test1Params.forEach(param => testNotLoggedOnUserBuyingFurniture(param.region, param.category, param.language))

            const test2Params = [
                {region: 'fr', language: 'fr'}, //all types of samples
                {region: 'se', language: 'sv'}, //not all types of samples
            ]
            test2Params.forEach(param => testNotLoggedOnUserBuyingSample(param.region, param.language))

            const test3Params = [
                {region: 'uk', language: 'en', category: 'wardrobes'},
                {region: 'pt', language: 'en', category: 'desks'},
                {region: 'cz', language: 'en', category: 'chestofdrawers'},
            ]
            test3Params.forEach(param => testNotLoggedOnUserBuyingFurnitureAndSample(param.region, param.category, param.language))
        }
    ))
})
