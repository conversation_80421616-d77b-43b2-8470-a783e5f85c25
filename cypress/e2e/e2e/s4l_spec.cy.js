import { pdp } from "../../pages/product_page"
import libraryPage from "../../pages/library_page"
import { generateRandomEmail, isProdEnv } from '../../test_modules/common_functions'

const url = Cypress.config('baseUrl')
const password = Cypress.env('CY_PASS')

beforeEach(() => {
    cy.viewport('iphone-se2')
})

describe ('Test save for later', () => {
    it('as logged on user, save and remove the design', () => {
        const itemsCount = 2
        cy.visit(`${url}/en-uk/furniture/bookcase/2710739,j,large-matte-black-bookcase-with-doors-and-drawers-110x263x32cm`)
        cy.registerWithApi(generateRandomEmail(), password)
        cy.agreeCookies()
        for (let i=0; i<itemsCount; i++) { saveItemAndVerifyModal() }
        pdp.goToWishlist()
        cy.get(libraryPage.selectors.library_header).should('be.visible').and('contain', 'Wishlist')
        cy.get(libraryPage.selectors.library_header_with_item_count).should('contain', itemsCount)
        cy.get(libraryPage.selectors.wishlist_item).find('a img').as('img')
        cy.get('@img').first().should('be.visible')
        cy.get(libraryPage.selectors.wishlist_item_a2c).should('have.length', itemsCount)
        cy.get(libraryPage.selectors.wishlist_item_bin_button).should('have.length', itemsCount)
        cy.get(libraryPage.selectors.wishlist_item_price).should('have.length', itemsCount)
        clearWishlist()
    })

    it('as not logged on user, send design on mail - NOT tested on PROD', () => {
        if (!isProdEnv()) {
            cy.visit(`${url}/en-uk/furniture/bookcase/3705289,j,large-dusty-pink-plywood-bookcase-with-doors-and-drawers-plywood-192x293x40cm`)
            cy.agreeCookies()
            const mail = '<EMAIL>'
            pdp.saveForLaterGuest(mail)
        }
    })
})

function saveItemAndVerifyModal() {
    pdp.saveForLater()
    cy.get('h2', { timeout:10000 }).should('be.visible')
    cy.get('[data-testid="wishlist-modal"]', { timeout:10000 }).should('be.visible')
    cy.get('[data-testid="wishlist-modal-preview"]', { timeout:10000 }).should('be.visible')
    cy.get('[data-testid="wishlist-modal-price"]', { timeout:10000 }).should('be.visible')
}

function clearWishlist() {
    removeItems()
    cy.get('@bin').first().click({ timeout:10000 })
    cy.get('@removeItem').click({ timeout:10000 })
    cy.get('[data-testid="library-continue-shopping-button"]').should('be.visible')

    function removeItems() {
        cy.get(libraryPage.selectors.wishlist_item).as('wishlistItem').its('length').then((initialNumOfItems) => {
            cy.get(libraryPage.selectors.wishlist_item_bin_button).as('bin').first()
            .scrollIntoView().click({ timeout:10000 })
            cy.contains('Remove item').as('removeItem').click({ timeout:10000 }) // TODO: add testid
            cy.get('@removeItem').should('not.exist')
            cy.get('@wishlistItem').its('length').then((updatedNumOfItems) => {
                expect(updatedNumOfItems).to.equal(initialNumOfItems - 1)
                if (updatedNumOfItems > 1) {
                    removeItems()
                }
            })
        })
    }
}
