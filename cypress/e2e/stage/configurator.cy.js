import { pdp } from "../../pages/product_page"

describe('Test configurator parameters', () => {
    const devices = [
        'macbook-15',
    ]
    const url = Cypress.config('baseUrl')

    devices.forEach(device => 
        context(`${device} resolution`, () => {
            beforeEach(() => {             
                cy.viewport(device)   
                cy.intercept('POST', `/api/v1/gallery/*/add_to_cart/`).as('addToCart')
            })

            const test1Params = [
                {region: 'belgium', category: 'sideboards', configurator_timeout:4000, furniture_link:"/furniture/sideboard/3516424,j", expected_cart_item_count:1},
            ]

            test1Params.forEach(param => testConfiguratorParametersColors(param.region, param.category, param.language, param.configurator_timeout, param.furniture_link, param.expected_cart_item_count))
            test1Params.forEach(param => testConfiguratorParametersWidth(param.region, param.category, param.language, param.configurator_timeout, param.furniture_link, param.expected_cart_item_count))
            test1Params.forEach(param => testConfiguratorParametersHeight(param.region, param.category, param.language, param.configurator_timeout, param.furniture_link, param.expected_cart_item_count))
            test1Params.forEach(param => testConfiguratorParametersDepth(param.region, param.category, param.language, param.configurator_timeout, param.furniture_link, param.expected_cart_item_count))
        })
    )

    function testConfiguratorParametersColors(region, category, language, configurator_timeout, furniture_link) {
        it(buildTestName(`as NOT logged on user`, region, category, language, configurator_timeout), () => {
            cy.visit(buildDirectUrl(region, language, furniture_link))
            cy.agreeCookies()
            pdp.waitForConfiguratorToBeReady(configurator_timeout)

            pdp.addToCart()
            cy.wait('@addToCart').then(({ response }) => {
                expect(response.statusCode).to.eq(201)
                cy.log('Przedmiot dodany do koszyka.')
            })

            cy.request('GET', '/api/v1/user_status/').then(response => {
                const responseDataBeforeAdd = response.body
                cy.log('Odpowiedź z API /user_status/ przed dodaniem przedmiotu:')
                cy.log(responseDataBeforeAdd)

                responseDataBeforeAdd.cartItems.forEach(item => {
                    cy.log(`Przedmiot: ${item.itemUrl}\nregion_price: ${item.region_price}\ncurrency_code: ${item.currency_code}\ncolor_name: ${item.color_name}\nwidth: ${item.width}\nheight: ${item.height}\ndensity: ${item.density}\ndepth: ${item.depth}\ndrawers: ${item.drawers}\ndoors: ${item.doors}\nshelf_type: ${item.shelf_type}\nmaterial: ${item.material}`)

                    cy.visit(item.itemUrl)
                    cy.get('[id="material-swatch-1-50"]').click({ timeout: 15000 })
                    pdp.addToCart()
                    cy.visit(item.itemUrl)
                    cy.get('[id="material-swatch-14-50"]').click({ timeout: 15000 })
                    pdp.addToCart()
                    cy.wait('@addToCart').then(() => {
                        cy.request('GET', '/api/v1/user_status/').then(response => {
                            const cartItems = response.body.cartItems
                            const uniqueColors = new Set()

                            cartItems.forEach(cartItem => uniqueColors.add(cartItem.color_name))

                            if (uniqueColors.size === cartItems.length) {
                                cy.log('Każdy z przedmiotów ma inny kolor.')
                            } else {
                                for (let i = 0; i < cartItems.length; i++) {
                                    const currentItem = cartItems[i]

                                    for (let j = i + 1; j < cartItems.length; j++) {
                                        const comparedItem = cartItems[j]

                                        if (currentItem.color_name === comparedItem.color_name) {
                                            throw new Error(`Znaleziono dwa przedmioty o tym samym kolorze: ${currentItem.itemUrl} i ${comparedItem.itemUrl}`)
                                        }
                                    }
                                }
                            }
                        })
                    })
                })
            })
        })
    }

    function testConfiguratorParametersWidth(region, category, language, configurator_timeout, furniture_link) {
        it(buildTestName(`as NOT logged on user`, region, category, language, configurator_timeout), () => {
            let initialWidth

            function addFurnitureWithChangedWidth(height) {
            cy.visit(buildDirectUrl(region, language, furniture_link))
            pdp.waitForConfiguratorToBeReady(configurator_timeout)
            cy.scrollTo('top')
        
            cy.get('.noUi-handle').eq(0).should('be.visible')
        
            cy.get('.noUi-handle').eq(0).trigger('mousedown', { force: true })
                                            .trigger('mousemove', { clientX: height, force: true }) // Ustaw dowolną dużą wartość przesunięcia w lewo
                                            .trigger('mouseup', { force: true })
        
            pdp.addToCart()
            cy.wait('@addToCart')
        }

            cy.visit(buildDirectUrl(region, language, furniture_link))
            cy.agreeCookies()
            pdp.waitForConfiguratorToBeReady(configurator_timeout)
            pdp.addToCart()
            cy.wait('@addToCart').then(({ response }) => {
                expect(response.statusCode).to.eq(201)
            })

            cy.request('GET', '/api/v1/user_status/').then(response => {
                const responseDataBeforeAdd = response.body
                initialWidth = responseDataBeforeAdd.cartItems[0].width
            })

            addFurnitureWithChangedWidth(5000)  // Dodaj drugi przedmiot do koszyka z przesunięciem suwaka na maxa w prawo
            addFurnitureWithChangedWidth(0)     // Dodaj trzeci przedmiot do koszyka z przesunięciem suwaka na maxa w lewo

            cy.request('GET', '/api/v1/user_status/').then(response => {
                const responseDataAfterAdd = response.body
                const firstItemWidth = responseDataAfterAdd.cartItems[0].width
                const secondItemWidth = responseDataAfterAdd.cartItems[1].width
                const thirdItemWidth = responseDataAfterAdd.cartItems[2].width

                expect(firstItemWidth).not.to.eq(secondItemWidth)
                expect(secondItemWidth).not.to.eq(initialWidth)

                expect(thirdItemWidth).not.to.eq(firstItemWidth)
                expect(thirdItemWidth).not.to.eq(secondItemWidth)
            })
        })
    }

    function testConfiguratorParametersHeight(region, category, language, configurator_timeout, furniture_link) {
        it(buildTestName(`as NOT logged on user`, region, category, language, configurator_timeout), () => {
            let initialHeight

            function addFurnitureWithChangedHeight(height) {
            cy.visit(buildDirectUrl(region, language, furniture_link))
            pdp.waitForConfiguratorToBeReady(configurator_timeout)
            cy.scrollTo('top')
        
            cy.get('.noUi-handle').eq(1).should('be.visible')
        
            cy.get('.noUi-handle').eq(1).trigger('mousedown', { force: true })
                                            .trigger('mousemove', { clientX: height, force: true }) // Ustaw dowolną dużą wartość przesunięcia w lewo
                                            .trigger('mouseup', { force: true })
        
            pdp.addToCart()
            cy.wait('@addToCart')
        }

            cy.visit(buildDirectUrl(region, language, furniture_link))
            cy.agreeCookies()
            pdp.waitForConfiguratorToBeReady(configurator_timeout)
            pdp.addToCart()
            cy.wait('@addToCart').then(({ response }) => {
                expect(response.statusCode).to.eq(201)
            })

            cy.request('GET', '/api/v1/user_status/').then(response => {
                const responseDataBeforeAdd = response.body
                initialHeight = responseDataBeforeAdd.cartItems[0].height
            })

            addFurnitureWithChangedHeight(0) // Dodaj drugi przedmiot do koszyka z przesunięciem suwaka
            addFurnitureWithChangedHeight(5000)    // Dodaj trzeci przedmiot do koszyka z wysokością ustawioną na 0

            cy.request('GET', '/api/v1/user_status/').then(response => {
                const responseDataAfterAdd = response.body
                const firstItemHeight = responseDataAfterAdd.cartItems[0].height
                const secondItemHeight = responseDataAfterAdd.cartItems[1].height
                const thirdItemHeight = responseDataAfterAdd.cartItems[2].height

                expect(firstItemHeight).not.to.eq(secondItemHeight)
                expect(secondItemHeight).not.to.eq(initialHeight)

                expect(thirdItemHeight).not.to.eq(firstItemHeight)
                expect(thirdItemHeight).not.to.eq(secondItemHeight)
            })
        })
    }

    function testConfiguratorParametersDepth(region, category, language, configurator_timeout, furniture_link) {
        it(buildTestName(`as NOT logged on user`, region, category, language, configurator_timeout), () => {
            let initialDepth

            function addFurnitureWithChangedDepth(eqIndex) {
                cy.visit(buildDirectUrl(region, language, furniture_link))
                pdp.waitForConfiguratorToBeReady(configurator_timeout)
                cy.scrollTo('top')
            
                // Wybierz odpowiedni przycisk .toggle-button
                cy.get('.toggle-button').eq(eqIndex).should('be.visible').click({ force: true })
            
                pdp.addToCart()
                cy.wait('@addToCart')
            }

            cy.visit(buildDirectUrl(region, language, furniture_link))
            cy.agreeCookies()
            pdp.waitForConfiguratorToBeReady(configurator_timeout)
            pdp.addToCart()
            cy.wait('@addToCart').then(({ response }) => {
                expect(response.statusCode).to.eq(201)
            })

            cy.request('GET', '/api/v1/user_status/').then(response => {
                const responseDataBeforeAdd = response.body
                initialDepth = responseDataBeforeAdd.cartItems[0].depth
            })

            addFurnitureWithChangedDepth(4)
            addFurnitureWithChangedDepth(5)
            addFurnitureWithChangedDepth(7)

            cy.request('GET', '/api/v1/user_status/').then(response => {
                const responseDataAfterAdd = response.body
                const firstItemDepth = responseDataAfterAdd.cartItems[0].depth
                const secondItemDepth = responseDataAfterAdd.cartItems[1].depth
                const thirdItemDepth = responseDataAfterAdd.cartItems[2].depth

                // Porównaj wszystkie 3 szafki
                expect(firstItemDepth).not.to.eq(secondItemDepth)
                expect(secondItemDepth).not.to.eq(initialDepth)
                expect(thirdItemDepth).not.to.eq(firstItemDepth)
                expect(thirdItemDepth).not.to.eq(secondItemDepth)
            })
        })
    }

    function buildDirectUrl(region, language, furniture_link) {
        let lang =''
        if (language) {lang=`/${language}`}
        return `${url}${lang}${furniture_link}?forced_region=${region}`
    }

    function buildTestName(asWho, region, items, language, configurator_timeout) {
        var region = region.charAt(0).toUpperCase() + region.toLowerCase().slice(1)
        var langText = ''
        if (language) {langText = ` (language: ${language}, timeout:${configurator_timeout})`}
        return `${asWho} from ${region}, I want to add to card ${items} ${langText}, than check if every piece have different parameters`
    }
})
