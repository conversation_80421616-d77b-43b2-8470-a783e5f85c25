{"name": "tylko-cypress", "version": "0.1.0", "description": "tylko-cypress", "repository": "", "author": "tylko.com", "license": "BSD", "engines": {"node": ">=15.14.0", "npm": ">=6.4.1"}, "scripts": {"cypress:open": "cypress open", "cypress:local:goldenpath:small": "cypress run --config baseUrl=http://localhost:8000 --spec e2e/e2e_small/golden_path_spec_small.cy.js", "cypress:local:goldenpath": "cypress run --config baseUrl=http://localhost:8000 --spec e2e/e2e/golden_path_spec.cy.js", "cypress:staging:goldenpath": "cypress run --config baseUrl=https://staging.oklyt.pl --spec e2e/e2e/golden_path_spec.cy.js"}, "devDependencies": {"@percy/cli": "^1.26.2", "@percy/cypress": "^3.1.2", "cypress": "^14.3.2", "cypress-file-upload": "^5.0.8", "cypress-iframe": "^1.0.1"}, "dependencies": {"cypress-plugin-tab": "^1.0.5", "cypress-real-events": "^1.14.0", "cypress-wait-until": "^3.0.2"}}