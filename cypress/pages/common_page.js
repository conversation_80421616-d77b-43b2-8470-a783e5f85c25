import { MegaMenu } from "../page_sections/mega_menu"
import { FooterDesktop } from "../page_sections/footer"

class CommonPage {

    url = '/'
    megaMenu = new MegaMenu()

    goToLoginPage() {
        cy.get(this.megaMenu.selectors.top_menu.account).first().click({timeout:10000})
    }

    goToLoginPageOnMobile() {
        cy.get(this.megaMenu.selectors.mobile_menu.mobile_signin).scrollIntoView().click({timeout:10000})
    }

    goToWishlist() {
        cy.get(this.megaMenu.selectors.top_menu.wishlist).first().click({timeout:10000})
    }

    openRegionNavOnDesktop() {
        cy.get(this.megaMenu.selectors.top_menu.changeregion, {timeout:5000}).filter(':visible').click({timeout:10000})
    }

    openMegaMenuOnMobile() {
        cy.get(`${this.megaMenu.selectors.mobile_menu.mobile_menu_burger}`).click({ timeout:6000, force:true })
    }

    changeRegion(region) {
        this.openRegionNavOnDesktop()
        cy.get(`[data-testid="region-${region}"]`).click({timeout:10000})
    }

    changeRegionOnMobile(region) {
        this.openMegaMenuOnMobile()
        cy.get(this.megaMenu.selectors.top_menu.changeregion).eq(1).scrollIntoView()
            .should('be.visible').click({timeout:10000})
        cy.get(`[data-testid="region-${region}"]`).eq(0).click({timeout:10000})
    }

    changeLanguage(language) {
        this.openRegionNavOnDesktop()
        cy.get(this.megaMenu.selectors.top_menu.navigation).as('nav')
        cy.get('@nav').find('[data-testid^="redirect-"] span').filter(':visible').contains(language).click({timeout:10000})
    }

    changeLanguageOnMobile(language) {
        this.openMegaMenuOnMobile()
        cy.get(this.megaMenu.selectors.top_menu.changeregion).eq(1).scrollIntoView()
        .should('be.visible').click({timeout:10000})
        cy.get('[data-testid="toggle-button"]').eq(1).click({timeout:10000})
        cy.get(this.megaMenu.selectors.mobile_menu.languages_list).contains(language).click({timeout:10000})
    }

    openOrderStatus() {
        cy.get(this.megaMenu.selectors.top_menu.orderstatus).first().click({ force:true, timeout:15000 })
    }

    fillOrderData(order_id, postcode) {
        cy.get(this.megaMenu.selectors.order_status_checker.order_number)
          .first().clear({ force:true }).type(order_id, { force:true })
        cy.get(this.megaMenu.selectors.order_status_checker.order_postcode)
          .first().clear({ force:true }).type(postcode, { force:true })
    }

    submitOrderData() {
        cy.get(this.megaMenu.selectors.order_status_checker.check_status).first().click({ force:true })
    }

    openMegaMenu() {
        cy.get(this.megaMenu.selectors.top_menu.shop).first().click({ force:true, timeout:15000 })
    }

    navigateToCategory(category) {
        cy.get(`${this.megaMenu.selectors.categories[category]}:visible`).click({ force:true })
    }

    navigateToCategoryOnMobile(category) {
        cy.get(this.megaMenu.selectors.mobile_menu.mobile_category).first().click({timeout:10000})
        cy.get(this.megaMenu.selectors.categories[category]).last().scrollIntoView().click({timeout:10000})
    }

    navigateToSamples() {
        cy.get(this.megaMenu.selectors.top_menu.material_samples).first().click({timeout:10000})
    }

    navigateToSamplesOnMobile() {
        cy.get(`${this.megaMenu.selectors.top_menu.material_samples}:visible`).click({timeout:10000})
    }

    verifyCartCounterEquals(num) {
        cy.get(this.megaMenu.selectors.top_menu.opencart).find('.navbar-item-counter')
        .first().should('have.text', num)
    }

    verifyWishlistCounterEquals(num) {
        cy.get(this.megaMenu.selectors.top_menu.wishlist).find('.navbar-item-counter')
        .should('have.text', num)
    }

    goToCart() {
        cy.get(this.megaMenu.selectors.top_menu.opencart).filter(':visible').click()
        cy.url().should('contain', '/cart')
    }

    getNotification() {
        return cy.get('.Vue-Toastification__toast-body', { timeout:6000 })
    }

    getSuccessNotification() {
        return cy.get('.Vue-Toastification__toast--success', { timeout:6000 })
    }

    closeModal() {
        cy.get('svg.transition-rotate:visible').click()
    }
}

export default CommonPage
