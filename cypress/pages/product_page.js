import CommonPage from './common_page'
import { extractPriceValue } from "../test_modules/common_functions"

class ProductPage extends CommonPage {

    url = '/furniture-c/'
    selectors = {
        cta_add_to_cart: '[data-testid="a2c-button"]',
        configurator_price: '[data-testid="configurator-price"]',
        configurator_loader: '[data-testid="configurator-loader"]',
        configurator: '[data-testid="configurator"]',
        s4l_cta: '[data-testid="s4l-button"]',
        s4l_header: '[data-testid="s4l-header"]',
        s4l_subtext: '[data-testid="s4l-subtext"]',
        s4l_email_input: '[data-testid="s4l-email-input"]',
        s4l_permission_checkbox: '[data-testid="s4l-permission-checkbox"]',
        s4l_submit_button: '[data-testid="s4l-submit-button"]',
        s4l_clipboard_input: '[data-testid="s4l-clipboard-input"]',
        s4l_clipboard_button: '[data-testid="s4l-clipboard-button"]',
        modal_view_cart_cta: '[data-testid="view-cart"]',
        modal_go_to_checkout_cta: '[data-testid="checkout"]',
    }

    waitForConfiguratorToBeReady() {
        cy.get(this.selectors.configurator_loader, { timeout:20000 }).should('not.exist')
    } // TODO: not working now - configurator loader is permanently existing in DOM

    waitForConfiguratorToBeReadyAndAddToCart() {
        cy.intercept('POST', `api/**/add_to_cart/`).as('a2c')
        cy.get('[data-testid="a2c-button"]', { timeout:10000 }).as('a2c_button').scrollIntoView().should('be.visible')
        cy.wait(2000)
        cy.get('@a2c_button').click()
        cy.wait('@a2c', { timeout:10000 }).its('response.statusCode').should('eq', 201)
    }

    addToCart() {
        cy.intercept('POST', `/api/**/add_to_cart/`).as('addedToCart')
        cy.get(this.selectors.cta_add_to_cart, { timeout:10000 }).filter(':visible').as('a2cButton')
        cy.get('@a2cButton').should('be.enabled', { timeout:10000 }).click()
        cy.wait('@addedToCart', { timeout:10000 }).its('response.statusCode').should('eq', 201)
    }

    addToCartOnMobile() {
        this.addToCart()
    }

    saveForLater() {
        cy.intercept('POST', 'api/v1/gallery/*/add_to_wishlist/?mm=true').as('addedToWishlist')
        cy.get(this.selectors.s4l_cta, { timeout:10000 })
            .click({ timeout: 10000 })
        cy.wait('@addedToWishlist', { timeout:10000 }).its('response.statusCode').should('eq', 201)
    }

    saveForLaterGuest(mail) {
        cy.get(this.selectors.s4l_cta, { timeout:20000 }).should('be.visible').click({ timeout:20000 })
        cy.get(this.selectors.s4l_email_input, { timeout:20000 }).should('be.visible')
            .clear().type(mail, { timeout:15000 }).invoke('val').then((typedText) => {
                expect(typedText).to.equal(mail)
            })
        cy.get(this.selectors.s4l_permission_checkbox, { timeout:20000 }).check()
        cy.get(this.selectors.s4l_permission_checkbox).uncheck()
        cy.get(this.selectors.s4l_submit_button, { timeout:20000 }).should('be.visible').click()
    }

    getPricingv3AsString() {
        return cy.waitUntil(() =>
            cy.get(this.selectors.configurator_price, { timeout:10000 }).invoke('text')
                .then((priceText) => {
                    const price = extractPriceValue(priceText)
                    return price > 0
                }),
                {
                  timeout: 10000,
                  interval: 500,
                }
        ).then(() => cy.get(this.selectors.configurator_price).invoke('text'))
    }

    acceptModalAndGoToCheckout() {
        cy.get(this.selectors.modal_go_to_checkout_cta, { timeout:10000 }).should('be.visible').click()
    }

    acceptModalAndGoToCart() {
        cy.get(this.selectors.modal_view_cart_cta, { timeout:10000 }).should('be.visible').click()
    }
}

export default ProductPage
export const pdp = new ProductPage()
