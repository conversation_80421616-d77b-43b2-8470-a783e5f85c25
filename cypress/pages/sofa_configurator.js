import CommonPage from './common_page'
import change_category from './sofa_change_category_page'

class SofaConfiguratorPage extends CommonPage {
    selectors = {
        cta_add_to_cart: '[data-testid="a2c-button"]',
        config_price: '[data-testid="configurator-price"]',
        s4l_mobile_heart_button_mobile: '[data-testid=""]', // TODO: add testid on page
        s4l_cta: '[data-testid="s4l-button"]',
        s4l_header: '[data-testid="s4l-header"]',
        s4l_subtext: '[data-testid="s4l-subtext"]',
        s4l_email_input: '[data-testid="s4l-email-input"]',
        s4l_permission_checkbox: '[data-testid="s4l-permission-checkbox"]',
        s4l_submit_button: '[data-testid="s4l-submit-button"]',
        s4l_clipboard_input: '[data-testid="s4l-clipboard-input"]',
        s4l_clipboard_button: '[data-testid="s4l-clipboard-button"]',
        back_button: '[data-testid="go-back-arrow"]',
        delivery_modal: '[data-testid="ModalDeliveryNotice"]',
        delivery_modal_accept: '[data-testid="modal-delivery-accept-exit"]',
        modal_view_cart_cta: '[data-testid="view-cart"]',
        modal_go_to_checkout_cta: '[data-testid="checkout"]',
        tylko_logo: '[data-testid="tylko-logo"]',
        add_module: '[data-testid="btn-add-module"]',
        delete_module: '[data-testid="btn-delete-module"]',
        add_module_icon: 'button.cable-icon',
        close_drawer_button: '.base-drawer-button-close', // TODO: add testid on page
    }

    saveForLaterGuest(mail) {
        cy.intercept('POST', 'api/v1/gallery/*/add_to_wishlist_popup').as('addedToWishlist')
        cy.get(this.selectors.s4l_cta, { timeout:20000 })
            .filter(':visible').should('be.enabled', { timeout:20000 })
            .click()
        cy.get(this.selectors.s4l_email_input, { timeout:20000 })
            .clear()
            .type(mail, { timeout:15000 }).should('have.value', mail)
        cy.get(this.selectors.s4l_permission_checkbox, { timeout: 20000 }).check().should('be.checked')
        cy.get(this.selectors.s4l_permission_checkbox).uncheck().should('not.be.checked')
        cy.get(this.selectors.s4l_submit_button, { timeout: 20000 }).click()
        cy.wait('@addedToWishlist', { timeout: 10000 }).then((interception) => {
            expect(interception.response.statusCode).to.eq(200)
            expect(interception.request.body.email).to.eq(mail)
        })
    }

    addToCart() {
        cy.intercept('POST', `/api/**/add_to_cart/`).as('addedToCart')
        cy.get(this.selectors.cta_add_to_cart, { timeout:10000 }).filter(':visible').as('a2cButton')
        cy.get('@a2cButton').should('be.enabled', { timeout: 10000 }).click()
        cy.wait('@addedToCart', { timeout:10000 }).its('response.statusCode').should('eq', 201)
    }

    goBack() {
        cy.get(this.selectors.back_button).should('be.visible').click()
    }

    acceptModalAndGoToCheckout() {
        cy.get(this.selectors.modal_go_to_checkout_cta).click()
    }

    acceptModalAndGoToCart() {
        cy.get(this.selectors.modal_view_cart_cta, { timeout: 10000 }).click()
    }

    changeSofaCategory(category) {
        cy.get('.controls-module').find('.cta-button', { timeout:20000 }).first().should('be.visible').click() // TODO: add testid in sofa configurator
        cy.get(this.selectors.delivery_modal_accept).click()
        change_category.selectCategory(category)
        cy.url().should('contain', '/configure')
    }
}

export default new SofaConfiguratorPage()
