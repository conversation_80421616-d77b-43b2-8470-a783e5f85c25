import ProductPage from './product_page'

class ProductPageSofa extends ProductPage {

    constructor() {
        super()
        this.selectors = {
            ...this.selectors,
            configure_yours_button: '[data-testid="configure-yours-button"]',
            navigation_a2c_sticky_bar: '[data-testid="navigation-add-to-cart-sticky-bar"]',
            navigation_config_yours_sticky_bar: '[data-testid="configure-yours-sticky-bar"]',
            s4l_heart_cta: '[data-testid="s4l-heart-button"]',

            comfort_levels: 'section[id="comfort_levels"]',
            color_selection: 'div[id="color_selection"]',
            pdp_samples_open_button: '[data-testid="pdp-samples-open-button"]',
            value_proposition: '[data-testid="sofa-pdp-value-proposition"]',
            sofa_creators: '[data-testid="sofa-pdp-creators"]',
            sofa_minigrid: '[data-testid="sofa-pdp-minigrid"]',
            showrooms_map: 'section[id="showrooms-map"]',

            sofa_pdp_gallery: '[data-testid="sofa-pdp-gallery"]',
            sofa_pdp_gallery_asset: '[data-testid="sofa-pdp-gallery-asset"]',
            sofa_pdp_gallery_caret_right: '[data-testid="sofa-pdp-gallery-caret-right"]',
            sofa_pdp_gallery_caret_left: '[data-testid="sofa-pdp-gallery-caret-left"]',
            sofa_pdp_gallery_pagination: '.swiper-pagination',

            product_card: '[data-section="section-product-card"]',
            review_score: '[data-testid="review-score"]',
            sofa_info_title: '[data-testid="sofa-info-title"]',
            item_price: '[data-testid="item-price"]',
            sofa_info_description: '[data-testid="sofa-info-description"]',
            sofa_info_dimensions: '[data-testid="sofa-info-dimensions"]',
            sofa_info_fabric: '[data-testid="sofa-info-fabric"]',
            sofa_info_color: '[data-testid="sofa-info-color"]',
            sofa_info_eu_made: '[data-testid="sofa-info-eu-made"]',
            sofa_info_delivery: '[data-testid="sofa-info-delivery"]',
            sofa_info_payment_info_button: '[data-testid="sofa-info-payment-info-button"]',
            hero_video: '[data-testid="hero-video"]',
            sofa_product_details: '[data-testid="sofa-pdp-product-details"]',
            sofa_highlights_heading: '[data-testid="sofa-pdp-highlights-heading"]',
            sofa_highlights_tiles: '[data-testid="sofa-pdp-highlights-tiles"]',
        }
    }

    configureYours() {
        cy.get(this.selectors.configure_yours_button).should('be.visible').click()
    }
}

export default new ProductPageSofa()
