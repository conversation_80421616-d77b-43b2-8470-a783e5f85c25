import hp from '../pages/home_page'
import login_page from '../pages/login_page'
import register_page from '../pages/register_page'
import { pdp } from '../pages/product_page'
import { generateRandomEmail } from './common_functions'

const baseUrl = Cypress.config('baseUrl')
const region = 'en-uk'
const loginUrl = `${baseUrl}/${region}${login_page.urlLogin}`
const registerUrl = `${baseUrl}/${region}${register_page.url}`
const registerLoginUrl = `${baseUrl}/${region}${login_page.url}`
const user = Cypress.config('user')
user.email = Cypress.env('CY_USER')
user.password = Cypress.env('CY_PASS')

function prepareTest(device) {
    cy.viewport(device)
    cy.intercept('POST', '/api/v2/auth/register/').as('registration')
    cy.intercept('POST', '/api/v2/auth/login/').as('login')
    cy.intercept('POST', '/api/v2/users/check-email/').as('checkEmail')
}

function accessToRegisterPageFromHP() {
    it('Access to register page from HP', () => {
        const mail = generateRandomEmail()
        cy.visit('/')
        cy.agreeCookies()
        hp.goToLoginPage()
        cy.url().should('include', login_page.url)
        login_page.fillLogin(mail)
        cy.wait(500)
        cy.get(login_page.selectors.login_button).click()
        cy.wait('@checkEmail').then(({response}) => {
            expect(response.statusCode).to.eq(200)
            expect(response.body.is_user_registered).to.be.false
        })
        cy.url().should('include', register_page.url, {timeout:6000})
        .and('not.include', login_page.url)
    })
}

function loginUserWithWrongPassword() {
    it('login user with wrong password', () => {
        cy.visit(registerLoginUrl)
        cy.agreeCookies()
        login_page.loginUser(user.email, user.wrong_password)
        cy.wait('@login').then(({response}) => {
            expect(response.statusCode).to.eq(400)
        })
    })
}

function loginUser(urls) {
    urls.forEach((url) => {
        it(`Login user being on site: ${url} should redirect to account`, () => {
            cy.visit(`${url}`)
            cy.url().then((entry_page) => {
                cy.agreeCookies()
                pdp.goToLoginPage()
                login_page.loginUser(user.email, user.password)
                cy.wait('@login').then(({response}) => {
                    expect(response.statusCode).to.eq(200)
                })
                cy.url().should('include', '/account')
            })
        })
    })
}

function registerUserAcceptedNewsletter() {
    it('Register user', () => {
        const randomMail = generateRandomEmail()
        cy.visit(registerLoginUrl)
        cy.agreeCookies()
        login_page.fillLogin(randomMail)
        cy.get(login_page.selectors.login_button).click()
        cy.url().should('be.equal', registerUrl)
        cy.get(register_page.selectors.email_input).should('have.value', randomMail)
        register_page.fillPassword(user.password)
        cy.get('.text-success-500').should('have.length', 3)
        register_page.acceptNewsletter()
        register_page.submitRegistration()
        cy.wait('@registration').then(({response}) => {
            expect(response.statusCode).to.eq(201)
            expect(response.body.newsletter).to.be.true
        })
        // cy.url().should('include', '/account')
    })
}

function registerUserUnacceptedNewsletter() {
    it('Register user', () => {
        const randomMail = generateRandomEmail()
        cy.visit(registerLoginUrl)
        cy.agreeCookies()
        login_page.fillLogin(randomMail)
        cy.get(login_page.selectors.login_button).click()
        cy.url().should('be.equal', registerUrl)
        cy.get(register_page.selectors.email_input).should('have.value', randomMail)
        register_page.fillPassword(user.password)
        cy.get('.text-success-500').should('have.length', 3)
        register_page.submitRegistration()
        cy.wait('@registration').then(({response}) => {
            expect(response.statusCode).to.eq(201)
            expect(response.body.newsletter).to.be.false
        })
        // cy.url().should('include', '/account')
    })
}

function registerUserWithTooShortPassword() {
    it('Register user with too short password', () => {
        const randomMail = generateRandomEmail()
        cy.visit(registerLoginUrl)
        cy.agreeCookies()
        login_page.fillLogin(randomMail)
        cy.get(login_page.selectors.login_button).click()
        cy.url().should('be.equal', registerUrl)
        cy.get(register_page.selectors.email_input).should('have.value', randomMail)
        register_page.fillPassword(user.wrong_password)
        register_page.submitRegistration()
        cy.get('.text-error-500').should('have.length', 3)
        cy.wait(2000)
        cy.url().should('be.equal', registerUrl)
    })
}

function checkIfDirectRegistrationIsNotPossible() {
    it('Check if redirect from register to the main login page works', () => {
        cy.visit(registerUrl)
        cy.agreeCookies()
        cy.url().should('be.equal', registerLoginUrl)
    })
}

function passwordReminder() {
    it('Password reminder', () => {
        cy.intercept('POST', '/api/v2/auth/password/reset').as('reset')
        const email = user.email
        cy.visit(registerLoginUrl)
        cy.agreeCookies()
        login_page.fillLogin(email)
        cy.get(login_page.selectors.login_button).click()
        login_page.sendPasswordReminder()
        cy.wait('@reset').then(({request, response}) => {
            expect(response.statusCode).to.eq(200)
            expect(request.body.email).to.eq(email)
        })
        cy.contains(email).should('be.visible')
        cy.get('[data-testid="button-got-it"]').should('be.visible').click()
        cy.url().should('be.equal', loginUrl, { timeout:10000 })
        cy.get(login_page.selectors.password_login_input).should('be.visible')
    })
}

module.exports = {
    prepareTest,
    accessToRegisterPageFromHP,
    loginUserWithWrongPassword,
    loginUser,
    registerUserAcceptedNewsletter,
    registerUserUnacceptedNewsletter,
    registerUserWithTooShortPassword,
    checkIfDirectRegistrationIsNotPossible,
    passwordReminder
}
