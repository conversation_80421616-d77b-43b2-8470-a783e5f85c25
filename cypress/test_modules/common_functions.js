import t01 from '../furniture_mocks/t01.json'
import t02 from '../furniture_mocks/t02.json'
import t03 from '../furniture_mocks/t03.json'

function extractCurrency(str) {
    const currencyRegex = /[^\d\s,.]+/
    return str.trim().match(currencyRegex)[0].replace('/\s+/', '')
}

function extractPriceValue(str) {
    return parseFloat(str.trim().replace(/[^\d,-]/g, '').replace(',', '.'))
}

function checkIfPricesAreEqual(priceA, priceB) {
    expect(extractPriceValue(priceA)).to.eql(extractPriceValue(priceB))
    expect(extractCurrency(priceA)).to.eql(extractCurrency(priceB))
}

function isProdEnv() {
    return Cypress.config('baseUrl') === Cypress.config('prodUrl')
}

function addT01BookcaseToCart() {
    cy.request('POST', `/api/v1/gallery/jetty/add_to_cart/`, t01)
    .then((response) => {
        expect(response.status).to.equal(201)
    })
}

function addT02BookcaseToCart() {
    cy.request('POST', `/api/v1/gallery/jetty/add_to_cart/`, t02)
    .then((response) => {
        expect(response.status).to.equal(201)
    })
}

function addT03ToCart() {
    cy.request('POST', `/api/v1/gallery/watty/add_to_cart/`, t03)
    .then((response) => {
        expect(response.status).to.equal(201)
    })
}

function addOriginalSampleToCart() {
    cy.request('POST', `/api/v1/gallery/sample_box/add_to_cart/`, {items: [{box_variant: 20000}]})
    .then((response) => {
        expect(response.status).to.equal(201)
    })
}

function addToneSampleToCart() {
    cy.request('POST', `/api/v1/gallery/sample_box/add_to_cart/`, {items: [{"box_variant":20300}]})
    .then((response) => {
        expect(response.status).to.equal(201)
    })
}

function addEdgeSampleToCart() {
    cy.request('POST', `/api/v1/gallery/sample_box/add_to_cart/`, {items: [{"box_variant":20400}]})
    .then((response) => {
        expect(response.status).to.equal(201)
    })
}

function addToCart(itemsList) {
    const itemsAvailable = {
        'OriginalBookcase': addT01BookcaseToCart,
        'ModernBookcase': addT02BookcaseToCart,
        'ToneWardrobe': addT03ToCart,
        'OriginalSample': addOriginalSampleToCart,
        'ToneSample': addToneSampleToCart,
        'EdgeSample': addEdgeSampleToCart,
    }
    itemsList.forEach((item) => { itemsAvailable[item]() })
    cy.reload()
    cy.wait(2000)
}

function generateRandomEmail(domain='cytest.pl') {
    const characters = 'abcdefghijklmnopqrstuvwxyz0123456789'
    let email = ''
    for (let i = 0; i < 8; i++) {
        email += characters.charAt(Math.floor(Math.random() * characters.length))
    }
    email += `@${domain}`
    return email
}

function checkIfCartItemsCountEquals(expectedCartItemCount) {
    cy.request("GET", "/api/v1/user-global/", { timeout:8000 }).then((response) => {
        expect(response.body.cartItemsCount).to.equal(expectedCartItemCount, { timeout:8000 })
    })
}

function checkIfWishlistItemsCountEquals(expectedWishlistItemCount) {
    cy.request("GET", "/api/v1/user-global/", { timeout:8000 }).then((response) => {
        expect(response.body.library_items).to.equal(expectedWishlistItemCount, { timeout:8000 })
    })
}

function verifyFooterDesktop() {
    cy.get('[data-testid="footer"]').as('footer').scrollIntoView()
    cy.get('@footer').find('[data-testid="footer-title"]').should('be.visible')
    cy.get('@footer').find('[data-testid="footer-accordion"]').should('be.visible')
    cy.get('@footer').find('[data-testid="footer-shipping"]').should('be.visible')
    cy.get('@footer').find('[data-testid="footer-change-region"]').should('be.visible')
    cy.get('@footer').find('[data-testid="footer-change-region-icon"]').should('be.visible')
    cy.get('@footer').find('[data-testid="footer-change-region-icon-caret"]').should('be.visible')
    cy.get('@footer').find('[data-testid="footer-payment-methods"]').should('be.visible')
    cy.get('@footer').find('[data-testid="footer-payment-icons"]').should('be.visible')
    cy.get('@footer').find('[data-testid="footer-security"]').should('be.visible')
    cy.get('@footer').find('[data-testid="footer-tylko-logo"]').should('be.visible')
    cy.get('@footer').find('[data-testid="footer-tylko-logo-icon"]').should('be.visible')
}

function verifyFooterMobile() {
    cy.get('[data-testid="footer"]').as('footer').scrollIntoView()
    cy.get('@footer').find('[data-testid="footer-title"]').should('be.visible')
    cy.get('@footer').find('[data-testid="footer-accordion"]').should('be.visible')
    cy.get('@footer').find('[data-testid="footer-accordion-button"]').should('be.visible')
    cy.get('@footer').find('[data-testid="footer-accordion-list"]').should('be.visible')
    cy.get('@footer').find('[data-testid="footer-accordion-list-item"]').should('be.visible')
    cy.get('@footer').find('[data-testid="footer-accordion-list-item-link"]').should('be.visible')
    cy.get('@footer').find('[data-testid="footer-shipping"]').should('be.visible')
    cy.get('@footer').find('[data-testid="footer-change-region"]').should('be.visible')
    cy.get('@footer').find('[data-testid="footer-change-region-icon"]').should('be.visible')
    cy.get('@footer').find('[data-testid="footer-change-region-icon-caret"]').should('be.visible')
    cy.get('@footer').find('[data-testid="footer-payment-methods"]').should('be.visible')
    cy.get('@footer').find('[data-testid="footer-payment-icons"]').should('be.visible')
    cy.get('@footer').find('[data-testid="footer-security"]').should('be.visible')
    cy.get('@footer').find('[data-testid="footer-tylko-logo"]').should('be.visible')
    cy.get('@footer').find('[data-testid="footer-tylko-logo-icon"]').should('be.visible')
}

function verifyNewsletter() {
    cy.get('[data-testid="footer-newsletter"]').as('newsletter').should('be.visible')
    cy.get('@newsletter').find('[data-testid=footer-newsletter-header]:visible')
    cy.get('@newsletter').find('[data-testid=footer-newsletter-subheader]:visible')
    cy.get('@newsletter').find('[data-testid=footer-newsletter-input]:visible')
    cy.get('@newsletter').find('[data-testid=footer-newsletter-button]:visible')
    cy.get('@newsletter').find('[data-testid=footer-newsletter-consent]:visible')
}

module.exports = {
    extractCurrency,
    extractPriceValue,
    checkIfPricesAreEqual,
    isProdEnv,
    addToCart,
    generateRandomEmail,
    checkIfCartItemsCountEquals,
    checkIfWishlistItemsCountEquals,
    verifyNewsletter,
    verifyFooterDesktop,
    verifyFooterMobile
}
