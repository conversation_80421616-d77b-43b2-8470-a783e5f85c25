import {
    isProdEnv,
    verifyFooterDesktop,
    verifyFooterMobile
 } from './common_functions'

function verifyConfigurator() {
    const configurator = 'section[id=pdp-section-configurator]'
    cy.get(configurator).should('be.visible')
}

function verifyUSP(number) {
    cy.get('section[data-observe-view="USP bar"]').as('USPBar').should('be.visible')
    cy.get('@USPBar').find('li').should('have.length.within', number-1, number).and('be.visible')
}

function verifyPDPNavBar() {
    cy.get('section[data-observe-view="Navigation bar"]').as('PDPNavBar')
    .find('.swiper-slide').should('have.length', 4)
}

function verifyGalleryOnDesktop() {
    cy.get('section[data-observe-view="Gallery"]').as('gallery').scrollIntoView()
    .find('[data-testid=pdp-mosaic-tile]').should('have.length', 8)
    cy.get('@gallery')
    .find('[data-testid=pdp-mosaic-tile-img]').should('have.length', 8)
    cy.get('@gallery').find('.pdp-gallery-swatches').should('be.visible')
}

function verifyGalleryOnMobile() {
    cy.get('section[data-observe-view="Gallery"]').as('gallery').scrollIntoView()
    cy.get('@gallery').find('[data-testid=pdp-mobile-gallery-main-picture]', { timeout:10000 })
    .should('have.length', 8).first().should('be.visible')
    cy.get('@gallery').find('[data-testid=pdp-mobile-gallery-carousel-picture]', { timeout:10000 })
    .as('picture').should('have.length', 8)
    cy.get('@picture').eq(0, { timeout:10000 }).should('be.visible')
    cy.get('@picture').eq(2, { timeout:10000 }).should('be.visible')
}

function verifyGalleryOnDesktopExpressions() {
    cy.get('section[data-observe-view="Gallery"]').as('gallery').scrollIntoView()
    .find('[data-testid=pdp-mosaic-tile]').should('have.length', 8)
    cy.get('@gallery')
    .find('[data-testid=pdp-mosaic-tile-img]').should('have.length.at.least', 6)
    cy.get('@gallery').find('.pdp-gallery-swatches').should('be.visible')
}

function verifyGalleryOnMobileExpressions() {
    cy.get('section[data-observe-view="Gallery"]').as('gallery').scrollIntoView()
    cy.get('@gallery').find('[data-testid=pdp-mobile-gallery-main-picture]', { timeout:10000 })
    .should('have.length.at.least', 6).first().should('be.visible')
    cy.get('@gallery').find('[data-testid=pdp-mobile-gallery-carousel-picture]', { timeout:10000 })
    .as('picture').should('have.length.at.least', 6)
    cy.get('@picture').eq(0, { timeout:10000 }).should('be.visible')
    cy.get('@picture').eq(2, { timeout:10000 }).should('be.visible')
}

function verifyProductDetails() {
    cy.get('[id=pdp-section-product-details]').filter(':visible').as('productDetails')
    //specification
    cy.get('section[data-observe-view="Specification"]').as('specification')
    cy.get('@specification').find('h2').should('be.visible')
    cy.get('@specification').find('.base-details').should('be.visible').and('have.length', 4)
    //product details
    cy.get('@productDetails').find('section').eq(1).find('header').as('header')
    cy.get('@header').find('p').should('be.visible')
    cy.get('@header').find('h2').should('be.visible')
    //line hero
    cy.get('[data-observe-view="Line hero"]')
    cy.get('[data-observe-view="Line hero bottom text"]').scrollIntoView().should('be.visible')
    //additional info
    cy.get('section[data-observe-view="Additional info"]').as('additionalInfo').scrollIntoView()
    .find('[data-testid=pdp-mosaic-tile]')
    .should('have.length.at.least', 5)
}

function verifyProductDetailsWithHighlights() {
    cy.get('[id=pdp-section-product-details]').filter(':visible').as('productDetails')
    //specification
    cy.get('section[data-observe-view="Specification"]').as('specification')
    cy.get('@specification').find('h2').should('be.visible')
    cy.get('@specification').find('.base-details').should('be.visible').and('have.length', 4)
    //product details
    cy.get('@productDetails').find('section').eq(1).find('header').as('header')
    cy.get('@header').find('p').should('be.visible')
    cy.get('@header').find('h2').should('be.visible')
    //line hero
    cy.get('[data-observe-view="Line hero"]')
    //highlights
    cy.get('section[data-section="section-highlights"]').as('highlights').scrollIntoView()
    cy.get('@highlights').find('[data-testid="heading"]').should('be.visible')
    cy.get('@highlights').find('[data-testid="subheading"]').should('be.visible')
    cy.get('@highlights').find('.grid-container')
    cy.get('@highlights').find('div.flex').should('have.length.gte', 5)
}

function verifyReviews() {
    cy.get('section[id="pdp-section-reviews"]').as('reviews').scrollIntoView()
    cy.get('@reviews').find('[data-testid=review-score]').should('be.visible')
    cy.get('@reviews').find('[data-testid=review-terms]').should('be.visible')
    cy.get('@reviews').find('[data-testid=review-show-all-link]').should('be.visible')

    if(isProdEnv()) {
        cy.get('@reviews').find('[data-testid=review-article]').as('articles').should('be.visible').and('have.length', 3)
        cy.get('@articles').find('[data-testid=review-article-stars]').should('be.visible').and('have.length', 3)
        cy.get('@articles').find('[data-testid=review-article-date]').should('be.visible').and('have.length', 3)
        cy.get('@articles').find('[data-testid=review-article-title]').should('be.visible').and('have.length', 3)
        cy.get('@articles').find('[data-testid=review-article-location]').should('be.visible').and('have.length', 3)
    }
}

function verifyGetInspired() {
    cy.get('section[data-observe-view="Get inspired"]').as('getInspired').scrollIntoView()
    cy.get('@getInspired').find('.sticky h1')
    cy.get('@getInspired').find('.sticky h2')
    cy.get('@getInspired').find('a').first().as('element')
    cy.get('@element').scrollIntoView({ block: 'center' }).should('be.visible')
    cy.get('@element').should('have.attr', 'href')
}

function verifyDiscoverYourPerfectFit() {
    cy.get('section[data-observe-view="Discover your perfect fit"]').as('perfectFit').scrollIntoView()
    cy.get('@perfectFit').find('p').should('be.visible')
    cy.get('@perfectFit').find('h2').should('be.visible')
    cy.get('@perfectFit').find('[data-testid=pdp-mosaic-tile]').should('be.visible').and('have.length', 3)
}

function verifyYouMightLike() {
    cy.get('section[data-observe-view="You might like"]').as('youMightLike').scrollIntoView()
    cy.get('@youMightLike').find('header p').should('be.visible')
    cy.get('@youMightLike').find('header h2').should('be.visible')
    cy.get('@youMightLike').find('.swiper').as('swiper').scrollIntoView()
    cy.get('@swiper').find('[data-testid="product-card-link"]').as('card').should('have.length.gte', 4)
    cy.get('@card').eq(0).should('be.visible')
}

function verifyExistanceOfVisibleKeys() {
    cy.contains(/(?:\S+_|\S+\.)+\S+/).should('not.exist')
}

function testPdpSectionsOnDesktop() {
    verifyConfigurator()
    verifyUSP(4)
    verifyPDPNavBar()
    verifyGalleryOnDesktop()
    verifyProductDetails()
    verifyReviews()
    verifyGetInspired()
    verifyDiscoverYourPerfectFit()
    verifyYouMightLike()
    verifyFooterDesktop()
}

function testPdpSectionsOnMobile() {
    verifyConfigurator()
    verifyUSP(4)
    verifyPDPNavBar()
    verifyGalleryOnMobile()
    verifyProductDetails()
    verifyReviews()
    verifyGetInspired()
    verifyDiscoverYourPerfectFit()
    verifyYouMightLike()
    verifyFooterMobile()
}

function testExpressionsPdpSectionsOnDesktop() {
    verifyConfigurator()
    verifyUSP(5)
    verifyPDPNavBar()
    verifyGalleryOnDesktopExpressions()
    verifyProductDetailsWithHighlights()
    verifyReviews()
    verifyGetInspired()
    verifyDiscoverYourPerfectFit()
    verifyYouMightLike()
    verifyFooterDesktop()
}

function testExpressionsPdpSectionsOnMobile() {
    verifyConfigurator()
    verifyUSP(5)
    verifyPDPNavBar()
    verifyGalleryOnMobileExpressions()
    verifyProductDetailsWithHighlights()
    verifyReviews()
    verifyGetInspired()
    verifyDiscoverYourPerfectFit()
    verifyYouMightLike()
    verifyFooterMobile()
}

function testOriginalsPdpSectionsOnDesktop() {
    verifyConfigurator()
    verifyUSP(5)
    verifyPDPNavBar()
    verifyGalleryOnDesktopExpressions()
    verifyProductDetailsWithHighlights()
    verifyReviews()
    verifyGetInspired()
    verifyDiscoverYourPerfectFit()
    verifyYouMightLike()
    verifyFooterDesktop()
}

function testOriginalsPdpSectionsOnMobile() {
    verifyConfigurator()
    verifyUSP(5)
    verifyPDPNavBar()
    verifyGalleryOnMobileExpressions()
    verifyProductDetailsWithHighlights()
    verifyReviews()
    verifyGetInspired()
    verifyDiscoverYourPerfectFit()
    verifyYouMightLike()
    verifyFooterMobile()
}

module.exports = {
    testPdpSectionsOnDesktop,
    testPdpSectionsOnMobile,
    testExpressionsPdpSectionsOnDesktop,
    testExpressionsPdpSectionsOnMobile,
    testOriginalsPdpSectionsOnDesktop,
    testOriginalsPdpSectionsOnMobile,
    verifyReviews
}
