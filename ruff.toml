line-length = 88
exclude = [
    ".*",
    "src/*/migrations/*.py",
    "tools_and_reports/*"
]

[lint.per-file-ignores]
"src/cstm_be/settings/*.py" = ["F403", "F405"]

[lint]
# enable opt-in rules from preview
preview = true
fixable = ["ALL"]
select = [
    "B", # flake8-bugbear
    "C4", # flake8-comprehensions
    "C90", # mccabe
    "DJ", # flake8-django
    "E", # pycodestyle errors
    "F", # pyflakes
    "I", # isort
    "ICN", # flake8-import-conventions
    "N", # pep8-naming
    "PERF", # perflint
    "PIE", # flake8-pie
    "PT", # flake8-pytest-style
    "Q", # flake8-quotes
    "RUF", # ruff
    "S", # flake8-bandit
    "T10", # flake8-debugger
    "W", # pycodestyle warnings
]
ignore = [
    "B024", # abstract-base-class-without-abstract-method
    "B904", # raise-without-from-inside-except
    "C901", # complex-structure
    "DJ008", # django-model-without-dunder-str
    "N816", # mixed-case-variable-in-global-scope
    "PT023", # pytest-incorrect-mark-parentheses-style
    "Q001", # bad-quotes-multiline-string
    "RUF012", # mutable-class-default
    "RUF052", # used-dummy-variable
    "S101", # assert
    "S113", # request-without-timeout
]
external = ["WPS"]

[lint.flake8-pytest-style]
fixture-parentheses = false

[lint.flake8-quotes]
inline-quotes = "single"
multiline-quotes = "single"

[lint.isort]
lines-between-types = 1
known-first-party = [
    "abtests",
    "accounting",
    "admin_customization",
    "automating_batching",
    "b2b",
    "catalogue",
    "checkout",
    "complaints",
    "cstm_be",
    "custom",
    "custom_audiences",
    "customer_service",
    "dixa",
    "dynamic_delivery",
    "ecommerce_api",
    "events",
    "feeds",
    "free_returns",
    "frontend_cms",
    "gallery",
    "gallery_editor",
    "internal_api",
    "invoice",
    "items_for_render",
    "kpi",
    "logger",
    "loose_ends",
    "mailing",
    "material_recovery",
    "model_transfers",
    "orders",
    "payments",
    "pricing_v3",
    "producers",
    "product_feeds",
    "production_margins",
    "promotions",
    "rating_tool",
    "regions",
    "render_tasks",
    "rest_auth",
    "reviews",
    "shortener",
    "taskapp",
    "user_consents",
    "user_profile",
    "vouchers",
    "waiting_list",
    "warehouse",
]
known-third-party = [
    "health_check",
    "rest_framework_simplejwt",
    "deepdiff",
    "pytest",
    "barcode",
    "cairosvg",
    "lxml"
]
sections = { "django" = ["django", "rest_framework"] }
section-order = [
    "future",
    "standard-library",
    "django",
    "third-party",
    "first-party",
    "local-folder"
]

[format]
quote-style = "preserve"
