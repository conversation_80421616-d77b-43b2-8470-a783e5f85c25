import random

from django.conf import settings
from django.core.validators import (
    MaxValueValidator,
    MinValueValidator,
)
from django.db import models
from django.db.models import QuerySet
from django.http.request import HttpRequest

from abtests.enums import TrafficTypeChoices
from custom.utils.in_memory_cache import expiring_lru_cache
from regions.types import RegionLikeObject


class ABTestManager(models.Manager):
    @expiring_lru_cache(ttl=settings.ABTESTS_CACHE_TTL_SECONDS)
    def get_tests_cached(self) -> QuerySet['ABTest']:
        return self.get_queryset().prefetch_related('available_regions')

    def get_active_tests_cached_list(self, region: RegionLikeObject) -> list['ABTest']:
        return [
            test
            for test in self.get_tests_cached()
            if test.active
            and region.id in [test.id for test in test.available_regions.all()]
        ]

    get_active_tests_cached_list.cache_clear = get_tests_cached.cache_clear


class ABTest(models.Model):
    objects = ABTestManager()

    codename = models.CharField(max_length=255)  # noqa: DJ012
    description = models.TextField()
    active = models.BooleanField(default=False)
    start_date = models.DateField(null=True, blank=True)
    end_date = models.DateField(null=True, blank=True)
    rate_split = models.IntegerField(
        help_text='0-100 rate for variation',
        default=50,
        validators=[
            MinValueValidator(0),
            MaxValueValidator(100),
        ],
    )
    traffic_type = models.IntegerField(choices=TrafficTypeChoices.choices)
    ga_custom_dimension = models.CharField(  # noqa: DJ001
        max_length=255,
        null=True,
        blank=True,
    )
    results_description = models.TextField(null=True, blank=True)  # noqa: DJ001
    results_url = models.TextField(null=True, blank=True)  # noqa: DJ001
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    feature_flag = models.BooleanField(default=False)
    available_regions = models.ManyToManyField('regions.Region', blank=True)

    class Meta:
        verbose_name = 'A/b test'
        verbose_name_plural = 'A/B tests'

    def __str__(self):
        return 'Ab test {} - {}: {} , active:{}'.format(
            self.id,
            self.codename,
            self.description,
            self.active,
        )

    def choose_variant(self, request: HttpRequest) -> bool:
        """Randomly choose one of A/B tests variants."""
        return random.randrange(100) < self.rate_split  # noqa: S311
