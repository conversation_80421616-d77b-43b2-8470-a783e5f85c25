import datetime
import re

from django.conf import settings
from rest_framework.request import Request

from abtests.models import ABTest
from django_user_agents.templatetags.user_agents import (
    is_ab_test,
    is_active_feature_flag,
)
from regions.cached_region import CachedRegionData


def should_add_ab_test(request, ab_test):
    return (
        not is_ab_test_ignored_path(request.path)
        and ab_test.codename not in request.session
        and ab_test.feature_flag is False
        and request.COOKIES.get(ab_test.codename, None) is None
    )


def is_ab_test_ignored_path(path: str):
    return path.startswith('/api') and not re.match(r'^/api(/v\d+)?/user-global/', path)


def get_variant_cookie_value_for(request, ab_test):
    request_session = getattr(request, 'session', dict())  # noqa: C408
    variant = request_session.get(ab_test.codename, ab_test.choose_variant(request))
    return 'ok' if variant else 'nok'


def set_response_ab_test_cookie(request, response, ab_test):
    response.set_cookie(
        ab_test.codename,
        get_variant_cookie_value_for(request, ab_test),
        domain=settings.SESSION_COOKIE_DOMAIN,
        secure=settings.SESSION_COOKIE_SECURE or None,
        expires=datetime.datetime.today() + datetime.timedelta(days=120),
    )
    if hasattr(request, 'session'):
        request.session.pop(ab_test.codename, None)


def get_sorted_active_ab_tests_data(
    request: Request, region: CachedRegionData
) -> tuple[dict, list]:
    ab_tests = {}
    feature_flags = []
    for test in ABTest.objects.get_active_tests_cached_list(region):
        if is_active_feature_flag(request, test.codename):
            feature_flags.append(test)
        else:
            ab_tests[test] = is_ab_test(request, test.codename)
    return ab_tests, feature_flags
