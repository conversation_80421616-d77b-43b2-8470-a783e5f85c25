import calendar
import json
import math

from datetime import (
    date,
    datetime,
    timedelta,
)
from decimal import Decimal

from django.conf import settings

import requests

from celery import shared_task
from past.utils import old_div

from admin_customization.anomaly_checks import (
    DeliveredButBadStatusCheck,
    InvoiceNumbering,
    OrdersInProductionCheckWithDifferentProductItems,
    SalesReportCheck,
)
from cstm_be.media_storage import private_media_storage
from custom.enums import Furniture
from custom.models import GlobalSettings
from custom.utils.decorators import production_only
from gallery.services.importer import ImportObjectsService
from kpi import kpis
from orders.enums import OrderType
from producers.choices import ProductStatus
from producers.models import ProductStatusHistory


@shared_task
@production_only
def email_delivered_but_bad_status_task():
    DeliveredButBadStatusCheck().check_and_notify()


@shared_task
@production_only
def email_sales_report_check_task():
    SalesReportCheck().check_and_notify()


@shared_task
@production_only
def email_orders_in_production_check_task():
    OrdersInProductionCheckWithDifferentProductItems().check_and_notify()


@shared_task
@production_only
def email_invoice_numbering_check_task():
    InvoiceNumbering().check_and_notify()


def money_checker():
    if not settings.SLACK_WEBHOOK or not settings.IS_PRODUCTION:
        return

    kpi_group = kpis.KPIMonthlyStandardGroup('Dashboard')
    kpi_value = kpis.KPIOrderTotalValue(kpi_group)
    kpi_count = kpis.KPIOrderFinished(kpi_group)
    kpi_value_segment_n = list(kpi_value.kpi_group.time_segments.segments.keys())[-1]
    kpi_count_segment_n = list(kpi_count.kpi_group.time_segments.segments.keys())[-1]
    kpi_group2 = kpis.KPIDailyGroup('Dashboard')
    kpi2 = kpis.KPIOrderTotalValue(kpi_group2)
    kpi_count_daily = kpis.KPIOrderFinished(kpi_group2)
    kpi_count_segment_n2 = list(kpi2.kpi_group.time_segments.segments.keys())[-1]
    kpi_time_segment_n2 = list(kpi2.kpi_group.time_segments.segments.keys())[-1]

    actual_target = GlobalSettings.actual_month_target()
    now = datetime.now()
    order_total_value = round(
        old_div(kpi_value.get_value(kpi_value_segment_n).value, Decimal(1000)), 2
    )
    order_daily_value = round(
        old_div(kpi2.get_value(kpi_time_segment_n2).value, Decimal(1000)),
        2,
    )
    order_total_count = kpi_count.get_value(kpi_count_segment_n).value
    order_daily_count = kpi_count_daily.get_value(kpi_count_segment_n2).value

    target_percentage = math.ceil((old_div(order_total_value, actual_target)) * 100)
    month_percentage = math.ceil(
        (now.day / float(calendar.monthrange(now.year, now.month)[1])) * 100
    )
    days_left = calendar.monthrange(now.year, now.month)[1] - (now.day - 1)
    target_per_day_left = round(
        old_div(
            (actual_target - order_total_value),
            (days_left if days_left > 0 else 1),
        ),
        2,
    )
    was_per_day_left = round(
        old_div(
            order_total_value,
            ((calendar.monthrange(now.year, now.month)[1] - days_left + 1) or 1),
        ),
        2,
    )
    text = f'''*{order_daily_count}* Today probably happy customers
*{order_total_count}* This month probably happy customers

*{order_daily_value}k* Today net sale in this month it was {was_per_day_left}k daily
*{order_total_value}k* This month net sale, {target_percentage}%
of target {actual_target}k / {month_percentage}% of month
It`s should be {target_per_day_left}k per day left
'''
    alarm = {
        'text': text,
        'channel': 'tylkopost',
        'username': 'Today news!',
        'icon_emoji': ':dollar:',
    }

    requests.post(
        settings.SLACK_WEBHOOK,
        json=alarm,
    )


@shared_task
def money_every_day():
    money_checker()


def _get_last_week_dates():
    days_to_go_back = 7 + date.today().weekday()
    changed_at_from = date.today() - timedelta(days=days_to_go_back)
    changed_at_to = changed_at_from + timedelta(days=7)
    return changed_at_from, changed_at_to


@shared_task
def production_releases():
    if not settings.SLACK_WEBHOOK or not settings.IS_PRODUCTION:
        return

    changed_at_from, changed_at_to = _get_last_week_dates()

    (
        released_products_month_jetty,
        released_products_week_jetty,
    ) = _get_product_to_report(changed_at_from, changed_at_to, Furniture.jetty.value)

    (
        released_products_month_watty,
        released_products_week__watty,
    ) = _get_product_to_report(changed_at_from, changed_at_to, Furniture.watty.value)

    text = f'''Last week we produced *{released_products_week_jetty.count()}*
shelves Original Classics and Original Moderns
and *{released_products_week__watty.count()}* Tones and Edges
In total this month *{released_products_month_jetty.count()}*
Original Classics and Original Moderns
and *{released_products_month_watty.count()}* Tones and Edges
'''

    alarm = {
        'text': text,
        'channel': 'tylkopost',
        'username': 'Operation news!',
        'icon_emoji': ':factory:',
    }

    requests.post(
        settings.SLACK_WEBHOOK,
        json=alarm,
    )


def _get_product_to_report(changed_at_from, changed_at_to, ptype):
    released_products_week = (
        ProductStatusHistory.objects.filter(
            status__in=[
                ProductStatus.TO_BE_SHIPPED,
                ProductStatus.ABORTED_DONE,
            ],
            changed_at__gte=changed_at_from,
            changed_at__lte=changed_at_to,
            product__cached_product_type=ptype,
        )
        .exclude(product__order__order_type=OrderType.COMPLAINT)
        .order_by('product')
        .distinct('product')
    )
    changed_at_from = date.today().replace(day=1)
    released_products_month = (
        ProductStatusHistory.objects.filter(
            status__in=[
                ProductStatus.TO_BE_SHIPPED,
                ProductStatus.ABORTED_DONE,
            ],
            changed_at__gte=changed_at_from,
            changed_at__lte=changed_at_to,
            product__cached_product_type=ptype,
        )
        .exclude(product__order__order_type=OrderType.COMPLAINT)
        .order_by('product')
        .distinct('product')
    )
    return released_products_month, released_products_week


@shared_task()
def create_objects_from_json(
    file_path,
    owner_id,
    process_to_production,
    is_influencers,
):
    file = private_media_storage.open(file_path)
    json_data_from_file = json.load(file)
    service = ImportObjectsService(
        json_data=json_data_from_file,
        owner_id=owner_id,
    )
    service.create_objects_from_json(process_to_production, is_influencers)
    private_media_storage.delete(file_path)
