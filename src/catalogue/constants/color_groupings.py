"""This module groups colors into distinct sets using constants.

These groups have multiple purposes, such as identifying bad neighbours,
managing color variants to prevent repetition, and adjusting color rules
for specific categories.
"""

from custom.enums import (
    ShelfType,
    Sofa01Color,
    Type01Color,
    Type02Color,
    Type03Color,
    Type13Color,
    Type23Color,
    Type24Color,
    Type25Color,
    VeneerType01Color,
    VeneerType13Color,
)

STRICT_WHITE_COLORS = {
    (ShelfType.TYPE01, Type01Color.WHITE),
    (ShelfType.TYPE02, Type02Color.WHITE),
    (ShelfType.TYPE03, Type03Color.WHITE),
    (ShelfType.TYPE13, Type13Color.WHITE),
    (ShelfType.TYPE13, Type13Color.WHITE_PLYWOOD),
    (ShelfType.TYPE23, Type23Color.OFF_WHITE),
    (ShelfType.TYPE24, Type24Color.OFF_WHITE),
    (ShelfType.TYPE25, Type25Color.OFF_WHITE),
    (ShelfType.SOFA_TYPE01, Sofa01Color.CORDUROY_ECRU),
}

WHITE_COLORS = {
    (ShelfType.TYPE01, Type01Color.WHITE),
    (ShelfType.TYPE02, Type02Color.WHITE),
    *[(ShelfType.TYPE03, color) for color in Type03Color.filter_by('WHITE')],
    *[(ShelfType.TYPE13, color) for color in Type13Color.filter_by('WHITE')],
    (ShelfType.TYPE23, Type23Color.OFF_WHITE),
    (ShelfType.TYPE24, Type24Color.OFF_WHITE),
    (ShelfType.TYPE25, Type25Color.OFF_WHITE),
    (ShelfType.SOFA_TYPE01, Sofa01Color.CORDUROY_ECRU),
}

BLACK_COLORS = {
    (ShelfType.TYPE01, Type01Color.BLACK),
    (ShelfType.TYPE02, Type02Color.MATTE_BLACK),
    (ShelfType.TYPE13, Type13Color.BLACK_PLYWOOD),
    (ShelfType.TYPE13, Type13Color.BLACK),
}

STRICT_GREY_COLORS = {
    (ShelfType.TYPE01, Type01Color.GREY),
    (ShelfType.TYPE02, Type02Color.GRAY),
    (ShelfType.TYPE02, Type02Color.STONE_GRAY),
    (ShelfType.TYPE13, Type13Color.GRAY),
    (ShelfType.TYPE13, Type13Color.GRAY_PLYWOOD),
    (ShelfType.SOFA_TYPE01, Sofa01Color.CORDUROY_STEEL),
}

DARK_GREY_COLORS = {
    (ShelfType.TYPE13, Type13Color.DARK_GRAY),
}

GREY_COLORS = {
    (ShelfType.TYPE01, Type01Color.GREY),
    (ShelfType.TYPE02, Type02Color.GRAY),
    (ShelfType.TYPE02, Type02Color.STONE_GRAY),
    (ShelfType.TYPE02, Type02Color.WALNUT),
    (ShelfType.TYPE03, Type03Color.GRAPHITE),
    (ShelfType.TYPE03, Type03Color.GRAPHITE_PINK),
    (ShelfType.TYPE13, Type13Color.GRAY),
    (ShelfType.TYPE13, Type13Color.GRAY_PLYWOOD),
    (ShelfType.TYPE13, Type13Color.DARK_GRAY),
    (ShelfType.SOFA_TYPE01, Sofa01Color.CORDUROY_STEEL),
    (ShelfType.SOFA_TYPE01, Sofa01Color.REWOOL2_LIGHT_GRAY),
}

STRICT_BEIGE_COLORS = {
    (ShelfType.TYPE02, Type02Color.COTTON),
    (ShelfType.TYPE03, Type03Color.BEIGE),
    (ShelfType.TYPE13, Type13Color.BEIGE),
    (ShelfType.TYPE23, Type23Color.OYSTER_BEIGE),
    (ShelfType.TYPE24, Type24Color.OYSTER_BEIGE),
    (ShelfType.TYPE25, Type25Color.OYSTER_BEIGE),
    (ShelfType.SOFA_TYPE01, Sofa01Color.CORDUROY_ROCK),
}

WOODEN_COLORS = {
    (ShelfType.TYPE01, VeneerType01Color.ASH),
    (ShelfType.VENEER_TYPE01, VeneerType01Color.OAK),
    (ShelfType.VENEER_TYPE01, VeneerType01Color.DARK_OAK),
    (ShelfType.VENEER_TYPE13, VeneerType13Color.DARK),
    (ShelfType.VENEER_TYPE13, VeneerType13Color.LIGHT),
}

SAND_COLORS = {
    (ShelfType.TYPE02, Type02Color.SAND),
    (ShelfType.TYPE13, Type13Color.SAND),
}

MUSTARD_YELLOW_COLORS = {
    (ShelfType.TYPE02, Type02Color.MUSTARD_YELLOW),
    (ShelfType.TYPE13, Type13Color.MUSTARD_YELLOW),
    (ShelfType.SOFA_TYPE01, Sofa01Color.CORDUROY_TOBACCO),
}

BEIGE_COLORS = {
    (ShelfType.TYPE02, Type02Color.COTTON),
    (ShelfType.TYPE02, Type02Color.SAND),
    (ShelfType.TYPE02, Type02Color.MUSTARD_YELLOW),
    (ShelfType.VENEER_TYPE01, VeneerType01Color.ASH),
    (ShelfType.VENEER_TYPE01, VeneerType01Color.OAK),
    (ShelfType.VENEER_TYPE13, VeneerType13Color.LIGHT),
    *[(ShelfType.TYPE03, color) for color in Type03Color.filter_by('BEIGE')],
    *[(ShelfType.TYPE03, color) for color in Type03Color.filter_by('CASHMERE')],
    (ShelfType.TYPE13, Type13Color.BEIGE),
    (ShelfType.TYPE13, Type13Color.SAND),
    (ShelfType.TYPE13, Type13Color.MUSTARD_YELLOW),
    (ShelfType.TYPE23, Type23Color.OYSTER_BEIGE),
    (ShelfType.TYPE24, Type24Color.OYSTER_BEIGE),
    (ShelfType.TYPE25, Type25Color.OYSTER_BEIGE),
    (ShelfType.SOFA_TYPE01, Sofa01Color.CORDUROY_ROCK),
    (ShelfType.SOFA_TYPE01, Sofa01Color.CORDUROY_ECRU),
    (ShelfType.SOFA_TYPE01, Sofa01Color.CORDUROY_TOBACCO),
}

VERY_DARK_COLORS = {
    (ShelfType.TYPE01, Type01Color.BLACK),
    (ShelfType.TYPE02, Type02Color.MIDNIGHT_BLUE),
    (ShelfType.TYPE02, Type02Color.MATTE_BLACK),
    *[(ShelfType.TYPE03, color) for color in Type03Color.filter_by('GRAPHITE')],
    *[(ShelfType.TYPE13, color) for color in Type13Color.filter_by('BLACK')],
    (ShelfType.TYPE23, Type23Color.INKY_BLACK),
    (ShelfType.TYPE24, Type24Color.INKY_BLACK),
    (ShelfType.TYPE25, Type25Color.INKY_BLACK),
}

LIGHT_VENEER_COLORS = {
    (ShelfType.VENEER_TYPE01, VeneerType01Color.ASH),
    (ShelfType.VENEER_TYPE01, VeneerType01Color.OAK),
    (ShelfType.VENEER_TYPE13, VeneerType13Color.LIGHT),
}

RED_AND_PINK_COLORS = {
    (ShelfType.TYPE01, Type01Color.DUSTY_PINK),
    (ShelfType.TYPE02, Type02Color.TERRACOTTA),
    (ShelfType.TYPE02, Type02Color.BURGUNDY),
    (ShelfType.TYPE02, Type02Color.REISINGERS_PINK),
    *[(ShelfType.TYPE03, color) for color in Type03Color.filter_by('PINK')],
    (ShelfType.TYPE23, Type23Color.POWDER_PINK),
    (ShelfType.TYPE24, Type24Color.POWDER_PINK),
    (ShelfType.TYPE25, Type25Color.POWDER_PINK),
    (ShelfType.SOFA_TYPE01, Sofa01Color.REWOOL2_SHADOW_PINK),
    (ShelfType.SOFA_TYPE01, Sofa01Color.CORDUROY_PINK),
}

YELLOW_COLORS = {
    (ShelfType.TYPE01, Type01Color.YELLOW),
    (ShelfType.TYPE02, Type02Color.MUSTARD_YELLOW),
    (ShelfType.TYPE13, Type13Color.MUSTARD_YELLOW),
    (ShelfType.SOFA_TYPE01, Sofa01Color.REWOOL2_BUTTER_YELLOW),
}

DIFFERENT_EDGES_COLORS = {
    (ShelfType.TYPE02, Type02Color.SAND),
    (ShelfType.TYPE02, Type02Color.MUSTARD_YELLOW),
    (ShelfType.TYPE02, Type02Color.WALNUT),
    (ShelfType.TYPE13, Type13Color.DARK_GRAY),
    (ShelfType.TYPE13, Type13Color.SAND),
    (ShelfType.TYPE13, Type13Color.MUSTARD_YELLOW),
}

BLUE_COLORS = {
    (ShelfType.TYPE01, Type01Color.BLUE),
    (ShelfType.TYPE02, Type02Color.SKY_BLUE),
    *[(ShelfType.TYPE03, color) for color in Type03Color.filter_by('BLUE')],
    (ShelfType.SOFA_TYPE01, Sofa01Color.CORDUROY_BLUE_KLEIN),
    (ShelfType.SOFA_TYPE01, Sofa01Color.REWOOL2_BABY_BLUE),
}

GREEN_COLORS = {
    (ShelfType.TYPE01, Type01Color.MOSS_GREEN),
    (ShelfType.TYPE02, Type02Color.SAGE_GREEN),
    *[(ShelfType.TYPE03, color) for color in Type03Color.filter_by('GREEN')],
    (ShelfType.TYPE13, Type13Color.OLIVE_GREEN),
    (ShelfType.TYPE23, Type23Color.PISTACHIO_GREEN),
    (ShelfType.TYPE24, Type24Color.PISTACHIO_GREEN),
    (ShelfType.TYPE25, Type25Color.PISTACHIO_GREEN),
    (ShelfType.SOFA_TYPE01, Sofa01Color.REWOOL2_OLIVE_GREEN),
    (ShelfType.SOFA_TYPE01, Sofa01Color.REWOOL2_GREEN),
    (ShelfType.SOFA_TYPE01, Sofa01Color.CORDUROY_CAMOUFLAGE),
}

BROWN_COLORS = {
    (ShelfType.TYPE01, Type01Color.DARK_BROWN),
    (ShelfType.TYPE02, Type02Color.WALNUT),
    (ShelfType.TYPE13, Type13Color.CLAY_BROWN),
    (ShelfType.VENEER_TYPE01, VeneerType01Color.DARK_OAK),
    (ShelfType.VENEER_TYPE13, VeneerType13Color.DARK),
    (ShelfType.SOFA_TYPE01, Sofa01Color.REWOOL2_BROWN),
}

BEIGE_DIFFERENT_INTERIOR_COLORS = {
    (ShelfType.TYPE03, Type03Color.BEIGE_PINK),
    (ShelfType.TYPE03, Type03Color.CASHMERE_STONE_GRAY),
    (ShelfType.TYPE03, Type03Color.CASHMERE_SAGE_GREEN),
    (ShelfType.TYPE03, Type03Color.CASHMERE_MISTY_BLUE),
}

WHITE_DIFFERENT_INTERIOR_COLORS = {
    (ShelfType.TYPE03, Type03Color.WHITE_PINK),
    (ShelfType.TYPE03, Type03Color.WHITE_STONE_GRAY),
    (ShelfType.TYPE03, Type03Color.WHITE_SAGE_GREEN),
    (ShelfType.TYPE03, Type03Color.WHITE_MISTY_BLUE),
}

GRAPHITE_DIFFERENT_INTERIOR_COLORS = {
    (ShelfType.TYPE03, Type03Color.GRAPHITE_PINK),
    (ShelfType.TYPE03, Type03Color.GRAPHITE_STONE_GRAY),
    (ShelfType.TYPE03, Type03Color.GRAPHITE_SAGE_GREEN),
    (ShelfType.TYPE03, Type03Color.GRAPHITE_MISTY_BLUE),
}

DIFFERENT_INTERIOR_COLORS = (
    BEIGE_DIFFERENT_INTERIOR_COLORS
    | WHITE_DIFFERENT_INTERIOR_COLORS
    | GRAPHITE_DIFFERENT_INTERIOR_COLORS
)
