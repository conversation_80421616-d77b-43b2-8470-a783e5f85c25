import logging

from typing import TYPE_CHECKING

from catalogue.constants import CATALOGUE_COLORS_MAP
from catalogue.enums import FurnitureAttributesEnum
from custom.enums import (
    Furniture,
    ShelfType,
    Type02Color,
)
from gallery.enums import (
    Fabric,
    ShelfPatternEnum,
)

if TYPE_CHECKING:
    from catalogue.models import CatalogueEntry

logger = logging.getLogger('cstm')


class AttributesAssigner:
    def __init__(self, entry: 'CatalogueEntry'):
        self.entry = entry
        self.furniture = entry.furniture
        self.batch = set()

    def assign_attributes(self) -> None:
        self._assign_color_attributes()
        self._assign_common_attributes()
        if self.furniture.furniture_type == Furniture.jetty.value:
            self._assign_jetty_specific_attributes()
        elif self.furniture.furniture_type == Furniture.sotty.value:
            self._assign_sotty_specific_attributes()
        elif self.furniture.furniture_type == Furniture.watty.value:
            self._assign_watty_specific_attributes()

        self.entry.attributes.add(*self.batch)

    def _assign_color_attributes(self) -> None:
        if hasattr(self.furniture, 'materials'):
            color_data = [
                (self.furniture.shelf_type, material)
                for material in self.furniture.materials
            ]
            # furniture is multicolor
            if len(self.furniture.materials) > 1:
                self.batch.add(FurnitureAttributesEnum.MIX)
        else:
            color_data = [(self.furniture.shelf_type, self.furniture.material)]
        for color_attribute, colors in CATALOGUE_COLORS_MAP.items():
            for color in color_data:
                if color in colors:
                    self.batch.add(color_attribute)

    def _assign_common_attributes(self) -> None:
        if getattr(self.furniture, 'drawers', []):
            self.batch.add(FurnitureAttributesEnum.DRAWERS)
        if getattr(self.furniture, 'doors', []):
            self.batch.add(FurnitureAttributesEnum.DOORS)
        if not getattr(self.furniture, 'drawers', []) and not getattr(
            self.furniture, 'doors', []
        ):
            self.batch.add(FurnitureAttributesEnum.OPEN)

        if self.entry.is_in_promo:
            self.batch.add(FurnitureAttributesEnum.SALE)
        if self.entry.is_new_arrival:
            self.batch.add(FurnitureAttributesEnum.NEW_ARRIVAL)
        if self.entry.is_special:
            self.batch.add(FurnitureAttributesEnum.SPECIAL)

        if self.furniture.color.is_special_edition_color:
            self.batch.add(FurnitureAttributesEnum.SPECIAL_EDITION)

    def _assign_jetty_specific_attributes(self) -> None:
        if self.furniture.shelf_type == ShelfType.TYPE01:
            self.batch.add(FurnitureAttributesEnum.PLYWOOD)
        if (
            self.furniture.shelf_type == ShelfType.TYPE02
            and self.furniture.material == Type02Color.MATTE_BLACK
        ):
            self.batch.add(FurnitureAttributesEnum.MATTE)
        elif self.furniture.shelf_type == ShelfType.TYPE02:
            self.batch.add(FurnitureAttributesEnum.CLASSIC_MATTE)

        if self.furniture.has_plinth:
            self.batch.add(FurnitureAttributesEnum.PLINTH)
        if self.furniture.long_legs:
            self.batch.add(FurnitureAttributesEnum.LEGS)
        if self.furniture.cable_management:
            self.batch.add(FurnitureAttributesEnum.CABLE_MANAGEMENT)
        if self.furniture.has_top_or_bottom_storage:
            self.batch.add(FurnitureAttributesEnum.EXTRA_STORAGE)
        if self.furniture.pattern in {0, 2}:  # pattern 0 - "slant", 2 - "pattern"
            self.batch.add(FurnitureAttributesEnum.SLANTED_STYLE)
        if self.furniture.has_visible_backpanels:
            self.batch.add(FurnitureAttributesEnum.VISIBLE_BACKPANELS)

        self.batch.add(ShelfPatternEnum(self.furniture.pattern).label)

    def _assign_sotty_specific_attributes(self) -> None:
        from gallery.models import Sotty

        if len(self.furniture.armrests) == 2:
            self.batch.add(FurnitureAttributesEnum.ARMREST)
        elif len(self.furniture.armrests) == 1:
            self.batch.add(FurnitureAttributesEnum.SEMI_OPENEND)
        elif not self.furniture.armrests:
            self.batch.add(FurnitureAttributesEnum.OPENEND)

        if self.furniture.corners:
            self.batch.add(FurnitureAttributesEnum.CORNER)
        if self.furniture.seaters:
            self.batch.add(FurnitureAttributesEnum.SEATING)
        if self.furniture.chaise_longues:
            self.batch.add(FurnitureAttributesEnum.CHAISE_LONGUE)
        if self.furniture.has_extended_chaise_longue_module:
            self.batch.add(FurnitureAttributesEnum.EXTENDED_CHAISE_LONGUE)

        if self.furniture.fabric == Fabric.CORDUROY:
            self.batch.add(FurnitureAttributesEnum.CORDUROY)
        if self.furniture.fabric == Fabric.WOOL:
            self.batch.add(FurnitureAttributesEnum.WOOL)

        if self.furniture.orientation == Sotty.Orientation.SYMMETRICAL:
            self.batch.add(FurnitureAttributesEnum.ORIENTATION_SYMMETRICAL)
        elif self.furniture.orientation == Sotty.Orientation.LEFT:
            self.batch.add(FurnitureAttributesEnum.ORIENTATION_LEFT)
        elif self.furniture.orientation == Sotty.Orientation.RIGHT:
            self.batch.add(FurnitureAttributesEnum.ORIENTATION_RIGHT)

    def _assign_watty_specific_attributes(self) -> None:
        if self.furniture.has_external_drawers:
            self.batch.add(FurnitureAttributesEnum.EXTERNAL_DRAWERS)
        if self.furniture.has_internal_drawers:
            self.batch.add(FurnitureAttributesEnum.INTERNAL_DRAWERS)
        if self.furniture.bars:
            self.batch.add(FurnitureAttributesEnum.HANG)
        if self.furniture.has_double_hangs:
            self.batch.add(FurnitureAttributesEnum.DOUBLE_HANG)
        if self.furniture.lighting:
            self.batch.add(FurnitureAttributesEnum.LIGHTING)
        if self.furniture.shelf_type in ShelfType.get_tone_shelf_types():
            self._assign_tone_specific_attributes()
        elif self.furniture.shelf_type in ShelfType.get_edge_shelf_types():
            self._assign_edge_specific_attributes()

    def _assign_tone_specific_attributes(self) -> None:
        self.batch.add(FurnitureAttributesEnum.MATTE)
        self.batch.add(FurnitureAttributesEnum.FULLY_CLOSED)
        if self.furniture.shelf_type in [
            ShelfType.TYPE23,
            ShelfType.TYPE24,
            ShelfType.TYPE25,
        ]:
            self.batch.add(FurnitureAttributesEnum.GRID)
        if self.furniture.shelf_type == ShelfType.TYPE24:
            self.batch.add(FurnitureAttributesEnum.HANGING)

        if self.furniture.shelf_type == ShelfType.TYPE25:
            self.batch.add(FurnitureAttributesEnum.LEGS)

    def _assign_edge_specific_attributes(self) -> None:
        if self.furniture.color.is_plywood_color:
            self.batch.add(FurnitureAttributesEnum.PLYWOOD)
        elif self.entry.shelf_type == ShelfType.VENEER_TYPE13.value:
            self.batch.add(FurnitureAttributesEnum.WOODEN_EFFECT)
        else:
            self.batch.add(FurnitureAttributesEnum.CLASSIC_MATTE)
        if self.furniture.has_front_facing_rail:
            self.batch.add(FurnitureAttributesEnum.FRONT_FACING_RAIL)
        if self.furniture.cable_management:
            self.batch.add(FurnitureAttributesEnum.CABLE_MANAGEMENT)
        if self.furniture.has_mixed_height:
            self.batch.add(FurnitureAttributesEnum.MIXED_HEIGHT)
        if not self.furniture.doors:
            self.batch.add(FurnitureAttributesEnum.FULLY_OPEN)
            self.batch.add(FurnitureAttributesEnum.OPEN_COMPARTMENTS)
        else:
            if not self.furniture.components:
                return
            try:
                is_closed = all(
                    c['doors_coverage'] == 'full' for c in self.furniture.components
                )
            except KeyError:
                return
            if is_closed:
                self.batch.add(FurnitureAttributesEnum.FULLY_CLOSED)
            else:
                self.batch.add(FurnitureAttributesEnum.PARTIALLY_OPEN)
                self.batch.add(FurnitureAttributesEnum.OPEN_COMPARTMENTS)

    def update_new_arrival_attribute(self) -> None:
        """New arrival attribute should be updated when it is decided that entries
        should not be labeled as new arrivals anymore.
        """
        self._update_attribute(
            FurnitureAttributesEnum.NEW_ARRIVAL,
            self.entry.is_new_arrival,
        )

    def update_promo_attribute(self) -> None:
        """Promo attribute should be updated after the promotion changes are made."""
        self._update_attribute(FurnitureAttributesEnum.SALE, self.entry.is_in_promo)

    def _update_attribute(
        self,
        attribute: FurnitureAttributesEnum,
        value: bool,
    ) -> None:
        if value:
            self.entry.attributes.add(attribute)
        else:
            self.entry.attributes.remove(attribute)

    def update_all_attributes(self) -> None:
        """Updates all attributes of the entry. The topseller attribute, which is
        assigned manually, is preserved.

        Useful when new attributes are added or promos and new arrivals are updated.
        """
        is_top_seller = FurnitureAttributesEnum.TOP_SELLER in {
            attr.name for attr in self.entry.attributes.all()
        }
        self.entry.attributes.clear()
        try:
            self.assign_attributes()
            if is_top_seller:
                self.entry.attributes.add(FurnitureAttributesEnum.TOP_SELLER)
        except AttributeError:
            pass
            # logger.error(
            #     'The furniture of CatalogueEntry %s has been deleted!',
            #     self.entry.id,
            # )
