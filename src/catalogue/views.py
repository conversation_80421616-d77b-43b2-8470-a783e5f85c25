import base64

from typing import Optional

from django.conf import settings
from django.core.files.base import ContentFile
from django.core.files.uploadedfile import SimpleUploadedFile
from django.db import transaction
from django.db.models import (
    Case,
    F,
    Max,
    Min,
    Prefetch,
    When,
)
from django.db.utils import IntegrityError
from django.shortcuts import get_object_or_404
from django.utils import translation
from django.utils.functional import cached_property
from rest_framework import status
from rest_framework.authentication import TokenAuthentication
from rest_framework.generics import (
    CreateAPIView,
    GenericAPIView,
    ListAPIView,
    RetrieveAPIView,
)
from rest_framework.mixins import (
    CreateModelMixin,
    DestroyModelMixin,
    UpdateModelMixin,
)
from rest_framework.permissions import (
    AllowAny,
    IsAdminUser,
)
from rest_framework.response import Response
from rest_framework.views import APIView

from django_filters.rest_framework import DjangoFilterBackend
from PIL import Image
from slugify import slugify

from catalogue.constants.merchandising_config import (
    FF_TESTING_NAME,
    STRATEGY_USED,
)
from catalogue.enums import (
    FurnitureAttributesEnum,
    ListingOrder,
    StrategyEnum,
)
from catalogue.filters import CatalogueFilterSet
from catalogue.models import (
    BoardManualOrder,
    CatalogueEntry,
)
from catalogue.paginator import CatalogueCachedPagination
from catalogue.serializers import (
    BoardCopySerializer,
    BoardManualDestroySerializer,
    BoardManualOrderSerializer,
    CatalogueEntrySerializer,
    EntryVariantSerializer,
    MinigridCatalogueEntrySerializer,
    TopSellerSerializer,
)
from custom.enums import LanguageEnum
from custom.mixins import GZipMixin
from django_user_agents.templatetags.user_agents import is_active_feature_flag
from ecommerce_api.settings import api_settings as ecom_api_settings
from gallery.enums import (
    CollectiveFurnitureCategory,
    FurnitureCategory,
)
from gallery.services.prices_for_serializers import get_currency_rate
from gallery.slugs import generate_title_based_on_filter_data
from promotions.models import (
    Promotion,
    PromotionConfig,
)
from promotions.services.categories_in_promo import get_categories_in_promo
from promotions.utils import (
    get_active_promotion,
    get_active_promotion_config,
)
from regions.cached_region import (
    CachedRegionData,
    get_region_data_from_request,
)
from regions.mixins import RegionCalculationsObject
from regions.models import Region


class CatalogueMixin:
    def get_serializer_context(self):
        return {
            'region_calculations_object': RegionCalculationsObject(self.region),
            'region': self.region,
            'current_promo': self.current_promo,
            'promo_config': self.current_promo_config,
            'currency_rate': get_currency_rate(self.region),
            **super().get_serializer_context(),
        }

    @cached_property
    def current_promo(self):
        return get_active_promotion(region=self.region)

    @cached_property
    def current_promo_config(self):
        return get_active_promotion_config(region=self.region)

    @cached_property
    def current_promo_data(self):
        if not self.current_promo_config:
            return {}

        language = self.request.query_params.get('lang', LanguageEnum.EN)
        copy = self.current_promo_config.get_copy(language, self.region)
        return {
            'code': getattr(self.current_promo.promo_code, 'code', None),
            'grid_header': copy.get('grid_copy_slot_1_header_1'),
            'grid_image_url': (
                self.current_promo_config.grid_picture.image.url
                if self.current_promo_config.grid_picture
                else None
            ),
        }

    @cached_property
    def region(self) -> Optional[CachedRegionData]:
        if self.region_name:
            try:
                return Region.objects.get(name=self.region_name).cached_region_data
            except Region.DoesNotExist:
                pass
        if self.request.user.is_authenticated:
            return self.request.user.profile.cached_region_data

    @property
    def region_name(self) -> str:
        return self.request.query_params.get('regionName', '')

    @property
    def category(self) -> str:
        return self.request.query_params.get('category', '')

    @property
    def collective_category(self) -> str:
        return self.request.query_params.get('collectiveCategory', '')


class SimpleCatalogueView(GenericAPIView):
    renderer_classes = ecom_api_settings.ECOMMERCE_RENDERER_CLASSES
    permission_classes = [AllowAny]
    filterset_class = CatalogueFilterSet
    filter_backends = (DjangoFilterBackend,)
    pagination_class = CatalogueCachedPagination
    queryset = CatalogueEntry.objects.with_attribute_names()
    allowed_methods = ['GET']

    # Typing
    region: CachedRegionData | None
    category: str
    collective_category: str

    def get_queryset(self):
        queryset = self.queryset.all_for_region(region=self.region)
        if self.category:
            queryset = queryset.filter(category=self.category)
        return queryset.order_by(self.ordering_field).select_related('content_type')

    def filter_queryset(self, queryset):
        filtered_queryset = super().filter_queryset(queryset)

        # We want to show entries not enabled yet (f.ex. in new colors) only on request
        if not self.is_testing:
            filtered_queryset = filtered_queryset.exclude(enabled=False)

        return self.prefetch_objects(filtered_queryset)

    def prefetch_objects(self, queryset):
        return queryset.prefetch_related('furniture', 'attributes')

    def get_additional_response_data(self):
        return {}

    def get(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        page = self.paginate_queryset(queryset)
        with translation.override(
            self.request.query_params.get('lang', LanguageEnum.EN)
        ):
            serializer = self.get_serializer(page, many=True)
            paginated_response = self.get_paginated_response(serializer.data)

        response_data = paginated_response.data
        response_data.update(self.get_additional_response_data())

        return Response(response_data)

    @property
    def ordering_field(self) -> str:
        region_name = self.region.name if self.region else None
        country_code = self.region.country_code if self.region else None
        if self.category:
            if self.is_edge_wardrobes_only():
                return self.strategy.get_alt_category_order_field()
            else:
                return self.strategy.get_category_order_field(
                    region_name,
                    country_code,
                )
        elif (
            self.collective_category
            in CollectiveFurnitureCategory.get_auto_merchandising_values()
        ):
            return self.strategy.get_dynamic_order(
                listing=ListingOrder.COLLECTIVE_CATEGORY, region=self.region
            )

        if self.is_edge_wardrobes_only():
            return self.strategy.get_alt_order_field()
        else:
            return self.strategy.get_order_field(
                region_name,
                country_code,
            )

    @property
    def strategy(self) -> StrategyEnum:
        return STRATEGY_USED

    @property
    def is_testing(self) -> bool:
        return is_active_feature_flag(self.request, FF_TESTING_NAME)

    def is_edge_wardrobes_only(self) -> bool:
        if self.strategy != StrategyEnum.PROFIT_NETTO:
            return False
        if not self.category or self.category == FurnitureCategory.WARDROBE:
            return self.region_name not in settings.T03_REGION_KEYS
        return False


class CatalogueView(GZipMixin, CatalogueMixin, SimpleCatalogueView):
    serializer_class = CatalogueEntrySerializer

    def get_min_max_width(self):
        queryset = self.get_queryset()
        return queryset.aggregate(minWidth=Min('width'), maxWidth=Max('width'))

    def get_special_filters_data(self) -> dict[str, bool]:
        # django_taggit has unique on slugs, so it's faster to query on them
        queryset = self.get_queryset()
        is_sale = bool(
            self.current_promo
            and queryset.filter(
                attributes__slug=slugify(FurnitureAttributesEnum.SALE)
            ).exists()
        )
        is_new = queryset.filter(
            attributes__slug=slugify(FurnitureAttributesEnum.NEW_ARRIVAL)
        ).exists()
        is_top_seller = queryset.filter(
            attributes__slug=slugify(FurnitureAttributesEnum.TOP_SELLER)
        ).exists()
        is_special_edition = queryset.filter(
            attributes__slug=slugify(FurnitureAttributesEnum.SPECIAL_EDITION)
        ).exists()
        return {
            'isSaleFilterAvailable': is_sale,
            'isNewProductsFilterAvailable': is_new,
            'isTopSellerFilterAvailable': is_top_seller,
            'isSpecialEditionFilterAvailable': is_special_edition,
        }

    def get_additional_response_data(self):
        additional_data = self.get_min_max_width()
        additional_data.update(
            {
                'specialFilters': self.get_special_filters_data(),
                'promo': self.current_promo_data,
                'categoriesInPromotion': get_categories_in_promo(region=self.region),
                'filter_title': generate_title_based_on_filter_data(
                    self.request.query_params
                ),
            }
        )
        return additional_data


class CatalogueDetailsView(CatalogueMixin, RetrieveAPIView):
    renderer_classes = SimpleCatalogueView.renderer_classes
    permission_classes = SimpleCatalogueView.permission_classes
    serializer_class = CatalogueEntrySerializer
    queryset = CatalogueEntry.objects.with_attribute_names().all()

    def get_object(self):
        queryset = self.filter_queryset(self.get_queryset())
        furniture_param = self.request.query_params.get('furniture')
        furniture_id, furniture_type = furniture_param.split(',')
        filter_kwargs = {
            'object_id': int(furniture_id),
            'content_type__model': furniture_type,
        }
        obj = get_object_or_404(queryset, **filter_kwargs)
        self.check_object_permissions(self.request, obj)

        return obj


class EntryVariantView(CatalogueMixin, RetrieveAPIView):
    renderer_classes = SimpleCatalogueView.renderer_classes
    permission_classes = SimpleCatalogueView.permission_classes
    serializer_class = EntryVariantSerializer
    queryset = CatalogueEntry.objects.all()

    def get_object(self):
        queryset = self.filter_queryset(self.get_queryset())
        furniture_param = self.request.query_params.get('furniture')
        furniture_id, furniture_type = furniture_param.split(',')
        filter_kwargs = {
            'object_id': int(furniture_id),
            'content_type__model': furniture_type,
        }
        obj = get_object_or_404(queryset, **filter_kwargs)
        self.check_object_permissions(self.request, obj)

        return obj


class EditTopSellerView(APIView):
    permission_classes = [IsAdminUser]

    def get_object(self):
        serializer = TopSellerSerializer(data=self.request.data)
        serializer.is_valid(raise_exception=True)
        entry_data = serializer.data
        return get_object_or_404(
            CatalogueEntry,
            content_type_id=entry_data['furniture_type'],
            object_id=entry_data['furniture_id'],
        )

    def post(self, request, *args, **kwargs):
        entry = self.get_object()
        entry.attributes.add(FurnitureAttributesEnum.TOP_SELLER)
        return Response()

    def delete(self, request, *args, **kwargs):
        entry = self.get_object()
        entry.attributes.remove(FurnitureAttributesEnum.TOP_SELLER)
        return Response(status=status.HTTP_204_NO_CONTENT)


class BoardManualOrderView(
    CreateModelMixin,
    DestroyModelMixin,
    UpdateModelMixin,
    GenericAPIView,
):
    authentication_classes = (TokenAuthentication,)
    queryset = BoardManualOrder.objects.all()

    def get_serializer_class(self):
        if self.request.method.lower() == 'delete':
            return BoardManualDestroySerializer
        return BoardManualOrderSerializer

    def get_object(self):
        queryset = self.filter_queryset(self.get_queryset())

        return get_object_or_404(
            queryset,
            board_name=self.request.data.get('board_name'),
            order=self.request.data.get('order'),
            published=True,
        )

    def put(self, request, *args, **kwargs):
        return self.update(request, *args, **kwargs)

    def post(self, request, *args, **kwargs):
        try:
            return self.create(request, *args, **kwargs)
        except IntegrityError:
            return Response(
                {'detail': 'Override with this board name and order already exists.'},
                status=status.HTTP_400_BAD_REQUEST,
            )

    def perform_create(self, serializer):
        with transaction.atomic():
            self._update_boards_order(
                board_name=serializer.validated_data['board_name'],
                new_order=serializer.validated_data['order'],
            )
            serializer.save()

    def delete(self, request, *args, **kwargs):
        return self.destroy(request, *args, **kwargs)

    @staticmethod
    def _update_boards_order(board_name: str, new_order: int) -> None:
        """
        Update for the given board_name all BoardManualOrder objects with order greater,
        but only if they are following integers.
        """
        batch = []
        queryset = BoardManualOrder.objects.filter(
            board_name=board_name, order__gte=new_order, published=True
        ).order_by('order')
        order = new_order
        for board_manual_order in queryset:
            if order < board_manual_order.order:
                break
            board_manual_order.order = F('order') + 1
            batch.append(board_manual_order)
            order += 1

        BoardManualOrder.objects.bulk_update(batch, ['order'])


class MinigridView(ListAPIView):
    renderer_classes = ecom_api_settings.ECOMMERCE_RENDERER_CLASSES
    permission_classes = [AllowAny]
    serializer_class = MinigridCatalogueEntrySerializer
    filterset_class = CatalogueFilterSet
    filter_backends = (DjangoFilterBackend,)

    @property
    def minigrid_board_name(self) -> str:
        return f'minigrid={self.kwargs.get("minigrid_name")}'

    def _get_region(self) -> Optional[CachedRegionData]:
        if region_name := self.request.query_params.get('regionName'):
            try:
                return Region.objects.get(name=region_name).cached_region_data
            except Region.DoesNotExist:
                return

        return get_region_data_from_request(self.request)

    def _get_current_promo(self) -> Optional[Promotion]:
        return get_active_promotion(region=self._region)

    def _get_current_promo_config(self) -> Optional[PromotionConfig]:
        return get_active_promotion_config(region=self._region)

    def get_serializer_context(self):
        return {
            'region': self._region,
            'current_promo': self._get_current_promo(),
            'promo_config': self._get_current_promo_config(),
            'currency_rate': get_currency_rate(self._region),
            'minigrid_name': self.minigrid_board_name,
            'region_calculations_object': RegionCalculationsObject(self._region),
            **super().get_serializer_context(),
        }

    def get_queryset(self):
        result_entries_ids = (
            BoardManualOrder.objects.filter(board_name=self.minigrid_board_name)
            .order_by('order')
            .values_list('entry', flat=True)
        )
        if not result_entries_ids:
            return CatalogueEntry.objects.none()
        # We have to preserve the order assigned to minigrid entries
        ordering = Case(
            *[When(id=pk, then=pos) for pos, pk in enumerate(result_entries_ids)]
        )
        return (
            CatalogueEntry.objects.with_attribute_names()
            .filter(id__in=result_entries_ids)
            .order_by(ordering)
            .prefetch_related(
                Prefetch(
                    'boardmanualorder_set',
                    queryset=BoardManualOrder.objects.filter(
                        published=True, board_name=self.minigrid_board_name
                    ),
                    to_attr='minigrid_order',
                ),
                'furniture',
                'attributes',
            )
            .select_related('content_type')
        )

    def get(self, request, *args, **kwargs):
        self._region = self._get_region()
        queryset = self.get_queryset()
        categories = list(set(queryset.values_list('category', flat=True)))
        queryset = self.filter_queryset(queryset)
        serializer = self.get_serializer(queryset, many=True)
        return Response(
            {
                'categories': categories,
                'results': serializer.data,
            }
        )


class RetoolMinigridView(MinigridView):
    """
    Only difference with parent class is taking regionName parameter into consideration
    when defining queryset. Should be deleted when we'll have regional minigrids
    established.
    """

    @property
    def minigrid_board_name(self) -> str:
        base = f'minigrid={self.kwargs.get("minigrid_name")}'
        region_name = None
        if self._region and self._region.name != '_other':
            region_name = self._region.name
        return f'{base}__region={region_name}' if region_name else base


class BoardCopyView(CreateAPIView):
    authentication_classes = (TokenAuthentication,)
    serializer_class = BoardCopySerializer

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        response_data = serializer.create(serializer.validated_data)
        return Response(response_data, status=status.HTTP_201_CREATED)


class CatalogueEntryImageUpdateView(APIView):
    authentication_classes = (TokenAuthentication,)
    permission_classes = (IsAdminUser,)

    def post(self, request, *args, **kwargs):
        data = request.data
        furniture_id = data['furniture_id']
        catalogue_entry = get_object_or_404(
            CatalogueEntry,
            object_id=furniture_id,
            content_type__model=data['content_type'],
        )
        image_file = ContentFile(
            base64.b64decode(data['magic_preview']),
            f'{furniture_id}_ca.png',
        )
        with Image.open(image_file) as result_image:
            catalogue_entry.image_alternative = SimpleUploadedFile(
                f'{furniture_id}_alt.jpg', b'', 'image/jpeg'
            )
            result_image = result_image.convert('RGB')
            result_image.save(catalogue_entry.image_alternative, format='JPEG')

            catalogue_entry.save()

        return Response('ok')
