import io
import zipfile

from typing import TYPE_CHECKING

from django.contrib import admin
from django.db.models.query import QuerySet
from django.http import HttpResponse
from django.utils import timezone

from checkout.models import ViesVatValidation
from custom.admin_filters import InputFilter

if TYPE_CHECKING:
    from django.http import HttpRequest


class VatNumberFilter(InputFilter):
    parameter_name = 'vat_number'
    title = 'Vat numbers (sep. by space)'

    def queryset(self, request, queryset):
        if not self.value():
            return queryset

        return queryset.filter(vat_number__in=self.value().split())


class ViesVatValidationAdmin(admin.ModelAdmin):
    list_display = (
        'vat_number',
        'confirmation_pdf',
        'response_status',
        'validation_trigger_action',
        'created_at',
    )

    list_filter = (
        VatNumberFilter,
        'response_status',
        'validation_trigger_action',
    )

    date_hierarchy = 'created_at'

    search_fields = ('vat_number',)

    actions = ['download_pdfs_zip']

    @admin.action(description='Download files as zip')
    def download_pdfs_zip(
        self: 'ViesVatValidationAdmin',
        request: 'HttpRequest',
        queryset: QuerySet[ViesVatValidation],
    ) -> HttpResponse:
        buffer = io.BytesIO()
        with zipfile.ZipFile(buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
            for vies_validation in queryset:
                if vies_validation.confirmation_pdf:
                    pdf_name = f'{vies_validation.vat_number}_{vies_validation.id}.pdf'
                    pdf_content = vies_validation.confirmation_pdf.read()
                    zip_file.writestr(pdf_name, pdf_content)

        buffer.seek(0)
        today_str = timezone.now().date().strftime('%Y%m%d')
        filename = f'vies_confirmations{today_str}.zip'

        response = HttpResponse(buffer, content_type='application/zip')
        response['Content-Disposition'] = f'attachment; filename={filename}'
        return response


admin.site.register(ViesVatValidation, ViesVatValidationAdmin)
