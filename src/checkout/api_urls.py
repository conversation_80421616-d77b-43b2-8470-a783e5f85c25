from django.urls import path
from rest_framework import routers

from checkout.views import (
    CheckoutCartApiViewSet,
    CheckoutOrderApiViewSet,
    PostalCodeValidationApiView,
)

router = routers.SimpleRouter()
router.register(
    r'v2/checkout/cart', CheckoutCartApiViewSet, basename='checkout_v2_cart'
)
router.register(
    r'v2/checkout/order', CheckoutOrderApiViewSet, basename='checkout_v2_order'
)

urlpatterns = [
    path(
        'v2/postal-code-validation/',
        PostalCodeValidationApiView.as_view(),
        name='postal-code-validation',
    ),
]
urlpatterns += router.urls
