import logging

from django.conf import settings
from django.utils import timezone
from rest_framework import status

from checkout.serializers import AdyenPaymentCaptureSerializer
from checkout.services.adyen import capture_klarna_payment
from invoice.utils import create_normal_from_proforma_after_success_klarna_payment
from orders.enums import OrderStatus
from orders.models import Order
from payments.choices import KlarnaStatus
from payments.constants import KLARNA_PAYMENT_METHODS
from payments.exceptions import PrimerKlarnaCaptureException
from payments.models import (
    KlarnaCaptureRequest,
    Notification,
    Transaction,
)
from payments.primer import PrimerKlarnaService

logger = logging.getLogger('cstm')


class ValidateKlarnaOrderException(Exception):  # noqa: N818
    """Raised when order do not fulfill requirements to send klarna capture."""


def send_klarna_capture_for_order(order: Order) -> None:
    if order.klarna_capture_attempt_date is None:
        order.klarna_capture_attempt_date = timezone.now()
        order.save(update_fields=['klarna_capture_attempt_date'])

    validate_sending_klarna_capture_for_order(order)
    transaction = get_order_klarna_transaction(order)

    send_klarna_capture_payment(transaction)


def send_klarna_capture_payment(transaction: Transaction) -> KlarnaCaptureRequest:
    error_message = None
    if transaction.primer_notification:
        # If payment was made by Primer, we need to send capture request from Primer
        response = PrimerKlarnaService().capture_klarna_payment(transaction)
        if response.status_code == status.HTTP_200_OK:
            # Get last transaction for primer notification object
            # Value in 'transactions' list inside notification are always sorted
            response_transaction_json = response.json()['transactions'][0]
            psp_reference = response_transaction_json['processorTransactionId']

            if response_transaction_json['processorStatus'] == 'FAILED':
                error_message = response_transaction_json['processorStatusReason'].get(
                    'message', None
                )
        else:
            logger.error(
                'Primer capture request for order %s failed with status code %s.',
                transaction.order.id,
                response.status_code,
            )
            raise PrimerKlarnaCaptureException(
                f'Primer capture request for order {transaction.order.id} '
                f'failed with status code {response.status_code}.'
            )

    else:
        # If payment was made by Adyen, we need to send capture request from Adyen
        data = AdyenPaymentCaptureSerializer(transaction).data
        response = capture_klarna_payment(data)
        data = response.message
        psp_reference = data.get('pspReference', '-')
        error_message = data.get('message', None)

    return KlarnaCaptureRequest.objects.create(
        transaction=transaction,
        psp_reference=psp_reference,
        status=KlarnaStatus.ERROR if error_message else KlarnaStatus.PENDING,
        error_message=error_message,
    )


def validate_sending_klarna_capture_for_order(order: Order) -> None:
    if not order.paid_at:
        raise ValidateKlarnaOrderException('Order is not paid.')

    if order.chosen_payment_method not in KLARNA_PAYMENT_METHODS:
        raise ValidateKlarnaOrderException('Order was not paid by klarna.')

    if order.status != OrderStatus.DELIVERED:
        raise ValidateKlarnaOrderException('Order is not delivered yet.')

    if order.suborders.count() != 0:  # old orders, without split logistic orders
        raise ValidateKlarnaOrderException('Order has more than one package.')

    if len(order.logistic_info) > 1 and [  # new orders, with split logistic orders
        logistic_order
        for logistic_order in order.logistic_info
        if logistic_order.order_type != ''
    ]:
        raise ValidateKlarnaOrderException('Order has more than one package.')

    if not order.get_delivery_date():
        raise ValidateKlarnaOrderException(
            'Logistic order does not have delivery date.'
        )


def get_order_klarna_transaction(order: Order) -> Transaction:
    try:
        # select related is made only for primer notification because in next step
        # it's needed to check did notification comes from primer or adyen
        transaction = order.transactions.select_related('primer_notification').get(
            payment_method__in=KLARNA_PAYMENT_METHODS,
            status='AUTHORISATION',
        )
    except Transaction.DoesNotExist:
        raise ValidateKlarnaOrderException('Correct klarna transaction is missing.')
    except Transaction.MultipleObjectsReturned:
        raise ValidateKlarnaOrderException('Order has to many klarna transactions')
    return transaction


def handle_capture_notification(notification: Notification) -> None:
    """
    1. Save capture notification.
    2. Update corresponding KlarnaCaptureRequest.
    3. If proforma invoice exists change to normal.
    """
    try:
        psp_reference = (
            notification.psp_reference
            if notification.merchant_account_code != settings.PRIMER_MERCHANT_ACCOUNT
            else notification.original_reference
        )
        klarna_capture = KlarnaCaptureRequest.objects.get(psp_reference=psp_reference)
    except KlarnaCaptureRequest.DoesNotExist:
        logger.debug(
            'Klarna capture request with psp reference %s does not exists.',
            notification.psp_reference,
        )
        return
    if notification.success:
        klarna_capture.status = KlarnaStatus.SUCCESS
        create_normal_from_proforma_after_success_klarna_payment(
            klarna_capture.transaction.order
        )
    else:
        klarna_capture.status = KlarnaStatus.ERROR
        klarna_capture.error_message = notification.reason
    klarna_capture.save()

    notification.klarna_capture = klarna_capture
    notification.save()
