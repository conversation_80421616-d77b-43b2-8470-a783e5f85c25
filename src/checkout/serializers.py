from typing import Final
from urllib.parse import urljoin

from django.conf import settings
from django.forms.models import model_to_dict
from django.urls import reverse
from django.utils.translation import get_language
from rest_framework import serializers

from carts.choices import CartStatusChoices
from carts.models import Cart
from carts.services.cart_service import CartService
from custom.enums import LanguageEnum
from custom.utils.adyen import (
    get_current_payment_settings,
    get_locale,
)
from orders.models import (
    Order,
    OrderItem,
)
from payments.models import Transaction

REQUIRED_CHECKOUT_ADDRESS_FIELDS: Final[frozenset[str]] = frozenset(
    {
        'first_name',
        'last_name',
        'email',
        'street_address_1',
        'city',
        'postal_code',
        'country',
        'phone',
        'phone_prefix',
    }
)


class AdyenAmountSerializer(serializers.ModelSerializer):
    value = serializers.IntegerField(source='adyen_price')
    currency = serializers.SerializerMethodField()

    def get_currency(self, instance):
        return instance.get_region().currency.code

    def get_value(self, instance):
        return instance.adyen_price

    class Meta:
        model = Order
        fields = ('value', 'currency')


class AdyenPaymentCaptureSerializer(serializers.ModelSerializer):
    originalReference = serializers.CharField(source='reference')  # noqa: N815
    modificationAmount = AdyenAmountSerializer(read_only=True, source='order')  # noqa: N815
    merchantAccount = serializers.SerializerMethodField()  # noqa: N815

    @classmethod
    def get_merchantAccount(cls, instance):  # noqa: N802
        adyen_settings = get_current_payment_settings()
        return adyen_settings.get('MERCHANT_ACCOUNT')

    class Meta:
        model = Transaction
        fields = ('originalReference', 'modificationAmount', 'merchantAccount')


class AdyenPaymentRequestSerializer(serializers.ModelSerializer):
    reference = serializers.CharField(source='adyen_reference')
    amount = AdyenAmountSerializer(source='*')
    return_url = serializers.SerializerMethodField()

    class Meta:
        model = Order
        fields = ['reference', 'amount', 'return_url']

    def get_return_url(self, instance):
        return urljoin(
            settings.SITE_URL,
            reverse('adyen-redirect', kwargs={'pk': instance.pk}),
        )


class AdyenItemSerializer(serializers.ModelSerializer):
    id = serializers.SerializerMethodField()
    quantity = serializers.ReadOnlyField(default=1)
    description = serializers.ReadOnlyField(source='get_furniture_type')
    amount_including_tax = serializers.ReadOnlyField(source='adyen_price')
    tax_percentage = serializers.ReadOnlyField(source='adyen_tax_rate')

    class Meta:
        model = OrderItem
        fields = [
            'id',
            'description',
            'quantity',
            'amount_including_tax',
            'tax_percentage',
        ]

    def get_id(self, obj):
        return f'{obj.content_type.model}-{obj.object_id}'

    def get_description(self, obj):
        return obj.order_item.get_title()


class BillingAddressSerializer(serializers.ModelSerializer):
    city = serializers.ReadOnlyField(source='invoice_city')
    country = serializers.ReadOnlyField(source='iso_alpha_2_country_code')
    postal_code = serializers.ReadOnlyField(source='invoice_postal_code')

    class Meta:
        model = Order
        fields = ['city', 'country', 'postal_code']


class AdyenPaymentExtendedRequestSerializer(AdyenPaymentRequestSerializer):
    country_code = serializers.ReadOnlyField(source='iso_alpha_2_country_code')
    shopper_email = serializers.CharField(source='email')
    shopper_reference = serializers.CharField(source='owner.id')
    shopper_locale = serializers.SerializerMethodField()
    telephone_number = serializers.ReadOnlyField(source='phone')
    billing_address = BillingAddressSerializer(source='*')
    line_items = serializers.SerializerMethodField()

    class Meta(AdyenPaymentRequestSerializer.Meta):
        fields = AdyenPaymentRequestSerializer.Meta.fields + [  # noqa: RUF005
            'country_code',
            'shopper_email',
            'shopper_reference',
            'shopper_locale',
            'telephone_number',
            'billing_address',
            'line_items',
        ]

    def get_shopper_locale(self, instance):
        return get_locale(
            get_language() or LanguageEnum(settings.LANGUAGE_CODE),
            instance.iso_alpha_2_country_code,
        )

    def get_line_items(self, obj):
        return AdyenItemSerializer(obj.items.all(), many=True).data


class FreeOrderSerializer(serializers.ModelSerializer):
    is_free = serializers.BooleanField()
    order_id = serializers.IntegerField(source='pk')

    class Meta:
        model = Cart
        fields = (
            'is_free',
            'order_id',
        )


class CheckoutAddressSerializer(serializers.Serializer):
    first_name = serializers.CharField(required=True)
    last_name = serializers.CharField(required=True)
    email = serializers.EmailField(required=True)
    street_address_1 = serializers.CharField(required=True)
    city = serializers.CharField(required=True)
    postal_code = serializers.CharField(required=True)
    country = serializers.CharField(required=True)
    phone = serializers.CharField(required=True)
    phone_prefix = serializers.CharField(required=True)

    @classmethod
    def validate_for(cls, obj, raise_exception: bool = False) -> bool:
        data = model_to_dict(obj, fields=REQUIRED_CHECKOUT_ADDRESS_FIELDS)
        return cls(data=data).is_valid(raise_exception=raise_exception)


class CheckoutCartStatusSerializer(serializers.ModelSerializer):
    form_validated = serializers.SerializerMethodField()
    is_empty = serializers.SerializerMethodField()
    status = serializers.SerializerMethodField()

    class Meta:
        fields = (
            'form_validated',
            'is_empty',
            'status',
        )
        model = Cart

    @staticmethod
    def get_form_validated(obj: Cart) -> bool:
        if order := obj.order:
            return CheckoutAddressSerializer.validate_for(order)
        return False

    @staticmethod
    def get_is_empty(obj: Cart) -> bool:
        return not obj.items.exists()

    @staticmethod
    def get_status(obj: Cart) -> int | None:
        if order := obj.order:
            return order.status

        return None


class CheckoutOrderStatusSerializer(serializers.ModelSerializer):
    form_validated = serializers.SerializerMethodField()
    is_empty = serializers.SerializerMethodField()
    cart_id = serializers.SerializerMethodField()

    class Meta:
        fields = (
            'form_validated',
            'status',
            'is_empty',
            'cart_id',
        )
        model = Order

    @staticmethod
    def get_form_validated(obj: Order) -> bool:
        return CheckoutAddressSerializer.validate_for(obj)

    @staticmethod
    def get_is_empty(obj: Order) -> bool:
        return not obj.items.exists()

    @staticmethod
    def get_cart_id(obj: Order) -> int:
        if cart := getattr(obj, 'cart', None):
            return cart.pk

        cart = CartService.create_cart_for_order(obj, status=CartStatusChoices.DRAFT)
        return cart.id
