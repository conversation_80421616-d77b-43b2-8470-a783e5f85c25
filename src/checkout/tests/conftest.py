import random

from unittest import mock

from django.core.files.base import File
from django.utils import timezone
from rest_framework import status

import pytest

from requests_mock import ANY

from invoice.choices import (
    InvoiceStatus,
    NumerationType,
)
from invoice.models import Invoice
from orders.enums import OrderStatus
from payments.constants import KLARNA_PAYMENT_METHODS

PSP_REFERENCE = '8825408195409505'

CORRECT_CAPTURE_RESPONSE = {
    'pspReference': PSP_REFERENCE,
    'response': '[capture-received]',
}


@pytest.fixture
def klarna_correct_order(db, order_factory, region_factory, logistic_order_dto_factory):
    delivery_date = timezone.now() - timezone.timedelta(seconds=1)
    logistic_order_dto = logistic_order_dto_factory(delivered_date=delivery_date.date())

    order = order_factory(
        paid_at=timezone.now(),
        chosen_payment_method=random.choice(KLARNA_PAYMENT_METHODS),  # noqa: S311
        status=OrderStatus.DELIVERED,
        region=region_factory(poland=True),
        country='poland',
        logistic_info=[logistic_order_dto],
    )

    return order


@pytest.fixture
def klarna_correct_order_with_undelivered(
    db, order_factory, logistic_order_dto_factory
):
    logistic_order_dto = logistic_order_dto_factory(delivered_date=None)

    order = order_factory(
        paid_at=timezone.now(),
        chosen_payment_method=random.choice(KLARNA_PAYMENT_METHODS),  # noqa: S311
        status=OrderStatus.DELIVERED,
        country='germany',
        logistic_info=[logistic_order_dto],
    )

    return order


@pytest.fixture
def klarna_correct_transaction(klarna_correct_order, transaction_factory):
    return transaction_factory(
        status='AUTHORISATION',
        payment_method=random.choice(KLARNA_PAYMENT_METHODS),  # noqa: S311
        order=klarna_correct_order,
    )


@pytest.fixture
def mock_success_adyen_capture_url(requests_mock):
    requests_mock.register_uri(  # FIXME: provide correct url
        'POST', ANY, status_code=status.HTTP_200_OK, json=CORRECT_CAPTURE_RESPONSE
    )


def return_none_mock(*args, **kwargs):
    return None


@pytest.fixture
def proforma_invoice(
    klarna_correct_order,
    pricing_factors,
    country_factory,
    invoice_factory,
    invoice_sequence_factory,
    monkeypatch,
):
    country = country_factory(
        name=klarna_correct_order.country,
        language_code=klarna_correct_order.country[:2],
        code=klarna_correct_order.country[:2],
    )
    invoice_sequence_factory(
        country=country,
        invoice_type=InvoiceStatus.ENABLED,
        numeration_type=NumerationType.NORMAL,
    )
    monkeypatch.setattr(Invoice, 'create_pdf', return_none_mock)
    return invoice_factory(
        status=InvoiceStatus.PROFORMA,
        order=klarna_correct_order,
    )


@pytest.fixture
def jetty_with_preview(jetty_factory):
    file_mock = mock.MagicMock(spec=File)
    file_mock.name = 'test_file.jpg'
    return jetty_factory.create(
        preset=True,
        preview=file_mock,
    )
