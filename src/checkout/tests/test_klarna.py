from django.utils import timezone
from rest_framework import status

import pytest

from freezegun import freeze_time

from checkout.klarna_capture import (
    ValidateKlarnaOrderException,
    get_order_klarna_transaction,
    send_klarna_capture_for_order,
    send_klarna_capture_payment,
    validate_sending_klarna_capture_for_order,
)
from checkout.serializers import AdyenPaymentCaptureSerializer
from checkout.services.adyen import capture_klarna_payment
from checkout.tests.conftest import (
    CORRECT_CAPTURE_RESPONSE,
    PSP_REFERENCE,
)
from custom.models import GlobalSettings
from invoice.choices import InvoiceStatus
from invoice.models import Invoice
from orders.enums import OrderStatus
from orders.tests.factories import OrderFactory
from payments.choices import KlarnaStatus
from payments.models import (
    KlarnaCaptureRequest,
    Notification,
)
from payments.tasks import accept_notification
from payments.tests.factories import KlarnaCaptureRequestFactory

SUCCESS_CAPTURE_NOTIFICATION_DATA = {
    'amount': {'currency': 'EUR', 'value': 500},
    'eventCode': 'CAPTURE',
    'eventDate': '2018-09-22T15:54:01+02:00',
    'merchantAccountCode': 'YourMerchantAccount',
    'originalReference': '****************',
    'paymentMethod': 'mc',
    'pspReference': PSP_REFERENCE,
    'reason': '',
    'success': 'true',
}

FAILED_CAPTURE_NOTIFICATION_DATA = {
    'amount': {'currency': 'EUR', 'value': 500},
    'eventCode': 'CAPTURE',
    'eventDate': '2018-09-22T15:54:01+02:00',
    'merchantAccountCode': 'YourMerchantAccount',
    'originalReference': '****************',
    'paymentMethod': 'mc',
    'pspReference': PSP_REFERENCE,
    'reason': 'bad idea',
    'success': 'false',
}


@pytest.mark.django_db
def test_klarna_capture_payment(
    mock_success_adyen_capture_url, klarna_correct_transaction
):
    GlobalSettings.objects.update(is_live_payment=False)
    data = AdyenPaymentCaptureSerializer(klarna_correct_transaction).data
    response = capture_klarna_payment(data)
    assert response.status_code == status.HTTP_200_OK
    assert response.message == CORRECT_CAPTURE_RESPONSE


@pytest.mark.django_db
def test_send_klarna_capture_payment(
    mock_success_adyen_capture_url, klarna_correct_transaction
):
    GlobalSettings.objects.update(is_live_payment=False)
    klarna_capture = send_klarna_capture_payment(klarna_correct_transaction)
    assert klarna_capture.transaction_id == klarna_correct_transaction.id
    assert klarna_capture.status == KlarnaStatus.PENDING
    assert klarna_capture.psp_reference == PSP_REFERENCE


@pytest.mark.django_db
@pytest.mark.nbp
@pytest.mark.parametrize(
    'notification_data, klarna_status, invoice_status',  # noqa: PT006
    [
        (
            SUCCESS_CAPTURE_NOTIFICATION_DATA,
            KlarnaStatus.SUCCESS,
            InvoiceStatus.ENABLED,
        ),
        (
            FAILED_CAPTURE_NOTIFICATION_DATA,
            KlarnaStatus.ERROR,
            InvoiceStatus.PROFORMA,
        ),
    ],
)
def test_handling_capture_notification(
    notification_data,
    klarna_status,
    invoice_status,
    proforma_invoice,
    klarna_correct_transaction,
    klarna_correct_order,
):
    date_time = timezone.now()
    with freeze_time(date_time):
        klarna_capture = KlarnaCaptureRequestFactory(
            status=KlarnaStatus.PENDING,
            psp_reference=PSP_REFERENCE,
            transaction=klarna_correct_transaction,
        )
        accept_notification(notification_data)
        klarna_capture.refresh_from_db()
        notification = Notification.objects.get(klarna_capture_id=klarna_capture.id)
        assert notification.transaction is None
        assert klarna_capture.status == klarna_status

        updated_invoice = Invoice.objects.filter(order=klarna_correct_order).last()
        assert updated_invoice.status == invoice_status
        if invoice_status == InvoiceStatus.ENABLED:
            assert (
                updated_invoice.sell_at.date().isoformat()
                == klarna_correct_order.get_delivery_date()
            )


@pytest.mark.nbp
def test_updating_invoice_during_handling(
    proforma_invoice, klarna_correct_transaction, klarna_correct_order
):
    klarna_capture = KlarnaCaptureRequestFactory(
        status=KlarnaStatus.PENDING,
        psp_reference=PSP_REFERENCE,
    )
    accept_notification(SUCCESS_CAPTURE_NOTIFICATION_DATA)
    klarna_capture.refresh_from_db()
    notification = Notification.objects.get(klarna_capture_id=klarna_capture.id)
    assert notification.transaction is None
    assert klarna_capture.status == KlarnaStatus.SUCCESS


def test_validating_order_for_sending_capture_request_passes(klarna_correct_order):
    try:
        validate_sending_klarna_capture_for_order(klarna_correct_order)
    except Exception as e:
        assert False, f'Exception should not be raised: {e}'  # noqa: B011, PT015, PT017


def test_validating_order_for_sending_capture_raises_error_when_more_types(
    klarna_correct_order,
):
    klarna_correct_order.suborders.set([OrderFactory()])
    with pytest.raises(ValidateKlarnaOrderException):
        validate_sending_klarna_capture_for_order(klarna_correct_order)


def test_validating_order_for_sending_capture_raises_error_when_no_delivery_date(
    klarna_correct_order_with_undelivered,
):
    with pytest.raises(ValidateKlarnaOrderException):
        validate_sending_klarna_capture_for_order(klarna_correct_order_with_undelivered)


def test_validating_order_for_sending_capture_raises_error_when_no_delivery_status(
    klarna_correct_order,
):
    klarna_correct_order.status = OrderStatus.PAYMENT_PENDING
    klarna_correct_order.save()
    with pytest.raises(ValidateKlarnaOrderException):
        validate_sending_klarna_capture_for_order(klarna_correct_order)


def test_getting_klarna_transaction(klarna_correct_transaction, klarna_correct_order):
    transaction = get_order_klarna_transaction(klarna_correct_order)
    assert transaction.id == klarna_correct_transaction.id


def test_sending_klarna_capture_for_order(
    mock_success_adyen_capture_url, klarna_correct_transaction, klarna_correct_order
):
    GlobalSettings.objects.update(is_live_payment=False)
    time_date = timezone.now()
    with freeze_time(time_date):
        send_klarna_capture_for_order(klarna_correct_order)
        assert KlarnaCaptureRequest.objects.filter(
            transaction=klarna_correct_transaction
        ).exists()
        klarna_correct_order.refresh_from_db()
        assert klarna_correct_order.klarna_capture_attempt_date == time_date
