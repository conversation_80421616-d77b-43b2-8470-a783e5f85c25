import logging
import time

from datetime import date

from django.conf import settings
from django.core.cache import cache as redis_cache

from zeep import Client
from zeep.cache import Base
from zeep.exceptions import Fault
from zeep.transports import Transport

from checkout.enums import (
    ViesResponseStatus,
    ViesValidationTriggerAction,
)

logger = logging.getLogger('cstm')

FAULT_ERROR_STRINGS = {
    'INVALID_INPUT': 'INVALID_INPUT',
    'GLOBAL_MAX_CONCURRENT_REQ': 'GLOBAL_MAX_CONCURRENT_REQ',
    'MS_MAX_CONCURRENT_REQ': 'MS_MAX_CONCURRENT_REQ',
    'SERVICE_UNAVAILABLE': 'SERVICE_UNAVAILABLE',
    'MS_UNAVAILABLE': 'MS_UNAVAILABLE',
    'TIMEOUT': 'TIMEOUT',
}


def retry_on_transport_exception(func):
    def wrapper(*args, **kwargs):
        retries = 3
        for attempt in range(retries):
            try:
                return func(*args, **kwargs)
            except (ConnectionError, TimeoutError, Fault) as e:  # noqa: PERF203
                if (
                    isinstance(e, Fault)
                    and e.message == FAULT_ERROR_STRINGS['INVALID_INPUT']
                ):
                    raise e
                if attempt < retries - 1:
                    time.sleep(3**attempt)
                else:
                    raise e

    return wrapper


class RedisCache(Base):
    def __init__(self):
        self.cache = redis_cache

    def add(self, url, content):
        self.cache.set(url, content, timeout=60 * 60 * 12)

    def get(self, url):
        return self.cache.get(url)


@retry_on_transport_exception
def get_vies_response(country_code: str, vat_number: str) -> dict:
    transport = Transport(operation_timeout=15, timeout=15, cache=RedisCache())
    client = Client(settings.VIES_URL, transport=transport)
    return client.service.checkVat(country_code, vat_number)


def validate_in_vies(
    country_code: str,
    vat_number: str,
    validation_trigger_action: ViesValidationTriggerAction,
) -> bool | None:
    """Connect to VIES and send a SOAP request to validate VAT number.

    Args:
        country_code: Country code for VAT number.
        vat_number: String containing VAT number.
        validation_trigger_action: Action that triggered validation.

    Returns:
        Bool signifying whether the number is correct or not.
    """

    cache_key = f'vat_{date.today()}_{country_code}_{vat_number}'
    cached_result = redis_cache.get(cache_key)
    if cached_result is not None:  # we want to return also False
        return cached_result
    vies_response_status = request_vies_validation(
        country_code,
        vat_number,
        validation_trigger_action,
    )
    if vies_response_status != ViesResponseStatus.UNAVAILABLE:
        redis_cache.set(
            cache_key,
            vies_response_status.is_valid(),
            timeout=60 * 60 * 24,
        )
    return vies_response_status.get_api_representation()


def request_vies_validation(
    country_code: str,
    vat_number: str,
    validation_trigger_action: ViesValidationTriggerAction,
) -> ViesResponseStatus:
    from checkout.tasks import create_and_save_vies_confirmation_task

    try:
        vies_response = get_vies_response(country_code, vat_number)
    except (ConnectionError, TimeoutError, Fault) as ex:
        if isinstance(ex, Fault) and ex.message == FAULT_ERROR_STRINGS['INVALID_INPUT']:
            vies_response_status = ViesResponseStatus.INVALID
        else:
            vies_response_status = ViesResponseStatus.UNAVAILABLE
        logger.debug('VAT validation connection problem: %s', ex)
    else:
        vies_response_status = (
            ViesResponseStatus.VALID
            if vies_response['valid']
            else ViesResponseStatus.INVALID
        )
    create_and_save_vies_confirmation_task.delay(
        country_code,
        vat_number,
        vies_response_status.value,
        validation_trigger_action,
    )
    return vies_response_status
