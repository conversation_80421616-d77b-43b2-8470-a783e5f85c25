import logging

from django.apps import apps
from django.contrib import messages
from django.contrib.admin import helpers
from django.core.cache import cache
from django.http import (
    HttpRequest,
    HttpResponseRedirect,
)
from django.http.response import HttpResponseBase
from django.utils import timezone
from django.utils.functional import LazyObject
from django.utils.module_loading import import_string
from django.utils.translation import gettext as _

logger = logging.getLogger('cstm')


# NOTE: copied from `django` source code
def response_action(model_admin, request, queryset):
    # There can be multiple action forms on the page (at the top
    # and bottom of the change list, for example). Get the action
    # whose button was pushed.
    try:
        action_index = int(request.POST.get('index', 0))
    except ValueError:
        action_index = 0

    # Construct the action form.
    data = request.POST.copy()
    data.pop(helpers.ACTION_CHECKBOX_NAME, None)
    data.pop('index', None)

    # Use the action whose button was pushed
    try:
        data.update({'action': data.getlist('action')[action_index]})
    except IndexError:
        # If we didn't get an action from the chosen form that's invalid
        # POST data, so by deleting action it'll fail the validation check
        # below. So no need to do anything here
        pass

    action_form = model_admin.action_form(data, auto_id=None)
    action_form.fields['action'].choices = model_admin.get_action_choices(request)

    # If the form's valid we can handle the action.
    if action_form.is_valid():
        action = action_form.cleaned_data['action']

        increment_action_usage_counter(request, model_admin, action)

        select_across = action_form.cleaned_data['select_across']
        func = model_admin.get_actions(request)[action][0]

        # Get the list of selected PKs. If nothing's selected, we can't
        # perform an action on it, so bail. Except we want to perform
        # the action explicitly on all objects.
        selected = request.POST.getlist(helpers.ACTION_CHECKBOX_NAME)
        if not selected and not select_across:
            # Reminder that something needs to be selected or nothing will happen
            msg = _(
                'Items must be selected in order to perform '
                'actions on them. No items have been changed.'
            )
            model_admin.message_user(request, msg, messages.WARNING)
            return None

        if not select_across:
            # Perform the action only on the selected objects
            queryset = queryset.filter(pk__in=selected)

        response = func(model_admin, request, queryset)

        # Actions may return an HttpResponse-like object, which will be
        # used as the response from the POST. If not, we'll be a good
        # little HTTP citizen and redirect back to the changelist page.
        if isinstance(response, HttpResponseBase):
            return response
        else:
            return HttpResponseRedirect(request.get_full_path())
    else:
        msg = _('No action selected.')
        model_admin.message_user(request, msg, messages.WARNING)
        return None


def increment_action_usage_counter(request: HttpRequest, model_admin, action_name):
    # increment counter only once for actions that need confirmation
    # e.g `delete_selected`
    if (
        helpers.ACTION_CHECKBOX_NAME in request.POST
        and request.POST.get('post')
        and 'index' not in request.POST
        and '_save' not in request.POST
    ):
        return
    model_admin_str = str(model_admin)
    # `django-admin-view-permission` overrides models registration and monkey
    # patches provided `ModelAdmin` subclass with new one that name
    # is always 'DynamicAdminViewPermissionModelAdmin'
    if model_admin_str.endswith('.DynamicAdminViewPermissionModelAdmin'):
        model_admin_str = '{}.{}'.format(
            model_admin.model._meta.app_label,
            model_admin.__class__.__bases__[0].__name__,
        )
    key = 'django_admin_action:{}.{}'.format(model_admin_str, action_name)
    request_id = request.COOKIES.get('requestid', '')
    logger.debug(
        'Admin action %s.%s for %s objects started by %s - %s (request id: %s)',
        model_admin_str,
        action_name,
        len(request.POST.getlist('_selected_action')),
        request.user,
        timezone.now(),
        request_id,
    )
    try:
        cache.incr(key, delta=1)
    except ValueError:
        cache.set(key, 1, timeout=None)


# NOTE: copied from django source code:
# https://github.com/django/django/blob/8954f255bbf5f4ee997fd6de62cb50fc9b5dd697/django/contrib/admin/sites.py#L538
class DefaultAdminSite(LazyObject):
    def _setup(self):
        AdminSiteClass = import_string(  # noqa: N806
            apps.get_app_config('admin').default_site,
        )
        self._wrapped = AdminSiteClass()
