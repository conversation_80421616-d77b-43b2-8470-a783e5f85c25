import datetime

from .common import (
    STATIC_URL,
    env,
)

# DIXA
DIXA_API_KEY = env.str('DIXA_API_KEY', default='')
DIXA_INTEGRATION_EMAIL = env.str('DIXA_INTEGRATION_EMAIL', default='')
DIXA_TOKEN_EN = env.str('DIXA_TOKEN_EN', default='')
DIXA_TOKEN_DE = env.str('DIXA_TOKEN_DE', default='')
DIXA_TOKEN_FR = env.str('DIXA_TOKEN_FR', default='')


# OLAPIC
OLAPIC_ADDRESS = env.str('OLAPIC_ADDRESS', default='')
OLAPIC_USERNAME = env.str('OLAPIC_USERNAME', default='')
OLAPIC_PASSWORD = env.str('OLAPIC_PASSWORD', default='')


# MEBLEPL API
MEBLEPL_API_TOKEN = env.str('MEBLEPL_API_TOKEN', default='')
MEBLEPL_API_URL = env.str('MEBLEPL_API_URL', default='')

# MEBLEPL SERVER FTP
MEBLEPL_FTP_HOST = env.str('MEBLEPL_FTP_HOST', default='')
MEBLEPL_FTP_PORT = env.int('MEBLEPL_FTP_PORT', default=21)
MEBLEPL_FTP_USERNAME = env.str('MEBLEPL_FTP_USERNAME', default='')
MEBLEPL_FTP_PASSWORD = env.str('MEBLEPL_FTP_PASSWORD', default='')


# ADYEN
ADYEN = {
    'ENVIRONMENT': 'test',
    'DEFAULT_SKIN': 'jkLuKwmh',
    'MERCHANT_ACCOUNT': 'CSTMCO',
    'MERCHANT_SECRET': 'dupadupadupadupa',
    'ADYEN_WS_USER': env.str('ADYEN_WS_TEST_USER', default='<EMAIL>'),
    'ADYEN_WS_PASSWORD': env.str(
        'ADYEN_WS_TEST_PASSWORD',
        default='jmqSJmMKBC9&brxy4-Ub)rLT{',
    ),
    'PUBLIC_KEY': (
        '10001|9703F469F0B9883EC4265E236482D28E238985ECF7E586D91A8292D0779'
        '7092E32556CBD1E6703C9E34DE8C7AF5E51213842BC9FDCC907C0FD49334BD8F7'
        'AC7DA4CB3BB5E0CBBC2E04F126F4E94C9DE4682814019DD41747ADE9D2871A67F'
        '3290B4C46171AA5F215F4CA962D860B02EEFBF931D84EC71D0F99CE0378241E07'
        'F42AECB2E7E341BB6672D3C43740857613CB915B432DE3B06D6ED1EF16DFC3135'
        'DF49768651BAFD4395F58AA71239621BD5226DDBA860BE73630BA8817019C8954'
        'B805F77A2EDFE77953A08BE22882F516E7451D901265E4DB755C2B81E97F11BDD'
        '17542911A2E74C8E64233F8860AE20E221324337450BDBC58720CB7E9E58B07'
    ),
    'ADYEN_REPORT_USER': '<EMAIL>',
    'ADYEN_REPORT_PASSWORD': '4-9dAeWg2W6ZH@=vzJ]c?97-m',
    'ADYEN_HMAC_KEY': (
        '471069ABCBAB48B40724041ECDC109DC452D2AD05EA275621C9AC058DF7CC214'
    ),
    'CHECKOUT_API_AUTH_HEADER': 'X-API-Key',
    'CHECKOUT_API_KEY': env.str(
        'TEST_CHECKOUT_API_KEY',
        default='AQEfhmfuXNWTK0Qc+iSToVAVbEcx5Z94CVB9dNmvI0+MxRDBXVsNvuR83LVYjEgiTGAH-'
        'B+qjOmlsDyVBu6+ZCsfhzcMkhP2xAXLy2176vsBpjQw=-hSwnA9ya=)#HpWx5',
    ),
    'ADYEN_CLIENT_KEY': env.str(
        'ADYEN_TEST_CLIENT_KEY',
        default='test_SGLSEFU3QVBKVFCAER6VVQZURMRVTYBC',
    ),
    'CHECKOUT_SETUP_URL': (
        'https://checkout-test.adyen.com/services/PaymentSetupAndVerification/setup'
    ),
    'WEBHOOK_USERNAME': env.str('ADYEN_TEST_WEBHOOK_USERNAME', 'kazimierz_deyna'),
    'WEBHOOK_PASSWORD': env.str('ADYEN_TEST_WEBHOOK_PASSWORD', 'legia1916'),
}
ADYEN_LIVE = {
    'ENVIRONMENT': 'live',
    'DEFAULT_SKIN': 'jkLuKwmh',
    'MERCHANT_ACCOUNT': env.str('ADYEN_MERCHANT_ACCOUNT', default=''),
    'MERCHANT_SECRET': env.str('ADYEN_MERCHANT_SECRET', default=''),
    'ADYEN_WS_USER': env.str('ADYEN_MERCHANT_WS_ACCOUNT', default=''),
    'ADYEN_WS_PASSWORD': env.str('ADYEN_MERCHANT_WS_SECRET', default=''),
    'PUBLIC_KEY': env.str('ADYEN_PUBLIC_KEY', default=''),
    'ADYEN_REPORT_USER': env.str('ADYEN_REPORT_USER', default=''),
    'ADYEN_REPORT_PASSWORD': env.str('ADYEN_REPORT_PASSWORD', default=''),
    'ADYEN_HMAC_KEY': env.str('ADYEN_HMAC_KEY', default=''),
    'CHECKOUT_API_AUTH_HEADER': 'X-API-Key',
    'CHECKOUT_API_KEY': env.str('CHECKOUT_API_KEY', default=''),
    'ADYEN_CLIENT_KEY': env.str('ADYEN_CLIENT_KEY', default=''),
    'CHECKOUT_SETUP_URL': env.str('CHECKOUT_SETUP_URL', default=''),
    'LIVE_ENDPOINT_PREFIX': env.str('ADYEN_LIVE_ENDPOINT_PREFIX', default=''),
    'WEBHOOK_USERNAME': env.str('ADYEN_WEBHOOK_USERNAME', default=''),
    'WEBHOOK_PASSWORD': env.str('ADYEN_WEBHOOK_PASSWORD', default=''),
}


# PRIMER
PRIMER_API_KEY = env.str('PRIMER_API_KEY', default='')
PRIMER_API_HOST = env.str('PRIMER_API_HOST', default='')
PRIMER_API_VERSION = env.str('PRIMER_API_VERSION', default='2.1')
PRIMER_WEBHOOK_SECRET = env.str('PRIMER_WEBHOOK_SECRET', default='')
PRIMER_MERCHANT_ACCOUNT = env.str('PRIMER_MERCHANT_ACCOUNT', default='CSTMCO_Primer')

# TRUSTED SHOPS
TRUSTEDSHOPS_TS_ID = env.str('TRUSTEDSHOPS_TS_ID', default='')
TRUSTEDSHOPS_USER = env.str('TRUSTEDSHOPS_USER', default='<EMAIL>')
TRUSTEDSHOPS_PASSWORD = env.str('TRUSTEDSHOPS_PASSWORD', default='')

# TRUSTPILOT
TRUSTPILOT_ID = env.str('TRUSTPILOT_ID', default='tylko.com')

# GOOGLE ANALYTICS
GA_ACCOUNT = env.str(
    'GA_ACCOUNT',
    default=(
        '<EMAIL>'
    ),
)
# TODO: consider loading it in a different way (credential + injection?)
# TODO: secret and file
GA_ACCOUNT_KEY_PATH = env.str('GA_ACCOUNT_KEY_PATH', default='')
GA_PROFILE_ID_WEB = env.int('GA_PROFILE_ID', default=*********)
USER_HMAC_HASH_SECRET = env.str('USER_HMAC_HASH_SECRET', default='')

# SLACK
SLACK_CS_WEBHOOK_URL = env.str('SLACK_CS_WEBHOOK_URL', default='')
SLACK_WEBHOOK = env.str('SLACK_WEBHOOK', default=None)
SLACK_BOT_TOKEN = env.str('SLACK_BOT_TOKEN', default='')
SLACK_WEBHOOK_SECRET = env.str('SLACK_WEBHOOK_SECRET', default='')

# TYPEFORM
TYPEFORM_TOKEN = env.str('TYPEFORM_TOKEN', default='')
TYPEFORM_FORMS = env.list('TYPEFORM_FORMS', default=[])


# django-ckeditor
CKEDITOR_BASEPATH = f'{STATIC_URL}/ckeditor/ckeditor/'
CKEDITOR_CONFIGS = {
    'simple': {
        'versionCheck': False,
        'height': 100,
        'toolbar_Full': [
            [
                'Styles',
                'Format',
                'FontSize',
                'Bold',
                'Italic',
                'Underline',
                'Strike',
                'SpellChecker',
            ],
            ['Link', 'Unlink', 'Anchor'],
            ['TextColor', 'BGColor'],
            ['Source'],
        ],
    },
}

# PIPEDRIVE
PIPEDRIVE_API_KEY = env.str('PIPEDRIVE_API_KEY', default='')

# SILK
USE_SILK = False

# AWS CREDENTIALS
AWS_ACCESS_KEY_ID = env.str('AWS_ACCESS_KEY_ID', '')
AWS_SECRET_ACCESS_KEY = env.str('AWS_SECRET_ACCESS_KEY', '')
AWS_S3_REGION_NAME = env.str('AWS_S3_REGION_NAME', 'eu-central-1')

# AWS CONFIG
AWS_DEFAULT_ACL = None
AWS_PRELOAD_METADATA = False
AWS_S3_FILE_OVERWRITE = False
AWS_IS_GZIPPED = True
AWS_QUERYSTRING_AUTH = False
AWS_S3_ENDPOINT_URL = (
    env.str(
        'AWS_S3_ENDPOINT_URL',
        default=None,
    )
    or None
)

# AWS MEDIA STORAGE
AWS_S3_MEDIA_BUCKET_NAME = env.str('AWS_S3_MEDIA_BUCKET_NAME', '')
AWS_S3_MEDIA_CUSTOM_DOMAIN = env.str(
    'AWS_S3_MEDIA_CUSTOM_DOMAIN', f'{AWS_S3_MEDIA_BUCKET_NAME}.s3.amazonaws.com'
)
AWS_S3_PRIVATE_MEDIA_CUSTOM_DOMAIN = env.str('AWS_S3_PRIVATE_MEDIA_CUSTOM_DOMAIN', '')
AWS_S3_MEDIA_LOCATION = env.str('AWS_S3_MEDIA_LOCATION', 'media')
AWS_S3_PRIVATE_MEDIA_LOCATION = f'{AWS_S3_MEDIA_LOCATION}/private'
AWS_S3_WHATSAPP_FILES_LOCATION = f'{AWS_S3_MEDIA_LOCATION}/whatsapp-files'

USE_AWS_S3_MEDIA_STORAGE = env.bool('USE_AWS_S3_MEDIA_STORAGE', default=False)
if USE_AWS_S3_MEDIA_STORAGE:
    MEDIA_URL = f'https://{AWS_S3_MEDIA_CUSTOM_DOMAIN}/{AWS_S3_MEDIA_LOCATION}/'
    DEFAULT_FILE_STORAGE = env.str(
        'DEFAULT_FILE_STORAGE',
        default='cstm_be.media_storage.PublicMediaS3Storage',
    )
    PRIVATE_FILE_STORAGE = env.str(
        'PRIVATE_FILE_STORAGE',
        default='cstm_be.media_storage.PrivateMediaS3Storage',
    )
    WHATSAPP_FILES_STORAGE = env.str(
        'WHATSAPP_FILES_STORAGE',
        default='cstm_be.media_storage.WhatsappFilesPrivateMediaS3Storage',
    )


# CELERY
CELERY_BROKER_URL = env('CELERY_BROKER_URL', default='django://')
CELERY_RESULT_BACKEND = CELERY_BROKER_URL
CELERY_ACCEPT_CONTENT = ['json']
CELERY_TASK_SERIALIZER = 'json'
CELERY_RESULT_SERIALIZER = 'json'
CELERY_SUPERUSER_USERNAME = 'celery_superuser'
CELERYD_TASK_TIME_LIMIT = datetime.timedelta(minutes=20).total_seconds()
CELERYD_TASK_SOFT_TIME_LIMIT = 60


# VAT
VAT_VALIDATOR = 'checkout.vat.validation.VatValidator'
VIES_URL = 'http://ec.europa.eu/taxation_customs/vies/checkVatService.wsdl'
VIES_PDF_FORM_URL = 'https://ec.europa.eu/taxation_customs/vies/#/vat-validation'


# Token expiration in days
PASSWORD_RESET_TOKEN_EXPIRATION = env.int(
    'PASSWORD_RESET_TOKEN_EXPIRATION',
    default=7,
)

# CRISPY FORMS
CRISPY_TEMPLATE_PACK = 'bootstrap3'


# CLOUDFLARE
CLOUDFLARE_ZONE_ID = env.str('CLOUDFLARE_ZONE_ID', default='')
CLOUDFLARE_API_TOKEN = env.str('CLOUDFLARE_API_TOKEN', default='')

# DEEPL
DEEPL_KEY = env.str('DEEPL_KEY', default='')

# BIGQUERY
BIGQUERY_SURVEY_SPACE = env.str('BIGQUERY_SURVEY_SPACE', default='')
BIGQUERY_CES_SURVEY_TABLE = env.str('BIGQUERY_CES_SURVEY_TABLE', default='')
BIGQUERY_CHECKOUT_SURVEY_TABLE = env.str('BIGQUERY_CHECKOUT_SURVEY_TABLE', default='')

# GOOGLE GEMINI
GEMINI_API_KEY = env.str('GEMINI_API_KEY', default='')
