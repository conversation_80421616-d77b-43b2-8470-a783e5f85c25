from .production import *

DEBUG = env.bool('DJANGO_DEBUG', default=False)

# EMAILS
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'

# LOCAL GENERAL SETTINGS
IS_DEV = True
IS_PRODUCTION = False

CSTM_PROD_TOKEN = env.str('CSTM_PROD_TOKEN', default='')

# WEBPACK LOADER
WEBPACK_LOADER['DEFAULT']['CACHE'] = not DEBUG
WEBPACK_LOADER['ES5']['CACHE'] = not DEBUG

FIXTURE_DIRS = ['fixtures']

EXPORT_CASH_FLOW_TO_BIG_QUERY = False

CELERY_BROKER_HEARTBEAT = 10
CELERY_REDIS_BACKEND_HEALTH_CHECK_INTERVAL = 10
CELERY_REDIS_SOCKET_TIMEOUT = 10
CELERY_REDIS_SOCKET_CONNECT_TIMEOUT = 10
CELERY_REDIS_RETRY_ON_TIMEOUT = True
CELERY_BROKER_POOL_LIMIT = None

BRAZE_SMS_SUBSCRIPTION_GROUPS = {'+48': '0bd319e5-9e07-45e8-8116-b1319f30e3db'}
