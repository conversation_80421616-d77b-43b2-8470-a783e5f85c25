import os
import pwd

from itertools import chain

import datadog
import sentry_sdk

from sentry_sdk.integrations.django import DjangoIntegration
from sentry_sdk.integrations.redis import RedisIntegration

from ..logging_filters import skip_locale_root_warn
from .base import *

_current_username = pwd.getpwuid(os.getuid()).pw_name

# DEBUG
DEBUG = False

# SECRET KEY
SECRET_KEY = env.str('DJANGO_SECRET_KEY')

# DATABASES
DATABASES['default']['DISABLE_SERVER_SIDE_CURSORS'] = True
DATABASES['default']['TEST'] = {
    'NAME': 'testdb_{}'.format(_current_username),
    'DISABLE_SERVER_SIDE_CURSORS': True,
}

# CACHES
CACHES['default']['KEY_PREFIX'] = env.str(
    'CACHE_KEY_PREFIX',
    default='{}_'.format(_current_username),
)

# EMAILS
# TODO: validate if TLS could be turned on
EMAIL_USE_TLS = False
EMAIL_HOST_PASSWORD = env.str('DJANGO_EMAIL_HOST_PASSWORD')
ADMINS = [
    ('Bartłomiej Wołyńczyk', '<EMAIL>'),
]
MANAGERS = ADMINS

# HTTP
ALLOWED_HOSTS = env.list('DJANGO_ALLOWED_HOSTS')

# SECURITY
# disabled by purpose, obsolete setting, brings no benefits
SECURE_BROWSER_XSS_FILTER = False
SECURE_CONTENT_TYPE_NOSNIFF = True
# disabled by purpose, redirect to https is handled by nginx and external proxy
SECURE_SSL_REDIRECT = False
SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTOCOL', 'https')

SESSION_COOKIE_SAMESITE = 'None'  # Required for third-party OAuth flows
SESSION_COOKIE_HTTPONLY = True
SESSION_COOKIE_SECURE = True
CSRF_COOKIE_HTTPONLY = True
CSRF_COOKIE_SECURE = True

# TODO: adjust this value
# SECURE_HSTS_SECONDS = 60
# SECURE_HSTS_INCLUDE_SUBDOMAINS = env.bool(
#     'DJANGO_SECURE_HSTS_INCLUDE_SUBDOMAINS',
#     default=True,
# )
# SECURE_HSTS_PRELOAD = env.bool('DJANGO_SECURE_HSTS_PRELOAD', default=True)
# X_FRAME_OPTIONS = 'DENY'

# LOGGING
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'request_format': {
            'format': (
                '%(levelname)s %(asctime)s %(remote_addr)s %(request_id)s '
                '%(user_id)s {%(funcName)s:%(lineno)d} %(message)s -- '
                '%(user_agent)s'
            ),
        },
        'verbose': {
            'format': (
                '[%(asctime)s] %(levelname)s {%(funcName)s:%(lineno)d} - "%(message)s"'
            ),
        },
        'simple': {'format': '%(levelname)s %(message)s'},
        'json': {
            '()': 'pythonjsonlogger.jsonlogger.JsonFormatter',
            'format': (
                '%(levelname)s %(asctime)s %(remote_addr)s %(request_id)s '
                '%(user_id)s {%(funcName)s:%(lineno)d} %(message)s -- '
                '%(user_agent)s'
            ),
        },
        'datadog': {
            '()': 'pythonjsonlogger.jsonlogger.JsonFormatter',
            'fmt': (
                '%(asctime)s %(levelname)s [%(name)s] [%(filename)s:%(lineno)d] '
                '[dd.service=%(dd.service)s dd.env=%(dd.env)s '
                'dd.version=%(dd.version)s '
                'dd.trace_id=%(dd.trace_id)s dd.span_id=%(dd.span_id)s] '
                '- %(message)s'
            ),
        },
        'celery': {
            '()': 'celery.app.log.TaskFormatter',
            'format': (
                '%(asctime)s - %(task_id)s - %(task_name)s - %(name)s - '
                '%(levelname)s - %(message)s'
            ),
        },
    },
    'filters': {
        'require_debug_false': {'()': 'django.utils.log.RequireDebugFalse'},
        'skip_locale_root_warn': {
            '()': 'django.utils.log.CallbackFilter',
            'callback': skip_locale_root_warn,
        },
        'request': {
            '()': 'custom.filters.MoreVerboseFilter',
        },
    },
    'handlers': {
        'console': {
            'level': 'DEBUG',
            'class': 'logging.StreamHandler',
            'filters': ['require_debug_false'],
        },
        'log_file': {
            'level': 'DEBUG',
            'class': 'logging.handlers.WatchedFileHandler',
            'filename': str(APPS_DIR.path('logs/django.log')),
            'formatter': 'request_format',
            'filters': ['skip_locale_root_warn', 'request'],
        },
        'producers_file': {
            'level': 'DEBUG',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': str(APPS_DIR.path('logs/django_producers.log')),
            'maxBytes': 1 * 1024 * 1024 * 1024,  # 1GB
            'backupCount': 5,
            'formatter': 'request_format',
            'filters': ['skip_locale_root_warn', 'request'],
        },
        'invoice_file': {
            'level': 'DEBUG',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': str(APPS_DIR.path('logs/django_invoice.log')),
            'maxBytes': 1 * 1024 * 1024 * 1024,  # 1GB
            'backupCount': 5,
            'formatter': 'request_format',
            'filters': ['skip_locale_root_warn', 'request'],
        },
        'orders_file': {
            'level': 'DEBUG',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': str(APPS_DIR.path('logs/django_orders.log')),
            'maxBytes': 1 * 1024 * 1024 * 1024,  # 1GB
            'backupCount': 5,
            'formatter': 'request_format',
            'filters': ['skip_locale_root_warn', 'request'],
        },
        'mail_log_file': {
            'level': 'DEBUG',
            'class': 'logging.handlers.WatchedFileHandler',
            'filename': str(APPS_DIR.path('logs/django_mail.log')),
            'formatter': 'verbose',
            'filters': ['skip_locale_root_warn'],
        },
        'celery_file': {
            'level': 'WARNING',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': str(APPS_DIR.path('logs/django_celery.log')),
            'maxBytes': 1 * 1024 * 1024 * 1024,  # 1GB
            'backupCount': 5,
            'formatter': 'celery',
            'filters': ['skip_locale_root_warn'],
        },
        'celery_task_file': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': str(APPS_DIR.path('logs/tasks.log')),
            'maxBytes': 1 * 1024 * 1024 * 1024,  # 1GB
            'backupCount': 5,
            'formatter': 'celery',
            'filters': ['skip_locale_root_warn'],
        },
        'celery_producers_task_file': {
            'level': 'WARNING',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': str(APPS_DIR.path('logs/tasks_producers.log')),
            'maxBytes': 1 * 1024 * 1024 * 1024,  # 1GB
            'backupCount': 5,
            'formatter': 'celery',
            'filters': ['skip_locale_root_warn'],
        },
        'celery_invoice_task_file': {
            'level': 'DEBUG',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': str(APPS_DIR.path('logs/tasks_invoice.log')),
            'maxBytes': 1 * 1024 * 1024 * 1024,  # 1GB
            'backupCount': 5,
            'formatter': 'celery',
            'filters': ['skip_locale_root_warn'],
        },
        'celery_mailing_task_file': {
            'level': 'DEBUG',
            'class': 'logging.handlers.RotatingFileHandler',
            'maxBytes': 1 * 1024 * 1024 * 1024,  # 1GB
            'backupCount': 5,
            'formatter': 'celery',
            'filename': str(APPS_DIR.path('logs/tasks_mailing.log')),
            'filters': ['skip_locale_root_warn'],
        },
        'celery_customer_service_task_file': {
            'level': 'DEBUG',
            'class': 'logging.handlers.RotatingFileHandler',
            'maxBytes': 1 * 1024 * 1024 * 1024,  # 1GB
            'backupCount': 5,
            'formatter': 'celery',
            'filename': str(APPS_DIR.path('logs/tasks_customer_service.log')),
            'filters': ['skip_locale_root_warn'],
        },
        'null': {
            'class': 'logging.NullHandler',
        },
        'datadog': {
            'level': 'WARNING',
            'class': 'logging.handlers.WatchedFileHandler',
            'formatter': 'datadog',
            'filename': str(APPS_DIR.path('logs/dd_json.log')),
        },
    },
    'loggers': {
        'cstm': {
            'handlers': ['log_file', 'datadog'],
            'level': 'DEBUG',
            'propagate': True,
        },
        'producers': {
            'handlers': ['producers_file', 'datadog'],
            'level': 'DEBUG',
            'propagate': True,
        },
        'invoice': {
            'handlers': ['invoice_file', 'datadog'],
            'level': 'DEBUG',
            'propagate': True,
        },
        'orders': {
            'handlers': ['orders_file', 'datadog'],
            'level': 'DEBUG',
            'propagate': True,
        },
        'django': {
            'handlers': ['console', 'log_file', 'datadog'],
            'level': 'INFO',
            'propagate': True,
        },
        'celery': {
            'handlers': ['celery_file', 'datadog'],
            'level': 'WARNING',
            'propagate': True,
        },
        'celery_task': {
            'handlers': ['celery_task_file', 'datadog'],
            'level': 'INFO',
            'propagate': False,
        },
        'producers.tasks': {
            'handlers': ['celery_producers_task_file', 'datadog'],
            'level': 'INFO',
            'propagate': False,
        },
        'invoice.tasks': {
            'handlers': ['celery_invoice_task_file', 'datadog'],
            'level': 'INFO',
            'propagate': False,
        },
        'mailing.tasks': {
            'handlers': ['celery_mailing_task_file', 'datadog'],
            'level': 'INFO',
            'propagate': False,
        },
        'customer_service.tasks': {
            'handlers': ['celery_customer_service_task_file', 'datadog'],
            'level': 'INFO',
            'propagate': False,
        },
        'django_mailer': {
            'handlers': ['mail_log_file', 'datadog'],
            'level': 'INFO',
            'propagate': True,
        },
        'sorl.thumbnail': {
            'handlers': ['log_file', 'console', 'datadog'],
            'level': 'ERROR',
            'propagate': True,
        },
        'django.security.DisallowedHost': {
            'handlers': ['null'],
            'propagate': False,
        },
    },
}

# WEBPACK LOADER
WEBPACK_LOADER['DEFAULT']['CACHE'] = not DEBUG
WEBPACK_LOADER['ES5']['CACHE'] = not DEBUG

# DATADOG
INSTALLED_APPS.append('datadog')
INSTALLED_APPS.append('ddtrace.contrib.django')
DATADOG_APP_NAME = env.str('DATADOG_APP_NAME')

# API
DATADOG_API_KEY = env.str('DATADOG_API_KEY')
DATADOG_APP_KEY = env.str('DATADOG_APP_KEY')
DATADOG_HOST = env.str('DATADOG_HOST', default='https://app.datadoghq.com')

# STATSD
DATADOG_STATSD_HOST = env.str('DATADOG_STATSD_HOST', default='localhost')
DATADOG_STATSD_PORT = env.int('DATADOG_STATSD_PORT', default=8125)

datadog.initialize(
    api_key=DATADOG_API_KEY,
    app_key=DATADOG_APP_KEY,
    statsd_host=DATADOG_STATSD_HOST,
    statsd_port=DATADOG_STATSD_PORT,
    statsd_constant_tags=[DATADOG_APP_NAME],
)

# SENTRY
SENTRY_DSN = env.str('SENTRY_DSN')
sentry_sdk.init(
    dsn=SENTRY_DSN,
    integrations=[DjangoIntegration(), RedisIntegration()],
    send_default_pii=True,
)

# GENERAL SETTINGS
IS_TESTING = False
IS_DEV = False
IS_PRODUCTION = True

# FACEBOOK
FB_APP_ID = env.str('FB_APP_ID')
FB_APP_SECRET = env.str('FB_APP_SECRET')
FB_APP_TOKEN = env.str('FB_APP_TOKEN')

# MAILCHIMP
MAILCHIMP_API_KEY = env.str('MAILCHIMP_API_KEY')

# TRUSTED SHOPS
TRUSTEDSHOPS_TS_ID = env.str('TRUSTEDSHOPS_TS_ID')
TRUSTEDSHOPS_PASSWORD = env.str('TRUSTEDSHOPS_PASSWORD')

# GOOGLE ANALYTICS
# TODO: secret and file
GA_ACCOUNT_KEY_PATH = env.str('GA_ACCOUNT_KEY_PATH')

# TYPEFORM
TYPEFORM_TOKEN = env.str('TYPEFORM_TOKEN')

# A/B TESTS SETTINGS
AB_TESTS_TOKEN_SETTINGS['signing_key'] = env.str(
    'AB_TESTS_TOKEN_SECRET_KEY',
    default=SECRET_KEY,
)

# ADYEN
ADYEN_LIVE = {
    'ENVIRONMENT': env.str('ADYEN_ENVIRONMENT', default='live'),
    'DEFAULT_SKIN': 'jkLuKwmh',
    'MERCHANT_ACCOUNT': env.str('ADYEN_MERCHANT_ACCOUNT'),
    'MERCHANT_SECRET': env.str('ADYEN_MERCHANT_SECRET'),
    'ADYEN_WS_USER': env.str('ADYEN_MERCHANT_WS_ACCOUNT'),
    'ADYEN_WS_PASSWORD': env.str('ADYEN_MERCHANT_WS_SECRET'),
    'PUBLIC_KEY': env.str('ADYEN_PUBLIC_KEY'),
    'ADYEN_REPORT_USER': env.str('ADYEN_REPORT_USER'),
    'ADYEN_REPORT_PASSWORD': env.str('ADYEN_REPORT_PASSWORD'),
    'ADYEN_HMAC_KEY': env.str('ADYEN_HMAC_KEY'),
    'CHECKOUT_API_AUTH_HEADER': 'X-API-Key',
    'CHECKOUT_API_KEY': env.str('CHECKOUT_API_KEY'),
    'ADYEN_CLIENT_KEY': env.str('ADYEN_CLIENT_KEY'),
    'CHECKOUT_SETUP_URL': env.str('CHECKOUT_SETUP_URL'),
    'LIVE_ENDPOINT_PREFIX': env.str('ADYEN_LIVE_ENDPOINT_PREFIX'),
    'WEBHOOK_USERNAME': env.str('ADYEN_WEBHOOK_USERNAME', default=''),
    'WEBHOOK_PASSWORD': env.str('ADYEN_WEBHOOK_PASSWORD', default=''),
}

# MAILING
ANOMALY_RECIPIENTS = list(
    chain(
        ADMINS,
        [('Bernard Pyrgies', '<EMAIL>')],
    )
)
ORDER_REPORT_RECIPIENTS = [
    ('Bartłomiej Wołyńczyk', '<EMAIL>'),
]
PAYMENT_FAILED_RECIPIENTS = [
    ('Axel Kostrzewski', '<EMAIL>'),
]
PAYMENT_CHARGEBACK_RECIPIENTS = [
    ('Bartek ', '<EMAIL>'),
    ('Bernard Pyrgies', '<EMAIL>'),
    ('Mateusz Zielinski', '<EMAIL>'),
    ('Artur Cieslak', '<EMAIL>'),
    ('Łukasz Mydliński', '<EMAIL>'),
    ('Maciej Jankowski', '<EMAIL>'),
    ('Wojciech Iwaszkiewicz', '<EMAIL>'),
    ('Marta Chyłek', '<EMAIL>'),
    ('Karolina Śliwowska ', '<EMAIL>'),
    ('Adrianna Popiołek', '<EMAIL>'),
]
LOGISTIC_RECIPIENTS = [
    ('Logistic Department', '<EMAIL>'),
]
LOGISTIC_RECIPIENTS_DAMAGE_FORM = [
    ('Logistic Backup', '<EMAIL>'),
]
LOGISTIC_RECIPIENTS_MANAGER = [
    ('Maciej Jankowski', '<EMAIL>'),
    ('Krzysztof Ziemski', '<EMAIL>'),
]

ACCOUNTING_RECIPIENTS = [
    ('Bartłomiej Wołyńczyk', '<EMAIL>'),
    ('Accounting team', '<EMAIL>'),
]
ACCOUNTING_MISSING_INVOICE_RECIPIENTS = [
    ('Aleksandra Blusz', '<EMAIL>'),
    ('Ewelina Miros', '<EMAIL>'),
]
ACCOUNTING_ORDER_ABORTED = [
    ('Aleksandra Jaszczerska', '<EMAIL>'),
]
INVOICE_BACKUP_RECIPIENTS = [
    ('Invoice Mail', '<EMAIL>'),
]
PRODUCTION_RECIPIENTS = [
    ('Dominika Jankowska', '<EMAIL>'),
    ('Artur Cieślak', '<EMAIL>'),
    ('Mateusz Zieliński', '<EMAIL>'),
]

STOCKS_RECIPIENTS = [
    ('Mateusz Zieliński', '<EMAIL>'),
    ('Bartłomiej Wołyńczyk', '<EMAIL>'),
]

MANUFACTURERS_RECIPIENTS = {
    1: (  # Drewtur
        ('Monika Kubacka', '<EMAIL>'),
        ('Anna Czekała', '<EMAIL>'),
        ('Milena Herbich', '<EMAIL>'),
        ('Joanna Klesta', '<EMAIL>'),
    ),
    29: (  # S93
        ('Andrzej Bartoszek', '<EMAIL>'),
        ('Grzegorz Sobocinski', '<EMAIL>'),
        ('Agnieszka', '<EMAIL>'),
    ),
    30: (('Biuro Amir', '<EMAIL>'),),  # Amir
    33: (  # Center Mebel
        ('Monika Parchanowicz', '<EMAIL>'),
        ('Tomasz Bajkiewicz', '<EMAIL>'),
    ),
    34: (  # Telmex
        ('Marek Ferenc', '<EMAIL>'),
        ('Zbigniew Szewczak', '<EMAIL>'),
        ('Dorota Slabon', '<EMAIL>'),
        ('Dariusz Paszkowski', '<EMAIL>'),
    ),
    35: (('Natalia Grzeskowiak', '<EMAIL>'),),  # Gala
}

PRODUCTION_RECIPIENTS_ON_ABORT = (
    ('Małgorzata Ciępka ', '<EMAIL>'),
    ('Dominika Jankowska', '<EMAIL>'),
)

MANDRILL_REPORT_SENDER_LIST = ['<EMAIL>', '<EMAIL>']

# PRODUCTION SYSTEM
PRODUCTION_SYSTEM_API_URL = env.str('PRODUCTION_SYSTEM_API_URL')
PRODUCTION_SYSTEM_API_URL = PRODUCTION_SYSTEM_API_URL.strip('/')

EXPORT_CASH_FLOW_TO_BIG_QUERY = True

QUALITY_CONTROL_EMAILS = [
    ('Dominika Jankowska', '<EMAIL>'),
    ('Slawomir Fujarczuk', '<EMAIL>'),
]

SHELF_MARKET_REPORT_CREATED_RECIPIENTS = [
    ('Maciej Fąk', '<EMAIL>'),
]

BRAZE_SMS_SUBSCRIPTION_GROUPS = {
    '+43': '4a6862ee-fa5d-45a9-a37f-b1b2d8234064',
    '+32': '9ce4a4f6-d4b2-4ab4-bd8d-6789b477802e',
    '+45': 'd362f620-29ef-40e1-abbe-cfe6295e7a0f',
    '+358': '1c31b848-50f0-4086-8d7b-c540099adcb8',
    '+33': '2f1fed01-f962-4ea8-b706-1a1a4d59b351',
    '+49': 'b9279ebe-3b89-4568-9282-6d9c0aab5a4c',
    '+39': '808ad629-600a-4536-95e5-dcf9a5eddc62',
    '+31': '4974708c-876f-4452-9532-85d255cd10cf',
    '+47': 'c538a7da-9642-4be8-988c-a2d6658ec21d',
    '+48': 'ecc94d4b-01c1-4024-9614-3b0046eca76c',
    '+351': '92797b07-99a5-43fd-a881-d20c6915b0bc',
    '+34': '220969bc-f8d3-4292-9b25-47b6a8f67946',
    '+46': '922d6728-27ca-4a18-8474-f613d1f6908b',
    '+41': 'fdbd81b0-08ae-4f71-b24d-3e401e48da55',
    '+44': '4b126a31-58d0-4fd2-a622-769345fd2ccd',
}
