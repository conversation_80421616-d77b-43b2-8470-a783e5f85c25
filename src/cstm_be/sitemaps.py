from django.contrib.sitemaps import Sitemap
from django.contrib.sites.models import Site
from django.core.exceptions import ImproperlyConfigured

from gallery.models import Jetty


class ImageTagException(Exception):  # noqa: N818
    pass


REQUIRED_IMAGE_TAGS = [
    'url',
]
OPTIONAL_IMAGE_TAGS = [
    'caption',
    'geo_location',
    'title',
    'license',
]


class ImageSitemap(Sitemap):
    def __get(self, name, obj, default=None, **kwargs):
        try:
            attr = getattr(self, name)
        except AttributeError:
            return default
        if callable(attr):
            return attr(obj, **kwargs)
        return attr

    def image_url(self, img, item=None):
        return img.url

    def images(self, obj):
        return [obj.preview]

    def get_urls(self, page=1, site=None, protocol='https'):
        if site is None:
            if Site._meta.installed:
                try:
                    site = Site.objects.get_current()
                except Site.DoesNotExist:
                    pass
            if site is None:
                raise ImproperlyConfigured(
                    'In order to use Sitemaps you must either use the sites '
                    'framework or pass in a Site or RequestSite object in '
                    'your view code.'
                )

        urls = []

        # methods with this prefix will be treated as image tags.
        ATTR_PREFIX = 'image_'  # noqa: N806

        for item in self.paginator.page(page).object_list:
            loc = '{}://{}{}'.format(
                protocol, site.domain, self.__get('location', item)
            )
            image_tags = []
            for attr in dir(self):
                if attr.startswith(ATTR_PREFIX):
                    image_tags.append(attr[len(ATTR_PREFIX) :])  # noqa E203

            url_info = {
                'location': loc,
            }

            optional_tags = image_tags[:]
            url_info['images'] = []
            for idx, img in enumerate(self.images(item)):
                url_info['images'].append({})

                # validate required tags.
                for req_tag in REQUIRED_IMAGE_TAGS:
                    if req_tag not in image_tags:
                        raise ImageTagException(
                            '<image:{}> is a required tag.'.format(req_tag)
                        )
                    try:
                        url_info['images'][idx][req_tag] = self.__get(
                            '{}{}'.format(ATTR_PREFIX, req_tag), img, None, item=item
                        )
                    except ValueError:
                        pass
                    if req_tag in optional_tags:
                        optional_tags.remove(req_tag)

                # validate the rest of the tags
                for tag in optional_tags:
                    if tag not in OPTIONAL_IMAGE_TAGS:
                        raise ImageTagException(
                            '<image:{}> tag is not supported.'.format(tag)
                        )

                url_info['images'][idx]['optional'] = {}
                for tag in optional_tags:
                    url_info['images'][idx]['optional'][tag] = self.__get(
                        '{}{}'.format(ATTR_PREFIX, tag),
                        img,
                        None,
                        item=item,
                    )
            urls.append(url_info)
        return urls


class FurnitureImageSitemap(ImageSitemap):
    def image_title(self, img, item=None):
        if item:
            return item.get_title()
        else:
            return 'Tylko Furniture'

    def images(self, obj):
        return [obj.preview]

    def items(self):
        return list(Jetty.objects.filter(preset=True).filter(preview__isnull=False))

    def location(self, obj):
        return obj.get_absolute_url()
