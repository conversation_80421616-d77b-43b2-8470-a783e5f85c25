import errno
import os

from collections import OrderedDict

from django.contrib.staticfiles.storage import ManifestStaticFilesStorage
from django.contrib.staticfiles.utils import matches_patterns
from django.utils.encoding import force_str


class HashedSymlinkFilesMixin(ManifestStaticFilesStorage):
    manifest_strict = False

    def _symlink(self, name, source):
        full_path = self.path(name)
        directory = os.path.dirname(full_path)
        source_path, source_filename = os.path.split(source)  # noqa: RUF059
        if not os.path.exists(directory):
            try:
                if self.directory_permissions_mode is not None:
                    old_umask = os.umask(0)
                    try:
                        os.makedirs(directory, self.directory_permissions_mode)
                    finally:
                        os.umask(old_umask)
                else:
                    os.makedirs(directory)
            except OSError as e:
                if e.errno != errno.EEXIST:
                    raise
        if not os.path.isdir(directory):
            raise IOError(
                '{} exists and is not a directory.'.format(directory),
            )
        if os.path.lexists(full_path):
            os.unlink(full_path)
        os.symlink(source_filename, full_path)
        return name

    def file_hash(self, name, content=None):
        _hash = super(HashedSymlinkFilesMixin, self).file_hash(name, content=content)
        hash_digits = ''
        for i, c in enumerate(_hash):
            if c.isdigit():
                hash_digits += c
            else:
                hash_digits += str(ord(c))
            if i == 11:
                break
        return hash_digits

    def post_process(self, paths, dry_run=False, **options):
        if dry_run:
            return
        hashed_files = OrderedDict()

        def matches(path):
            return matches_patterns(path, list(self._patterns.keys()))

        def path_level(name):
            return len(name.split(os.sep))

        adjustable_paths = [path for path in paths if matches(path)]

        for name in sorted(list(paths.keys()), key=path_level, reverse=True):  # noqa: C414
            storage, path = paths[name]
            with storage.open(path) as original_file:
                hashed_name = self.hashed_name(name, original_file)
                if hasattr(original_file, 'seek'):
                    original_file.seek(0)
                hashed_file_exists = self.exists(hashed_name)
                processed = False
                if name in adjustable_paths:
                    if hashed_file_exists:
                        os.unlink(self.path(hashed_name))
                    saved_name = self._symlink(hashed_name, path)
                    hashed_name = force_str(self.clean_name(saved_name))
                    processed = True
                else:
                    if not hashed_file_exists:
                        processed = True
                        saved_name = self._symlink(hashed_name, path)
                        hashed_name = force_str(self.clean_name(saved_name))
                hashed_files[self.hash_key(name)] = hashed_name
                yield name, hashed_name, processed
        self.hashed_files.update(hashed_files)
