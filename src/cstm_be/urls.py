import datetime

from django.conf import settings
from django.conf.urls.i18n import i18n_patterns
from django.conf.urls.static import static
from django.contrib import admin
from django.contrib.sitemaps import views
from django.urls import (
    include,
    path,
    re_path,
    register_converter,
)
from django.views.decorators.cache import cache_page
from django.views.i18n import JavaScriptCatalog

from allauth.socialaccount.providers.apple.views import (
    AppleOAuth2Adapter,
    apple_post_callback,
)
from allauth.socialaccount.providers.facebook.views import FacebookOAuth2Adapter
from allauth.socialaccount.providers.google.views import GoogleOAuth2Adapter
from allauth.socialaccount.providers.oauth2.views import (
    OAuth2CallbackView,
    OAuth2LoginView,
)
from health_check import urls as health_urls

from cstm_be.sitemaps import FurnitureImageSitemap
from custom.utils.hashid_converter import HashIdConverter
from custom.views import page_not_found_handler
from dixa.urls import whatsapp_file_router
from payments.views import AdyenRedirectView
from socials.views import redirect_to_hp_view

ONE_DAY = datetime.timedelta(days=1).total_seconds()

sitemaps_images = {
    'furniture-images': FurnitureImageSitemap,
}

register_converter(HashIdConverter, 'hashid')

handler404 = page_not_found_handler

allauth_paths = [
    path(
        'accounts/social/login/cancelled/',
        redirect_to_hp_view,
        name='socialaccount_login_cancelled',
    ),
    path(
        'accounts/social/login/error/',
        redirect_to_hp_view,
        name='socialaccount_login_error',
    ),
    # Apple login
    path(
        'accounts/apple/login/',
        OAuth2LoginView.adapter_view(AppleOAuth2Adapter),
        name='apple_login',
    ),
    path(
        'accounts/apple/login/callback/',
        apple_post_callback,
        name='apple_callback',
    ),
    path(
        'accounts/apple/login/callback/finish/',
        OAuth2CallbackView.adapter_view(AppleOAuth2Adapter),
        name='apple_finish_callback',
    ),
    # Google login
    path(
        'accounts/google/login/',
        OAuth2LoginView.adapter_view(GoogleOAuth2Adapter),
        name='google_login',
    ),
    path(
        'accounts/google/login/callback/',
        OAuth2CallbackView.adapter_view(GoogleOAuth2Adapter),
        name='google_callback',
    ),
    # Facebook login
    path(
        'accounts/facebook/login/',
        OAuth2LoginView.adapter_view(FacebookOAuth2Adapter),
        name='facebook_login',
    ),
    path(
        'accounts/facebook/login/callback/',
        OAuth2CallbackView.adapter_view(FacebookOAuth2Adapter),
        name='facebook_callback',
    ),
]

urlpatterns = [  # noqa: RUF005
    path(
        'api/v1/api-auth/', include('rest_framework.urls', namespace='rest_framework')
    ),
    # apps urls
    path('api/v1/catalogue/', include('catalogue.urls')),
    path('api/v2/', include('carts.urls')),
    path('api/v1/', include('b2b.urls')),
    path('api/', include('checkout.api_urls')),
    path('api/v1/', include('complaints.urls')),
    path('api/v1/', include('customer_service.api.urls')),
    path('', include('custom.urls')),
    path('api/v1/custom_audiences/', include('custom_audiences.urls')),
    path('cs/', include('customer_service.urls')),
    path('api/v1/', include('dixa.urls')),
    path('api/v1/', include('dynamic_delivery.urls')),
    path('api/v1/ecommerce/', include('ecommerce_api.urls')),
    path('api/v1/', include('gallery.urls')),
    path('items/', include('items_for_render.urls')),
    path('', include('mailing.urls')),
    path('', include('material_recovery.urls')),
    path('api/v1/', include('orders.urls')),
    path('api/', include('payments.urls')),
    path('', include('pricing_v3.urls')),
    path('', include('producers.urls')),
    path('', include('product_feeds.urls')),
    path('', include('feeds.urls')),
    path('api/v1/', include('production_margins.urls')),
    path('api/v1/', include('production_margins.api.urls')),
    path('api/v1/', include('rating_tool.urls')),
    path('api/v1/regions/', include('regions.urls')),
    path('api/v1/promotions/', include('promotions.urls')),
    path('api/v1/render_tasks/', include('render_tasks.urls')),
    path('api/', include('reviews.urls')),
    path('api/v1/', include('user_consents.urls')),
    path('api/', include('user_profile.urls')),
    path('api/v1/', include('vouchers.urls')),
    path('api/v1/waiting_list/', include('waiting_list.urls')),
    path('api/v1/', include('warehouse.api.urls')),
    path('api/v2/auth/', include('rest_auth.urls')),
    path('api/v1/showrooms/', include('showrooms.urls')),
    path('api/v1/skus/', include('skus.urls')),
    path('api/v1/socials/', include('socials.urls')),
    path('internal-api/v1/', include('internal_api.urls')),
    # socials
    path('', include(allauth_paths)),
    # Health checks:
    path('health/', include(health_urls)),
    # admin urls
    path('admin/', admin.site.urls),
    re_path(
        r'^sitemap\.xml$',
        cache_page(ONE_DAY)(views.index),
        {
            'sitemaps': dict(**sitemaps_images),
            'sitemap_url_name': ('django.contrib.sitemaps.views.sitemap'),
        },
        name='django.contrib.sitemaps.views.index',
    ),
    re_path(
        r'^sitemap\-(?P<section>[a-z]+\-images).xml$',
        cache_page(ONE_DAY)(views.sitemap),
        {
            'sitemaps': sitemaps_images,
            'template_name': 'sitemap_images.xml',
        },
        name='django.contrib.sitemaps.views.sitemap',
    ),
    path('whatsapp-files/', include(whatsapp_file_router.urls)),
] + static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)


urlpatterns += i18n_patterns(
    re_path(r'^jsi18n/$', JavaScriptCatalog.as_view(), name='javascript-catalog'),
    re_path(r'^x/', include('shortener.urls')),
    re_path(r'^', include('frontend_cms.urls')),
    re_path(r'^', include('checkout.urls')),
    path('', include('custom.urls_logistic')),
    path(
        'adyen_redirect/<int:pk>/',
        AdyenRedirectView.as_view(),
        name='adyen-redirect',
    ),
    path('editor/', include('gallery_editor.urls')),
    prefix_default_language=True,
)

if settings.DEBUG:
    import debug_toolbar

    from drf_spectacular.views import (
        SpectacularAPIView,
        SpectacularRedocView,
        SpectacularSwaggerView,
    )

    urlpatterns = [  # noqa: RUF005
        path('__debug__/', include(debug_toolbar.urls)),
        path('api/v1/docs/', SpectacularAPIView.as_view(), name='schema'),
        path('api/v1/docs/swagger/', SpectacularSwaggerView.as_view(), name='swagger'),
        path('api/v1/docs/redoc/', SpectacularRedocView.as_view(), name='redoc'),
    ] + urlpatterns

    if settings.USE_SILK:
        urlpatterns += [path('silk/', include('silk.urls', namespace='silk'))]

    urlpatterns += static(
        settings.LOCAL_MEDIA_URL_PREFIX, document_root=settings.MEDIA_ROOT
    )
