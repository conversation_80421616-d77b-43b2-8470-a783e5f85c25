import contextlib
import enum

from typing import (
    TYPE_CHECKING,
    Any,
    Callable,
    Optional,
    Type,
)
from urllib.parse import urljoin

from django.apps import apps
from django.conf import settings
from django.db import models
from django.utils.translation import gettext as _

from checkout.enums import TypeformSurveyMode
from custom.enums.colors import (
    Sofa01Color,
    Type01Color,
    Type02Color,
    Type03Color,
    Type03ExteriorInteriorColor,
    Type13Color,
    Type23Color,
    Type24Color,
    Type25Color,
    VeneerType01Color,
    VeneerType13Color,
)

if TYPE_CHECKING:
    from gallery.enums import FurnitureCategory
    from gallery.models import SellableFurnitureAbstract


class ChoicesMixin:
    @classmethod
    def choices(cls):
        return [(member.value, member.name) for member in cls]


class ChoicesReplacedUnderscoreMixin:
    @classmethod
    def choices(cls):
        return [(member.value, member.name.replace('_', ' ')) for member in cls]


class ShelfType(ChoicesReplacedUnderscoreMixin, enum.IntEnum):
    TYPE01 = 0
    TYPE02 = 1
    VENEER_TYPE01 = 2
    TYPE03 = 3
    TYPE13 = 4
    VENEER_TYPE13 = 5
    TYPE23 = 6
    TYPE24 = 7
    TYPE25 = 8
    SOFA_TYPE01 = 10

    @classmethod
    def get_jetty_shelf_types(cls) -> set['ShelfType']:
        return {cls.TYPE01, cls.TYPE02, cls.VENEER_TYPE01}

    @classmethod
    def get_sotty_shelf_types(cls) -> set['ShelfType']:
        return {cls.SOFA_TYPE01}

    @classmethod
    def get_watty_shelf_types(cls) -> set['ShelfType']:
        return {
            cls.TYPE03,
            cls.TYPE13,
            cls.VENEER_TYPE13,
            cls.TYPE23,
            cls.TYPE24,
            cls.TYPE25,
        }

    @classmethod
    def get_tone_shelf_types(cls) -> set['ShelfType']:
        return {
            cls.TYPE03,
            cls.TYPE23,
            cls.TYPE24,
            cls.TYPE25,
        }

    @classmethod
    def get_edge_shelf_types(cls) -> set['ShelfType']:
        return {
            cls.TYPE13,
            cls.VENEER_TYPE13,
        }

    def get_new_categories_for_shelf_type(self) -> set['FurnitureCategory']:
        from gallery.enums import FurnitureCategory

        return {
            self.TYPE01: {FurnitureCategory.DRESSING_TABLE},
            self.TYPE02: {FurnitureCategory.DRESSING_TABLE},
        }.get(self, set())

    @property
    def colors(self):
        colors = {
            self.TYPE01: Type01Color,
            self.TYPE02: Type02Color,
            self.VENEER_TYPE01: VeneerType01Color,
            self.TYPE03: Type03Color,
            self.TYPE13: Type13Color,
            self.VENEER_TYPE13: VeneerType13Color,
            self.TYPE23: Type23Color,
            self.TYPE24: Type24Color,
            self.TYPE25: Type25Color,
            self.SOFA_TYPE01: Sofa01Color,
        }.get(self)
        if colors:
            return colors
        raise ValueError(
            "{} doesn't have any `ColorEnum` subclass assigned".format(self),
        )

    @property
    def colors_for_samples(self):
        if self == self.TYPE03:
            return Type03ExteriorInteriorColor
        return self.colors

    @property
    def short_name(self):
        return {
            self.TYPE01: 'T01',
            self.TYPE02: 'T02',
            self.VENEER_TYPE01: 'T01v',
            self.TYPE03: 'T03',
            self.TYPE13: 'T13',
            self.VENEER_TYPE13: 'T13v',
            self.TYPE23: 'T23',
            self.TYPE24: 'T24',
            self.TYPE25: 'T25',
            self.SOFA_TYPE01: 'S01',
        }[self]

    @property
    def translated_name(self):
        return {
            self.TYPE01: _('Type01'),
            self.TYPE02: _('Type02'),
            self.VENEER_TYPE01: _('veneer_shelf_name'),
            self.TYPE03: _('Type03'),
            self.TYPE13: _('Type13'),
            self.VENEER_TYPE13: _('Veneer_Type13'),
            self.TYPE23: _('Type23'),
            self.TYPE24: _('Type24'),
            self.TYPE25: _('Type25'),
            self.SOFA_TYPE01: _('Sofa_Type01'),
        }[self]

    @property
    def slug(self):
        return {
            ShelfType.TYPE01: 'type01',
            ShelfType.TYPE02: 'type02',
            ShelfType.VENEER_TYPE01: 'type01v',
            ShelfType.TYPE03: 'type03',
            ShelfType.TYPE13: 'type13',
            ShelfType.VENEER_TYPE13: 'type13v',
            ShelfType.TYPE23: 'type23',
            ShelfType.TYPE24: 'type24',
            ShelfType.TYPE25: 'type25',
            ShelfType.SOFA_TYPE01: 'sofa01',
        }[self]

    @property
    def wooden_materials(self):
        """Translated material type."""
        return {
            self.TYPE01: _('Plywood'),
            self.VENEER_TYPE01: _('material_veneer'),
        }.get(self, _(''))

    @property
    def seo_wooden_materials(self):
        """Translated_material for seo"""
        materials = {self.TYPE01: _('seo_plywood'), self.VENEER_TYPE01: _('seo_veneer')}
        return materials.get(self, _(''))

    @classmethod
    def values(cls):
        return [member.value for member in cls]

    @property
    def production_code(self):
        return {
            self.TYPE01: 'T1',
            self.TYPE02: 'T2',
            self.VENEER_TYPE01: 'F1',
            self.TYPE03: 'W3',
            self.TYPE13: 'W13',
            self.VENEER_TYPE13: 'F13',
            self.TYPE23: 'T23',
            self.TYPE24: 'T24',
            self.TYPE25: 'T25',
            self.SOFA_TYPE01: 'S1',
        }[self]

    @property
    def furniture_type(self):
        return {
            self.TYPE01: Furniture.jetty,
            self.TYPE02: Furniture.jetty,
            self.VENEER_TYPE01: Furniture.jetty,
            self.TYPE03: Furniture.watty,
            self.TYPE13: Furniture.watty,
            self.VENEER_TYPE13: Furniture.watty,
            self.TYPE23: Furniture.watty,
            self.TYPE24: Furniture.watty,
            self.TYPE25: Furniture.watty,
            self.SOFA_TYPE01: Furniture.sotty,
        }[self]

    @classmethod
    def not_introduced(cls):
        return []

    @classmethod
    def jetty_choices(cls):
        return [(member.value, member.name) for member in cls.get_jetty_shelf_types()]

    @classmethod
    def watty_choices(cls):
        return [(member.value, member.name) for member in cls.get_watty_shelf_types()]

    @classmethod
    def sotty_choices(cls):
        return [(member.value, member.name) for member in cls.get_sotty_shelf_types()]

    @classmethod
    def choices_active(cls):
        return [
            (member.value, member.name.replace('_', ' '))
            for member in cls
            if member not in cls.not_introduced()
        ]

    @classmethod
    def unique_types(cls) -> set['ShelfType']:
        # keep just one new expression, because they are basically the same
        return {
            cls.TYPE01,
            cls.TYPE02,
            cls.VENEER_TYPE01,
            cls.TYPE03,
            cls.TYPE13,
            cls.VENEER_TYPE13,
            cls.TYPE23,
            cls.SOFA_TYPE01,
        }

    @property
    def product_line(self) -> Optional[str]:
        return {
            self.TYPE01: 'Original Classic',
            self.TYPE02: 'Original Modern',
            self.VENEER_TYPE01: 'Original Classic',
            self.TYPE03: 'Tone',
            self.TYPE13: 'Edge',
            self.VENEER_TYPE13: 'Edge',
            self.TYPE23: 'Tone',
            self.TYPE24: 'Tone',
            self.TYPE25: 'Tone',
            self.SOFA_TYPE01: 'Sofa',  # TODO: confirm
        }[self]

    @property
    def standard_depths(self) -> set[int]:
        jetty_standard_depths = {240, 320, 400}
        type03_standard_depths = {630, 530, 430}
        type13_standard_depths = {270, 360, 450, 600}
        matty_standard_depths = {360, 460, 560}
        sotty_standard_depths = {1000, 1125}
        return {
            self.TYPE01: jetty_standard_depths,
            self.TYPE02: jetty_standard_depths,
            self.VENEER_TYPE01: jetty_standard_depths,
            self.TYPE03: type03_standard_depths,
            self.TYPE13: type13_standard_depths,
            self.VENEER_TYPE13: type13_standard_depths,
            self.TYPE23: matty_standard_depths,
            self.TYPE24: matty_standard_depths,
            self.TYPE25: matty_standard_depths,
            self.SOFA_TYPE01: sotty_standard_depths,
        }[self]

    @classmethod
    def from_production_code(cls, production_code) -> 'ShelfType':
        try:
            return next(
                member for member in cls if member.production_code == production_code
            )
        except StopIteration as error:
            raise ValueError('Invalid production code') from error

    @property
    def is_new(self) -> bool:
        return {self.SOFA_TYPE01: True}.get(self, False)


class SampleBoxVariantEnum(ChoicesReplacedUnderscoreMixin, enum.IntEnum):
    TYPE01_CLASSIC = 1
    TYPE01_NEW = 2
    TYPE02 = 3
    TYPE01_WITHOUT_VENEER = 4
    TYPE02_WITHOUT_VENEER = 5
    BOLD_COLORS = 6
    TYPE02_WITHOUT_VENEER_AND_BLACK = 7
    TYPE02_MATTE_BLACK = 8

    TYPE01_CLASSIC_VARIANT_1 = 101
    TYPE01_CLASSIC_VARIANT_2 = 102
    BOLD_COLORS_VARIANT_1 = 103
    TYPE02_VARIANT_1 = 104

    TYPE02_MIX_SET = 105
    TYPE02_TRENDSETTER_SET = 106

    ORIGINAL_STATEMENT_SET = 111
    ORIGINAL_GREY_SET = 112
    ORIGINAL_CLASSIC_SET = 113
    ORIGINAL_WOODY_SET = 114

    TYPE03_WHITE = 1001
    TYPE03_CASHMERE = 1002
    TYPE03_BEIGE_AND_ANTIQUE_PINK = 1003
    TYPE03_GRAPHITE_GREY = 1004
    TYPE03_WHITE_AND_ANTIQUE_PINK = 1005
    TYPE03_GRAPHITE_GREY_AND_ANTIQUE_PINK = 1006
    TYPE03_TONE_WHITE_SET = 1007
    TYPE03_TONE_GRAPHITE_SET = 1008
    TYPE03_TONE_CASHMERE_SET = 1009

    # T23, T24, T25
    TYPE23_TONE_SUBTLE_SHADES_SET = 1010

    TYPE13_MIX_SET = 1101
    TYPE13_MONOCHROME_SET = 1102
    TYPE13_EDGE_EARTH_TONE_SET = 1103

    TYPE13_MIXED_WARM_SET = 1201

    CUSTOM = 99  # for totally custom sets

    @classmethod
    def all_type03_variants(cls):
        return [member for member in cls if member.name.startswith('TYPE03')]


class BIReportCategory(ChoicesMixin, enum.IntEnum):
    ORDERS = 0
    CARTS = 1

    PRODUCTION = 2
    LOGISTIC = 3
    CS = 4

    WEBKPIS = 5

    SOCIALMEDIA = 6

    PERFORMANCE = 7
    CHANNELS = 8
    TESTS = 9

    CONFIGURATOR = 10


class BIEventType(ChoicesMixin, enum.IntEnum):
    CLICKED = 0
    LIKED = 1
    UNLIKED = 2


class PhysicalProductVersion(ChoicesMixin, enum.IntEnum):
    """
    Describes the physical product manufacturing standard.

    Why is it named after dinosaurs?
    Because the name of the version is irrelevant,
    it's the number that matters.
    """

    TREX = 1  # introduced in 2015 in Ivy (row configurator shelf)
    RAPTOR = 2  # introduced in 2020 in Sideboard+
    DIPLO = 3  # introduced in 2021 in Sideboard+ T01
    PTERO = 4  # introduced in 2021 with drawer 3.0
    BAMBI = 5  # introduced in 2022 with chipboard supports in T01
    STEGO = 6  # introduced in 2022 with chipboard drawers
    BRONTO = 7  # introduced in 2022 with non-spring pins in backs and supports
    PACHY = 8  # introduced in 2022 to handle non-standard plywood thickness
    BRACHIO = 9  # introduced in 2023 with chipboard drawers in T01
    TRICE = 10  # introduced in 2024 with new handles in T01v / new T01 hinges
    KARNO = 11  # introduced in 2024 with drawer unification

    DEFAULT = TREX

    @classmethod
    def values(cls):
        return [member.value for member in cls]

    @classmethod
    def get_name(cls, value):
        return {
            cls.TREX: 'Trex',
            cls.RAPTOR: 'Raptor',
            cls.DIPLO: 'Diplo',
            cls.PTERO: 'Ptero',
            cls.BAMBI: 'Bambi',
        }.get(value, '-')

    def get_plywood_thickness(self):
        if self > self.BRONTO:
            return 19
        return 18


class LanguageEnum(models.TextChoices):
    """Main source of available languages in our app.

    This class is designed to help developers add new languages with ease.

    To implement a new language, developers can simply add a new language code
    to the LanguageEnum class and check all methods to see whether they need to be
    enriched with new language-related content.

    Keep in mind that there still might be places in app that don't rely on this
    class and needs to be updated manually.
    """

    EN = 'en', 'English'
    DE = 'de', 'Deutsch'
    FR = 'fr', 'Français'
    ES = 'es', 'Español'
    NL = 'nl', 'Nederlands'
    PL = 'pl', 'Polski'
    IT = 'it', 'Italiana'
    SV = 'sv', 'Svenska'
    DA = 'da', 'Dansk'
    NO = 'no', 'Norsk'

    @classmethod
    def _missing_(cls, value: Any) -> 'LanguageEnum':
        if isinstance(value, str):
            with contextlib.suppress(AttributeError):
                return getattr(cls, value.upper())
        return getattr(cls, settings.LANGUAGE_CODE.upper())

    @classmethod
    def map_english_labels(cls) -> dict:
        return {
            cls.EN: 'english',
            cls.DE: 'german',
            cls.FR: 'french',
            cls.ES: 'spanish',
            cls.NL: 'dutch',
            cls.PL: 'polish',
            cls.IT: 'italian',
            cls.SV: 'swedish',
            cls.DA: 'danish',
            cls.NO: 'norwegian',
        }

    @classmethod
    def get_code_by_language(cls, language: str) -> 'LanguageEnum':
        return next(
            (
                code
                for code, english_label in LanguageEnum.map_english_labels().items()
                if language.lower() == english_label
            ),
            LanguageEnum.EN,
        )

    @classmethod
    def get_locale(
        cls,
        language_code: str,
        country_code=None,
        united_states=True,
    ) -> str:
        country_code = country_code or language_code
        if language_code == cls.EN:
            country_code = 'US' if united_states else 'GB'
        elif language_code == cls.DA:
            country_code = 'DK'

        return f'{language_code}_{country_code.upper()}'

    def get_typeform_prefix(self, mode: TypeformSurveyMode):
        lang = self.typeform_language
        # those are typform generated prefixes
        if mode == TypeformSurveyMode.POST_DELIVERY:
            return {
                LanguageEnum.EN: 'BmtKwHe5',
                LanguageEnum.DE: 'Lpdxs8km',
                LanguageEnum.FR: 'rhvWR481',
            }.get(lang)

        elif mode == TypeformSurveyMode.POST_CHECKOUT:
            return {
                LanguageEnum.EN: 'PeBjA0FF',
                LanguageEnum.DE: 'vOys9Vnt',
                LanguageEnum.FR: 'VlVtzYpQ',
            }.get(lang)

    @property
    def url_prefix(self) -> str:
        return f'/{self}/' if self != self.EN else '/'

    @property
    def typeform_language(self) -> 'LanguageEnum':
        available_languages = [self.EN, self.DE, self.FR]
        return self if self in available_languages else self.EN

    @property
    def disassembly_manual_language(self) -> 'LanguageEnum':
        available_languages = [self.EN, self.DE, self.FR, self.ES, self.NL]
        return self if self in available_languages else self.EN

    def get_slug_template_callable(self, furniture_type: 'Furniture') -> Callable:
        # circular import
        from gallery.slugs import (
            get_shelf_slug_default_template,
            get_shelf_slug_template_french,
            get_shelf_slug_template_polish,
            get_sofa_slug_default_template,
            get_wardrobe_slug_default_template,
            get_wardrobe_slug_template_french,
            get_wardrobe_slug_template_polish,
        )

        # add for italian language
        templates = {
            Furniture.jetty.value: {
                self.EN: get_shelf_slug_default_template,
                self.DE: get_shelf_slug_default_template,
                self.FR: get_shelf_slug_template_french,
                self.ES: get_shelf_slug_default_template,
                self.NL: get_shelf_slug_default_template,
                self.PL: get_shelf_slug_template_polish,
                self.IT: get_shelf_slug_default_template,
                self.SV: get_shelf_slug_default_template,
                self.DA: get_shelf_slug_default_template,
                self.NO: get_shelf_slug_default_template,
            },
            Furniture.sotty.value: {
                self.EN: get_sofa_slug_default_template,
                self.DE: get_sofa_slug_default_template,
                self.FR: get_sofa_slug_default_template,
                self.ES: get_sofa_slug_default_template,
                self.NL: get_sofa_slug_default_template,
                self.PL: get_sofa_slug_default_template,
                self.IT: get_sofa_slug_default_template,
                self.SV: get_sofa_slug_default_template,
                self.DA: get_sofa_slug_default_template,
                self.NO: get_sofa_slug_default_template,
            },
            Furniture.watty.value: {
                self.EN: get_wardrobe_slug_default_template,
                self.DE: get_wardrobe_slug_default_template,
                self.FR: get_wardrobe_slug_template_french,
                self.ES: get_wardrobe_slug_default_template,
                self.NL: get_wardrobe_slug_default_template,
                self.PL: get_wardrobe_slug_template_polish,
                self.IT: get_shelf_slug_default_template,
                self.SV: get_shelf_slug_default_template,
                self.DA: get_shelf_slug_default_template,
                self.NO: get_shelf_slug_default_template,
            },
        }
        return templates[furniture_type][self]

    def get_grid_slug_template_callable(self, furniture_type: 'Furniture') -> Callable:
        # circular import
        from gallery.slugs import (
            get_shelf_grid_slug_default_template,
            get_shelf_grid_slug_template_polish,
            get_sofa_grid_slug_default_template,
            get_sofa_grid_slug_template_german,
            get_sofa_grid_slug_template_italian,
            get_sofa_grid_slug_template_polish,
            get_sofa_grid_slug_template_spanish,
            get_wardrobe_grid_slug_default_template,
            get_wardrobe_grid_slug_template_french,
        )

        templates = {
            Furniture.jetty.value: {
                self.EN: get_shelf_grid_slug_default_template,
                self.DE: get_shelf_grid_slug_default_template,
                self.FR: get_shelf_grid_slug_default_template,
                self.ES: get_shelf_grid_slug_default_template,
                self.NL: get_shelf_grid_slug_default_template,
                self.PL: get_shelf_grid_slug_template_polish,
                self.IT: get_shelf_grid_slug_default_template,
                self.SV: get_shelf_grid_slug_default_template,
                self.DA: get_shelf_grid_slug_default_template,
                self.NO: get_shelf_grid_slug_default_template,
            },
            Furniture.sotty.value: {
                self.EN: get_sofa_grid_slug_default_template,
                self.DE: get_sofa_grid_slug_template_german,
                self.FR: get_sofa_grid_slug_default_template,
                self.ES: get_sofa_grid_slug_template_spanish,
                self.NL: get_sofa_grid_slug_default_template,
                self.PL: get_sofa_grid_slug_template_polish,
                self.IT: get_sofa_grid_slug_template_italian,
                self.SV: get_sofa_grid_slug_default_template,
                self.DA: get_sofa_grid_slug_default_template,
                self.NO: get_sofa_grid_slug_default_template,
            },
            Furniture.watty.value: {
                self.EN: get_wardrobe_grid_slug_default_template,
                self.DE: get_shelf_grid_slug_default_template,
                self.FR: get_wardrobe_grid_slug_template_french,
                self.ES: get_shelf_grid_slug_default_template,
                self.NL: get_shelf_grid_slug_default_template,
                self.PL: get_shelf_grid_slug_template_polish,
                self.IT: get_shelf_grid_slug_default_template,
                self.SV: get_shelf_grid_slug_default_template,
                self.DA: get_shelf_grid_slug_default_template,
                self.NO: get_shelf_grid_slug_default_template,
            },
        }
        return templates[furniture_type][self]

    def get_seo_category_gender(
        self,
        furniture_category: 'FurnitureCategory',
    ) -> 'GenderEnum':
        # circular import
        from gallery.enums import FurnitureCategory

        return {
            self.DE: {
                FurnitureCategory.BOOKCASE: GenderEnum.NONE,
                FurnitureCategory.SIDEBOARD: GenderEnum.NONE,
                FurnitureCategory.TV_STAND: GenderEnum.MASCULINE,
                FurnitureCategory.SHOERACK: GenderEnum.NONE,
                FurnitureCategory.WARDROBE: GenderEnum.MASCULINE,
                FurnitureCategory.WALL_STORAGE: GenderEnum.NONE,
                FurnitureCategory.BEDSIDE_TABLE: GenderEnum.MASCULINE,
                FurnitureCategory.CHEST: GenderEnum.FEMININE,
                FurnitureCategory.VINYL_STORAGE: GenderEnum.FEMININE,
                FurnitureCategory.TWO_SEATER: GenderEnum.MASCULINE,
                FurnitureCategory.THREE_SEATER: GenderEnum.MASCULINE,
                FurnitureCategory.FOUR_PLUS_SEATER: GenderEnum.MASCULINE,
                FurnitureCategory.CORNER: GenderEnum.NEUTER,
                FurnitureCategory.CHAISE_LONGUE: GenderEnum.FEMININE,
                FurnitureCategory.ARMCHAIR: GenderEnum.MASCULINE,
                FurnitureCategory.FOOTREST_AND_MODULES: GenderEnum.FEMININE,
                FurnitureCategory.COVER: GenderEnum.MASCULINE,
            },
            self.FR: {
                FurnitureCategory.BOOKCASE: GenderEnum.FEMININE,
                FurnitureCategory.SIDEBOARD: GenderEnum.FEMININE,
                FurnitureCategory.TV_STAND: GenderEnum.MASCULINE,
                FurnitureCategory.SHOERACK: GenderEnum.MASCULINE,
                FurnitureCategory.WARDROBE: GenderEnum.FEMININE,
                FurnitureCategory.WALL_STORAGE: GenderEnum.FEMININE,
                FurnitureCategory.BEDSIDE_TABLE: GenderEnum.FEMININE,
                FurnitureCategory.CHEST: GenderEnum.FEMININE,
                FurnitureCategory.VINYL_STORAGE: GenderEnum.MASCULINE,
                FurnitureCategory.TWO_SEATER: GenderEnum.MASCULINE,
                FurnitureCategory.THREE_SEATER: GenderEnum.MASCULINE,
                FurnitureCategory.FOUR_PLUS_SEATER: GenderEnum.MASCULINE,
                FurnitureCategory.CORNER: GenderEnum.MASCULINE,
                FurnitureCategory.CHAISE_LONGUE: GenderEnum.FEMININE,
                FurnitureCategory.ARMCHAIR: GenderEnum.MASCULINE,
                FurnitureCategory.FOOTREST_AND_MODULES: GenderEnum.MASCULINE,
                FurnitureCategory.COVER: GenderEnum.FEMININE,
            },
            self.ES: {
                FurnitureCategory.BOOKCASE: GenderEnum.FEMININE,
                FurnitureCategory.SIDEBOARD: GenderEnum.FEMININE,
                FurnitureCategory.TV_STAND: GenderEnum.MASCULINE,
                FurnitureCategory.SHOERACK: GenderEnum.MASCULINE,
                FurnitureCategory.WARDROBE: GenderEnum.FEMININE,
                FurnitureCategory.WALL_STORAGE: GenderEnum.FEMININE,
                FurnitureCategory.BEDSIDE_TABLE: GenderEnum.FEMININE,
                FurnitureCategory.CHEST: GenderEnum.FEMININE,
                FurnitureCategory.VINYL_STORAGE: GenderEnum.MASCULINE,
                FurnitureCategory.TWO_SEATER: GenderEnum.MASCULINE,
                FurnitureCategory.THREE_SEATER: GenderEnum.MASCULINE,
                FurnitureCategory.FOUR_PLUS_SEATER: GenderEnum.MASCULINE,
                FurnitureCategory.CORNER: GenderEnum.MASCULINE,
                FurnitureCategory.CHAISE_LONGUE: GenderEnum.FEMININE,
                FurnitureCategory.ARMCHAIR: GenderEnum.MASCULINE,
                FurnitureCategory.FOOTREST_AND_MODULES: GenderEnum.MASCULINE,
                FurnitureCategory.COVER: GenderEnum.FEMININE,
            },
            self.NL: {
                FurnitureCategory.BOOKCASE: GenderEnum.FEMININE,
                FurnitureCategory.SIDEBOARD: GenderEnum.FEMININE,
                FurnitureCategory.TV_STAND: GenderEnum.MASCULINE,
                FurnitureCategory.SHOERACK: GenderEnum.MASCULINE,
                FurnitureCategory.WARDROBE: GenderEnum.FEMININE,
                FurnitureCategory.WALL_STORAGE: GenderEnum.FEMININE,
                FurnitureCategory.BEDSIDE_TABLE: GenderEnum.MASCULINE,
                FurnitureCategory.CHEST: GenderEnum.FEMININE,
                FurnitureCategory.VINYL_STORAGE: GenderEnum.MASCULINE,
                FurnitureCategory.TWO_SEATER: GenderEnum.NEUTER,
                FurnitureCategory.THREE_SEATER: GenderEnum.NEUTER,
                FurnitureCategory.FOUR_PLUS_SEATER: GenderEnum.NEUTER,
                FurnitureCategory.CORNER: GenderEnum.NEUTER,
                FurnitureCategory.CHAISE_LONGUE: GenderEnum.NEUTER,
                FurnitureCategory.ARMCHAIR: GenderEnum.NEUTER,
                FurnitureCategory.FOOTREST_AND_MODULES: GenderEnum.NEUTER,
                FurnitureCategory.COVER: GenderEnum.NEUTER,
            },
            self.PL: {
                FurnitureCategory.BOOKCASE: GenderEnum.MASCULINE,
                FurnitureCategory.SIDEBOARD: GenderEnum.FEMININE,
                FurnitureCategory.TV_STAND: GenderEnum.FEMININE,
                FurnitureCategory.SHOERACK: GenderEnum.FEMININE,
                FurnitureCategory.WARDROBE: GenderEnum.FEMININE,
                FurnitureCategory.WALL_STORAGE: GenderEnum.MASCULINE,
                FurnitureCategory.DESK: GenderEnum.NEUTER,
                FurnitureCategory.BEDSIDE_TABLE: GenderEnum.MASCULINE,
                FurnitureCategory.CHEST: GenderEnum.FEMININE,
                FurnitureCategory.VINYL_STORAGE: GenderEnum.FEMININE,
                FurnitureCategory.TWO_SEATER: GenderEnum.FEMININE,
                FurnitureCategory.THREE_SEATER: GenderEnum.FEMININE,
                FurnitureCategory.FOUR_PLUS_SEATER: GenderEnum.FEMININE,
                FurnitureCategory.CORNER: GenderEnum.MASCULINE,
                FurnitureCategory.CHAISE_LONGUE: GenderEnum.FEMININE,
                FurnitureCategory.ARMCHAIR: GenderEnum.MASCULINE,
                FurnitureCategory.FOOTREST_AND_MODULES: GenderEnum.NEUTER,
                FurnitureCategory.COVER: GenderEnum.MASCULINE,
            },
            self.DA: {
                FurnitureCategory.BOOKCASE: GenderEnum.NONE,
                FurnitureCategory.SIDEBOARD: GenderEnum.NONE,
                FurnitureCategory.TV_STAND: GenderEnum.NONE,
                FurnitureCategory.SHOERACK: GenderEnum.NONE,
                FurnitureCategory.WARDROBE: GenderEnum.NONE,
                FurnitureCategory.WALL_STORAGE: GenderEnum.NONE,
                FurnitureCategory.BEDSIDE_TABLE: GenderEnum.NONE,
                FurnitureCategory.CHEST: GenderEnum.NONE,
                FurnitureCategory.VINYL_STORAGE: GenderEnum.NONE,
                FurnitureCategory.TWO_SEATER: GenderEnum.NONE,
                FurnitureCategory.THREE_SEATER: GenderEnum.NONE,
                FurnitureCategory.FOUR_PLUS_SEATER: GenderEnum.NONE,
                FurnitureCategory.CORNER: GenderEnum.NONE,
                FurnitureCategory.CHAISE_LONGUE: GenderEnum.NONE,
                FurnitureCategory.ARMCHAIR: GenderEnum.NONE,
                FurnitureCategory.FOOTREST_AND_MODULES: GenderEnum.NONE,
                FurnitureCategory.COVER: GenderEnum.NONE,
            },
            self.NO: {
                FurnitureCategory.BOOKCASE: GenderEnum.NONE,
                FurnitureCategory.SIDEBOARD: GenderEnum.NONE,
                FurnitureCategory.TV_STAND: GenderEnum.NONE,
                FurnitureCategory.SHOERACK: GenderEnum.NONE,
                FurnitureCategory.WARDROBE: GenderEnum.NONE,
                FurnitureCategory.WALL_STORAGE: GenderEnum.NONE,
                FurnitureCategory.BEDSIDE_TABLE: GenderEnum.NONE,
                FurnitureCategory.CHEST: GenderEnum.NONE,
                FurnitureCategory.VINYL_STORAGE: GenderEnum.NONE,
                FurnitureCategory.TWO_SEATER: GenderEnum.NONE,
                FurnitureCategory.THREE_SEATER: GenderEnum.NONE,
                FurnitureCategory.FOUR_PLUS_SEATER: GenderEnum.NONE,
                FurnitureCategory.CORNER: GenderEnum.NONE,
                FurnitureCategory.CHAISE_LONGUE: GenderEnum.NONE,
                FurnitureCategory.ARMCHAIR: GenderEnum.NONE,
                FurnitureCategory.FOOTREST_AND_MODULES: GenderEnum.NONE,
                FurnitureCategory.COVER: GenderEnum.NONE,
            },
        }.get(self, {}).get(furniture_category, GenderEnum.NONE)

    def get_referral_form_url(self) -> str:
        return {
            self.EN: 'tylko.com/referral-program/',
            self.DE: 'tylko.com/de/empfehlungsprogramm/',
            self.FR: 'tylko.com/fr/programme-de-parrainage/',
            self.ES: 'tylko.com/es/programa-de-referidos/',
            self.NL: 'tylko.com/nl/verwijs-program/',
        }[self]

    def get_jetty_instruction_language(self) -> 'LanguageEnum':
        available_languages = [self.EN, self.DE, self.FR]
        return self if self in available_languages else self.EN

    def get_trusted_shop_order_link(self) -> str:
        base_url = 'https://www.trustedshops.com/buyerrating/'
        page = 'rate_X6E8FACC0D65F5AD2D411A4931C67D3CC.html'
        if self == self.DE:
            page = 'rate_X72D2EEC6DEB7538979C7A2E75C11E62C.html'

        return f'{base_url}{page}'

    def get_disassembly_manual_link(self, furniture_height: int) -> str:
        limit_height = 135
        height_prefix = 'tall' if furniture_height > limit_height else 'low'
        lang_prefix = self.disassembly_manual_language.upper()

        static_catalogue_url = urljoin(settings.SITE_URL, settings.STATIC_URL)
        return urljoin(
            static_catalogue_url,
            (
                f'disassembly_manuals/{height_prefix}/'
                f'{lang_prefix}_Disassembly_{height_prefix}_furniture.pdf'
            ),
        )

    def get_mailing_language(self) -> 'LanguageEnum':
        # [ECO-2790] Temporary stop sending emails in Polish language
        # due to broken translations.
        return self if self != self.PL else self.EN


class Furniture(ChoicesMixin, enum.Enum):
    jetty = 'jetty'
    sample_box = 'sample_box'
    watty = 'watty'
    sotty = 'sotty'

    @classmethod
    def furniture_types_choices(cls):
        return [
            (member.value, member.name) for member in cls if member != cls.sample_box
        ]

    @property
    def model(self) -> Type['SellableFurnitureAbstract']:
        model_mapping = {
            self.jetty: 'Jetty',
            self.sample_box: 'SampleBox',
            self.sotty: 'Sotty',
            self.watty: 'Watty',
        }
        model_name = model_mapping[self]
        return apps.get_model('gallery', model_name)


class ActionsEnum(enum.Enum):
    registered = 'has registered'
    retargeted_by_email = 'has received retargeting email'
    invited = 'invited people'
    entered_cart = 'entered cart'
    started_checkout = 'started checkout'
    paid = 'going to pay'
    confirmation = 'paid'
    visited_gettheapp = 'visited get the app page'
    login = 'logged in'
    opened_shelf = 'opened item'
    opened_review = 'opened review'
    opened_checkout = 'opened checkout'
    purchased = 'made a purchase'
    new_session = 'opened a new session'
    view_pdp_reviews = 'saw pdp reviews'
    payment_failure = 'failed to pay'


class Platform(enum.Enum):
    android = 'android'
    desktop = 'desktop'
    ios = 'ios'


class Axis(enum.StrEnum):
    X = 'x'
    Y = 'y'
    Z = 'z'


class GridLabelType(enum.StrEnum):
    FEATURE = 'feature'
    PROMOTION = 'discount'


class GridLabelValue(models.TextChoices):
    NEW = 'new'
    SOON = 'soon'
    TOP_SELLER = 'top_seller'
    SPECIAL_EDITION = 'special_edition'
    SPECIAL = 'special'


class GenderEnum(models.TextChoices):
    MASCULINE = 'masculine'
    FEMININE = 'feminine'
    NEUTER = 'neuter'
    NONE = ''


class AdminGroup(enum.StrEnum):
    ACCOUNTING = 'Accounting'
