import json

from django.conf import settings

from google import genai
from google.genai import types


class GeminiClient:
    def __init__(self, model_name: str = 'gemini-2.0-flash'):
        self.client = genai.Client(api_key=settings.GEMINI_API_KEY)
        self.model_name = model_name

    def analyze_dixa_conversation(self, conversation: str, prompt: str):
        response = self.client.models.generate_content(
            model=self.model_name,
            contents=conversation,
            config=types.GenerateContentConfig(system_instruction=prompt),
        )
        return self.parse_json_response_to_dict(response.text)

    def generate_content(
        self, contents: str, prompt: str, **config_kwargs
    ) -> types.GenerateContentResponse:
        return self.client.models.generate_content(
            model=self.model_name,
            contents=contents,
            config=types.GenerateContentConfig(
                system_instruction=prompt,
                **config_kwargs,
            ),
        )

    @classmethod
    def parse_json_response_to_dict(cls, text):
        return json.loads(text.replace('```json\n', '').replace('\n```', ''))
