import csv
import inspect
import logging

from datetime import datetime
from tempfile import NamedTemporaryFile

from django.core.mail.message import EmailMessage
from django.http import HttpResponse
from django.utils.encoding import force_str
from django.utils.html import strip_tags

logger = logging.getLogger('cstm')


def _send_email(to, file_, subject, body):
    message = EmailMessage(
        subject=subject, body=body, from_email='<EMAIL>', to=to
    )
    message.attach_file(file_)
    message.send(fail_silently=False)


def dump_list_as_csv(
    list_of_lists,
    output,
    headers=None,
    mail=None,
    mail_subject='Export',
    mail_body='',
    delimiter=',',
):
    if mail:
        output = NamedTemporaryFile(suffix=output, delete=False, mode='w+')
    writer = csv.writer(output, delimiter=delimiter)
    if headers:
        writer.writerow(headers)
    for lst in list_of_lists:
        row = []
        for e in lst:
            if callable(e):
                e = e()
            row.append(e)
        writer.writerow(row)
    if mail:
        output.close()
        _send_email(mail, output.name, mail_subject, mail_body)


def dump_list_as_txt(
    results_list,
    output,
    mail,
    mail_subject='Export',
    mail_body='',
):
    output_file = NamedTemporaryFile(suffix=output, delete=False, mode='w+')
    for record in results_list:
        output_file.write(f'{record}\n')
    output_file.close()
    _send_email(mail, output_file.name, mail_subject, mail_body)


def export_modeladmin_as_csv(modeladmin, request, queryset):
    header_row = [x for x in modeladmin.list_display]  # noqa: C416
    rows = []
    for item in queryset:
        rows.append(  # noqa: PERF401
            [
                strip_tags(force_str(getattr(item, x)()))
                if hasattr(item, x) and inspect.ismethod(getattr(item, x))
                else strip_tags(force_str(getattr(item, x)))
                if hasattr(item, x)
                else x
                for x in header_row
                if isinstance(x, str)
            ]
        )
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = 'attachment; filename=%s%s_%s.csv' % (
        'generic_',
        modeladmin.__class__.__name__,
        datetime.today().strftime('%y%m%d_%H%M'),
    )
    writer = csv.writer(response, delimiter=';')
    writer.writerow(header_row)
    for row in rows:
        writer.writerow(row)
    return response


export_modeladmin_as_csv.short_description = 'Export selected view to csv'
