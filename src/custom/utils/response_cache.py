import hashlib

from functools import wraps
from typing import Final

from django.core.cache import cache

from rest_framework.response import Response

ONE_HOUR: Final[int] = 60 * 60 * 1


def cache_response(ttl: int = ONE_HOUR):
    def decorator(func):
        @wraps(func)
        def wrapper(self, request, *args, **kwargs):
            if request.method != 'GET':
                return func(self, request, *args, **kwargs)

            path = request.get_full_path()
            hashed_path = hashlib.md5(path.encode()).hexdigest()
            key = (
                f'response_cache:{self.__class__.__name__}:{self.action}:{hashed_path}'
            )
            cached_response = cache.get(key)

            if cached_response:
                return Response(
                    cached_response['data'], status=cached_response['status']
                )

            response = func(self, request, *args, **kwargs)
            if isinstance(response, Response):
                cache.set(
                    key,
                    {'data': response.data, 'status': response.status_code},
                    timeout=ttl,
                )
            return response

        return wrapper

    return decorator
