from django.db import models
from django.utils.functional import cached_property

from django_filters import (
    filters,
    filterset,
)


class BasePythonFilter(filters.Filter):
    """Filter already pre-filtered and evaluated queryset in Python."""

    def __init__(self, *args, filter_func, **kwargs):
        """Initialize ``BasePythonFilter`` instance."""
        super().__init__(*args, **kwargs)
        self.filter_func = filter_func

    def get_method(self, qs):
        """Get filter method based on ``exclude`` value."""
        if self.exclude:
            return lambda instance, value: not self.filter_func(instance, value)
        return self.filter_func

    def filter(self, qs, value):
        """Filter ``qs`` with proper method and yield matched instances."""
        filter_method = self.get_method(qs)
        for instance in qs:
            if filter_method(instance, value):
                yield instance


class PythonFilterSet(filterset.FilterSet):
    """``FilterSet`` adding possibility to filter query sets in python."""

    @cached_property
    def python_filters(self):
        """Names of all ``BasePythonFilter`` subclasses instances."""
        return {
            name
            for name, python_filter in self.filters.items()
            if isinstance(python_filter, BasePythonFilter)
        }

    def filter_queryset(self, queryset):
        """Filter ``queryset`` on queryset and python levels."""
        python_filters_data = {}
        for name, value in self.form.cleaned_data.items():
            if name in self.python_filters:
                python_filters_data[name] = value
                continue
            queryset = self.filters[name].filter(queryset, value)
            assert isinstance(queryset, models.QuerySet), (
                "Expected '%s.%s' to return a QuerySet, but got a %s instead."
                % (
                    type(self).__name__,
                    name,
                    type(queryset).__name__,
                )
            )
        for name, value in python_filters_data.items():
            queryset = self.filters[name].filter(queryset, value)
        return queryset
