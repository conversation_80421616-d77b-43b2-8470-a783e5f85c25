import datetime

from django.contrib.contenttypes.models import ContentType
from django.core.validators import EMPTY_VALUES
from django.db.models import (
    Date<PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>ield,
    Exists,
    ExpressionWrapper,
    F,
    OuterRef,
    Q,
    TextField,
)
from django.db.models.functions import Cast

from django_filters import rest_framework as django_filters

from complaints.models import Complaint
from custom.enums import (
    Furniture,
    LanguageEnum,
    ShelfType,
)
from custom_audiences.filters.bases import PythonFilterSet
from gallery.enums import FurnitureStatusEnum
from gallery.models import Jetty
from orders.choices import OrderSource
from orders.enums import OrderStatus
from orders.models import (
    Order,
    OrderItem,
)
from reviews.models import Review


class CharInFilter(
    django_filters.BaseInFilter,
    django_filters.CharFilter,
):
    """Filter by multiple string values."""


class ChoiceInFilter(
    django_filters.BaseInFilter,
    django_filters.ChoiceFilter,
):
    """Filter by multiple choice values."""


class NumberRangeFilter(
    django_filters.BaseRangeFilter,
    django_filters.NumberFilter,
):
    """Filter by numbers range."""


class CustomAudienceFilterSet(PythonFilterSet):
    """``FilterSet`` for custom audience."""

    has_vouchers = django_filters.BooleanFilter(
        lookup_expr='isnull',
        exclude=True,
        field_name='vouchers',
    )
    use_promo_codes = CharInFilter(field_name='vouchers__code')
    has_delays = django_filters.BooleanFilter(
        method='filter_has_delays',
    )
    use_languages = ChoiceInFilter(
        field_name='owner__profile__language',
        choices=LanguageEnum.choices,
    )
    has_complaints = django_filters.BooleanFilter(
        method='filter_has_complaints',
    )
    is_returning_user = django_filters.BooleanFilter(
        method='filter_is_returning_user',
    )
    owner_has_reviews = django_filters.BooleanFilter(
        method='filter_owner_has_reviews',
    )
    order_source = ChoiceInFilter(
        choices=OrderSource.choices,
        method='filter_order_source',
    )
    value = NumberRangeFilter(
        field_name='total_price',
        lookup_expr='range',
    )
    placed_at = django_filters.DateFromToRangeFilter(field_name='placed_at')
    paid_at = django_filters.DateFromToRangeFilter(field_name='paid_at')
    updated_at = django_filters.DateFromToRangeFilter(field_name='updated_at')
    has_contact_phone_number = django_filters.BooleanFilter(
        method='filter_has_contact_phone_number',
    )
    has_email = django_filters.BooleanFilter(
        method='filter_has_email',
    )
    country = CharInFilter(field_name='country')
    status = ChoiceInFilter(
        choices=OrderStatus.choices,
        method='filter_status',
    )
    furniture_type = ChoiceInFilter(
        choices=Furniture.choices(),
        method='filter_furniture_type',
    )
    shelf_type = ChoiceInFilter(
        choices=ShelfType.choices(),
        method='filter_shelf_type',
    )
    has_free_return = django_filters.BooleanFilter(
        method='filter_has_free_return',
    )
    contains_only_samples = django_filters.BooleanFilter(
        method='filter_contains_only_samples',
    )
    has_save_for_later = django_filters.BooleanFilter(
        method='filter_has_save_for_later'
    )

    class Meta:
        model = Order
        fields = (
            'use_promo_codes',
            'has_vouchers',
            'has_delays',
            'use_languages',
            'has_complaints',
            'is_returning_user',
            'owner_has_reviews',
            'furniture_type',
            'shelf_type',
            'order_source',
            'value',
            'placed_at',
            'paid_at',
            'updated_at',
            'has_contact_phone_number',
            'has_email',
            'country',
            'status',
            'has_free_return',
            'contains_only_samples',
            'has_save_for_later',
        )

    def filter_has_delays(self, queryset, name, value):
        """Filter ``queryset`` delayed orders."""
        queryset = (
            queryset.annotate(
                extracted_delivered_date_text=Cast(
                    'serialized_logistic_info__0__delivered_date',
                    output_field=TextField(),
                ),
            )
            .annotate(
                extracted_delivered_date=Cast(
                    'extracted_delivered_date_text',
                    output_field=DateTimeField(),
                ),
            )
            .annotate(
                delivery_delay=ExpressionWrapper(
                    (F('extracted_delivered_date') - F('estimated_delivery_time')),
                    output_field=DurationField(),
                ),
            )
        )
        filter_method = queryset.filter
        if not value:
            filter_method = queryset.exclude
        return filter_method(delivery_delay__gte=datetime.timedelta(days=14))

    def filter_has_complaints(self, queryset, name, value):
        """Filter ``queryset`` by orders with/without complaints."""
        complaints_queryset = Complaint.objects.filter(
            product_id=OuterRef('product__id'),
            reported_date__gt=OuterRef('paid_at'),
        ).values('id')
        queryset = queryset.annotate(
            has_any_complaint=Exists(complaints_queryset),
        )
        return queryset.filter(has_any_complaint=value)

    def filter_is_returning_user(self, queryset, name, value):
        """Filter ``queryset`` by owners with at least one other paid order."""
        other_orders_queryset = Order.objects.exclude(id=OuterRef('id'))
        other_orders_queryset = other_orders_queryset.filter(
            email=OuterRef('email'),
            status__in=[
                OrderStatus.DELIVERED,
                OrderStatus.IN_PRODUCTION,
                OrderStatus.TO_BE_SHIPPED,
                OrderStatus.SHIPPED,
            ],
        ).values('id')
        queryset = queryset.annotate(
            has_other_orders=Exists(other_orders_queryset),
        )
        return queryset.filter(has_other_orders=value)

    def filter_owner_has_reviews(self, queryset, name, value):
        """Filter ``queryset`` by owners with at least one added review."""
        reviews_queryset = Review.objects.filter(
            Q(email=OuterRef('email')) | Q(email=OuterRef('owner__email')),
            created_at__gt=OuterRef('paid_at'),
        ).values('id')
        queryset = queryset.annotate(has_reviews=Exists(reviews_queryset))
        return queryset.filter(has_reviews=value)

    def filter_furniture_type(self, queryset, name, value):
        """Filter ``queryset`` by furniture type.

        Orders with at least one furniture type
        from given 'value' list.

        """
        order_items_queryset = OrderItem.objects.filter(
            Q(order__email=OuterRef('email'))
            | Q(order__owner__email=OuterRef('owner__email')),
            content_type__model__in=value,
        ).values('id')
        queryset = queryset.annotate(
            has_chosen_furniture_type=Exists(order_items_queryset),
        )
        return queryset.filter(has_chosen_furniture_type=True)

    def filter_shelf_type(self, queryset, name, value):
        """Filter ``queryset`` by shelf type.

        Orders with at least one shelf type
        from given 'value' list.

        """
        value = list(map(int, value))

        order_items_queryset = OrderItem.objects.filter(
            order_id=OuterRef('id'),
            content_type__model='jetty',
            jetty__shelf_type__in=value,
        )
        queryset = queryset.annotate(
            has_chosen_shelf_type=Exists(order_items_queryset),
        )
        return queryset.filter(
            has_chosen_shelf_type=True,
        )

    def filter_order_source(self, queryset, name, value):
        """Filter ``queryset`` by order source."""
        value = list(map(int, value))
        return queryset.filter(
            order_source__in=value,
        )

    def filter_status(self, queryset, name, value):
        """Filter ``queryset`` by order status."""
        value = list(map(int, value))
        return queryset.filter(
            status__in=value,
        )

    def filter_has_contact_phone_number(self, queryset, name, value):
        """Filter ``queryset`` by availability of phone number."""
        empty_phone_queryset = queryset.filter(
            Q(phone__in=EMPTY_VALUES) | Q(phone__isnull=True),
        ).values('id')
        filter_method = queryset.exclude
        if not value:
            filter_method = queryset.filter
        return filter_method(id__in=empty_phone_queryset)

    def filter_has_email(self, queryset, name, value):
        """Filter ``queryset`` by availability of email address."""
        empty_email_queryset = queryset.filter(
            Q(owner__email__in=EMPTY_VALUES) & Q(email__in=EMPTY_VALUES),
        ).values('id')
        if not value:
            return queryset.filter(id__in=empty_email_queryset)
        else:
            queryset = queryset.exclude(Q(email=None) | Q(owner__email=None))
            return queryset.exclude(id__in=empty_email_queryset)

    def filter_has_free_return(self, queryset, name, value):
        """Filter ``queryset`` by possible free return action - orders delivered
        within 100 days.
        """
        now = datetime.date.today()
        delivered_date__gte = now - datetime.timedelta(days=100)

        queryset = queryset.filter(
            status=OrderStatus.DELIVERED,
            serialized_logistic_info__0__delivered_date__gte=str(delivered_date__gte),
            items__free_return__isnull=not value,
        ).exclude(
            Q(
                serialized_logistic_info__0__mailing_disabled=True,
                serialized_logistic_info__0__reenable_at__isnull=True,
            )
            | Q(
                serialized_logistic_info__0__mailing_disabled=False,
                serialized_logistic_info__0__mailing_reenable_at__gt=str(now),
            )
        )
        return queryset

    def filter_has_save_for_later(self, queryset, name, value):
        save_for_later_shelf = Jetty.objects.filter(
            furniture_status=FurnitureStatusEnum.SAVED,
            owner__email=OuterRef('email'),
        )
        queryset = queryset.annotate(has_save_for_later=Exists(save_for_later_shelf))
        return queryset.filter(has_save_for_later=value)

    def filter_contains_only_samples(self, queryset, name, value):
        """Filter ``queryset`` by containing only sample-box objects."""
        # TODO: change this dummy filter into `Count('items', filter=Q(...))`
        #  on Django 3
        sample_box = ContentType.objects.get(model='samplebox')
        watty = ContentType.objects.get(model='watty')
        jetty = ContentType.objects.get(model='jetty')
        only_samplebox_orders = (
            queryset.filter(items__content_type=sample_box)
            .exclude(items__content_type__in=[jetty, watty])
            .values('id')
        )
        filter_method = queryset.filter
        if not value:
            filter_method = queryset.exclude
        return filter_method(id__in=only_samplebox_orders)
