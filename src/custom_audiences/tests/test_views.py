from io import StringIO

from django.core.files.base import ContentFile
from django.shortcuts import reverse
from rest_framework import status

from custom_audiences.tests.factories import CustomAudienceBatchFactory


class TestCustomAudienceBatch:
    def test_endpoint_permission(self, api_client, wisely_api_client):
        string_io_mock = StringIO('mock')
        batch = CustomAudienceBatchFactory.create(
            csv_file=ContentFile(string_io_mock.read(), name=str('mock.csv')),
        )
        response = wisely_api_client.get(
            reverse('customaudiencebatch-detail', args=[batch.id])
        )
        assert response.status_code == status.HTTP_200_OK

    def test_returns_403_for_user_without_permission(self, db, api_client, user):
        api_client.force_authenticate(user)
        response = api_client.get(reverse('customaudiencebatch-detail', args=[1945]))
        assert response.status_code == status.HTTP_403_FORBIDDEN

    def test_download_endpoint_works_for_ready_batch(
        self,
        wisely_api_client,
        custom_audience_batch_factory,
    ):
        string_io_mock = StringIO('mock')
        batch = custom_audience_batch_factory.create(
            csv_file=ContentFile(string_io_mock.read(), name=str('mock.csv'))
        )
        response = wisely_api_client.get(
            reverse(
                'customaudiencebatch-result',
                kwargs={'pk': batch.id},
            ),
        )
        assert response.status_code == status.HTTP_200_OK
        assert response['content-type'] == 'application/csv'

    def test_download_endpoint_returns_400_for_not_ready_batch(
        self,
        wisely_api_client,
        custom_audience_batch_factory,
    ):
        batch = custom_audience_batch_factory.create()
        url = reverse('customaudiencebatch-result', kwargs={'pk': batch.id})
        response = wisely_api_client.get(url)
        assert response.status_code == status.HTTP_400_BAD_REQUEST
