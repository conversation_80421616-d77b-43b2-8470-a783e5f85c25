from django.http import HttpResponse
from django.views.generic.base import TemplateView
from rest_framework import (
    mixins,
    status,
)
from rest_framework.authentication import (
    SessionAuthentication,
    TokenAuthentication,
)
from rest_framework.decorators import action
from rest_framework.exceptions import ValidationError
from rest_framework.pagination import PageNumberPagination
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.viewsets import GenericViewSet

from custom_audiences.enums import CustomAudienceBatchStatus
from custom_audiences.models import CustomAudienceBatch
from custom_audiences.permissions import HasWiselyPermission
from custom_audiences.serializers import CustomAudienceBatchSerializer
from custom_audiences.tasks import generate_custom_audience_batch_for_filters


class CustomAudiencePagination(PageNumberPagination):
    page_size = 20
    page_size_query_param = 'page_size'
    max_page_size = 500


class CustomAudienceBatchViewSet(
    mixins.CreateModelMixin,
    mixins.ListModelMixin,
    mixins.RetrieveModelMixin,
    GenericViewSet,
):
    serializer_class = CustomAudienceBatchSerializer
    queryset = CustomAudienceBatch.objects.all()
    authentication_classes = (TokenAuthentication, SessionAuthentication)
    permission_classes = (IsAuthenticated, HasWiselyPermission)
    pagination_class = CustomAudiencePagination

    @action(methods=['get'], detail=True)
    def result(self, request, *args, **kwargs):
        instance = self.get_object()
        if instance.status != CustomAudienceBatchStatus.READY.value:
            raise ValidationError(
                'Cannot download file for batch with status {}'.format(instance.status)
            )
        response = HttpResponse(
            instance.csv_file,
            content_type='application/csv',
        )
        response['Content-Disposition'] = 'attachment; filename="{0}"'.format(
            instance.csv_file.name,
        )
        return response

    def create(self, request, *args, **kwargs):
        filters = {key: request.GET.get(key) for key in request.GET}
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        filters = {key: request.GET.get(key) for key in request.GET}
        generate_custom_audience_batch_for_filters.delay(
            filters, serializer.instance.pk
        )
        return Response(serializer.data, status=status.HTTP_201_CREATED)


class CustomAudienceToolView(TemplateView):
    template_name = 'admin_custom/custom_audiences.html'
