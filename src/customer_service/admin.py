import logging

from decimal import Decimal

from django.contrib import admin
from django.db.models import (
    JSONField,
    Q,
    Sum,
    Value,
)
from django.db.models.functions import Coalesce
from django.urls import reverse
from django.utils.html import format_html
from django.utils.safestring import mark_safe

from django_json_widget.widgets import JSONEditorWidget
from django_object_actions import DjangoObjectActions
from past.utils import old_div
from rangefilter.filters import DateTimeRangeFilter

from custom.admin_mixins import ViewOnlyAdminMixin
from customer_service.admin_actions import CSCorrectionRequestActionsMixin
from customer_service.enums import (
    CSCorrectionRequestType,
    CSUnsuccessfulPaymentsStatus,
    KlarnaPriceChangeType,
    KlarnaSource,
)
from customer_service.filters import (
    ReturningCustomerCountriesFilter,
    ReturningCustomerHasAssemblyFilter,
    ReturningCustomerOrderCountFilter,
    ReturningCustomerOrderSourceFilter,
)
from customer_service.models import (
    CSActivityLog,
    CSCorrectionRequest,
    CSOrder,
    CSUnsuccessfulPayments,
    CSUserProfile,
    KlarnaAdjustment,
    OrderItemAbortRequest,
    ReturningCustomer,
)
from invoice.choices import InvoiceStatus
from logger.admin import LoggerMixin
from orders.enums import OrderStatus
from orders.models import Order

logger = logging.getLogger('cstm')


class CSCorrectionRequestAdmin(
    ViewOnlyAdminMixin,
    CSCorrectionRequestActionsMixin,
    DjangoObjectActions,
    LoggerMixin,
):
    def has_delete_permission(self, request, obj=None):
        return True

    log_actions = True
    change_form_template = 'admin/cs_correction_request_change_form.html'
    list_display = (
        'id',
        'type_cs',
        'status',
        'correction_amount_gross',
        'correction_vat',
        'get_invoice_description',
        'get_correction_invoice',
        'issuer',
        'reviewer',
        'created_at',
        'updated_at',
    )
    list_filter = ('status', 'type_cs', 'tag')

    fieldsets = (
        (
            None,
            {
                'fields': (
                    'correction_amount_gross',
                    'correction_context',
                    'correction_vat',
                    'invoice',
                    'display_correction_invoice',
                    'issuer',
                    'reviewer',
                    'status',
                    'type_cs',
                    'added_invoice_items',
                    'deleted_invoice_items',
                    'cs_correction_request',
                    'tag',
                    'discount_tag',
                    'complaint',
                ),
            },
        ),
    )

    actions = [
        'decline_correction_request',
        'accept_correction_request',
    ]

    change_actions = ('edit_tag',)

    @admin.display(description='Correction Invoice')
    def display_correction_invoice(self, obj):
        if (
            obj.correction_invoice
            and obj.correction_invoice.status in InvoiceStatus.preview_statuses()
        ):
            url = reverse(
                'admin:invoice_invoicepreview_change', args=[obj.correction_invoice_id]
            )
        else:
            url = reverse(
                'admin:invoice_invoice_change',
                args=[obj.correction_invoice_id],
            )
        return format_html(f"<a href='{url}'>{obj.correction_invoice}</a>")

    def render_change_form(self, request, context, *args, **kwargs):
        self.exclude = ('cs_correction_request',)
        context['diff'] = context['original'].address_diff()
        return super().render_change_form(request, context, *args, **kwargs)

    @mark_safe  # noqa: S308
    def get_invoice_description(self, obj):
        if obj.type_cs == CSCorrectionRequestType.TYPE_ADDRESS.value:
            info = obj.address_diff()

            return (
                f"<a href='/admin/invoice/invoice/{obj.invoice_id}/'>"
                f'{obj.invoice.pretty_id}, sell at: {obj.invoice.sell_at}, '
                f'issued at: {obj.invoice.issued_at} </a><br/> {info}'
            )

        invoice_total_gross = Decimal(obj.invoice.get_total_gross())
        after_correction = invoice_total_gross - obj.correction_amount_gross
        correction_for = (
            (old_div(obj.correction_amount_gross, invoice_total_gross)) * Decimal(100)
            if invoice_total_gross
            else 0
        )
        return (
            f"<a href='/admin/invoice/invoice/{obj.invoice_id}/'>"
            f'{obj.invoice.pretty_id}, '
            f'sell at: {obj.invoice.sell_at}, '
            f'issued at: {obj.invoice.issued_at} </a><br/> '
            f'Gross amount: {invoice_total_gross:.2f} €, '
            f'after correction {after_correction:.2f} €, '
            f'correction for {correction_for:.2f}%'
        )

    @mark_safe  # noqa: S308
    def get_correction_invoice(self, obj):
        if not obj.correction_invoice:
            return ''
        if not obj.correction_invoice.pdf:
            return 'Not yet generated, generate!'
        return (
            f'<a href="{obj.correction_invoice.pdf.url}">'
            f'{obj.correction_invoice.pretty_id}'
            f'</a>'
        )


class CSActivityLogAdmin(admin.ModelAdmin):
    list_display = ('timestamp', 'user', 'activity_type', 'activity_context')
    list_display_links = None
    list_filter = ('activity_type',)
    raw_id_fields = ('user',)


class CSUnsuccessfulPaymentsAdmin(admin.ModelAdmin):
    raw_id_fields = ('order', 'user')
    list_display = (
        'pk',
        'status',
        'order',
        'language',
        'source',
        'updated_at',
        'note',
        'total_price_net',
        'total_price',
        'email',
        'phone',
        'user',
    )
    list_filter = (
        ('order__updated_at', DateTimeRangeFilter),
        'status',
    )
    ordering = ('-order__updated_at',)

    def save_model(self, request, obj, form, change):
        obj.user = request.user
        if obj.status == CSUnsuccessfulPaymentsStatus.REJECTED:
            order = obj.order
            order.change_status(OrderStatus.CANCELLED)
            order.save()
        super(CSUnsuccessfulPaymentsAdmin, self).save_model(request, obj, form, change)


class ReturningCustomerAdmin(admin.ModelAdmin):
    list_display = (
        'email',
        'email_owner',
        'orders_count',
        'orders_info',
    )
    search_fields = (
        'email',
        'email_owner',
    )
    list_filter = (
        ReturningCustomerOrderCountFilter,
        ReturningCustomerHasAssemblyFilter,
        ReturningCustomerOrderSourceFilter,
        ReturningCustomerCountriesFilter,
    )

    def get_search_results(self, request, queryset, search_term):
        """
        Email of Order can be different than Order Owner Email,
        so if we not find results by order.email
        then check in ReturningCustomer's orders data based on order.owner.email
        that are stored in ReturningCustomer.orders_data
        """
        qs, may_have_duplicates = super().get_search_results(
            request, queryset, search_term
        )
        if qs.exists():
            return qs, may_have_duplicates
        elif not qs.exists() and search_term:
            search_term = search_term.strip()
            return (
                queryset.filter(
                    orders_data__customer_order_details__customer_emails__contains=search_term
                ),
                may_have_duplicates,
            )
        return qs, may_have_duplicates

    def orders_info(self, obj):
        if not obj:
            return ''
        returning_client_orders = self.get_orders_for_returning_client(obj)
        order_data = [self.get_order_data(order) for order in returning_client_orders]
        html = self.get_html_for_order_data(order_data)
        return format_html(html)

    @staticmethod
    def orders_count(obj):
        return obj.orders_data['customer_order_details']['orders_count']

    @staticmethod
    def get_orders_for_returning_client(obj):
        return (
            Order.objects.filter(
                id__in=obj.orders_data['customer_order_details']['ids']
            )
            .select_related('region', 'region__currency', 'owner')
            .prefetch_related('items')
            .annotate(
                items_count=Coalesce(
                    Sum('items__quantity', filter=Q(items__deleted=None)), Value(0)
                )
            )
        )

    def get_html_for_order_data(self, orders_data):
        html = self.get_html_table_header()
        for order_data in orders_data:
            html += self.get_html_row_order_data(order_data)
        html += '</table>'
        return html

    @staticmethod
    def get_html_table_header():
        return '''
        <table>
            <tr>
                <th>Order ID</th>
                <th>Owner CS URL</th>
                <th>Status</th>
                <th>Country</th>
                <th>Total Price</th>
                <th>Curency</th>
                <th>Order source</th>
                <th>Chosen Payment Method</th>
                <th>Promo text</th>
                <th>Total items</th>
                <th>Promo amount</th>
                <th>Has assembly</th>
            </tr>
        '''

    @staticmethod
    def get_html_row_order_data(order_data):
        return f'''
        <tr>
            <td>{order_data['order_id']}</td>
            <td>{order_data['owner_cs_url']}</td>
            <td>{order_data['status']}</td>
            <td>{order_data['country']}</td>
            <td>{order_data['total_price']}</td>
            <td>{order_data['currency']}</td>
            <td>{order_data['order_source']}</td>
            <td>{order_data['chosen_payment_method']}</td>
            <td>{order_data['promo_text']}</td>
            <td>{order_data['total_items']}</td>
            <td>{order_data['promo_amount']}</td>
            <td>{order_data['has_assembly']}</td>
        <tr>
        '''

    def get_order_data(self, order):
        return {
            'order_id': self.get_order_id_url(order),
            'owner_cs_url': self.get_owner_cs_url(order),
            'status': order.get_status_display(),
            'country': order.country,
            'total_price': order.total_price,
            'currency': order.region.currency.symbol,
            'order_source': order.get_order_source_display(),
            'chosen_payment_method': order.chosen_payment_method,
            'promo_text': order.promo_text,
            'total_items': order.items_count,
            'promo_amount': order.promo_amount,
            'has_assembly': order.assembly,
        }

    @staticmethod
    def get_order_id_url(order):
        order_id_url = reverse('admin:orders_order_change', args=[order.id])
        return format_html(
            f"<a href='{order_id_url}' rel='noopener noreferrer' "
            f"target='_blank'>OrderID {order.id}</a>"
        )

    @staticmethod
    def get_owner_cs_url(order):
        owner_cs_url = reverse('cs_user_overview', args=[order.owner_id])
        return format_html(
            f"<a href='{owner_cs_url}' rel='noopener noreferrer' "
            f"target='_blank'>UserOverview {order.owner_id}</a>"
        )


class CSOrderAdmin(admin.ModelAdmin):
    show_full_result_count = False

    list_display = (
        'id',
        'email',
        'first_name',
        'last_name',
        'phone',
        'street_address_1',
        'street_address_2',
        'company_name',
        'city',
    )

    search_fields = ('email',)


class CSUserProfileAdmin(admin.ModelAdmin):
    show_full_result_count = False

    list_display = (
        'id',
        'user_email',
        'first_name',
        'last_name',
        'user_username',
        'phone',
        'street_address_1',
        'street_address_2',
        'city',
    )

    search_fields = ('user_email',)


class KlarnaAdjustmentAdmin(LoggerMixin):
    log_actions = True
    change_list_template = 'admin/klarna_adjustment_change_list_results.html'
    list_display = (
        'id',
        'get_order_id',
        'get_order_status',
        'change_type',
        'current_price_with_currency',
        'amount_with_currency',
        'new_price',
        'conversation_link',
        'reason',
        'display_source',
        'finished_at',
    )
    list_filter = (
        'change_type',
        'source',
    )
    list_select_related = ['invoice', 'invoice__order']
    search_fields = ('id', 'invoice__order__id')

    raw_id_fields = ('invoice',)

    actions = [
        'finish_klarna_adjustment',
    ]

    @admin.display(description='Order id')
    def get_order_id(self, obj):
        return obj.invoice.order_id

    @admin.display(description='Source')
    def display_source(self, obj):
        return obj.get_source_display()

    @admin.display(description='Order status')
    def get_order_status(self, obj):
        return obj.invoice.order.get_status_display()

    @staticmethod
    def current_price_with_currency(obj):
        return f'{obj.current_price} {obj.currency_symbol}'

    @staticmethod
    def amount_with_currency(obj):
        return f'{obj.amount} {obj.currency_symbol}'

    @staticmethod
    def new_price(obj):
        if obj.change_type == KlarnaPriceChangeType.DISCOUNT:
            new_price = obj.current_price - obj.amount
        else:
            new_price = obj.current_price + obj.amount
        return f'{new_price} {obj.currency_symbol}'

    @admin.action(description='Finish Klarna Adjustment')
    def finish_klarna_adjustment(self, request, queryset):
        klarna_adjustments_add_extra_discount = queryset.filter(
            finished_at__isnull=True,
            source=KlarnaSource.ADD_EXTRA_DISCOUNT,
        )

        for klarna_adjustment in klarna_adjustments_add_extra_discount:
            klarna_adjustment.accept_add_extra_discount_source()

        klarna_adjustments_customer_service = queryset.filter(
            finished_at__isnull=True,
            source=KlarnaSource.CUSTOMER_SERVICE,
        )
        for klarna_adjustment in klarna_adjustments_customer_service:
            klarna_adjustment.accept_customer_service_source()


class OrderItemAbortRequestAdmin(admin.ModelAdmin):
    formfield_overrides = {JSONField: {'widget': JSONEditorWidget}}

    list_display = (
        'id',
        'order_id',
        'order_items_with_quantity',
        'status',
        'requested_at',
        'executed_at',
        'exception_messages',
        'requested_by',
    )

    search_fields = ('order',)
    list_filter = ('status',)
    raw_id_fields = (
        'order',
        'requested_by',
    )


admin.site.register(CSCorrectionRequest, CSCorrectionRequestAdmin)
admin.site.register(CSUnsuccessfulPayments, CSUnsuccessfulPaymentsAdmin)
admin.site.register(CSActivityLog, CSActivityLogAdmin)
admin.site.register(ReturningCustomer, ReturningCustomerAdmin)
admin.site.register(CSOrder, CSOrderAdmin)
admin.site.register(CSUserProfile, CSUserProfileAdmin)
admin.site.register(KlarnaAdjustment, KlarnaAdjustmentAdmin)
admin.site.register(OrderItemAbortRequest, OrderItemAbortRequestAdmin)
