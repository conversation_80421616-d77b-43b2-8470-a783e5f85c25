from django.contrib import (
    admin,
    messages,
)
from django.db import transaction
from django.shortcuts import redirect
from django.urls import reverse

from django_object_actions import action

from custom.admin_action import admin_action_with_form
from customer_service.admin_forms import CSCorrectionRequestForm
from customer_service.correction_request_strategies import (
    get_correction_request_strategy,
)
from customer_service.enums import (
    CSCorrectionRequestStatus,
    CSCorrectionRequestType,
)
from invoice.models import InvoiceDomestic


def save_correction_request_and_redirect(
    modeladmin, request, queryset, form, object_id
):
    form.save()
    url = reverse(
        'admin:customer_service_cscorrectionrequest_change', args=(object_id,)
    )
    return redirect(url)


class CSCorrectionRequestActionsMixin:
    @action(description='Edit tag')
    def edit_tag(self, request, obj):
        return admin_action_with_form(
            modeladmin=self,
            request=request,
            instance=obj,
            queryset=[],
            form_class=CSCorrectionRequestForm,
            form_initial={
                'tag': obj.tag,
            },
            success_function=save_correction_request_and_redirect,
            success_function_kwargs={'object_id': obj.id},
        )

    @admin.display(description='Accept correction invoice requests')
    def accept_correction_request(self, request, queryset):
        with transaction.atomic():
            queryset = queryset.select_for_update(skip_locked=True)
            if queryset.filter(
                status=CSCorrectionRequestStatus.STATUS_ACCEPTED
            ).exists():
                self.message_user(
                    request,
                    'Please refresh page. You selected already accepted corrections',
                    level=messages.ERROR,
                )
                return

            for correction_request in queryset:
                strategy = get_correction_request_strategy(correction_request)
                try:
                    correction_invoice = strategy.accept(user=request.user)
                except ValueError as e:
                    self.message_user(request, e, level=messages.ERROR)
                else:
                    if correction_invoice.order.earliest_invoice_domestic_version_supported():  # noqa: E501
                        InvoiceDomestic.objects.create_domestic_from_correction(
                            correction_invoice
                        )

    @admin.action(description='Decline correction invoice requests')
    def decline_correction_request(self, request, queryset):
        for correction_request in queryset.exclude(
            type_cs=CSCorrectionRequestType.TYPE_SWITCH
        ):
            correction_request.reviewer = request.user
            correction_request.status = CSCorrectionRequestStatus.STATUS_REJECTED
            correction_request.save()
            correction_request.correction_invoice.delete()

        excluded_correction_requests = queryset.filter(
            type_cs=CSCorrectionRequestType.TYPE_SWITCH
        )
        if excluded_correction_requests.exists():
            correction_request_ids = ','.join(
                [
                    str(pk)
                    for pk in excluded_correction_requests.values_list('pk', flat=True)
                ]
            )
            self.message_user(
                request,
                f"You can't reject because switch shelf has been successfully applied "
                f'for given requests: {correction_request_ids}',
                level=messages.ERROR,
            )
