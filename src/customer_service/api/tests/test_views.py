from rest_framework.reverse import reverse

import pytest


@pytest.mark.django_db
class TestOrderSwitchRollbackItemOnHoldAPIView:
    def test_cs_order_switch_rollback_item_on_hold_should_rollback_item_on_hold_and_redirect_to_cs_user_overview(  # noqa: E501
        self, mocker, client, admin_user, order_item_factory
    ):
        order_item = order_item_factory()

        mocker.patch(
            'orders.models.OrderSwitchStatusTransitionsMixin.rollback_item_on_hold'
        )

        client.force_login(admin_user)

        url = reverse(
            'cs-order-switch-rollback-item-on-hold',
            kwargs={'order_id': order_item.order.id},
        )

        response = client.post(url)

        redirect_url = reverse(
            'cs_user_overview', kwargs={'pk': order_item.order.owner.id}
        )

        assert response.status_code == 200
        assert response.data['redirect_url'] == redirect_url
