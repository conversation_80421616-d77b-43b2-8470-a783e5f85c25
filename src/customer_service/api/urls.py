from django.urls import path

from customer_service.api.views import (
    CancelFreeReturnAPIView,
    OrderItemAbortRequestRollbackAPIView,
    OrderItemSplitByQuantityAPIView,
    OrderSwitchEditVoucherAPIView,
    OrderSwitchRollbackItemOnHoldAPIView,
    OrderSwitchRollbackItemReplacementAPIView,
    OrderSwitchRollbackItemReplacementItemOnHoldAPIView,
    OrderSwitchRollbackPaymentRecalculationsItemReplacementItemOnHoldAPIView,
    OrderSwitchRollbackRecalculationsItemReplacementItemOnHoldAPIView,
    ProductPostponePriorityAPIView,
)

urlpatterns = [
    path(
        'free_return/<int:order_id>/',
        CancelFreeReturnAPIView.as_view(),
        name='cancel-free-return',
    ),
    path(
        'request-product-postpone/<int:pk>',
        ProductPostponePriorityAPIView.as_view(),
        name='request-product-postpone',
    ),
    path(
        'rollback-abort-request/<int:pk>',
        OrderItemAbortRequestRollbackAPIView.as_view(),
        name='rollback-abort-request',
    ),
    path(
        'order_switch/edit-voucher/<int:pk>',
        OrderSwitchEditVoucherAPIView.as_view(),
        name='cs-order-switch-edit-voucher',
    ),
    path(
        'order-item-split-by-quantity/<int:pk>',
        OrderItemSplitByQuantityAPIView.as_view(),
        name='order-item-split-by-quantity',
    ),
    path(
        'order_switch/rollback_item_on_hold/<int:order_id>',
        OrderSwitchRollbackItemOnHoldAPIView.as_view(),
        name='cs-order-switch-rollback-item-on-hold',
    ),
    path(
        'order_switch/rollback_item_replacement/<int:order_id>/',
        OrderSwitchRollbackItemReplacementAPIView.as_view(),
        name='cs-order-switch-rollback-item-replacement',
    ),
    path(
        'order_switch/rollback_item_replacement_and_hold/<int:order_id>',
        OrderSwitchRollbackItemReplacementItemOnHoldAPIView.as_view(),
        name='cs-order-switch-rollback-item-replacement-hold',
    ),
    path(
        'order_switch/rollback_recalculations_item_replacement_hold/<int:order_id>',
        OrderSwitchRollbackRecalculationsItemReplacementItemOnHoldAPIView.as_view(),
        name='cs-order-switch-rollback-recalculations-item-replacement-hold',
    ),
    path(
        'order_switch/rollback_payment_recalculations_item_replacement_hold/<int:order_id>',
        OrderSwitchRollbackPaymentRecalculationsItemReplacementItemOnHoldAPIView.as_view(),
        name='cs-order-switch-rollback-payment-recalculations-item-replacement-hold',
    ),
]
