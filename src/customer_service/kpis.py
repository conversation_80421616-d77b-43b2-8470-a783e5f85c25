from decimal import Decimal

from django.db.models import (
    <PERSON>,
    Sum,
)
from django.db.models.functions import Coalesce

from customer_service.enums import CSCorrectionRequestStatus
from customer_service.models import CSCorrectionRequest
from invoice.choices import InvoiceStatus
from invoice.models import Invoice
from kpi.kpis import (
    KPICachableAllButLastMixin,
    KPIJSONMixin,
    KPINoPredictionMixin,
    KPINoShareComponentStringHtmlMixin,
    KPINoShareHTMLMixin,
    KPIStandardCSVMixin,
    KPIType,
    KPIValueComponent,
    KPIValueObject,
    KPIValueSuffixEuroMixin,
    kpi_cache_value,
)
from orders.enums import (
    OrderStatus,
    OrderType,
)
from orders.models import Order
from producers.choices import ProductStatus
from vouchers.enums import (
    VoucherOrigin,
    VoucherType,
)
from vouchers.models import Voucher


class KPICustomerServiceBase(
    KPICachableAllButLastMixin,
    KPINoPredictionMixin,
    KPINoShareHTMLMixin,
    KPIStandardCSVMixin,
    KPIJSONMixin,
):
    def _is_cachable(self, time_segment):
        return False

    def _is_savable(self, time_segment):
        return False

    def get_time_limits(self, time_segment_n, naive=True):
        segment_beginning, segment_end = self.kpi_group.time_segments.segments[
            time_segment_n
        ]
        if naive:
            segment_beginning = segment_beginning.replace(tzinfo=None)
            segment_end = segment_end.replace(tzinfo=None)
        return segment_beginning, segment_end

    @property
    def name(self):
        return 'Nr of replies sent'

    def create_test_objects(self, time_segment_n, quantity=1):
        objects = []
        return KPIValueObject(
            value=0, value_components=(KPIValueComponent('objects', '', objects),)
        )


class KPICustomOrdersSold(
    KPINoShareComponentStringHtmlMixin, KPICustomerServiceBase, KPIType
):
    @kpi_cache_value
    def get_value(self, time_segment_n):
        """Number of Orders of type custom order
        with paid_at date in given time segment
        """
        segment_start, segment_end = self.get_time_limits(time_segment_n, naive=False)
        qs = Order.objects.filter(
            paid_at__gte=segment_start,
            paid_at__lte=segment_end,
            order_type=OrderType.CUSTOM_ORDER,
        )
        ids = qs.values_list('id', flat=True)
        return KPIValueObject(
            value=len(ids),
            value_components=(
                KPIValueComponent(
                    'order ids', 'order ids', ', '.join([str(id) for id in ids])
                ),
            ),
        )

    @property
    def name(self):
        return 'Custom orders sold'

    def create_test_objects(self, time_segment_n, quantity=1):
        objects = []
        return KPIValueObject(
            value=0, value_components=(KPIValueComponent('objects', '', objects),)
        )


class KPICustomOrdersTotalRevenue(KPICustomerServiceBase, KPIType):
    @kpi_cache_value
    def get_value(self, time_segment_n):
        """Sum of total_price_net of orders from KPI for number of custom orders"""
        segment_start, segment_end = self.get_time_limits(time_segment_n, naive=False)
        agg = Order.objects.filter(
            paid_at__gte=segment_start,
            paid_at__lte=segment_end,
            order_type=OrderType.CUSTOM_ORDER,
        ).aggregate(total_revenue=Sum('total_price_net'))
        value = agg['total_revenue'] or 0
        return KPIValueObject(value=value)

    @property
    def name(self):
        return 'Total revenue for custom orders'

    def create_test_objects(self, time_segment_n, quantity=1):
        objects = []
        return KPIValueObject(
            value=0, value_components=(KPIValueComponent('objects', '', objects),)
        )


class KPICSAbsoluteVouchers(KPICustomerServiceBase, KPIType):
    def __init__(self, group, bounds=None, *args, **kwargs):
        """bounds: tuple of lower and upper bounds"""
        self.lower, self.upper = bounds or (None, None)
        super(KPICSAbsoluteVouchers, self).__init__(group, *args, **kwargs)

    def get_cache_key(self, time_segment, global_cache=False):
        cache_key = super(KPICSAbsoluteVouchers, self).get_cache_key(
            time_segment, global_cache=global_cache
        )
        return cache_key + '_{}_{}'.format(self.lower, self.upper)

    @kpi_cache_value
    def get_value(self, time_segment_n):
        """Number of vouchers with origin CS and type absolute
        and created_at date in given time segment
        and with voucher value in given range
        """
        segment_start, segment_end = self.get_time_limits(time_segment_n, naive=False)
        qs = Voucher.objects.filter(
            origin=VoucherOrigin.CUSTOMER_SUPPORT,
            created_at__gte=segment_start,
            created_at__lte=segment_end,
            kind_of=VoucherType.ABSOLUTE,
        )
        if self.upper:
            qs = qs.filter(value__lte=self.upper)
        if self.lower:
            qs = qs.filter(value__gt=self.lower)
        return KPIValueObject(value=qs.count())

    @property
    def name(self):
        name_parts = []
        if self.lower:
            name_parts.append('>{} Euro'.format(self.lower))
        if self.upper:
            name_parts.append('<={} Euro'.format(self.upper))
        return ' '.join(name_parts)

    def create_test_objects(self, time_segment_n, quantity=1):
        objects = []
        return KPIValueObject(
            value=0, value_components=(KPIValueComponent('objects', '', objects),)
        )


class KPICSPercentageVouchers(KPICustomerServiceBase, KPIType):
    @kpi_cache_value
    def get_value(self, time_segment_n):
        """Number of vouchers with origin CS and type percentage
        and created_at date in given time segment
        """
        segment_start, segment_end = self.get_time_limits(time_segment_n, naive=False)
        qs = Voucher.objects.filter(
            origin=VoucherOrigin.CUSTOMER_SUPPORT,
            created_at__gte=segment_start,
            created_at__lte=segment_end,
            kind_of=VoucherType.PERCENTAGE,
        )
        return KPIValueObject(value=qs.count())

    @property
    def name(self):
        return 'promo codes in percentage'

    def create_test_objects(self, time_segment_n, quantity=1):
        objects = []
        return KPIValueObject(
            value=0, value_components=(KPIValueComponent('objects', '', objects),)
        )


class KPICSPromoTotalRevenue(KPICustomerServiceBase, KPIType):
    def get_value(self, time_segment_n):
        segment_start, segment_end = self.get_time_limits(time_segment_n, naive=False)
        sums = (
            Order.objects.filter(
                paid_at__gte=segment_start,
                paid_at__lte=segment_end,
                vouchers__origin=VoucherOrigin.CUSTOMER_SUPPORT,
            )
            .disctinct()
            .aggregate(
                promo_amount=Coalesce(Sum('promo_amount'), 0),
                total_price=Coalesce(Sum('total_price'), 0),
            )
        )
        return KPIValueObject(value=sum(sums.values()))

    @property
    def name(self):
        return 'Total revenue of orders paid with CS promocode'

    def create_test_objects(self, time_segment_n, quantity=1):
        objects = []
        return KPIValueObject(
            value=0, value_components=(KPIValueComponent('objects', '', objects),)
        )


class KPICSPromoCount(KPICustomerServiceBase, KPIType):
    def get_value(self, time_segment_n):
        segment_start, segment_end = self.get_time_limits(time_segment_n, naive=False)
        count = (
            Order.objects.filter(
                paid_at__gte=segment_start,
                paid_at__lte=segment_end,
                vouchers__origin=VoucherOrigin.CUSTOMER_SUPPORT,
            )
            .distinct()
            .count()
        )
        return KPIValueObject(value=count)

    @property
    def name(self):
        return 'Number of orders paid with CS promocode'

    def create_test_objects(self, time_segment_n, quantity=1):
        objects = []
        return KPIValueObject(
            value=0, value_components=(KPIValueComponent('objects', '', objects),)
        )


class KPICSCorrectionsValue(KPICustomerServiceBase, KPIType):
    def get_value(self, time_segment_n):
        segment_start, segment_end = self.get_time_limits(time_segment_n, naive=False)
        qs = CSCorrectionRequest.objects.filter(
            created_at__gte=segment_start,
            created_at__lte=segment_end,
            status=CSCorrectionRequestStatus.STATUS_ACCEPTED,
        )
        amount_sum = qs.aggregate(value=Sum('correction_amount_gross'))
        value = amount_sum['value'] or 0
        return KPIValueObject(value=value)

    @property
    def name(self):
        return 'Total value of invoice corrections'

    def create_test_objects(self, time_segment_n, quantity=1):
        objects = []
        return KPIValueObject(
            value=0, value_components=(KPIValueComponent('objects', '', objects),)
        )


def get_invoices_qs(segment_start, segment_end):
    return (
        Invoice.objects.filter(
            corrections__status=InvoiceStatus.CORRECTING,
            corrections__isnull=False,
            order__status=OrderStatus.DELIVERED,
        )
        .annotate(max_correction_date=Max('corrections__issued_at'))
        .filter(
            max_correction_date__gte=segment_start,
            max_correction_date__lt=segment_end,
        )
        .prefetch_related('corrections')
    )


class KPIReturns(KPINoShareComponentStringHtmlMixin, KPICustomerServiceBase, KPIType):
    @kpi_cache_value
    def get_value(self, time_segment_n):
        """Number of invoice corrections for orders with status delivered
        with correction issued_at in given time segment
        with total net value equal 0
        """
        segment_start, segment_end = self.get_time_limits(time_segment_n, naive=False)
        qs = get_invoices_qs(segment_start, segment_end)
        ids = []
        for invoice in qs.all():
            correction = invoice.corrections.get(issued_at=invoice.max_correction_date)
            diff = correction.get_correction_differences()
            if diff['totals']['net'] == 0:
                ids.append(str(correction.order.id))
        return KPIValueObject(
            value=len(ids),
            value_components=(
                KPIValueComponent('order ids', 'order ids', ', '.join(ids)),
            ),
        )

    @property
    def name(self):
        return 'Amount of returns'

    def create_test_objects(self, time_segment_n, quantity=1):
        objects = []
        return KPIValueObject(
            value=0, value_components=(KPIValueComponent('objects', '', objects),)
        )


class KPIReturnsValue(KPIValueSuffixEuroMixin, KPICustomerServiceBase, KPIType):
    @kpi_cache_value
    def get_value(self, time_segment_n):
        """
        Sum of total net values of original invoices for corrections from above kpi
        """
        segment_start, segment_end = self.get_time_limits(time_segment_n, naive=False)
        qs = get_invoices_qs(segment_start, segment_end)
        value = Decimal(0)
        for invoice in qs.all():
            correction = invoice.corrections.get(issued_at=invoice.max_correction_date)
            diff = correction.get_correction_differences()
            if diff['totals']['net'] == 0:
                value += Decimal(diff['totals']['net_old'])
        return KPIValueObject(value=value)

    @property
    def name(self):
        return 'Total value of returns (net)'

    def create_test_objects(self, time_segment_n, quantity=1):
        objects = []
        return KPIValueObject(
            value=0, value_components=(KPIValueComponent('objects', '', objects),)
        )


class KPIRefunds(KPINoShareComponentStringHtmlMixin, KPICustomerServiceBase, KPIType):
    @kpi_cache_value
    def get_value(self, time_segment_n):
        """Number of invoice corrections for orders with status delivered
        with correction issued_at in given time segment
        with total net value greater than 0
        and total net difference lesser than 0
        """
        segment_start, segment_end = self.get_time_limits(time_segment_n, naive=False)
        qs = get_invoices_qs(segment_start, segment_end)
        ids = []
        for invoice in qs.all():
            correction = invoice.corrections.get(issued_at=invoice.max_correction_date)
            diff = correction.get_correction_differences()
            if diff['totals']['net'] > 0 > diff['totals']['net_diff']:
                ids.append(str(correction.order.id))
        return KPIValueObject(
            value=len(ids),
            value_components=(
                KPIValueComponent('order ids', 'order ids', ', '.join(ids)),
            ),
        )

    @property
    def name(self):
        return 'Amount of refunds'

    def create_test_objects(self, time_segment_n, quantity=1):
        objects = []
        return KPIValueObject(
            value=0, value_components=(KPIValueComponent('objects', '', objects),)
        )


class KPIRefundsValue(KPIValueSuffixEuroMixin, KPICustomerServiceBase, KPIType):
    @kpi_cache_value
    def get_value(self, time_segment_n):
        """Sum of total net differences for corrections from above kpi"""
        segment_start, segment_end = self.get_time_limits(time_segment_n, naive=False)
        qs = get_invoices_qs(segment_start, segment_end)
        value = Decimal(0)
        for invoice in qs.all():
            correction = invoice.corrections.get(issued_at=invoice.max_correction_date)
            diff = correction.get_correction_differences()
            if diff['totals']['net'] > 0 > diff['totals']['net_diff']:
                value += abs(diff['totals']['net_diff'])
        return KPIValueObject(value=value)

    @property
    def name(self):
        return 'Total value of refunds (net)'

    def create_test_objects(self, time_segment_n, quantity=1):
        objects = []
        return KPIValueObject(
            value=0, value_components=(KPIValueComponent('objects', '', objects),)
        )


class KPICancellations(
    KPINoShareComponentStringHtmlMixin, KPICustomerServiceBase, KPIType
):
    @kpi_cache_value
    def get_value(self, time_segment_n):
        """Number of invoice corrections of status 'correcting'
        for orders with product in status 'aborted'
        with correction issued at in given time segment
        """
        segment_start, segment_end = self.get_time_limits(time_segment_n, naive=False)
        qs = Invoice.objects.filter(
            issued_at__gte=segment_start,
            issued_at__lt=segment_end,
            status=InvoiceStatus.CORRECTING,
            order__product__status=ProductStatus.ABORTED,
        )
        ids = qs.values_list('order__id', flat=True)
        return KPIValueObject(
            value=len(ids),
            value_components=(
                KPIValueComponent(
                    'order ids', 'order ids', ', '.join([str(id) for id in ids])
                ),
            ),
        )

    @property
    def name(self):
        return 'Amount of cancellations'

    def create_test_objects(self, time_segment_n, quantity=1):
        objects = []
        return KPIValueObject(
            value=0, value_components=(KPIValueComponent('objects', '', objects),)
        )


class KPICancellationsValue(KPIValueSuffixEuroMixin, KPICustomerServiceBase, KPIType):
    @kpi_cache_value
    def get_value(self, time_segment_n):
        """Sum of total values of orders from above kpi"""
        segment_start, segment_end = self.get_time_limits(time_segment_n, naive=False)
        qs = Invoice.objects.filter(
            issued_at__gte=segment_start,
            issued_at__lt=segment_end,
            status=InvoiceStatus.CORRECTING,
            order__product__status=ProductStatus.ABORTED,
        )
        value = Decimal(0)
        for correction in qs.all():
            value += (
                correction.order.get_base_total_value()
                + correction.order.get_base_promo_amount()
            )
        return KPIValueObject(value=value)

    @property
    def name(self):
        return 'Total value of cancellations'

    def create_test_objects(self, time_segment_n, quantity=1):
        objects = []
        return KPIValueObject(
            value=0, value_components=(KPIValueComponent('objects', '', objects),)
        )
