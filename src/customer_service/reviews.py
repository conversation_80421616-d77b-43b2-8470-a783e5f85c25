import requests

from requests.auth import HTTPBasicAuth

from orders.models import Order


class TrustedShopReview(object):
    def __init__(self, review):
        self.creation_date = review['creationDate']
        self.mark = int(float(review['mark']))
        self.comment = review['comment']
        self.email = review['consumerEmail']
        self.order = review['orderReference']
        self.criteria = [
            TrustedShopReviewCriteria(service_review)
            for service_review in review['criteria']
        ]

    @property
    def order_owner(self):
        return self.get_order_owner_from_order_id() or self.get_order_owner_from_email()

    def get_order_owner_from_email(self):
        order = None
        if self.email:
            order = Order.objects.filter(email=self.email).first()
        if order:
            return order.owner_id
        return None

    def get_order_owner_from_order_id(self):
        try:
            order_id = int(self.order)
        except ValueError:
            return None

        order = Order.objects.filter(id=order_id).first()
        if order:
            return order.owner_id
        return None


class TrustedShopReviewCriteria(object):
    def __init__(self, service_review):
        self.type = service_review['type']
        self.mark = int(service_review['mark'])
        self.mark_description = service_review['markDescription']


class TrustedShopReviews(object):
    def __init__(self, ts_id, user, password):
        self.session = requests.Session()
        self.ts_id = ts_id
        self.auth = HTTPBasicAuth(user, password)

    def _page_review(self):
        url = (
            f'https://api.trustedshops.com/rest/restricted/v2/'
            f'shops/{self.ts_id}/reviews.json'
        )
        response = self.session.get(url, auth=self.auth)
        if not response.ok:
            raise requests.exceptions.ConnectionError('connection failed')
        data = response.json()
        return data['response']['data']['shop']['reviews']

    def get_all(self):
        reviews = []
        for review in self._page_review():
            reviews.append(TrustedShopReview(review))  # noqa: PERF401
        return reviews


class TrustPilotReviews(object):
    def __init__(self, website_name, verbose=True):
        self.session = requests.Session()
        headers = {
            'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) '
            'AppleWebKit/537.36 (KHTML, like Gecko) '
            'Chrome/75.0.3770.80 Safari/537.36',
            'Accept': 'application/json',
        }
        self.session.headers.update(headers)
        self.verbose = verbose
        self.www = 'www'
        self.businesses_id, self.businesses_name = self.get_business_info(website_name)

    def get_business_info(self, key):
        business_id, business_name = None, None
        url = 'https://www.trustpilot.com/businessunit/search'
        params = {'country': self.www, 'query': key}
        response = self.session.get(url, params=params)
        if response.ok:
            data = response.json()
            for items in data['businessUnits']:
                if items['name']['identifying'] == params['query']:
                    business_name = items['displayName'].strip()
                    business_id = items['id']
                    break
            return business_id, business_name
        else:
            raise requests.exceptions.ConnectionError('Connection Error')

    def _page_review(self):
        url = 'https://{}.trustpilot.com/review/{}/jsonld?'.format(
            self.www, self.businesses_id
        )
        response = self.session.get(url)

        if not response.ok:
            raise requests.exceptions.ConnectionError('connection failed')

        response = response.json()
        return response[0]['review']

    def get_all(self):
        reviews = []
        for review in self._page_review():
            reviews.append(TrustPilotReview(review))  # noqa: PERF401
        return reviews


class TrustPilotReview(object):
    def __init__(self, review):
        self.author_name = review['author']['name']
        self.comment = review['reviewBody']
        self.headline = review['headline']
        self.creation_date = review['datePublished']
        self.mark = review['reviewRating']['ratingValue']
