const serializeForm = form => {
  const formData = new FormData(form);
  const data = {};
  for (const [key, value] of formData.entries()) {
    data[key] = value;
  }
  return JSON.stringify(data);
};

document.addEventListener('DOMContentLoaded', () => {
  const splitByQuantityForms = document.querySelectorAll(".order-item-split-by-quantity-form");
  splitByQuantityForms.forEach(splitForm => {
    splitForm.addEventListener("submit", (event) => {
      event.preventDefault();
      const form = event.currentTarget;

      fetch(form.action, {
        method: 'POST',
        body: serializeForm(form),
        headers: {
          'Content-Type': 'application/json'
        }
      })
        .then(response => {
          if (!response.ok) {
            return response.json().then(errorResponse => {
              throw new Error(errorResponse.non_field_errors);
            });
          }
          return response.json();
        })
        .then(response => {
          window.location.reload();
        })
        .catch(error => {
          window.alert(error);
        });
    });
  });
});
