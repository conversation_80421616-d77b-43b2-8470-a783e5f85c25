const serializeForm = form => {
  const formData = new FormData(form);
  const data = {};
  for (const [key, value] of formData.entries()) {
    data[key] = value;
  }
  return JSON.stringify(data);
};

document.addEventListener('DOMContentLoaded', () => {
  const editVoucherForm = document.getElementById("switch-edit-voucher-form");
  editVoucherForm.addEventListener("submit", (event) => {
    event.preventDefault();
    const form = event.currentTarget;

    fetch(form.action, {
      method: 'POST',
      body: serializeForm(form),
      headers: {
        'Content-Type': 'application/json'
      }
    })
      .then(response => {
        if (!response.ok) {
          return response.json().then(errorResponse => {
            throw new Error(errorResponse.__all__);
          });
        }
        return response.json();
      })
      .then(response => {
        window.alert(`${response.target_voucher} will be used for calculations.`);
      })
      .catch(error => {
        window.alert(error);
      });
  });
});
