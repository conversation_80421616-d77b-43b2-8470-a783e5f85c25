{% extends "customer_service/base.html" %}
{% load crispy_forms_tags static %}

{% block extrascripts %}
    <script type="text/javascript">
        const sourceProduct = {{ serialized_source_product|safe}};
    </script>
    <script src="{% static 'js/preventDoubleClickOnSubmit.js' %}"></script>
    <script src="{% static 'js/switchRollback.js' %}"></script>
    <script src="{% static 'js/addExtraDiscount.js' %}"></script>
    <script src="{% static 'js/switchAddExtraDiscount.js' %}"></script>
{% endblock %}

{% block content %}
    {% include "customer_service/confirmation_modal.html" %}

    <div class="row">
        <div class="col-md-10">
            <h2>Switch Product - Status <b>"{{ order.get_switch_status_display }}"</b></h2>
            <p class="bolder">
                <a href="{% url 'cs_user_overview' order.owner.id %}">Order: {{ order.pk }}</a>
                / {{ order.first_name}} {{ order.last_name }}
            </p>
            <p>Assembly: {{ order.assembly|yesno:"Yes,No" }}</p>
            <div>
                <p>Estimated Delivery Time: {{ order.estimated_delivery_time }}</p>
                {% if order.main_voucher %}
                    <hr/>
                    <p>Promo: <a href="{% url 'admin:vouchers_voucher_change' order.main_voucher.pk %}">{{ order.main_voucher }}</a></p>
                    {% if voucher_form %}
                        <form id="switch-edit-voucher-form" action="{% url 'cs-order-switch-edit-voucher' order.pk %}" method="post">
                            {% csrf_token %}
                            {{ voucher_form|crispy }}
                            <input type="submit" class="btn btn-info btn-next" value="Update Voucher" />
                        </form>
                    {% endif %}

                    {% with order.region.get_currency.symbol as currency %}
                        <p><b>Regionalized Promo:</b>
                            Netto: {{ order.region_promo_amount_net }} {{ currency }} /
                            Brutto: {{ order.region_promo_amount }} {{ currency }}
                        </p>
                    {% endwith %}
                    <p><b>In Euro Promo:</b>
                        Netto: {{ order.promo_amount_net }} € /
                        Brutto: {{ order.promo_amount }} €
                    </p>
                {% endif %}
                <hr/>
                {% with order.region.get_currency.symbol as currency %}
                    <p>
                        Region Netto: {{ order.region_total_price_net }} {{ currency }}  /
                        Region Brutto: {{ order.region_total_price }} {{ currency }}
                    </p>
                {% endwith %}
                <p>
                    Netto: {{ order.total_price_net }} € /
                    Brutto: {{ order.total_price }} €
                </p>
            </div>
            {% include 'customer_service/order_switch/_source_target_diff.html' %}
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <form action="{% url 'cs_order_switch_item_replacement' order.pk %}" method="post">
                {% csrf_token %}
                {{ form|crispy }}
                <input type="submit" class="btn btn-info btn-next btn-prevent-doubleclick" value="Recalculate Pricing" />
            </form>

            <form id="switch-cancel" action="{% url 'cs-order-switch-rollback-item-on-hold' order.pk %}" method="post">
                {% csrf_token %}
                <input type="submit" class="btn btn-info btn-cancel btn-prevent-doubleclick" value="Cancel" />
            </form>
        </div>
    </div>
{% endblock %}

