from decimal import Decimal

import pytest

from complaints.models import Complaint
from complaints.tests.factories import ComplaintFactory
from gallery.tests.factories import WattyFactory
from invoice.enums import InvoiceItemDiscountTag
from orders.tests.conftest import order_with_products  # noqa
from orders.tests.factories import OrderItemFactory
from producers.tests.factories import (
    ManufactorFactory,
    ProductFactory,
)
from user_profile.choices import UserType
from user_profile.tests.factories import UserProfileFactory


@pytest.fixture
def complaint_fixture(order) -> Complaint:
    user_profile = UserProfileFactory(user_type=UserType.STAFF)
    manufacturer = ManufactorFactory(id=1)
    order_item = OrderItemFactory.create(order=order, order_item=WattyFactory())
    product = ProductFactory.create(
        manufactor=manufacturer,
        order=order,
        order_item=order_item,
        cached_product_type='watty',
    )
    return ComplaintFactory.create(
        owner=user_profile.user,
        reporter=user_profile.user,
        product=product,
        complaint_costs__currency='EUR',
    )


@pytest.fixture
def complaint_with_reproduction(complaint_fixture) -> Complaint:
    complaint_fixture.elements = {
        'other': [],
        'elements': ['b3 - BOX:3', 'b5 - BOX:8'],
        'features': [
            'f1 - BOX:14',
            'f10 - BOX:19',
        ],
        'fittings': {},
    }

    complaint_fixture.reproduction = True
    complaint_fixture.assembly_team_intervention = True
    complaint_fixture.serialized_complaint_service = {'id': 1, 'service_price': '15.00'}
    complaint_fixture.save()
    return complaint_fixture


@pytest.fixture
def complaint_with_reproduction_batched(
    complaint_with_reproduction, product_batch_factory
):
    product = complaint_with_reproduction.product
    manufactor = product.manufactor
    product.batch = product_batch_factory(manufactor=manufactor)
    product.save()
    return complaint_with_reproduction


@pytest.fixture
def complaint_with_refund(complaint_fixture) -> Complaint:
    complaint_fixture.elements = {
        'other': [],
        'elements': [],
        'features': [],
        'fittings': {},
    }
    complaint_fixture.complaint_costs.refund_amount = Decimal('113.1')
    complaint_fixture.complaint_costs.save()
    complaint_fixture.refund = True
    complaint_fixture.reproduction = False
    complaint_fixture.refund_reason = InvoiceItemDiscountTag.DELIVERY_DELAY.value
    complaint_fixture.save()
    return complaint_fixture
