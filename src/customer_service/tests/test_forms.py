from decimal import Decimal

from django.contrib.auth import get_user_model
from django.urls import reverse
from django.utils import timezone

import pytest

from customer_service.enums import KlarnaPriceChangeType
from customer_service.forms import KlarnaAdjustmentCreateForm
from invoice.choices import InvoiceStatus
from invoice.enums import InvoiceItemTag
from logger.models import Log
from orders.enums import OrderStatus

User = get_user_model()


@pytest.mark.django_db
def test_order_status_change(order_factory, admin_client):
    order = order_factory(status=OrderStatus.CART)
    url = reverse('cs_change_order_status', args=[order.id])

    admin_client.post(url, data={'status': OrderStatus.SHIPPED})
    order.refresh_from_db()

    assert (  # noqa: PT018
        order.status == OrderStatus.SHIPPED
        and order.status_previous == OrderStatus.CART
    )

    log = Log.objects.get(model=order._meta.label, model_id=order.id)
    assert log.data == {
        'old_status': OrderStatus.CART,
        'new_status': OrderStatus.SHIPPED,
    }


@pytest.mark.django_db
def test_discount_tag_cant_be_empty_when_quality_dissatisfaction_selected(
    admin_client, invoice_factory, order, admin_user
):
    invoice = invoice_factory(
        pretty_id='normal/1/2',
        status=InvoiceStatus.ENABLED,
        order=order,
    )
    url = reverse('cs_request_correction', kwargs={'pk': invoice.pk})
    data = {
        'pk': invoice.id,
        'correction_amount_gross': Decimal('155.00'),
        'tag': InvoiceItemTag.DISCOUNT_QUALITY_DISSATISFACTION.value,
        'discount_tag': '---------',
        'invoice': invoice.id,
    }
    result = admin_client.post(url, data=data)
    assert (
        result.context_data['form'].errors['discount_tag'][0]
        == 'Discount tag must be selected'
    )


@pytest.mark.django_db
class TestKlarnaAdjustmentCreateForm:
    @pytest.fixture
    def proforma_invoice(self, invoice_factory, order, invoice_item_factory):
        invoice = invoice_factory(
            pretty_id='normal/1/2',
            status=InvoiceStatus.PROFORMA,
            order=order,
        )
        invoice_item_factory(gross_price=1000, invoice=invoice)
        return invoice

    def get_form_data(self, invoice, change_type, amount=None):
        return {
            'invoice': invoice.id,
            'change_type': change_type,
            'amount': amount or Decimal('125.00'),
            'conversation_link': 'https://www.dixa.com/blablabla',
            'reason': 'additional drawer',
        }

    def test_create_klarna_adjustment(self, admin_client, proforma_invoice, admin_user):
        data = self.get_form_data(proforma_invoice, KlarnaPriceChangeType.INCREASE)
        form = KlarnaAdjustmentCreateForm(
            data, initial={'invoice': proforma_invoice.id}
        )
        form.is_valid()
        assert len(form.errors) == 0

    def test_can_only_create_for_proforma(
        self, admin_client, invoice_factory, order, admin_user
    ):
        invoice = invoice_factory(
            pretty_id='normal/1/2',
            status=InvoiceStatus.ENABLED,
            order=order,
        )
        data = self.get_form_data(invoice, KlarnaPriceChangeType.INCREASE)
        form = KlarnaAdjustmentCreateForm(data, initial={'invoice': invoice.id})
        form.is_valid()
        assert len(form.errors) == 1
        assert form.errors == {'__all__': ['Can only change for pro forma']}

    def test_can_have_only_one_active_adjustment(
        self,
        admin_client,
        proforma_invoice,
        admin_user,
        klarna_adjustment_factory,
    ):
        klarna_adjustment_factory(
            invoice=proforma_invoice, change_type=KlarnaPriceChangeType.INCREASE
        )
        data = self.get_form_data(proforma_invoice, KlarnaPriceChangeType.INCREASE)
        form = KlarnaAdjustmentCreateForm(
            data, initial={'invoice': proforma_invoice.id}
        )
        form.is_valid()
        assert len(form.errors) == 1
        assert form.errors == {'__all__': ['There is already pending adjustment!']}

    def test_can_have_many_adjustments(
        self,
        admin_client,
        proforma_invoice,
        admin_user,
        klarna_adjustment_factory,
    ):
        klarna_adjustment_factory(
            invoice=proforma_invoice,
            change_type=KlarnaPriceChangeType.INCREASE,
            finished_at=timezone.now(),
        )
        data = self.get_form_data(proforma_invoice, KlarnaPriceChangeType.INCREASE)
        form = KlarnaAdjustmentCreateForm(
            data, initial={'invoice': proforma_invoice.id}
        )
        form.is_valid()
        assert len(form.errors) == 0

    def test_cant_create_discount_bigger_than_invoice_gross_price(
        self,
        admin_client,
        proforma_invoice,
        admin_user,
    ):
        data = self.get_form_data(
            proforma_invoice, KlarnaPriceChangeType.DISCOUNT, Decimal('9999999999')
        )
        form = KlarnaAdjustmentCreateForm(
            data, initial={'invoice': proforma_invoice.id}
        )
        form.is_valid()
        assert len(form.errors) == 1
        assert form.errors == {
            '__all__': [
                f'Max possible discount: {proforma_invoice.sum_invoice_items_gross_price()}'  # noqa: E501
            ]
        }
