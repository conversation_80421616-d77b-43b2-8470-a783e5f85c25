from decimal import Decimal

import pytest

from customer_service.splitters import OrderItemByQuantitySplitter
from producers.choices import ProductStatus


@pytest.mark.django_db
class TestOrderItemByQuantitySplitter:
    def test_split_by_quantity_should_split_ok_when_partial(
        self, order_item_factory, product_factory
    ):
        source_order_item = order_item_factory(
            quantity=5,
            order__region_total_price=Decimal('0.0'),
        )

        product_factory.create_batch(
            5, order_item=source_order_item, status=ProductStatus.IN_PRODUCTION
        )
        source_quantity = source_order_item.quantity
        target_quantity = 2

        target_order_item = OrderItemByQuantitySplitter(
            request_user=None, order_item=source_order_item
        ).split_by_quantity(target_quantity)

        source_order_item.refresh_from_db()
        target_order_item.refresh_from_db()

        assert source_order_item.quantity == source_quantity - target_quantity
        assert target_order_item.quantity == target_quantity
        assert target_order_item.pk != source_order_item.pk
        assert target_order_item.product_set.count() == target_quantity
        assert (
            source_order_item.product_set.count() == source_quantity - target_quantity
        )
