from unittest import mock

import pytest

from customer_service.utils import (
    TrackingLinkURLGenerator,
    update_order_and_product_notes_from_cs_notes,
)


def test_url_generator_dpd():
    generator = TrackingLinkURLGenerator('DPD')
    assert (
        generator.generate_url(12312312123)
        == 'https://tracktrace.dpd.com.pl/EN/parcelDetails?typ=1&p1=12312312123'
    )


def test_url_generator_tnt():
    generator = TrackingLinkURLGenerator('TNT')
    assert (
        generator.generate_url(195351444)
        == 'https://www.tnt.com/express/en_gb/site/shipping-tools/tracking.html?searchType=CON&cons=195351444'
    )


def test_url_generator_fedex():
    generator = TrackingLinkURLGenerator('FEDEX')
    assert (
        generator.generate_url(12312332)
        == 'https://www.fedex.com/fedextrack/?action=track&trackingnumber=12312332&locale=en_us'
    )


def test_url_generator_invalid_carrier_type():
    generator = TrackingLinkURLGenerator('INVALID')
    assert generator.generate_url(12312312123) == ''


@pytest.mark.django_db
class TestUtils:
    @mock.patch('customer_service.utils.OrderRefreshEvent')
    def test_update_order_and_product_notes_from_cs_notes_should_set_when_no_previous_cs_notes(  # noqa: E501
        self,
        mocked_order_refresh_event,
        order_with_products,
    ):
        order = update_order_and_product_notes_from_cs_notes(
            order_with_products, 'CS note'
        )
        assert order.cs_notes == 'CS note'
        assert order.order_notes == 'CS note'
        for product in order.product_set.all():
            assert product.notes == 'CS note'

    @mock.patch('customer_service.utils.OrderRefreshEvent')
    def test_update_order_and_product_notes_from_cs_notes_should_replace_when_previous_cs_notes(  # noqa: E501
        self,
        mocked_order_refresh_event,
        order_with_products,
    ):
        order_with_products.cs_notes = 'CS note existing'
        order_with_products.order_notes = 'VIP Order, CS note existing'
        order_with_products.save(update_fields=['order_notes', 'cs_notes'])

        for product in order_with_products.product_set.all():
            product.notes = 'Production note, CS note existing'
            product.save(update_fields=['notes'])

        order = update_order_and_product_notes_from_cs_notes(
            order_with_products, 'CS note updated'
        )
        assert order.cs_notes == 'CS note updated'
        assert order.order_notes == 'VIP Order, CS note updated'

        for product in order_with_products.product_set.all():
            assert product.notes == 'Production note, CS note updated'
