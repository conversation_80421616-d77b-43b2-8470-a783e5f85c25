from django import forms

from dixa.models import SystemInstruction


class AnalyzeConversationsForm(forms.Form):
    system_instruction = forms.ModelChoiceField(
        queryset=SystemInstruction.objects.all(),
        required=True,
        label='System Instruction',
    )
    model_name = forms.ChoiceField(
        choices=[
            ('gemini-2.0-flash', 'gemini-2.0-flash'),
            ('gemini-1.5-flash', 'gemini-1.5-flash'),
            ('gemini-1.5-pro', 'gemini-1.5-pro'),
        ],
        initial='gemini-2.0-flash',
        label='AI Model',
    )
    without_stop_words = forms.BooleanField(initial=False, required=False)
    _selected_action = forms.CharField(widget=forms.MultipleHiddenInput)
