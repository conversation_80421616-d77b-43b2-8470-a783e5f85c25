# Generated by Django 4.2.23 on 2025-07-22 12:44

import datetime

import django.db.models.deletion

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        ('dixa', '0004_alter_whatsappfile_file'),
    ]

    operations = [
        migrations.CreateModel(
            name='ConversationForAI',
            fields=[
                (
                    'id',
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('dixa_id', models.IntegerField()),
                ('channel', models.CharField(blank=True, default='', max_length=100)),
                ('email', models.EmailField(blank=True, default='', max_length=254)),
                ('phone', models.CharField(blank=True, default='', max_length=100)),
            ],
        ),
        migrations.CreateModel(
            name='DailyUsage',
            fields=[
                (
                    'id',
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'usage_date',
                    models.DateField(default=datetime.date.today, unique=True),
                ),
                (
                    'limit',
                    models.PositiveIntegerField(
                        default=50,
                        help_text='Maximum number of analysis actions allowed per day',
                    ),
                ),
                ('usage', models.PositiveIntegerField(default=0)),
            ],
        ),
        migrations.CreateModel(
            name='SystemInstruction',
            fields=[
                (
                    'id',
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('name', models.CharField(max_length=100)),
                ('created_by', models.CharField(max_length=100)),
            ],
        ),
        migrations.CreateModel(
            name='MessageForAI',
            fields=[
                (
                    'id',
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('uuid', models.CharField(max_length=100)),
                ('raw_data', models.JSONField()),
                ('text_without_stop_words', models.TextField(blank=True, default='')),
                ('cleaned_text', models.TextField(blank=True, default='')),
                ('language', models.CharField(blank=True, default='en', max_length=10)),
                ('is_customer_message', models.BooleanField(default=False)),
                ('phone', models.CharField(blank=True, max_length=100)),
                ('email', models.EmailField(blank=True, max_length=254)),
                ('name', models.CharField(blank=True, max_length=100)),
                ('created_at', models.DateTimeField()),
                (
                    'conversation',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='messages',
                        to='dixa.conversationforai',
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name='ConversationAnalysisResult',
            fields=[
                (
                    'id',
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('model_name', models.CharField(max_length=100)),
                ('input', models.TextField()),
                ('output', models.TextField()),
                ('data', models.JSONField(help_text='JSON formatted output')),
                (
                    'conversation',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='analysis',
                        to='dixa.conversationforai',
                    ),
                ),
                (
                    'system_instruction',
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to='dixa.systeminstruction',
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name='AnalysisOutputField',
            fields=[
                (
                    'id',
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('field_name', models.CharField(max_length=100)),
                ('possible_values', models.TextField(blank=True)),
                ('description', models.TextField(blank=True)),
                (
                    'system_instruction',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='output_fields',
                        to='dixa.systeminstruction',
                    ),
                ),
            ],
            options={
                'unique_together': {('system_instruction', 'field_name')},
            },
        ),
    ]
