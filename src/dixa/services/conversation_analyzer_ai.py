from custom.gemini import GeminiClient
from dixa.models import (
    ConversationAnalysisResult,
    ConversationForAI,
    SystemInstruction,
)


def analyze_conversation(
    conversation: ConversationForAI,
    system_instruction: SystemInstruction,
    gemini_client: GeminiClient,
    without_stop_words: bool = False,
):
    conversation_text = conversation.as_text(without_stop_words=without_stop_words)
    analysis_result = gemini_client.analyze_dixa_conversation(
        conversation=conversation_text,
        prompt=system_instruction.prompt_text,
    )
    ConversationAnalysisResult.objects.create(
        conversation=conversation,
        system_instruction=system_instruction,
        model_name=gemini_client.model_name,
        input=conversation_text,
        data=analysis_result,
    )
