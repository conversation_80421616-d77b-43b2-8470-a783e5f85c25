import html
import logging
import re

from dataclasses import dataclass
from datetime import (
    date,
    datetime,
)
from operator import itemgetter

from django.conf import settings

import nltk
import requests

from email_reply_parser import EmailReplyParser
from langdetect import (
    LangDetectException,
    detect,
)
from nltk.corpus import stopwords
from nltk.tokenize import word_tokenize

from dixa.models import (
    ConversationForAI,
    MessageForAI,
)

logger = logging.getLogger('cstm')


class DixaExportClient:
    URL = 'https://exports.dixa.io/v1/message_export'
    TOKEN = settings.DIXA_API_KEY

    @classmethod
    def fetch_messages(cls, start_date: date, end_date: date) -> requests.Response:
        query_params = {
            'created_after': start_date.strftime('%Y-%m-%d'),
            'created_before': end_date.strftime('%Y-%m-%d'),
        }
        headers = {'Authorization': f'bearer {cls.TOKEN}'}
        response = requests.get(cls.URL, headers=headers, params=query_params)
        response.raise_for_status()
        return response


@dataclass
class DixaMessage:
    csid: str
    id: str
    text: str
    initial_channel: str
    direction: str
    from_phone_number: str
    author_email: str
    author_name: str
    created_at: datetime
    raw_data: dict

    @classmethod
    def from_json(cls, data: dict) -> 'DixaMessage':
        return cls(
            csid=data['csid'],
            id=data['id'],
            text=data['text'],
            initial_channel=data['initial_channel'],
            direction=data['direction'],
            from_phone_number=data.get('from_phone_number', None) or '',
            author_email=data.get('author_email', None) or '',
            author_name=data.get('author_name', None) or '',
            created_at=datetime.fromtimestamp(data['created_at'] / 1000),
            raw_data=data,
        )

    @property
    def is_email(self) -> bool:
        return self.initial_channel == 'email'

    @property
    def language(self) -> str:
        if getattr(self, '_language', ''):
            return self._language
        self._language = DixaTextParser.detect_language(self.cleaned_text)
        return self._language

    @property
    def cleaned_text(self) -> str:
        if getattr(self, '_cleaned_text', ''):
            return self._cleaned_text
        self._cleaned_text = DixaTextParser.clean_text(self.text, self.is_email)
        return self._cleaned_text

    @property
    def is_customer_message(self) -> bool:
        return self.direction == 'inbound'

    @property
    def is_phone_call(self) -> bool:
        return self.initial_channel == 'pstnphone'


class ExportConversationsToDB:
    def __init__(self, date_start: date, date_end: date):
        self.date_start = date_start
        self.date_end = date_end
        self.text_parser = DixaTextParser()

    def get_messages(self) -> dict:
        response = DixaExportClient.fetch_messages(self.date_start, self.date_end)
        return response.json()

    def save_messages(self):
        messages = self.get_messages()
        conversation = None
        for message_data in sorted(messages, key=itemgetter('csid')):
            dixa_message = DixaMessage.from_json(message_data)
            if dixa_message.is_phone_call:
                continue
            if not conversation or conversation.dixa_id != dixa_message.csid:
                conversation = self.get_or_create_conversation(
                    dixa_message.csid,
                    dixa_message.initial_channel,
                )
            try:
                self.get_or_create_message(dixa_message, conversation)
            except Exception as e:
                logger.exception(e)

    def get_or_create_conversation(
        self, dixa_id: str, channel: str
    ) -> ConversationForAI:
        conversation, _ = ConversationForAI.objects.get_or_create(
            dixa_id=dixa_id,
            channel=channel,
        )
        return conversation

    def get_or_create_message(
        self,
        dixa_message: DixaMessage,
        conversation: ConversationForAI,
    ) -> MessageForAI:
        text_without_stop_words = self.text_parser.remove_stopwords(
            dixa_message.cleaned_text, dixa_message.language
        )
        message, _ = MessageForAI.objects.get_or_create(
            uuid=dixa_message.id,
            defaults=dict(  # noqa: C408
                conversation=conversation,
                raw_data=dixa_message.raw_data,
                cleaned_text=dixa_message.cleaned_text,
                text_without_stop_words=text_without_stop_words,
                language=dixa_message.language,
                is_customer_message=dixa_message.is_customer_message,
                phone=dixa_message.from_phone_number,
                email=dixa_message.author_email,
                name=dixa_message.author_name,
                created_at=dixa_message.created_at,
            ),
        )
        if not conversation.email and message.is_customer_message and message.email:
            conversation.email = message.email
            conversation.save()

        return message


class DixaTextParser:
    def __init__(self):
        nltk.download('stopwords')
        nltk.download('punkt')

    @classmethod
    def clean_text(cls, text: str, is_email: bool) -> str:
        if is_email:
            text = EmailReplyParser.parse_reply(text)
        text = cls.remove_html_tags(text)
        text = cls.trim_email_ending(text)
        text = re.sub(r'https?://\S+', '', text)
        # Remove extra whitespace and newlines
        text = re.sub(r'\s+', ' ', text)  # collapse whitespace
        text = re.sub(r'\s+([?.!,;:])', r'\1', text)
        # Remove email addresses using a regex pattern
        text = re.sub(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,7}\b', '', text)
        text = re.sub(r'\[image: [^\]]+\]', '', text)
        return re.split(r'\\---------- Forwarded message ---------', text)[0]

    @classmethod
    def trim_email_ending(cls, text: str) -> str:
        ending_phrases = [
            'best regards',
            'kind regards',
            'mit freundlichen grüßen',
            'freundliche grüße',
            'cordialement',
            'bien cordialement',
            'avec mes salutations distinguées',
            'yours sincerely',
        ]
        pattern = (
            r'(?i)(?:'
            + '|'.join(re.escape(phrase) for phrase in ending_phrases)
            + r')([\s\S]*)$'
        )
        return re.sub(pattern, '', text).strip()

    @classmethod
    def detect_language(cls, text: str) -> str:
        if not text.strip() or text.isdigit():
            return 'unknown'
        try:
            language = detect(text)
        except LangDetectException:
            language = 'unknown'

        if language not in {'en', 'de', 'fr', 'es', 'it', 'pt', 'nl', 'pl'}:
            return 'unknown'
        return language

    @classmethod
    def remove_html_tags(cls, text: str) -> str:
        if not text:
            return ''
        clean_text = re.sub(r'<[^>]*>', '', text)
        return re.sub(
            r'&[#0-9a-zA-Z]+;', lambda x: html.unescape(x.group()), clean_text
        )

    def remove_stopwords(self, text: str, language: str) -> str:
        words = word_tokenize(text)
        stopwords_dict = {
            'en': set(stopwords.words('english')),
            'fr': set(stopwords.words('french')),
            'de': set(stopwords.words('german')),
        }
        if language in stopwords_dict:
            return ' '.join(
                [word for word in words if word.lower() not in stopwords_dict[language]]
            )
        return text
