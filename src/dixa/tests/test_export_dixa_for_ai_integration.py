from datetime import date
from unittest.mock import (
    Mock,
    patch,
)

import pytest

from dixa.models import (
    ConversationForAI,
    MessageForAI,
)
from dixa.services.export_dixa_for_ai import ExportConversationsToDB


@pytest.fixture
def mock_stopwords():
    def _side_effect(text, *args, **kwargs):
        return text

    with patch(
        'dixa.services.export_dixa_for_ai.DixaTextParser.remove_stopwords',
        side_effect=_side_effect,
    ) as mock:
        yield mock


@pytest.fixture
def mock_parse_text_init():
    with patch(
        'dixa.services.export_dixa_for_ai.DixaTextParser.__init__',
        return_value=None,
    ) as mock:
        yield mock


@pytest.fixture
def mock_fetch():
    with patch(
        'dixa.services.export_dixa_for_ai.DixaExportClient.fetch_messages',
        autospec=True,
    ) as mock:
        yield mock


@pytest.mark.django_db
class TestExportConversationsToDBIntegration:
    date_start = date(2024, 1, 1)
    date_end = date(2024, 1, 31)

    conversation_data = (
        {
            'csid': '123',
            'id': 'msg_001',
            'text': 'Hello, I have a problem with my order #12345. '
            + 'The delivery is late.',
            'initial_channel': 'email',
            'direction': 'inbound',
            'from_phone_number': '',
            'author_email': '<EMAIL>',
            'author_name': 'John Smith',
            'created_at': 1704067200000,  # 2024-01-01 00:00:00
        },
        {
            'csid': '123',
            'id': 'msg_002',
            'text': 'Thank you for contacting us. '
            + 'I will check your order status right away.',
            'initial_channel': 'email',
            'direction': 'outbound',
            'from_phone_number': '',
            'author_email': '<EMAIL>',
            'author_name': 'Agent Smith',
            'created_at': 1704067800000,  # 2024-01-01 00:10:00
        },
        {
            'csid': '123',
            'id': 'msg_003',
            'text': 'Your order has been delayed due to weather conditions. '
            + 'It will arrive tomorrow.',
            'initial_channel': 'email',
            'direction': 'outbound',
            'from_phone_number': '',
            'author_email': '<EMAIL>',
            'author_name': 'Agent Smith',
            'created_at': 1704068400000,  # 2024-01-01 00:20:00
        },
        {
            'csid': '456',
            'id': 'msg_004',
            'text': 'Can I change my shipping address?',
            'initial_channel': 'chat',
            'direction': 'inbound',
            'from_phone_number': '',
            'author_email': '<EMAIL>',
            'author_name': 'Jane Doe',
            'created_at': 1704154800000,  # 2024-01-02 00:20:00
        },
    )

    def test_complete_export_workflow_single_conversation(
        self,
        mock_parse_text_init,
        mock_stopwords,
        mock_fetch,
    ):
        mock_response = Mock()
        mock_response.json.return_value = self.conversation_data[:3]
        mock_fetch.return_value = mock_response

        exporter = ExportConversationsToDB(self.date_start, self.date_end)
        exporter.save_messages()

        assert ConversationForAI.objects.count() == 1
        conversation = ConversationForAI.objects.first()
        assert conversation.dixa_id == 123
        assert conversation.channel == 'email'
        assert conversation.email == '<EMAIL>'
        assert MessageForAI.objects.count() == 3

        customer_msg = MessageForAI.objects.get(uuid='msg_001')
        assert customer_msg.is_customer_message
        assert customer_msg.email == '<EMAIL>'
        assert customer_msg.name == 'John Smith'
        assert 'problem' in customer_msg.cleaned_text.lower()
        assert 'order' in customer_msg.text_without_stop_words.lower()

    def test_complete_export_workflow_multiple_conversations(
        self,
        mock_parse_text_init,
        mock_stopwords,
        mock_fetch,
    ):
        """Test complete export workflow for multiple conversations."""
        mock_response = Mock()
        mock_response.json.return_value = self.conversation_data
        mock_fetch.return_value = mock_response

        exporter = ExportConversationsToDB(self.date_start, self.date_end)
        exporter.save_messages()

        assert ConversationForAI.objects.count() == 2
