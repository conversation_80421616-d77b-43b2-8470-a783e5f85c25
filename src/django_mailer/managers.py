import operator

from functools import reduce

from django.db import models
from django.db.models import Q
from django.utils import timezone

from django_mailer import constants
from mailing.templates import (
    OrderCorrectionInvoiceMail,
    OrderInvoiceMail,
    OrderInvoiceProformaMail,
)


class MessageQuerySet(models.query.QuerySet):
    def exclude_invoicing(self):
        return self.exclude(reduce(operator.or_, self._invoicing_related()))

    def filter_invoicing(self):
        return self.filter(reduce(operator.or_, self._invoicing_related()))

    def _invoicing_related(self):
        translations = ['en', 'de', 'fr', 'es', 'nl']
        invoice_mails = [
            OrderInvoiceMail,
            OrderInvoiceProformaMail,
            OrderCorrectionInvoiceMail,
        ]
        return [
            Q(extra_headers__contains={'X-MC-Tags': f'{mail.__name__},{translation}'})
            for translation in translations
            for mail in invoice_mails
        ]


class QueueQuerySet(models.query.QuerySet):
    """
    A queryset which provides extra methods.

    """

    def exclude_future(self):
        """
        Exclude future time-delayed messages.

        """
        return self.exclude(date_queued__gt=timezone.now())

    def high_priority(self):
        """
        Return a QuerySet of high priority queued messages.

        """
        return self.filter(priority=constants.PRIORITY_HIGH)

    def normal_priority(self):
        """
        Return a QuerySet of normal priority queued messages.

        """
        return self.filter(priority=constants.PRIORITY_NORMAL)

    def low_priority(self):
        """
        Return a QuerySet of low priority queued messages.

        """
        return self.filter(priority=constants.PRIORITY_LOW)

    def non_deferred(self):
        """
        Return a QuerySet containing all non-deferred queued messages,
        excluding "future" messages.

        """
        return self.exclude_future().filter(deferred=None)

    def deferred(self):
        """
        Return a QuerySet of all deferred messages in the queue, excluding
        "future" messages.

        """
        return self.exclude_future().exclude(deferred=None)


class QueueManager(models.Manager):
    def retry_deferred(self, max_retries=None, new_priority=None):
        """
        Reset the deferred flag for all deferred messages so they will be
        retried.

        If ``max_retries`` is set, deferred messages which have been retried
        more than this many times will *not* have their deferred flag reset.

        If ``new_priority`` is ``None`` (default), deferred messages retain
        their original priority level. Otherwise all reset deferred messages
        will be set to this priority level.

        """
        queryset = self.deferred()
        if max_retries:
            queryset = queryset.filter(retries__lte=max_retries)
        count = queryset.count()
        update_kwargs = dict(deferred=None, retries=models.F('retries') + 1)  # noqa: C408
        if new_priority is not None:
            update_kwargs['priority'] = new_priority
        queryset.update(**update_kwargs)
        return count
