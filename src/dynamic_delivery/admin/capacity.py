import json
import logging

from django.contrib import admin
from django.forms import model_to_dict

from custom.enums import ShelfType
from dynamic_delivery.models import (
    ChangeLog,
    CustomCapacity,
    ManufactorCapacity,
    ProductionPauseWeek,
    ShelfCapacity,
    ShelfColorCapacity,
    ShelfColorCategorySubtract,
    ShelfColorRange,
    WeekCapacity,
)
from dynamic_delivery.services import (
    BacklogUpdater,
    ProductBacklogCounter,
)

from ..utils import (
    CAPACITY_INFO_FIELDS,
    DateTimeEncoder,
)
from .forms import ColorForm

logger = logging.getLogger('cstm')


class ShelfCapacityInline(admin.TabularInline):
    model = ShelfCapacity
    ordering = ('shelf_type', 'valid_from')
    readonly_fields = ('share_in_all', 'week_occupancy', 'backlog')
    fieldsets = (
        (
            'fields',
            {
                'fields': (
                    'shelf_type',
                    'capacity',
                    'margin_of_error',
                    'backlog',
                    'share_in_all',
                    'week_occupancy',
                    'valid_from',
                )
            },
        ),
    )
    extra = 1


class ManufactorCapacityInline(admin.TabularInline):
    model = ManufactorCapacity
    ordering = ('manufactor', 'valid_from')
    readonly_fields = ('share_in_production', 'week_occupancy', 'backlog')
    fieldsets = (
        (
            'fields',
            {
                'fields': (
                    'manufactor',
                    'capacity',
                    'margin_of_error',
                    'backlog',
                    'share_in_production',
                    'week_occupancy',
                    'valid_from',
                )
            },
        ),
    )
    extra = 1


class ProductionWeekOffInline(admin.TabularInline):
    model = ProductionPauseWeek
    ordering = ('year', 'week')
    extra = 1
    readonly_fields = ('weeks_left_to_pause',)
    fields = ('year', 'week', 'weeks_left_to_pause')


class ChangeLogMixin:
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.diff = {}
        self.inline_count = 0

    def model_to_dict_with_readable_choices(self, model_instance):
        object_dict = model_to_dict(model_instance)

        for field in model_instance._meta.get_fields():
            if hasattr(field, 'choices') and field.choices:
                display_method = f'get_{field.name}_display'
                if hasattr(model_instance, display_method):
                    object_dict[field.name] = getattr(model_instance, display_method)()

        return object_dict

    def _calculate_diff(self, old_instance, new_instance):
        old_values = self.model_to_dict_with_readable_choices(old_instance)
        new_values = self.model_to_dict_with_readable_choices(new_instance)

        return {
            field: {'old': old_values[field], 'new': new_values[field]}
            for field in old_values
            if old_values[field] != new_values[field] or field in CAPACITY_INFO_FIELDS
        }

    def save_model(self, request, obj, form, change):
        try:
            self.save_change_log_for_form(change, obj, request)
        except Exception:
            logger.exception(
                f'Could not save ChangeLog for model:'
                f' [{self.model._meta.model_name} id: {obj.pk}'
            )

        super().save_model(request, obj, form, change)

    def save_change_log_for_form(self, change, obj, request):
        if change:
            old_obj = self.model.objects.get(pk=obj.pk)
            self.diff = {
                self.model._meta.model_name: self._calculate_diff(old_obj, obj)
            }

        if len(self.inlines) == 0 and self.diff:
            ChangeLog.objects.create(
                user=request.user,
                model_name=self.model._meta.model_name,
                changes=json.dumps(self.diff, cls=DateTimeEncoder),
            )

    def save_formset(self, request, form, formset, change):
        try:
            self.save_changelog_for_formset(formset, request)
        except Exception:
            logger.exception(
                f'Could not save formset ChangeLog for model:'
                f' [{self.model._meta.model_name} id: {form.instance.pk}'
            )

        formset.save()

    def save_changelog_for_formset(self, formset, request):
        self.inline_count += 1
        for inline_form in formset.forms:
            if inline_form.has_changed():
                if inline_form.instance.pk:
                    old_instance = type(inline_form.instance).objects.get(
                        pk=inline_form.instance.pk
                    )
                    inline_diff = self._calculate_diff(
                        old_instance, inline_form.instance
                    )
                    inline_model_name = inline_form.instance._meta.model_name
                    if inline_model_name not in self.diff:
                        self.diff[inline_model_name] = []
                    self.diff[inline_model_name].append(inline_diff)
                inline_form.instance.save()
        if self.inline_count == len(self.inlines):
            if self.diff:
                ChangeLog.objects.create(
                    user=request.user,
                    model_name=self.model._meta.model_name,
                    changes=json.dumps(self.diff, cls=DateTimeEncoder),
                )
            self.diff = {}
            self.inline_count = 0


class WeekCapacityAdmin(ChangeLogMixin, admin.ModelAdmin):
    change_form_template = 'admin/week_capacity_change_form.html'

    list_display = (
        'capacity',
        'margin_of_error',
        'backlog',
        'sent_to_production',
        'share_in_production',
    )
    readonly_fields = (
        'backlog',
        'week_occupancy',
        'sent_to_production',
        'share_in_production',
    )
    fieldsets = (
        ('Setup', {'fields': ('capacity', 'margin_of_error')}),
        (
            'Stats',
            {
                'fields': (
                    'backlog',
                    'sent_to_production',
                    'share_in_production',
                    'week_occupancy',
                )
            },
        ),
    )
    inlines = (
        ShelfCapacityInline,
        ManufactorCapacityInline,
        ProductionWeekOffInline,
    )

    actions = ('update_capacities',)

    def has_add_permission(self, request):
        return False

    def has_delete_permission(self, request, obj=None):
        return False

    @staticmethod
    def update_capacities(model_admin, request, queryset):
        updater = BacklogUpdater()
        updater.update()


class ShelfTypeColorMixin:
    form = ColorForm

    def changeform_view(self, request, object_id=None, form_url='', extra_context=None):
        shelf_types_to_colors = {
            shelf_type.value: [list(i) for i in shelf_type.colors.choices()]
            for shelf_type in ShelfType
        }
        extra_context = extra_context or {}
        extra_context['shelf_types_to_colors'] = json.dumps(shelf_types_to_colors)
        return super().changeform_view(
            request,
            object_id=object_id,
            form_url=form_url,
            extra_context=extra_context,
        )


class CustomCapacityAdmin(
    ChangeLogMixin,
    ShelfTypeColorMixin,
    admin.ModelAdmin,
):
    change_form_template = 'admin/custom_capacity_change_form.html'
    list_display = (
        'shelf_type',
        'is_desk',
        'get_color_display',
        'all_colors',
        'feature',
        'ship_in_range',
        'active',
    )


class ShelfColorCapacityAdmin(ChangeLogMixin, ShelfTypeColorMixin, admin.ModelAdmin):
    change_form_template = 'admin/shelf_color_capacity_change_form.html'

    ordering = ('shelf_type', 'color', 'valid_from')
    readonly_fields = ('share_in_all', 'week_occupancy', 'backlog')
    list_display = (
        'shelf_type',
        'get_color_display',
        'capacity',
        'margin_of_error',
        'backlog',
        'week_occupancy',
        'valid_from',
    )
    fields = (
        'shelf_type',
        'color',
        'capacity',
        'margin_of_error',
        'backlog',
        'share_in_all',
        'week_occupancy',
        'valid_from',
    )
    actions = ('update_capacities_shelf_color_capacities',)

    @staticmethod
    @admin.action(description='Update capacities for shelf color capacities')
    def update_capacities_shelf_color_capacities(model_admin, request, queryset):
        updater = BacklogUpdater()
        updater.shelf_color_capacities_update()

    def save_model(self, request, obj, form, change):
        super().save_model(request, obj, form, change)
        obj.refresh_products_no(ProductBacklogCounter())


class ShelfColorRangeAdmin(ChangeLogMixin, ShelfTypeColorMixin, admin.ModelAdmin):
    change_form_template = 'admin/shelf_color_range_change_form.html'

    ordering = ('shelf_type', 'color')
    list_display = (
        'shelf_type',
        'get_color_display',
        'min_ship_in_range',
        'max_ship_in_range',
    )
    fields = (
        'shelf_type',
        'color',
        'min_ship_in_range',
        'max_ship_in_range',
    )


class ShelfColorCategorySubtractAdmin(
    ChangeLogMixin,
    ShelfTypeColorMixin,
    admin.ModelAdmin,
):
    change_form_template = 'admin/shelf_color_range_change_form.html'

    ordering = ('shelf_type', 'furniture_category')
    list_display = (
        'shelf_type',
        'furniture_category',
        'get_color_display',
        'weeks_to_subtract',
    )


class ChangeLogAdmin(admin.ModelAdmin):
    list_display = ('created_at', 'user', 'model_name')
    list_filter = ('created_at', 'model_name')
    search_fields = ('user__username', 'model_name')
    readonly_fields = ('user', 'created_at', 'model_name')
    change_form_template = 'admin/change_log_change_form.html'

    def change_view(self, request, object_id, form_url='', extra_context=None):
        obj = self.get_object(request, admin.utils.unquote(object_id))
        if obj:
            extra_context = extra_context or {}
            git_diff = self.convert_to_git_diff(json.loads(obj.changes))
            extra_context['git_diff'] = json.dumps(git_diff)
        return super().change_view(request, object_id, form_url, extra_context)

    def convert_to_git_diff(self, changes):
        all_diffs = ''
        for model_name, model_changes in changes.items():
            all_diffs += self.format_git_diff(model_name, model_changes)
        return all_diffs

    def format_git_diff(self, model_name, model_changes):
        diff_str = f'diff --git a/{model_name} b/{model_name}\n'
        diff_str += 'index 0000000..1111111\n'

        diff_str += 'index 0000000..1111111 100644\n'
        diff_str += f'--- a/{model_name}\n'
        diff_str += f'+++ b/{model_name}\n'
        diff_str += '@@ -1,2 +1,2 @@\n'

        if isinstance(model_changes, list):
            for change in model_changes:
                for field, value in change.items():
                    diff_str += f"-{field}: {value['old']!s}\n"
                    diff_str += f"+{field}: {value['new']!s}\n"
        else:
            for field, value in model_changes.items():
                diff_str += f"-{field}: {value['old']!s}\n"
                diff_str += f"+{field}: {value['new']!s}\n"

        return diff_str

    def has_add_permission(self, request, obj=None):
        return False

    def has_change_permission(self, request, obj=None):
        return False

    def has_delete_permission(self, request, obj=None):
        return False


admin.site.register(ChangeLog, ChangeLogAdmin)
admin.site.register(WeekCapacity, WeekCapacityAdmin)
admin.site.register(CustomCapacity, CustomCapacityAdmin)
admin.site.register(ShelfColorCapacity, ShelfColorCapacityAdmin)
admin.site.register(ShelfColorRange, ShelfColorRangeAdmin)
admin.site.register(ShelfColorCategorySubtract, ShelfColorCategorySubtractAdmin)
