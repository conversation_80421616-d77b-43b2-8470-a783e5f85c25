import factory
import pytest


@pytest.fixture
def promotion_with_images(
    promotion_config_factory,
    promotion_picture_factory,
    country_factory,
):
    germany = country_factory(germany=True)
    promotion = promotion_config_factory(
        desktop_picture=promotion_picture_factory(
            image=factory.django.ImageField(),
            image_webp=factory.django.ImageField(),
            image_base64_thumbnail=factory.Faker('pystr'),
        ),
        mobile_picture=promotion_picture_factory(
            image=factory.django.ImageField(),
            image_webp=factory.django.ImageField(),
            image_base64_thumbnail=factory.Faker('pystr'),
        ),
    )
    promotion.enabled_regions.add(germany.region)

    return promotion
