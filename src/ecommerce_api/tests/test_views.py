from unittest.mock import (
    MagicMock,
    patch,
)

from django.conf import settings
from django.test import override_settings
from django.urls import reverse
from rest_framework import status

import pytest

from custom.context_processors.settings_cp import (
    get_user_email_hash,
    get_user_email_hash_no_hmac,
    get_user_hash,
)
from custom.enums import (
    LanguageEnum,
    ShelfType,
)
from ecommerce_api.enums import SurveyType
from ecommerce_api.serializers import ColorListSerializer
from gallery.enums import FurnitureCategory
from gallery.models import SampleBox
from vouchers.enums import VoucherType


@pytest.fixture
def active_promotion(
    country_factory,
    promotion_config_factory,
    promotion_config_copy_factory,
    countdown_config_factory,
):
    """This is a duplicate of promotions/tests/conftest.py::active_promotion

    TODO: Remove once global endpoint is removed.
    """
    promotion_config = promotion_config_factory(
        ribbon_enabled=True,
        ribbon_text_color='#000000',
        ribbon_background_color='#FFFFFF',
        cart_ribbon_enabled=True,
        hp_promo=True,
        hp_promo_video=True,
        theme='Test theme',
        theme_light=True,
    )
    promotion_config.enabled_regions.add(country_factory(germany=True).region)
    promotion_config.enabled_regions.add(country_factory(france=True).region)
    promotion_config.enabled_regions.add(country_factory(spain=True).region)
    promotion_config.enabled_regions.add(country_factory(netherlands=True).region)

    for lang, label in LanguageEnum.choices:
        promotion_config_copy_factory(
            config=promotion_config,
            language=lang,
            cart_ribbon_copy_header_1=f'{label} cart ribbon header',
            cart_ribbon_copy_header_mobile_1=f'{label} cart ribbon header mobile',
        )

    countdown_config_factory(promotion_config=promotion_config)
    return promotion_config


@pytest.fixture
def german_authenticated_user(country_factory, user_profile_factory, api_client):
    country_factory(germany=True)
    user = user_profile_factory(german=True).user
    api_client.force_login(user)
    return user, api_client


@pytest.mark.django_db
class TestGlobalAPIView:
    url = reverse('ecommerce:global')

    def test_unauthenticated_user_response(self, api_client, region_factory):
        region_factory(other=True)
        response = api_client.get(self.url)

        assert response.status_code == status.HTTP_200_OK

    def test_no_active_promotion_response(
        self,
        settings,
        german_authenticated_user,
        cart_factory,
    ):
        settings.T03_REGION_KEYS = []
        settings.ASSEMBLY_REGION_KEYS = []

        user, api_client = german_authenticated_user
        cart = cart_factory(owner=user)

        request = MagicMock(user=user)
        response = api_client.get(self.url)

        expected_response = {
            'userId': user.id,
            'userLanguage': user.profile.language,
            'userType': user.profile.user_type,
            'userHashId': get_user_hash(request),
            'userHashEmail': get_user_email_hash(request),
            'userHashEmailNoHmac': get_user_email_hash_no_hmac(request),
            'isSignedIn': True,
            'cartItemsCount': 2,
            'hasT03': False,
            'libraryItemsCount': 0,
            't03Available': False,
            'assemblyAvailable': False,
            'regionCode': 'DE',
            'regionName': 'germany',
            'currencyCode': 'EUR',
            'countryLocale': 'de_DE',
            'promoIsActive': False,
            'newsletterVoucherValue': '300€',
            'abIds': '',
            'abTestsList': [],
            'featureFlagsIds': '',
            'featureFlagsList': [],
            'cartRibbon': {
                'enabled': False,
                'header': None,
                'headerMobile': None,
            },
            'ribbon': {
                'enabled': False,
                'textColor': '',
                'backgroundColor': '',
                'lines': [],
            },
            'countdown': None,
            'reviewsAverageScore': None,
            'reviewsCount': None,
            'isSalesEnabled': True,
            'waitingListTokenActive': None,
            'waitingListTokenExpired': None,
            'samplePrice': int(
                SampleBox().get_regionalized_price(region=user.profile.region)
            ),
            'orderId': cart.order.id,
            'cartId': cart.id,
            'globalSessionId': api_client.session.session_key,
        }

        assert response.status_code == status.HTTP_200_OK
        assert response.json() == expected_response

    @pytest.mark.parametrize('language, locale_name', LanguageEnum.choices)  # noqa: PT006
    def test_active_promotion_supported_language_response(
        self,
        language,
        locale_name,
        settings,
        cart_factory,
        german_authenticated_user,
        active_promotion,
    ):
        settings.T03_REGION_KEYS = ['germany']
        settings.ASSEMBLY_REGION_KEYS = ['germany']
        settings.USE_TZ = False

        user, api_client = german_authenticated_user
        cart = cart_factory(owner=user)

        request = MagicMock(user=user)
        response = api_client.get(
            self.url, HTTP_REFERER=f'https://www.tylko.com/{language}'
        )

        expected_response = {
            'userId': user.id,
            'userLanguage': user.profile.language,
            'userType': user.profile.user_type,
            'userHashId': get_user_hash(request),
            'userHashEmail': get_user_email_hash(request),
            'userHashEmailNoHmac': get_user_email_hash_no_hmac(request),
            'isSignedIn': True,
            'cartItemsCount': 2,
            'hasT03': False,
            'libraryItemsCount': 0,
            't03Available': True,
            'assemblyAvailable': True,
            'regionCode': 'DE',
            'regionName': 'germany',
            'currencyCode': 'EUR',
            'countryLocale': 'de_DE',
            'promoIsActive': True,
            'newsletterVoucherValue': '300€',
            'abIds': '',
            'abTestsList': [],
            'featureFlagsIds': '',
            'featureFlagsList': [],
            'cartRibbon': {
                'enabled': True,
                'header': f'{locale_name} cart ribbon header',
                'headerMobile': f'{locale_name} cart ribbon header mobile',
            },
            'ribbon': {
                'enabled': True,
                'textColor': '#000000',
                'backgroundColor': '#FFFFFF',
                'lines': [],
            },
            'countdown': {
                'showOnPdp': True,
                'startDate': active_promotion.countdown.start_date.isoformat(),
                'chevronTheme': 1,
                'textColor': '#0000000',
                'endDate': active_promotion.promotion.end_date.isoformat(),
            },
            'reviewsAverageScore': None,
            'reviewsCount': None,
            'isSalesEnabled': True,
            'waitingListTokenActive': None,
            'waitingListTokenExpired': None,
            'samplePrice': SampleBox().get_regionalized_price(
                region=user.profile.region,
            ),
            'orderId': cart.order.id,
            'cartId': cart.id,
            'globalSessionId': api_client.session.session_key,
        }

        assert response.status_code == status.HTTP_200_OK
        assert response.json() == expected_response

    def test_active_promotion_unsupported_language_response(
        self,
        german_authenticated_user,
        active_promotion,
    ):
        user, api_client = german_authenticated_user  # noqa: RUF059

        response = api_client.get(self.url, HTTP_REFERER='https://www.tylko.com/bla/')

        default_locale = LanguageEnum(settings.LANGUAGE_CODE).label

        assert response.status_code == status.HTTP_200_OK
        assert response.json()['cartRibbon'] == {
            'enabled': True,
            'header': f'{default_locale} cart ribbon header',
            'headerMobile': f'{default_locale} cart ribbon header mobile',
        }
        assert response.json()['ribbon'] == {
            'enabled': True,
            'textColor': '#000000',
            'backgroundColor': '#FFFFFF',
            'lines': [],
        }

    def test_item_discount_smoke_test(
        self,
        api_client,
        user,
        promotion_config_factory,
        voucher_factory,
        item_discount_factory,
    ):
        voucher = voucher_factory(
            kind_of=VoucherType.PERCENTAGE,
            amount_starts=0,
            value=10,
            amount_limit=200000.00,
        )
        voucher.discounts.add(
            item_discount_factory(
                value=20,
                furniture_category=FurnitureCategory.VINYL_STORAGE.value,
            )
        )
        voucher.discounts.add(item_discount_factory(value=10))
        promotion_config_factory(
            grid_show_category_promotion=True,
            promotion__promo_code=voucher,
            promotion__active=True,
        )
        api_client.force_authenticate(user)
        response = api_client.get(self.url)
        assert response.status_code == status.HTTP_200_OK

    def test_global_session_id_doesnt_change_when_user_logs_in(self, user, api_client):
        session_id = api_client.session.session_key
        response_anonymous = api_client.get(self.url)
        assert response_anonymous.status_code == status.HTTP_200_OK
        assert response_anonymous.json()['userId'] is None
        assert response_anonymous.json()['globalSessionId'] == session_id

        api_client.force_login(user)
        response_authenticated = api_client.get(self.url)
        assert response_authenticated.status_code == status.HTTP_200_OK
        assert response_authenticated.json()['userId'] == user.id
        assert response_authenticated.json()['globalSessionId'] == session_id

    def test_global_session_id_doesnt_change_when_user_logs_out(self, user, api_client):
        api_client.force_login(user)
        session_id = api_client.session.session_key
        response_authenticated = api_client.get(self.url)
        assert response_authenticated.status_code == status.HTTP_200_OK
        assert response_authenticated.json()['userId'] == user.id
        assert response_authenticated.json()['globalSessionId'] == session_id

        api_client.post(reverse('logout'))
        assert api_client.session.session_key != session_id

        response_anonymous = api_client.get(self.url)
        assert response_anonymous.status_code == status.HTTP_200_OK
        assert response_anonymous.json()['userId'] is None
        assert response_anonymous.json()['globalSessionId'] == session_id


@pytest.mark.django_db
class TestColorAPIView:
    url = reverse('ecommerce:colors')

    def test_color_view(self, api_client):
        response = api_client.get(self.url)

        assert response.status_code == status.HTTP_200_OK
        assert response.data == ColorListSerializer(list(ShelfType), many=True).data


@pytest.mark.django_db
class TestSurveyView:
    @pytest.mark.parametrize(
        'survey_type, expected_status',  # noqa: PT006
        [
            (SurveyType.CHECKOUT, status.HTTP_401_UNAUTHORIZED),
            (SurveyType.CONFIGURATOR, status.HTTP_400_BAD_REQUEST),
        ],
    )
    def test_permissions(self, api_client, survey_type, expected_status):
        url = reverse('ecommerce:survey')
        response = api_client.post(
            url, data={'type': survey_type, 'body': {}}, format='json'
        )
        assert response.status_code == expected_status

    @override_settings(
        BIGQUERY_SURVEY_SPACE='test_space', BIGQUERY_CHECKOUT_SURVEY_TABLE='test_table'
    )
    def test_checkout_survey(self, api_client, user, order_factory):
        api_client.force_authenticate(user)
        order = order_factory(owner=user)
        payload = {
            'type': SurveyType.CHECKOUT,
            'body': {
                'orderId': order.id,
                'score': 5,
                'feedback': 'Great!',
                'lang': 'en',
            },
        }
        with patch(
            'ecommerce_api.serializers.export_survey_data_to_bigquery.delay'
        ) as mock_task:
            url = reverse('ecommerce:survey')
            response = api_client.post(url, data=payload, format='json')
            assert response.status_code == status.HTTP_200_OK
            assert response.data == 'Data has been sent'
            mock_task.assert_called_once()

    @override_settings(
        BIGQUERY_SURVEY_SPACE='test_space', BIGQUERY_CES_SURVEY_TABLE='test_table'
    )
    @pytest.mark.parametrize(
        'furniture',
        ['jetty', 'sotty', 'watty'],
    )
    def test_configurator_survey(self, api_client, furniture, request):
        item = request.getfixturevalue(furniture)
        payload = {
            'type': SurveyType.CONFIGURATOR,
            'body': {
                'itemId': item.id,
                'itemContentType': furniture,
                'score': 4,
                'feedback': 'Nice tool',
                'lang': 'en',
            },
        }
        with patch(
            'ecommerce_api.serializers.export_survey_data_to_bigquery.delay'
        ) as mock_task:
            url = reverse('ecommerce:survey')
            response = api_client.post(url, data=payload, format='json')
            assert response.status_code == status.HTTP_200_OK
            assert mock_task.called
