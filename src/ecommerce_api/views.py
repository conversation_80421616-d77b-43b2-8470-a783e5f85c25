from django.db.models import (
    QuerySet,
    prefetch_related_objects,
)
from django.http import Http404
from rest_framework import (
    permissions,
    status,
)
from rest_framework.exceptions import ValidationError
from rest_framework.generics import (
    CreateAPIView,
    RetrieveAPIView,
)
from rest_framework.response import Response
from rest_framework.views import APIView

from djangorestframework_camel_case.parser import Camel<PERSON><PERSON><PERSON><PERSON>NPars<PERSON>
from djangorestframework_camel_case.render import Camel<PERSON><PERSON><PERSON><PERSON><PERSON>ender<PERSON>

from abtests.utils import get_sorted_active_ab_tests_data
from carts.services.cart_service import CartService
from custom.enums import ShelfType
from ecommerce_api.enums import SurveyType
from ecommerce_api.mixins import EcommerceAPIMixin
from ecommerce_api.serializers import (
    CheckoutSurveySerializer,
    ColorListSerializer,
    ConfiguratorSurveySerializer,
    GlobalVariablesSerializer,
)
from gallery.enums import FurnitureCategory
from reviews.choices import Spaces
from reviews.models import Review
from reviews.queries import get_pdp_reviews
from reviews.serializers import GeneralReviewSerializer


class GlobalAPIView(EcommerceAPIMixin, APIView):
    serializer_class = GlobalVariablesSerializer

    @property
    def cart(self):
        if self.request.user.is_anonymous:
            return None

        cart = CartService.get_cart(self.request.user)
        prefetch_related_objects([cart], 'items', 'items__cart_item')

        return cart

    def get(self, request):
        ab_tests, feature_flags = get_sorted_active_ab_tests_data(request, self.region)
        context = {
            'request': request,
            'region': self.region,
            'language': self.language,
            'cart': self.cart,
            'ab_tests': ab_tests,
            'feature_flags': feature_flags,
            'promotion_copy_data': (
                self.promotion_config.get_copy(self.language, self.region)
                if self.promotion_config
                else {}
            ),
        }
        serializer = self.serializer_class(self.promotion_config, context=context)

        return Response(serializer.data)


class SpacesLandingPageApi(EcommerceAPIMixin, RetrieveAPIView):
    serializer_class = GeneralReviewSerializer

    def get_queryset(self, space: Spaces = None) -> QuerySet[Review]:
        spaces_reviews = Review.latest_objects.filter(
            featured_on_spaces=True,
            space=space,
            translations__language=self.language,
        )
        if spaces_review_count := spaces_reviews.count():
            return spaces_reviews[: min(spaces_review_count, 12)]

        return get_pdp_reviews(
            shelf_type=ShelfType.TYPE01,
            furniture_category=FurnitureCategory.BOOKCASE,
            language=self.language,
        )

    def retrieve(self, request, space=None, *args, **kwargs):
        space = self._validate_space(space)
        queryset = self.get_queryset(space=space)
        serializer = self.serializer_class(queryset)
        return Response(serializer.data)

    @staticmethod
    def _validate_space(space: str) -> Spaces:
        try:
            return Spaces.get_space_by_name(space)
        except AttributeError:
            raise Http404


class ColorView(APIView):
    permission_classes = (permissions.AllowAny,)
    renderer_classes = [CamelCaseJSONRenderer]

    def get(self, request):
        return Response(ColorListSerializer(list(ShelfType), many=True).data)


class SurveyView(CreateAPIView):
    parser_classes = [CamelCaseJSONParser]

    def get_permissions(self):
        if self.survey_type == SurveyType.CHECKOUT:
            return (permissions.IsAuthenticated(),)
        return (permissions.AllowAny(),)

    def get_serializer_class(self):
        if self.survey_type == SurveyType.CHECKOUT:
            return CheckoutSurveySerializer
        elif self.survey_type == SurveyType.CONFIGURATOR:
            return ConfiguratorSurveySerializer
        else:
            raise ValidationError(
                f'Survey type must be `{SurveyType.CHECKOUT}` '
                f'or `{SurveyType.CONFIGURATOR}`'
            )

    def create(self, request, *args, **kwargs):
        try:
            serializer = self.get_serializer(data=request.data.get('body', {}))
        except ValidationError as e:
            return Response(str(e), status=status.HTTP_400_BAD_REQUEST)
        serializer.is_valid(raise_exception=True)
        serializer.create_task(user=self.request.user)

        return Response('Data has been sent')

    @property
    def survey_type(self):
        return self.request.data.get('type')
