from datetime import (
    date,
    datetime,
)
from unittest.mock import (
    Mock,
    patch,
)

import pytest

from pytest_cases import (
    case,
    parametrize_with_cases,
)

from events.domain_events.marketing_events import (
    DeliveryAnniversaryEvent,
    WinBack100Event,
    WinBack120Event,
    WinBack300Event,
)
from events.tasks import (
    _emit_win_back_event_for_class,
    emit_win_back_events,
)


class DeliveryDateCalculationCases:
    @case(tags=['winback'])
    def case_winback_100_event(self):
        return WinBack100Event, datetime(2024, 1, 15), date(2023, 10, 7)

    @case(tags=['winback'])
    def case_winback_120_event(self):
        return WinBack120Event, datetime(2024, 1, 15), date(2023, 9, 17)

    @case(tags=['winback'])
    def case_winback_300_event(self):
        return WinBack300Event, datetime(2024, 1, 15), date(2023, 3, 21)

    @case(tags=['anniversary'])
    def case_delivery_anniversary_event(self):
        return DeliveryAnniversaryEvent, datetime(2024, 1, 15), date(2023, 1, 15)


@pytest.mark.django_db
class TestEmitWinBackEvents:
    @patch('events.tasks._emit_win_back_event_for_class')
    def test_emit_win_back_events_calls_helper_for_each_event_class(
        self, mock_emit_helper
    ):
        emit_win_back_events()

        assert mock_emit_helper.call_count == 4
        mock_emit_helper.assert_any_call(WinBack100Event)
        mock_emit_helper.assert_any_call(WinBack120Event)
        mock_emit_helper.assert_any_call(WinBack300Event)
        mock_emit_helper.assert_any_call(DeliveryAnniversaryEvent)

    @parametrize_with_cases(
        'event_class, current_date, expected_delivery_date',
        cases=DeliveryDateCalculationCases,
    )
    @patch('events.tasks.get_delivered_orders_queryset')
    @patch('events.tasks.timezone')
    def test_emit_win_back_event_for_class_calculates_correct_delivery_date(
        self,
        mock_timezone,
        mock_get_queryset,
        event_class,
        current_date,
        expected_delivery_date,
    ):
        mock_timezone.now.return_value = current_date
        mock_get_queryset.return_value = Mock(spec=['__iter__'])

        with patch('events.tasks.Paginator') as mock_paginator:
            mock_paginator.return_value.page_range = []
            _emit_win_back_event_for_class(event_class)
            mock_get_queryset.assert_called_once_with(expected_delivery_date)

    @pytest.mark.parametrize(
        'event_class',
        [WinBack100Event, WinBack120Event, WinBack300Event, DeliveryAnniversaryEvent],
    )
    @patch('events.tasks.get_delivered_orders_queryset')
    def test_emit_win_back_event_for_class_creates_events_for_orders(
        self, mock_get_queryset, order_factory, user_factory, event_class
    ):
        user_1 = user_factory()
        user_2 = user_factory()
        order_1 = order_factory(owner=user_1, email='<EMAIL>')
        order_2 = order_factory(owner=user_2, email='<EMAIL>')

        mock_get_queryset.return_value = [order_1, order_2]
        with patch.object(
            event_class, '__init__', return_value=None
        ) as mock_event_init:
            _emit_win_back_event_for_class(event_class)

            assert mock_event_init.call_count == 2
            mock_event_init.assert_any_call(user=user_1, email='<EMAIL>')
            mock_event_init.assert_any_call(user=user_2, email='<EMAIL>')
