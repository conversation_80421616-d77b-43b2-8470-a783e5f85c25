import csv

from tempfile import NamedTemporaryFile
from typing import Iterator

from django.core.files.base import ContentFile

from feeds.services.data_exporters.base import BaseDataExporter


class CSVDataExporter(BaseDataExporter):
    def export(
        self, data_iterator: Iterator, headers: list, filename: str
    ) -> ContentFile:
        with NamedTemporaryFile(
            mode='w+', delete=True, newline='', suffix='.csv'
        ) as temp_file:
            writer = csv.DictWriter(temp_file, fieldnames=headers)
            writer.writeheader()

            for chunk in data_iterator:
                writer.writerows(chunk)

            temp_file.flush()
            temp_file.seek(0)

            with open(temp_file.name, 'rb') as f:
                return ContentFile(f.read(), name=f'{filename}.csv')
