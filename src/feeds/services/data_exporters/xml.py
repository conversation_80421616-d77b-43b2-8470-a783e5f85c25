import xml.etree.ElementTree as ET  # noqa: S405

from tempfile import NamedTemporaryFile
from typing import Iterator

from django.core.files.base import ContentFile
from django.utils import timezone

from feeds.services.data_exporters.base import BaseDataExporter


class XmlDataExporter(BaseDataExporter):
    def export(
        self, data_iterator: Iterator, headers: list, filename: str
    ) -> ContentFile:
        root = self._create_root_element()
        self._add_feed_metadata(root)
        self._add_data_entries(root, data_iterator)
        tree = ET.ElementTree(root)
        with NamedTemporaryFile(mode='w+b', delete=True, suffix='.xml') as temp_file:
            tree.write(temp_file, encoding='utf-8', xml_declaration=True)
            temp_file.seek(0)
            return ContentFile(temp_file.read(), name=f'{filename}.xml')

    def _create_root_element(self) -> ET.Element:
        return ET.Element(
            'feed',
            {
                'xmlns:g': 'http://base.google.com/ns/1.0',
                'xmlns:c': 'http://base.google.com/cns/1.0',
            },
        )

    def _add_feed_metadata(self, root: ET.Element) -> None:
        ET.SubElement(root, 'title').text = 'Tylko'
        ET.SubElement(root, 'link', {'rel': 'self', 'href': 'https://www.tylko.com'})
        ET.SubElement(root, 'updated').text = timezone.now().strftime(
            '%Y-%m-%dT%H:%M:%S'
        )

    def _add_data_entries(self, root: ET.Element, data_iterator: Iterator) -> None:
        for chunk in data_iterator:
            for row in chunk:
                item_elem = ET.SubElement(root, 'entry')
                self._add_row_data(item_elem, row)

    def _add_row_data(self, item_elem: ET.Element, row: dict) -> None:
        for key, value in row.items():
            child = ET.SubElement(item_elem, key)
            child.text = str(value if value is not None else '')
