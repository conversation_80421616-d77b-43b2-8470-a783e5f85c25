from feeds.models import Feed
from feeds.serializers import SyneriseSerializer
from feeds.services.data_exporters.xml import XmlDataExporter
from feeds.services.file_generation.base import BaseExportService


class SyneriseExportService(BaseExportService):
    serializer_class = SyneriseSerializer

    def __init__(self, feed: Feed, data_exporter=None) -> None:
        super().__init__(feed, data_exporter)
        self.feed = feed
        self.data_exporter = data_exporter or XmlDataExporter()
