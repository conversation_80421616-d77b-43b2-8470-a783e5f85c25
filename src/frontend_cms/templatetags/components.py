import base64

from django import template
from django.urls import reverse_lazy
from django.utils.translation import gettext_lazy as _

from django_user_agents.templatetags.user_agents import is_ab_test
from gallery.enums import FurnitureCategory

register = template.Library()


@register.inclusion_tag('components/placeholder-svg.html', takes_context=True)
def placeholder_svg(context, width='200', height='200', img=True):
    svg = f'''
        <svg
        width="{width}"
        height="{height}"
        viewBox="0 0 {width} {height}"
        fill="none"
        xmlns="http://www.w3.org/2000/svg">
            <style type="text/css">
                @keyframes grey-filed {{
                    0% {{ opacity: 1; }}
                    42% {{ opacity: 0.6; }}
                    100% {{ opacity: 1; }}
                }}
                .animate-grey-field {{
                    animation-name: grey-filed;
                    animation-duration: 0.7s;
                    animation-timing-function: ease-in-out;
                    animation-iteration-count: infinite;
                }}
            </style>
            <rect
            width="{width}"
            height="{height}"
            fill="#D7DADB"
            class="animate-grey-field"/>
        </svg>
    '''
    svg_base64 = base64.b64encode(svg.encode()).decode()
    return {
        'width': width,
        'height': height,
        'img': img,
        'svg_base64': svg_base64,
    }


# cookie - session, long
@register.inclusion_tag('components/popup/popup_base.html', takes_context=True)
def popup(
    context,
    content_name,
    is_modal=False,
    auto_open=False,
    exit_trap=False,
    open_delay=0,
    show_close=True,
    cookie='',
    cookie_repeat=1,
    cookie_interval=0,
    dataLayer='',  # noqa: N803
    dataLayerAdditional='',  # noqa: N803
    onCloseDataLayer='',  # noqa: N803
    its_webview=False,
    en_id='',
    de_id='',
    fr_id='',
    extra_class='',
):
    return {
        'content_name': content_name,
        'content_template': 'components/popup/popup_' + content_name + '.html',
        'is_modal': is_modal,
        'auto_open': auto_open,
        'exit_trap': exit_trap,
        'open_delay': open_delay,
        'show_close': show_close,
        'cookie': cookie,
        'cookie_repeat': cookie_repeat,
        'cookie_interval': cookie_interval,
        'dataLayer': dataLayer,
        'dataLayerAdditional': dataLayerAdditional,
        'onCloseDataLayer': onCloseDataLayer,
        # deprecated, use NEWSLETTER_VOUCHER_VALUE
        'NEWSLETTER_VOUCHER_VALUE_STR': context.get('NEWSLETTER_VOUCHER_VALUE_STR'),
        'NEWSLETTER_VOUCHER_VALUE': context.get('NEWSLETTER_VOUCHER_VALUE'),
        'VOUCHER_VALUE_R': context.get('VOUCHER_VALUE_R', None),
        'VOUCHER_AMOUNT_STARTS_R': context.get('VOUCHER_AMOUNT_STARTS_R', None),
        'request': context['request'],
        'its_webview': its_webview,
        'en_id': en_id,
        'de_id': de_id,
        'fr_id': fr_id,
        'extra_class': extra_class,
    }


@register.inclusion_tag('components/forms/input_text.html')
def input_text(
    input_name,
    label,
    value='',
    input_id='',
    is_half=False,
    is_one_fourth=False,
    is_three_fourth=False,
    required=False,
    input_type='text',
    sub='',
    errors='',
    extra_attr='',
    custom_class='',
    placeholder='',
    parsleyUi='',  # noqa: N803
    parsleyCharMin=1,  # noqa: N803
    parsleyCharMax=142,  # noqa: N803
):
    if input_id == '':
        input_id = input_name

    return locals()


@register.inclusion_tag('components/forms/account/input.html')
def input_account(
    input_name,
    type='',
    placeholder='',
    id='',
    value='',
    required=False,
    label='',
    error_message='',
    autofocus=False,
    wrapper_class='',
):
    if id == '':
        id = input_name

    return locals()


@register.inclusion_tag('components/forms/account/show_password.html')
def password_account(
    input_name,
    id='',
    field='',
):
    if id == '':
        id = input_name

    return locals()


@register.inclusion_tag(
    'components/popup-luke/popup_luke_base.html', takes_context=True
)
def popup_assembly(
    context,
    content_name,
    modify_class,
    user_region,
    delivery_delay=False,
    with_cta=True,
    cart_handler=True,
    is_modal=False,
    auto_open=False,
    exit_trap=False,
    open_delay=0,
    show_close=True,
    cookie='',
    cookie_repeat=1,
    cookie_interval=0,
    dataLayer='',  # noqa: N803
    dataLayerAdditional='',  # noqa: N803
    onCloseDataLayer='',  # noqa: N803
    content_template='components/popup-luke/popup_luke_assembly.html',
):
    return {
        'content_name': content_name,
        'cart_handler': cart_handler,
        'delivery_delay': delivery_delay,
        'modify_class': modify_class,
        'with_cta': with_cta,
        'content_template': content_template,
        'is_modal': is_modal,
        'auto_open': auto_open,
        'exit_trap': exit_trap,
        'open_delay': open_delay,
        'show_close': show_close,
        'cookie': cookie,
        'cookie_repeat': cookie_repeat,
        'cookie_interval': cookie_interval,
        'dataLayer': dataLayer,
        'dataLayerAdditional': dataLayerAdditional,
        'onCloseDataLayer': onCloseDataLayer,
        'user_region': user_region,
    }


@register.inclusion_tag(
    'components/popup-luke/popup_luke_base.html', takes_context=True
)
def popup_fasttrack(
    context,
    content_name,
    modify_class,
    user_region,
    delivery_delay=False,
    with_cta=True,
    cart_handler=True,
    is_modal=False,
    auto_open=False,
    exit_trap=False,
    open_delay=0,
    show_close=True,
    cookie='',
    cookie_repeat=1,
    cookie_interval=0,
    dataLayer='',  # noqa: N803
    dataLayerAdditional='',  # noqa: N803
    onCloseDataLayer='',  # noqa: N803
    content_template='components/popup-luke/popup_luke_fasttrack.html',
):
    return {
        'content_name': content_name,
        'cart_handler': cart_handler,
        'delivery_delay': delivery_delay,
        'modify_class': modify_class,
        'with_cta': with_cta,
        'content_template': content_template,
        'is_modal': is_modal,
        'auto_open': auto_open,
        'exit_trap': exit_trap,
        'open_delay': open_delay,
        'show_close': show_close,
        'cookie': cookie,
        'cookie_repeat': cookie_repeat,
        'cookie_interval': cookie_interval,
        'dataLayer': dataLayer,
        'dataLayerAdditional': dataLayerAdditional,
        'onCloseDataLayer': onCloseDataLayer,
        'user_region': user_region,
    }


@register.inclusion_tag(
    'components/popup-luke/popup_luke_base.html', takes_context=True
)
def popup_delivery(
    context,
    content_name,
    modify_class,
    delivery_delay=False,
    with_cta=True,
    cart_handler=True,
    is_modal=False,
    auto_open=False,
    exit_trap=False,
    open_delay=0,
    show_close=True,
    cookie='',
    cookie_repeat=1,
    cookie_interval=0,
    dataLayer='',  # noqa: N803
    dataLayerAdditional='',  # noqa: N803
    onCloseDataLayer='',  # noqa: N803
):
    return {
        'content_name': content_name,
        'cart_handler': cart_handler,
        'delivery_delay': delivery_delay,
        'modify_class': modify_class,
        'with_cta': with_cta,
        'content_template': 'components/popup-luke/popup_luke_delivery.html',
        'is_modal': is_modal,
        'auto_open': auto_open,
        'exit_trap': exit_trap,
        'open_delay': open_delay,
        'show_close': show_close,
        'cookie': cookie,
        'cookie_repeat': cookie_repeat,
        'cookie_interval': cookie_interval,
        'dataLayer': dataLayer,
        'dataLayerAdditional': dataLayerAdditional,
        'onCloseDataLayer': onCloseDataLayer,
    }


@register.inclusion_tag(
    'components/popup-luke/popup_luke_base.html', takes_context=True
)
def popup_promocode(
    context,
    content_name,
    modify_class,
    delivery_delay=False,
    with_cta=True,
    cart_handler=True,
    is_modal=False,
    auto_open=False,
    exit_trap=False,
    open_delay=0,
    show_close=True,
    cookie='',
    cookie_repeat=1,
    cookie_interval=0,
    dataLayer='',  # noqa: N803
    dataLayerAdditional='',  # noqa: N803
    onCloseDataLayer='',  # noqa: N803
):
    return {
        'content_name': content_name,
        'cart_handler': cart_handler,
        'delivery_delay': delivery_delay,
        'modify_class': modify_class,
        'content_template': 'components/popup-luke/popup_luke_promocode.html',
        'is_modal': is_modal,
        'auto_open': auto_open,
        'exit_trap': exit_trap,
        'open_delay': open_delay,
        'show_close': show_close,
        'cookie': cookie,
        'cookie_repeat': cookie_repeat,
        'cookie_interval': cookie_interval,
        'dataLayer': dataLayer,
        'dataLayerAdditional': dataLayerAdditional,
        'onCloseDataLayer': onCloseDataLayer,
    }


@register.inclusion_tag(
    'components/popup-luke/popup_luke_base.html', takes_context=True
)
def popup_recycle_tax(
    context,
    content_name,
    modify_class,
    user_region,
    delivery_delay=False,
    with_cta=True,
    cart_handler=True,
    is_modal=True,
    auto_open=False,
    exit_trap=False,
    open_delay=0,
    show_close=True,
    cookie='',
    cookie_repeat=1,
    cookie_interval=0,
    dataLayer='',  # noqa: N803
    dataLayerAdditional='',  # noqa: N803
    onCloseDataLayer='',  # noqa: N803
    content_template='components/popup-luke/popup_luke_recycle_tax.html',
):
    return {
        'content_name': content_name,
        'cart_handler': cart_handler,
        'delivery_delay': delivery_delay,
        'modify_class': modify_class,
        'with_cta': with_cta,
        'content_template': content_template,
        'is_modal': is_modal,
        'auto_open': auto_open,
        'exit_trap': exit_trap,
        'open_delay': open_delay,
        'show_close': show_close,
        'cookie': cookie,
        'cookie_repeat': cookie_repeat,
        'cookie_interval': cookie_interval,
        'dataLayer': dataLayer,
        'dataLayerAdditional': dataLayerAdditional,
        'onCloseDataLayer': onCloseDataLayer,
        'user_region': user_region,
    }


@register.inclusion_tag('components/configurator/loader.html', takes_context=True)
def configurator_loader(
    context,
    furniture_title=None,
):
    context['furniture_title'] = furniture_title
    return context


@register.inclusion_tag(
    'components/header/components/mega-menu/_mega-menu-links.html',
    takes_context=True,
)
def mega_menu_links(
    context, category, header='', name='', badge_visibility=False, is_desktop=True
):
    is_t13_flag_active = is_ab_test(context.get('request'), 't13')
    categories = {
        'spaces': [
            {
                'index': 'spaces-all',
                'name': _('menu_bar_spaces_all'),
                'url': _('url.lp.rooms'),
                'trackData': {
                    'eventAction': 'features-filters',
                    'eventLabel': 'spaces_all',
                    'eventParam_0': 2,
                },
            },
            {
                'index': 'spaces-bedroom',
                'name': _('menu_bar_spaces_bedroom'),
                'url': _('url.lp.bedroom'),
                'trackData': {
                    'eventAction': 'features-filters',
                    'eventLabel': 'bedroom',
                    'eventParam_0': 2,
                },
            },
            {
                'index': 'spaces-living-room',
                'name': _('menu_bar_spaces_living_room'),
                'url': _('url.lp.livingroom'),
                'trackData': {
                    'eventAction': 'features-filters',
                    'eventLabel': 'living_room',
                    'eventParam_0': 2,
                },
            },
            {
                'index': 'line-hallway',
                'name': _('menu_bar_spaces_hallway'),
                'url': _('url.lp.hallway'),
                'trackData': {
                    'eventAction': 'features-filters',
                    'eventLabel': 'hallway',
                    'eventParam_0': 2,
                },
            },
            {
                'index': 'line-studio-office',
                'name': _('menu_bar_spaces_office'),
                'url': _('url.lp.studiooffice'),
                'trackData': {
                    'eventAction': 'features-filters',
                    'eventLabel': 'studio_office',
                    'eventParam_0': 2,
                },
            },
            {
                'index': 'line-kids-room',
                'name': _('menu_bar_kids_room'),
                'url': _('url.lp.kidsroom'),
                'badge_visibility': '' if is_t13_flag_active else 'vueGrid_new_label',
                'trackData': {
                    'eventAction': 'features-filters',
                    'eventLabel': 'kids_room',
                    'eventParam_0': 2,
                },
            },
        ],
        'category': [
            {
                'index': 'category-all',
                'name': _('ola_menu_bar_style'),
                'url': reverse_lazy('front-products-list-shelf'),
                'trackData': {
                    'eventAction': 'features-filters',
                    'eventLabel': 'all_styles',
                    'eventParam_0': 1,
                },
            },
            {
                'index': 'category-sideboard',
                'name': _('ola_menu_bar_use_1_plural'),
                'url': reverse_lazy(
                    'front-products-list-shelf',
                    args=[FurnitureCategory.SIDEBOARD.unidecoded_translated_name],
                ),
                'trackData': {
                    'eventAction': 'features-filters',
                    'eventLabel': 'sideboard',
                    'eventParam_0': 1,
                },
            },
            {
                'index': 'category-bookcase',
                'name': _('ola_menu_bar_use_2_plural'),
                'url': reverse_lazy(
                    'front-products-list-shelf',
                    args=[FurnitureCategory.BOOKCASE.unidecoded_translated_name],
                ),
                'trackData': {
                    'eventAction': 'features-filters',
                    'eventLabel': 'bookcase',
                    'eventParam_0': 1,
                },
            },
            {
                'index': 'category-wallstorage',
                'name': _('martin_common_wall_storage_plural'),
                'url': reverse_lazy(
                    'front-products-list-shelf',
                    args=[FurnitureCategory.WALL_STORAGE.unidecoded_translated_name],
                ),
                'trackData': {
                    'eventAction': 'features-filters',
                    'eventLabel': 'wallstorage',
                    'eventParam_0': 1,
                },
            },
            {
                'index': 'category-wardrobe',
                'name': _('ola_menu_bar_use_7_plural'),
                'url': reverse_lazy(
                    'front-products-list-shelf',
                    args=[FurnitureCategory.WARDROBE.unidecoded_translated_name],
                ),
                'watty': True,
                'trackData': {
                    'eventAction': 'features-filters',
                    'eventLabel': 'wardrobes',
                    'eventParam_0': 1,
                },
            },
            {
                'index': 'category-desk',
                'name': _('martin_common_desk_plural'),
                'badge_visibility': False
                if is_t13_flag_active
                else 'vueGrid_new_label',
                'url': reverse_lazy(
                    'front-products-list-shelf',
                    args=[FurnitureCategory.DESK.unidecoded_translated_name],
                ),
                'trackData': {
                    'eventAction': 'features-filters',
                    'eventLabel': 'desk',
                    'eventParam_0': 1,
                },
            },
            {
                'index': 'category-tvstand',
                'name': _('ola_menu_bar_use_4_plural'),
                'url': reverse_lazy(
                    'front-products-list-shelf',
                    args=[FurnitureCategory.TV_STAND.unidecoded_translated_name],
                ),
                'trackData': {
                    'eventAction': 'features-filters',
                    'eventLabel': 'tv_stand',
                    'eventParam_0': 1,
                },
            },
            {
                'index': 'category-chest',
                'name': _('ola_menu_bar_use_6_plural'),
                'url': reverse_lazy(
                    'front-products-list-shelf',
                    args=[FurnitureCategory.CHEST.unidecoded_translated_name],
                ),
                'trackData': {
                    'eventAction': 'features-filters',
                    'eventLabel': 'chest',
                    'eventParam_0': 1,
                },
            },
            {
                'index': 'category-shoerack',
                'name': _('ola_menu_bar_use_5_plural'),
                'url': reverse_lazy(
                    'front-products-list-shelf',
                    args=[FurnitureCategory.SHOERACK.unidecoded_translated_name],
                ),
                'trackData': {
                    'eventAction': 'features-filters',
                    'eventLabel': 'shoerack',
                    'eventParam_0': 1,
                },
            },
            {
                'index': 'category-bedside',
                'name': _('martin_common_bedside_plural'),
                'badge_visibility': False
                if is_t13_flag_active
                else 'vueGrid_new_label',
                'url': reverse_lazy(
                    'front-products-list-shelf',
                    args=[FurnitureCategory.BEDSIDE_TABLE.unidecoded_translated_name],
                ),
                'trackData': {
                    'eventAction': 'features-filters',
                    'eventLabel': 'bedside_tables',
                    'eventParam_0': 1,
                },
            },
            {
                'index': 'category-vinyl',
                'name': _('ola_menu_bar_use_3_plural'),
                'url': reverse_lazy(
                    'front-products-list-shelf',
                    args=[FurnitureCategory.VINYL_STORAGE.unidecoded_translated_name],
                ),
                'trackData': {
                    'eventAction': 'features-filters',
                    'eventLabel': 'vinyl_storage',
                    'eventParam_0': 1,
                },
            },
        ],
    }

    return {
        'items': categories.get(category),
        'header': header,
        'badge_visibility': badge_visibility,
        't03_available': context.get('t03_available', False),
        'name': name,
        'category': category,
        'is_desktop': is_desktop,
    }
