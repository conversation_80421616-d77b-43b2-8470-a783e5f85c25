import json
import logging

from urllib.request import Request

from django import forms
from django.conf import settings
from django.contrib import (
    admin,
    messages,
)
from django.contrib.contenttypes.admin import GenericStackedInline
from django.core.files.base import ContentFile
from django.core.paginator import Paginator
from django.db.models import QuerySet
from django.http import (
    HttpResponse,
    JsonResponse,
)
from django.shortcuts import redirect
from django.urls import reverse
from django.utils.html import format_html
from django.utils.safestring import mark_safe

from rangefilter.filters import DateTimeRangeFilter

from admin_customization.admin import ButtonActionsBaseAdmin
from catalogue.services.presets_production.entry_from_furniture_creator import (
    EntriesCreator,
)
from custom.admin_action import admin_action_with_form
from custom.enums import (
    Furniture,
    LanguageEnum,
    ShelfType,
)
from feeds.forms import FeedItemsForm
from gallery.data_from_ps import get_packs
from gallery.enums import (
    ConfiguratorTypeEnum,
    FurnitureStatusEnum,
    ShelfPatternEnum,
)
from gallery.models import (
    CustomDna,
    FurnitureGridImage,
    FurnitureImage,
    Jetty,
    SampleBox,
    Sotty,
    SottyProxyForPresetAnalysis,
    Watty,
)
from gallery.serializers import (
    JettyImportExportSerializer,
    SottyImportExportSerializer,
    WattyImportExportSerializer,
)
from gallery.services.bag import (
    fetch_custom_dna_from_bag,
    send_preset_to_bag,
)
from gallery.services.copy_furniture import copy_furniture
from gallery.tasks import (
    calculate_weight_task,
    create_sketch_image,
)
from gallery.types import FurnitureType
from orders.enums import OrderStatus
from orders.models import (
    Order,
    OrderItem,
)
from pricing_v3.services.price_calculators import OrderPriceCalculator
from regions.models import Region
from render_tasks.choices import WebglRenderTaskType
from render_tasks.models import WebglRenderTask

logger = logging.getLogger('cstm')


@admin.action(description='Create FAKE order from this furniture')
def create_order_with_furniture(modeladmin, request, queryset):
    order = Order(
        owner=queryset.first().owner,
        first_name='TEST ORDER FIRST NAME',
        email='EMAIL@TEST',
        last_name='TEST ORDER LAST NAME',
        street_address_1='TEST ORDER ADDRESS',
        country='poland',
        city='TEST CITY',
        region=Region.get_region_for_language(LanguageEnum.PL),
    )
    order.total_price = 0
    order.save()
    for furniture in queryset:
        order.items.create(
            order_item=furniture,
            price=25,
            region_price=25,
            price_net=20.32,
            region_price_net=20.32,
            vat_amount=4.68,
            quantity=1,
        )
    order.change_products_to_ordered()


@mark_safe  # noqa: S308
def item_preview(obj):
    if obj.preview:
        return (
            '<div style="width:300px; float:left">'
            + f'<img src="{obj.preview.url}" width="300px" /></div>'
        )
    else:
        return '-'


item_preview.short_description = 'preview'


@admin.display(description='preview')
def grid_preview(obj):
    if getattr(obj, 'image', False):
        return format_html(
            '<div style="width:200px; float:left"><img src="{}" width="200px" /></div>',
            obj.image.url,
        )
    return '-'


class FurnitureImageWebpFilter(admin.SimpleListFilter):
    title = 'webp image generated'
    parameter_name = 'image_webp'

    def lookups(self, request, model_admin):
        return ('1', 'True'), ('0', 'False')

    def queryset(self, request, queryset):
        if self.value() == '1':
            return queryset.exclude(image_webp='')
        if self.value() == '0':
            return queryset.filter(image_webp='')
        else:
            return queryset


class FurnitureImageAdmin(admin.ModelAdmin):
    actions = ['update_webp_versions']
    list_display = ('id', 'image', 'furniture_object_id', 'type', 'color', grid_preview)
    list_filter = ('type', FurnitureImageWebpFilter)
    search_fields = ('furniture_object_id__iexact',)

    def update_webp_versions(self, request, queryset):
        for m in queryset:
            m.create_webp_version()
        messages.info(request, 'Created webp for {} entries'.format(queryset.count()))

    update_webp_versions.short_description = 'Update and create webp'


class FurnitureImageInline(GenericStackedInline):
    ct_field = 'furniture_content_type'
    ct_fk_field = 'furniture_object_id'
    readonly_fields = (grid_preview,)
    fields = (grid_preview, 'image_webp', 'type', 'color')
    model = FurnitureImage


class FurnitureMaterialFilter(admin.SimpleListFilter):
    title = 'Material'
    parameter_name = 'material'

    def lookups(self, request, model_admin):
        shelf_types = ShelfType
        model_name = model_admin.model._meta.verbose_name
        if model_name == Furniture.jetty.value:
            shelf_types = shelf_types.get_jetty_shelf_types()
        elif model_name == Furniture.watty.value:
            shelf_types = shelf_types.get_watty_shelf_types()
        return sorted(
            {
                (f'{shelf_type}-{color}', str(color))
                for shelf_type in shelf_types
                for color in shelf_type.colors
            },
            key=lambda color: color[1],
        )

    def queryset(self, request, queryset):
        if shelf_type_material := self.value():
            shelf_type, material = shelf_type_material.split('-')
            return queryset.filter(material=material, shelf_type=shelf_type)

        return queryset


class FurnitureAdmin(ButtonActionsBaseAdmin):
    actions = [
        create_order_with_furniture,
        'send_to_bag',
        'duplicate',
        'add_to_feed_category',
        'export_flipped',
        'export',
        'send_to_catalogue',
        'get_shelf_packaging',
        'recalculate_geometry',
        'refresh_preview',
        'generate_unreal_render_tasks',
        'show_dynamic_delivery_result',
    ]
    inlines = [FurnitureImageInline]
    list_display = [
        'id',
        'furniture_status',
        'preset',
        'created_at',
        'shelf_type',
        item_preview,
    ]
    list_display_links = ('id', 'furniture_status')
    list_filter = [
        'furniture_status',
        'preset',
        'custom_order',
        'shelf_type',
        FurnitureMaterialFilter,
    ]
    list_per_page = 25
    raw_id_fields = ('owner',)
    search_fields = ['id', 'owner__username']
    button_actions = ['import_json']

    @admin.action(description='Show dynamic delivery')
    def show_dynamic_delivery_result(self, request, queryset):
        for furniture in queryset:
            ship_in_range = furniture._get_ship_in_range()
            messages.info(
                request,
                f'Object {furniture.id} ship in range: {ship_in_range.name}',
            )

    @admin.action(description='Add furniture to feed category')
    def add_to_feed_category(self, request, queryset):
        success_function = FeedItemsForm.create_feed_items_for_furniture

        kwargs = {
            'modeladmin': self,
            'request': request,
            'queryset': queryset,
            'form_class': FeedItemsForm,
            'success_function': success_function,
            'success_function_kwargs': {},
        }
        return admin_action_with_form(**kwargs)

    def add_webgl_render_task(self, request, queryset, task_type):
        paginator = Paginator(queryset, 100)
        tasks = []
        for page in paginator:
            for furniture in page.object_list:
                tasks.append(  # noqa: PERF401
                    WebglRenderTask(
                        task_type=task_type,
                        furniture=furniture,
                    )
                )
        WebglRenderTask.objects.bulk_create(tasks)
        messages.info(request, 'Webgl tasks created')

    def recalculate_geometry(self, request, queryset):
        self.add_webgl_render_task(
            request,
            queryset,
            WebglRenderTaskType.RECALCULATE_GEOMETRY,
        )

    def refresh_preview(self, request, queryset):
        self.add_webgl_render_task(
            request,
            queryset,
            WebglRenderTaskType.PREVIEW,
        )

    def send_to_bag(self, request, queryset):
        paginator = Paginator(queryset, 100)
        bag_ids = []
        for page in paginator:
            bag_ids.extend(send_preset_to_bag(page.object_list))
        messages.info(
            request,
            f'Created {len(bag_ids)} shelves in bag: {bag_ids}',
        )

    @admin.action(description='DUPLICATE')
    def duplicate(self, request: Request, queryset: QuerySet[Jetty, Watty]) -> None:
        for furniture in queryset:
            old_id = furniture.id
            new_id = furniture.duplicate()
            messages.info(
                request,
                f'Duplicated {furniture.furniture_type} with id: {old_id} -> {new_id}',
            )

    @admin.action(description='Send to catalogue')
    def send_to_catalogue(
        self, request: Request, queryset: QuerySet[FurnitureType]
    ) -> None:
        entries_creator = EntriesCreator(queryset)
        entries_creator.run()
        duplicates_info = ''
        if duplicates_ids := entries_creator.duplicates_ids:
            duplicates_ids_str = ', '.join(
                [str(duplicate_id) for duplicate_id in duplicates_ids]
            )
            duplicates_info = (
                f' The following duplicates where omitted: {duplicates_ids_str}.'
            )
        messages.info(
            request,
            f'Sent {entries_creator.queryset.count()} furniture to catalogue.'
            f'{duplicates_info}',
        )

    def get_search_results(self, request, queryset, search_term):
        queryset, may_have_duplicates = super().get_search_results(
            request,
            queryset,
            search_term,
        )
        search_term = filter(
            lambda term: term.isdigit(), search_term.replace(' ', '').split(',')
        )
        queryset |= self.model.objects.filter(id__in=search_term)
        return queryset, may_have_duplicates

    def flip_geometry(self, serialized_obj, geometry_fields):
        """Flip geometry along the x-axis, adjusting doors direction."""
        coordinates = ('x', 'x1', 'x2')
        for field in geometry_fields:
            for geometry in serialized_obj.get(field, []):
                for coordinate in coordinates:
                    if coordinate in geometry:
                        geometry[coordinate] *= -1
                if geometry['x1'] > geometry['x2']:
                    geometry['x1'], geometry['x2'] = geometry['x2'], geometry['x1']
                if 'flip' in geometry:
                    geometry['flip'] = 0 if geometry['flip'] == 1 else 1
                if 'direction' in geometry:
                    geometry['direction'] = 2 if geometry['direction'] == 1 else 1
                if 'rotation_z' in geometry:
                    # adjust rotation in outermost long legs
                    geometry['rotation_z'] = {
                        0: 270,
                        90: 180,
                        180: 90,
                        270: 0,
                    }.get(geometry['rotation_z'], geometry['rotation_z'])
        return serialized_obj

    @admin.action(description='--- EXPORT ---')
    def export(self, request, queryset, flipped=False):
        data = {}
        filename = queryset.model.__name__.lower()
        serializer = {
            Jetty: JettyImportExportSerializer,
            Watty: WattyImportExportSerializer,
            Sotty: SottyImportExportSerializer,
        }.get(queryset.model, None)
        if not serializer:
            messages.error(request, 'Unsupported model, contact developers')
            return
        paginator = Paginator(queryset, 100)
        for page in paginator:
            for obj in page.object_list:
                filename += f'_{obj.id}'
                serialized_obj = dict(serializer(obj).data)
                if flipped:
                    serialized_obj = self.flip_geometry(serialized_obj)
                data[obj.id] = {'gallery': serialized_obj}

        response = JsonResponse(
            data,
            json_dumps_params=dict(indent=2, separators=(',', ': '), sort_keys=True),  # noqa: C408
        )
        response['Content-Disposition'] = f'attachment; filename={filename}.json'
        return response

    @admin.action(description='Export flipped')
    def export_flipped(self, request, queryset):
        return self.export(request, queryset, flipped=True)

    @admin.action(description='Import')
    def import_json(self, request):
        return redirect(reverse('admin:create_objects'))

    @admin.action(description='Get shelf packaging for logistic')
    def get_shelf_packaging(self, request, queryset):
        summary = []
        for page in Paginator(queryset, 100):
            for obj in page.object_list:
                if getattr(obj, 'covers_only', False):
                    summary.append(f'Object {obj.id} is covers only, Group packaging')
                    continue
                packages = get_packs(obj)
                summary.append(f'For Shelf id: {obj.id}')
                sum_weight = 0
                for pack in packages:
                    dim_x = pack.get('dim_x', 0)
                    dim_y = pack.get('dim_y', 0)
                    dim_z = pack.get('dim_z', 0)
                    weight = round(pack.get('weight', 0), 2)
                    sum_weight += weight
                    summary.append(f'{dim_x}x{dim_y}x{dim_z}x{weight}kg')
                summary.append(f'Razem paczek: {len(packages)}, wagi {sum_weight}kg')
                summary.append('<br>')
        return HttpResponse('<br>'.join(summary))

    def has_delete_permission(self, request, obj=None):
        return False

    def has_add_permission(self, request, obj=None):
        return False


class SampleBoxAdmin(admin.ModelAdmin):
    list_display = (
        'id',
        'updated_at',
        'created_at',
        'box_variant_id',
    )
    list_filter = ('box_variant_id',)
    raw_id_fields = ('owner',)
    search_fields = [
        'id',
        'owner__username',
    ]


class WattyAdmin(FurnitureAdmin):
    """Admin entry for a Watty model."""

    def flip_geometry(self, serialized_obj, geometry_fields=None):
        geometry_fields = (
            'doors',
            'backs',
            'drawers',
            'walls',
            'slabs',
            'frame',
            'bars',
            'hinges',
            'masking_bars',
        )
        return super().flip_geometry(serialized_obj, geometry_fields)


class SottyAdmin(FurnitureAdmin):
    """Admin entry for a Sotty model."""

    list_filter = [
        'furniture_status',
        'preset',
        'custom_order',
        ('weight', admin.EmptyFieldListFilter),
    ]
    actions = (
        FurnitureAdmin.actions
        + ['generate_technical_sketch', 'recalculate_weight']
        + (['add_sotty_to_order'] if settings.IS_DEV else [])
    )

    @admin.action()
    def generate_technical_sketch(self, request, queryset):
        for sotty in queryset:
            create_sketch_image.delay(
                furniture_type=Furniture.sotty.value, furniture_id=sotty.id
            )

    @admin.action()
    def recalculate_weight(self, request, queryset):
        for sotty in queryset:
            calculate_weight_task.delay(
                furniture_type=Furniture.sotty.value, furniture_id=sotty.id
            )

    @admin.action(description='Add Sotty to your last PAYMENT_PENDING Order')
    def add_sotty_to_order(self, request, queryset):
        if request.user.is_anonymous:
            return
        order = request.user.order_set.filter(
            status=OrderStatus.PAYMENT_PENDING
        ).first()
        if order is None:
            messages.error(
                request,
                'You do not have any order with pending payment. '
                'Please create one first.',
            )
            return
        batch = []
        for sotty in queryset:
            copied_sotty = copy_furniture(
                old_furniture=sotty,
                owner=request.user,
                new_furniture_status=FurnitureStatusEnum.ORDERED,
            )
            batch.append(
                OrderItem(
                    order=order,
                    order_item=copied_sotty,
                    price=0,
                    price_net=0,
                    region_price=0,
                    region_price_net=0,
                    with_assembly=order.assembly,
                    free_assembly_service=False,
                    region=order.region,
                )
            )

        OrderItem.objects.bulk_create(batch)
        OrderPriceCalculator(order=order).calculate(check_vat=True)


class SottyProxyForPresetAnalysisAdmin(SottyAdmin):
    list_display = [
        'id',
        'furniture_status',
        'preset',
        item_preview,
        'base_preset',
        'is_base_preset_preset',
        'base_preset_preview',
    ]

    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        # Get all unique base_preset IDs from the current queryset
        base_preset_ids = (
            queryset.exclude(base_preset__isnull=True)
            .values_list('base_preset', flat=True)
            .distinct()
        )

        # Fetch all base presets in one query and store in a class attribute
        if base_preset_ids:
            self._base_presets_cache = {
                preset.id: preset
                for preset in Sotty.objects.filter(id__in=base_preset_ids)
            }
        else:
            self._base_presets_cache = {}

        return queryset

    def _get_base_preset(self, obj: Sotty):
        """Helper method to get base preset from cached data"""
        if not obj.base_preset:
            return None

        # Use the cached base presets
        return getattr(self, '_base_presets_cache', {}).get(obj.base_preset)

    @admin.display(description='Base preset preview')
    def base_preset_preview(self, obj: Sotty):
        base_preset = self._get_base_preset(obj)
        if not base_preset or not base_preset.preview:
            return '-'
        return format_html(
            '<div style="width:300px; float:left"><img src="{}" width="300px" /></div>',
            base_preset.preview.url,
        )

    @admin.display(description='Is base preset a preset?', boolean=True)
    def is_base_preset_preset(self, obj: Sotty):
        base_preset = self._get_base_preset(obj)
        if not base_preset:
            return False
        return base_preset.preset


class JettyPatternFilter(admin.SimpleListFilter):
    title = 'Pattern'
    parameter_name = 'pattern_nice_look_for_Pioter'

    def lookups(self, request, model_admin):
        return ShelfPatternEnum.choices

    def queryset(self, request, queryset):
        if self.value():
            return queryset.filter(pattern=self.value())
        else:
            return queryset


class BagPresetForm(forms.Form):
    presets_list = forms.CharField()


class JettyAdmin(FurnitureAdmin):
    fieldsets = (
        (
            'GENERAL INFORMATION',
            {
                'fields': (
                    'furniture_status',
                    'owner',
                    'price',
                    'created_at',
                    'updated_at',
                    'preset',
                    'preview',
                    'description',
                )
            },
        ),
        (
            'DETAILED INFO',
            {
                'fields': (
                    'shelf_type',
                    'created_platform',
                    'physical_product_version',
                    'configurator_type',
                    'shelf_category',
                    'material',
                    'pattern',
                    'property1',
                    'grid_all_colors',
                    'grid_all_colors_webp',
                    'base_preset',
                    'grid_preset',
                )
            },
        ),
        (
            'DNA',
            {
                'fields': (
                    'dna_name',
                    'dna_object',
                )
            },
        ),
        (
            'GEOMETRY',
            {
                'fields': (
                    'width',
                    'height',
                    'depth',
                    'verticals',
                    'horizontals',
                    'supports',
                    'modules',
                    'rows',
                    'joints',
                    'doors',
                    'backs',
                    'legs',
                    'drawers',
                    'cable_management',
                    'row_styles',
                    'backpanel_styles',
                    'plinth',
                    'long_legs',
                    'components',
                    'inserts',
                    'desk_beams',
                    'row_amount',
                    'max_capacity',
                    'configurator_params',
                )
            },
        ),
    )
    readonly_fields = (
        'created_at',
        'updated_at',
    )
    list_display = FurnitureAdmin.list_display + [  # noqa: RUF005
        'shelf_category',
        'get_width',
        'dna_object',
    ]
    list_filter = FurnitureAdmin.list_filter + [  # noqa: RUF005
        'configurator_type',
        'physical_product_version',
        'created_platform',
        'shelf_category',
        ('created_at', DateTimeRangeFilter),
        JettyPatternFilter,
    ]

    def flip_geometry(self, serialized_obj, geometry_fields=None):
        geometry_fields = (
            'doors',
            'backs',
            'drawers',
            'verticals',
            'horizontals',
            'supports',
            'legs',
            'inserts',
            'plinth',
            'long_legs',
            'cable_management',
            'desk_beams',
        )
        return super().flip_geometry(serialized_obj, geometry_fields)


class FurnitureGridImageAdmin(admin.ModelAdmin):
    list_display = ('id', 'image', grid_preview, 'furniture')
    search_fields = ('object_id',)
    fields = (
        'image',
        'color',
        'furniture',
    )
    readonly_fields = ('furniture',)
    list_select_related = ('content_type',)

    def furniture(self, grid_image: FurnitureGridImage) -> str:
        url = reverse(
            f'admin:{grid_image.content_type.app_label}'
            f'_{grid_image.content_type.model}_change',
            args=(grid_image.object_id,),
        )
        return format_html(
            '<a href="{}">{} {}</a>',
            url,
            grid_image.content_type.model,
            grid_image.object_id,
        )


class CustomDnaAdminForm(forms.ModelForm):
    bag_object_id = forms.IntegerField(required=False)

    class Meta:
        model = CustomDna
        fields = (
            'author',
            'pattern_slot',
            'shelf_type',
            'new_dna_tools',
            'visible_on_web',
            'title',
            'dna_objects',
            'dna',
            'configurator_type',
        )

    def clean(self):
        cleaned_data = super().clean()
        configurator_type = cleaned_data.get('configurator_type', False)
        pattern_slot = cleaned_data.get('pattern_slot', None)
        if (
            'configurator_type' in self.changed_data
            or 'pattern_slot' in self.changed_data
        ):
            if (
                configurator_type
                in {ConfiguratorTypeEnum.COLUMN, ConfiguratorTypeEnum.MIXED_ROW_COLUMN}
                and pattern_slot
            ):
                self.add_error(
                    'pattern_slot',
                    'Please leave this field empty '
                    + 'for serialization for column configurator',
                )

            if configurator_type == ConfiguratorTypeEnum.ROW and not pattern_slot:
                self.add_error(
                    'pattern_slot',
                    'Please fill this field in.',
                )

        return cleaned_data

    def save(self, author=None, commit=False):
        bag_object_id = self.cleaned_data.get('bag_object_id', None)
        if bag_object_id:
            self._process_dna_from_bag(bag_object_id)
        self.instance.author = author
        self.instance.assign_data_from_dna_json()
        return super().save(commit=commit)

    def _process_dna_from_bag(self, bag_object_id):
        dna_json = fetch_custom_dna_from_bag(bag_object_id)
        file = ContentFile(bytes(json.dumps(dna_json), 'utf-8'))
        self.dna.save('dna_from_bag.json', file, save=False)


class CustomDnaAdmin(admin.ModelAdmin):
    ordering = ('-id',)
    list_display = (
        'id',
        'author',
        'title',
        'visible_on_web',
        'shelf_type',
        'collection_type',
        'configurator_type',
        'pattern_slot',
        'created_at',
        'updated_at',
    )
    readonly_fields = (
        'title',
        'created_at',
        'updated_at',
        'dna_objects',
        'author',
    )
    fieldsets = (
        (
            'Summary',
            {
                'fields': (
                    'title',
                    'author',
                )
            },
        ),
        (
            'Bag import',
            {'classes': ('collapse',), 'fields': ('bag_object_id',)},
        ),
        (
            'Standard upload',
            {
                'classes': ('collapse',),
                'fields': (
                    'configurator_type',
                    'shelf_type',
                    'pattern_slot',
                    'dna',
                ),
            },
        ),
        (
            'DNA details',
            {
                'fields': ('visible_on_web',),
            },
        ),
    )
    raw_id_fields = ('author',)
    list_filter = ('visible_on_web', 'shelf_type', 'new_dna_tools')
    form = CustomDnaAdminForm

    def save_form(self, request, form, change):
        return form.save(author=request.user)


admin.site.register(Jetty, JettyAdmin)
admin.site.register(SampleBox, SampleBoxAdmin)
admin.site.register(Watty, WattyAdmin)
admin.site.register(Sotty, SottyAdmin)
admin.site.register(CustomDna, CustomDnaAdmin)
admin.site.register(FurnitureGridImage, FurnitureGridImageAdmin)
admin.site.register(FurnitureImage, FurnitureImageAdmin)
admin.site.register(SottyProxyForPresetAnalysis, SottyProxyForPresetAnalysisAdmin)
