from decimal import Decimal
from typing import Final

from immutabledict import immutabledict

from custom.enums import (
    Furniture,
    Type01Color,
    Type02Color,
    Type03Color,
    Type23Color,
    VeneerType01Color,
)
from custom.enums.colors import (
    Sofa01Color,
    Type03ExteriorInteriorColor,
    Type13Color,
    VeneerType13Color,
)

FURNITURE_COLOR_CHOICES = [
    (0, 'T01 White / T02 White / T03 White / T01 Veneer Ash / T13 White'),
    (
        1,
        'T01 Black / T02 Terracotta / T03 Cashmere Beige / T01 Veneer Oak / T13 Sand + '
        'Midnight Blue',
    ),
    (2, 'T02 Midnight Blue / T03 Graphite Grey / T13 Mustard Yellow'),
    (3, 'T01 Grey / T02 Sand + Midnight Blue / T03 Cashmere + Antique Pink / T13 Gray'),
    (4, 'T03 White Pink / T13 Dark Gray'),
    (5, 'T13 White Plywood'),
    (
        6,
        'T01 Classic Red / T02 Matte Black / T03 Graphite Grey + Anti<PERSON> Pink / '
        'T13 Gray Plywood',
    ),
    (7, 'T01 Yellow / T02 Sky Blue / T13 Black Plywood'),
    (8, 'T01 Dusty Pink / T02 Burgundy Red / T13 Clay Brown'),
    (9, 'T01 Blue / T02 Cotton Beige / T13 Olive Green'),
    (10, 'T01 Dark Brown / T02 Gray / T13 Beige'),
    (11, 'T02 Dark Gray / T13 Black'),
    (12, 'T02 Mustard Yellow / T03 Graphite Grey + Stone Grey'),
    (13, 'T03 Graphite Grey + Sage Green'),
    (14, 'T03 Graphite Grey + Misty Blue'),
    (15, 'T02 Reisingers Pink / T03 White + Stone Grey'),
    (16, 'T02 Sage Green / T03 White + Sage Green'),
    (17, 'T02 Stone Gray / T03 White + Misty Blue'),
    (18, 'T02 Walnut / T03 Cashmere Beige + Stone Grey'),
    (19, 'T03 Cashmere Beige + Sage Green'),
    (20, 'T03 Cashmere Beige + Misty Blue'),
]


def get_dynamic_color_choices(use_exterior_interior: bool = False) -> list[tuple]:
    """
    Just like FURNITURE_COLOR_CHOICES but made on actual available colors
    """
    all_colors_map = {
        'T01': Type01Color.choices_active(),
        'T02': Type02Color.choices_active(),
        'T03': Type03ExteriorInteriorColor.choices_active()
        if use_exterior_interior
        else Type03Color.choices_active(),
        'T01 Veneer': VeneerType01Color.choices_active(),
        'T13': Type13Color.choices_active(),
        'T13 Veneer': VeneerType13Color.choices_active(),
        'Tone Expressions': Type23Color.choices_active(),
        'Sofa': Sofa01Color.choices_active(),
    }
    parsed_colors = {}
    for shelf_type, colors in all_colors_map.items():
        for number, name in colors:
            if number not in parsed_colors:
                parsed_colors[number] = f'{shelf_type} {name}'
            else:
                parsed_colors[number] += f' / {shelf_type} {name}'
    return list(parsed_colors.items())


FURNITURE_DYNAMIC_COLOR_CHOICES = get_dynamic_color_choices()
FURNITURE_DYNAMIC_COLOR_CHOICES_SAMPLES = get_dynamic_color_choices(
    use_exterior_interior=True,
)

EXCLUDED_REGIONS_CORDUROY_MATERIAL = {
    'united_kingdom',
}

NEAR = 'near'
MEDIUM = 'medium'
FAR = 'far'
VERY_FAR = 'very_far'
EXTREMELY_FAR = 'extremely_far'
OTHER = 'other'

ONE_MODULE_OR_LESS = 1
TWO_MODULES = 2
THREE_MODULES_OR_MORE = 3

SOTTY_DELIVERY_PRICE_MAPPER = {
    NEAR: {
        ONE_MODULE_OR_LESS: Decimal('39.00'),
        TWO_MODULES: Decimal('69.00'),
        THREE_MODULES_OR_MORE: Decimal('99.00'),
    },
    MEDIUM: {
        ONE_MODULE_OR_LESS: Decimal('69.00'),
        TWO_MODULES: Decimal('99.00'),
        THREE_MODULES_OR_MORE: Decimal('169.00'),
    },
    FAR: {
        ONE_MODULE_OR_LESS: Decimal('99.00'),
        TWO_MODULES: Decimal('169.00'),
        THREE_MODULES_OR_MORE: Decimal('199.00'),
    },
    VERY_FAR: {
        ONE_MODULE_OR_LESS: Decimal('249.00'),
        TWO_MODULES: Decimal('399.00'),
        THREE_MODULES_OR_MORE: Decimal('599.00'),
    },
    EXTREMELY_FAR: {
        ONE_MODULE_OR_LESS: Decimal('399.00'),
        TWO_MODULES: Decimal('599.00'),
        THREE_MODULES_OR_MORE: Decimal('899.00'),
    },
}

SOTTY_DELIVERY_REGIONS_MAP = {
    'austria': NEAR,
    'belgium': NEAR,
    'czech': NEAR,
    'denmark': MEDIUM,
    'france': MEDIUM,
    'germany': NEAR,
    'italy': MEDIUM,
    'luxembourg': MEDIUM,
    'netherlands': NEAR,
    'poland': NEAR,
    'portugal': MEDIUM,
    'slovakia': NEAR,
    'spain': MEDIUM,
    'switzerland': FAR,
    'united_kingdom': FAR,
    'sweden': VERY_FAR,
    'finland': VERY_FAR,
    'ireland': VERY_FAR,
    'norway': EXTREMELY_FAR,
}

ARMREST_HEIGHT = 58
SEAT_HEIGHT = 84
# Calculated based on configurator logic:
# SEATING_DEPTH_OFFSET is the difference between
# the full seating depth and the seating depth value.
# Formula: FULL_SEATING_DEPTH - SEATING_DEPTH = SEATING_DEPTH_OFFSET
SEATING_DEPTH_OFFSET = 420

WHITE_GLOVES_DELIVERY_PRICE_MAP: Final[immutabledict[Furniture, int]] = immutabledict(
    {Furniture.sotty: 79}
)
# FurnitureCategory enum mock to use instead of plain string
# we went with sofa subcategories in our database layer
SOFA_FURNITURE_CATEGORY: Final[str] = 'sofa'
