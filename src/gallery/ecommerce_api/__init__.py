from gallery.ecommerce_api.configurator import (
    JettyConfiguratorView,
    SottyConfiguratorView,
    WattyConfiguratorView,
)
from gallery.ecommerce_api.dna import CustomDnaView
from gallery.ecommerce_api.mailing import (
    MailingJettyAPIView,
    MailingSavedJettyAPIView,
    MailingSavedSottyAPIView,
    MailingSavedWattyAPIView,
    MailingSottyAPIView,
    MailingWattyAPIView,
)
from gallery.ecommerce_api.pdp import (
    ProductPageJettyAPIView,
    ProductPageSottyAPIView,
    ProductPageSottySingleModuleAPIView,
    ProductPageWattyAPIView,
    SottyConfiguratorPreloadAPIView,
)

__all__ = (
    'CustomDnaView',
    'JettyConfiguratorView',
    'MailingJettyAPIView',
    'MailingSavedJettyAPIView',
    'MailingSavedSottyAPIView',
    'MailingSavedWattyAPIView',
    'MailingSottyAPIView',
    'MailingWattyAPIView',
    'ProductPageJettyAPIView',
    'ProductPageSottyAPIView',
    'ProductPageSottySingleModuleAPIView',
    'ProductPageWattyAPIView',
    'SottyConfiguratorPreloadAPIView',
    'SottyConfiguratorView',
    'WattyConfiguratorView',
)
