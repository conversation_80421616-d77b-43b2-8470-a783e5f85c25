from django.utils import translation
from rest_framework.authentication import Token<PERSON>uthentication
from rest_framework.generics import RetrieveAPIView
from rest_framework.response import Response

from custom.enums import LanguageEnum
from custom.permissions import BrazePermission
from gallery.enums import FurnitureStatusEnum
from gallery.models import (
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON><PERSON>,
)
from gallery.serializers.furniture.mailing import MailingSavedFurnitureSerializer


class MailingFurnitureAPIView(RetrieveAPIView):
    """Base view for retrieving a furniture serialization for mailing purposes."""

    authentication_classes = (TokenAuthentication,)
    permission_classes = (BrazePermission,)
    serializer_class = MailingSavedFurnitureSerializer

    def dispatch(self, request, *args, **kwargs):
        self.object = self.get_object()
        return super().dispatch(request, *args, **kwargs)

    def get_queryset(self):
        return (
            super()
            .get_queryset()
            .select_related('owner__profile')
            .prefetch_related(
                'additional_images',
            )
        )

    def get_serializer_context(self):
        return {
            'region': self.object.owner.profile.region,
            **super().get_serializer_context(),
        }

    def retrieve(self, request, *args, **kwargs):
        language = LanguageEnum(self.object.owner.profile.language)
        with translation.override(language):
            serializer = self.get_serializer(self.object)
            return Response(serializer.data)


class MailingJettyAPIView(MailingFurnitureAPIView):
    queryset = Jetty.objects.all()


class MailingSottyAPIView(MailingFurnitureAPIView):
    queryset = Sotty.objects.all()


class MailingWattyAPIView(MailingFurnitureAPIView):
    queryset = Watty.objects.all()


class MailingSavedJettyAPIView(MailingFurnitureAPIView):
    queryset = Jetty.objects.filter(furniture_status=FurnitureStatusEnum.SAVED)


class MailingSavedSottyAPIView(MailingFurnitureAPIView):
    queryset = Sotty.objects.filter(furniture_status=FurnitureStatusEnum.SAVED)


class MailingSavedWattyAPIView(MailingFurnitureAPIView):
    queryset = Watty.objects.filter(furniture_status=FurnitureStatusEnum.SAVED)
