from rest_framework.exceptions import APIException


class BagConnectionError(Exception):
    """Error while connecting to BAG."""


class UnsupportedModel(APIException):
    status_code = 400
    default_detail = 'Given model is unsupported by this view.'
    default_code = 'unsupported_model'


class NotCreatedAnyObjectsException(Exception):  # noqa: N818
    """Not created any objects while exporting objects"""


class BadCategoryConfigurationError(Exception):
    """Bad configurator_type for given category"""


class GenericForeignKeyProtectedException(Exception):  # noqa: N818
    """Raised when trying to delete furniture used as GenericForeignKey"""
