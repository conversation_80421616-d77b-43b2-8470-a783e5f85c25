import logging

from collections import defaultdict
from typing import (
    Any,
    Callable,
    Iterable,
)

import pandas as pd

from past.utils import old_div

from producers.gh.moduly_glowne import ivy_production as ip

logger = logging.getLogger('cstm')

reporting_prefixes = dict(  # noqa: C408
    # materials_usage='MU',
    fitting='MU1',
    material='MU1',
    semiproduct='MU2',
    service='MU3',
    packaging='MU4',
    statistic_m_u='S',
    logistics='L',
    logistics_country='LC',
    additional_info='A',
    pricing='P',
    margins_elem='ME',
    margins_cat='MC',
    margins='M',
    features_cat='F',
    features_num='N',
    furniture_cat='C',
)


class IvyMaterialMixin:
    def get_material_name(self, get_for_batch=False):
        # TODO: names language should be unified.
        # Now some models have mix english/polish
        # names.
        material_name = '-'
        polish_material_names_type01 = [
            '<PERSON><PERSON><PERSON> sklejka',
            '<PERSON><PERSON><PERSON> sklejka',
            '<PERSON><PERSON><PERSON>',
            '<PERSON><PERSON><PERSON> sklejka',
            '<PERSON><PERSON><PERSON><PERSON> sklejka',
            'Fornir jesionowy sklejka',
            '<PERSON><PERSON><PERSON><PERSON> sklejka',
            'Ż<PERSON>łta sklejka',
            'Różowa sklejka',
            'Niebieska sklejka',
            'Ciemnobrązowa sklejka',
            'Zielona agawa sklejka',
        ]
        eng_material_names_type01 = [
            'White laminate Egger',
            'Black laminate Egger',
            'Sycamore',
            'Gray laminate Egger',
            'Egger plywood',
            'Plywood ash veneer',
            'Classic Red laminate Kronospan',
            'Yellow laminate Egger',
            'Dusty Pink laminate Pfleiderer',
            'Cobalt Blue laminate Egger',
            'Dark Brown laminate Egger',
            'Green agave laminate Egger',
        ]
        polish_material_names_type02 = [
            'Snow White - Kronospan 8685 BS',
            'Ceramic Red - Kronospan K098 SU',
            'Indigo Blue - Egger U599 ST9',
            'Sand Beige - Egger U156 ST9',
            'Pastel Green - Kronospan 7063 SU',
            '-',
            'Black mat - VELVET 7322',
            'Sky Blue - Kronospan Capri Blue 0121 BS',
            'Burgundy Red - EGGER Burgundy U399 ST9',
            'Cotton Beige - EGGER Cotton U113 ST9',
            'Gray - EGGER U773 ST9',
            'Gray + Dark Gray - EGGER U773 ST9',
            'Sand + Mustard Yellow - Egger U156 ST9',
            '-',  # forest green
            '-',  # lilac
            'Reisinger’s Pink - Egger Różowy Flamingo U363 ST9',  # noqa: RUF001
            'Sage Green - Egger Zielony Pistacja U608 ST9',
            'Stone Grey - Egger Szary Kamienny U727 ST9',
            'Stone Grey - Egger Szary Kamienny U727 ST9 + obrzeże orzechowe',
        ]
        polish_material_names_veneer_type01 = [
            'Fornir jesion',
            'Fornir dab',
            'Fornir orzech',
        ]
        if self.shelf_type == 0 and self.material < len(polish_material_names_type01):
            if get_for_batch:
                material_name = eng_material_names_type01[self.material]
            else:
                material_name = polish_material_names_type01[self.material]
        elif self.shelf_type == 1 and self.material < len(polish_material_names_type02):
            material_name = polish_material_names_type02[self.material]
        elif self.shelf_type == 2 and self.material < len(
            polish_material_names_veneer_type01
        ):
            material_name = polish_material_names_veneer_type01[self.material]
        return material_name


class JettySerialized:
    def __init__(self, serialization, product=None):
        self.product = product
        self.serialization = serialization
        self._ivy_producion = None

    # -- Dane na podstawie Ivy Production

    @property
    def production_ivy(self):
        if self._ivy_producion is None:
            self._ivy_producion = ip.IvyProduction(production_data=self.serialization)
        return self._ivy_producion

    @property
    def packaging(self):
        return self.production_ivy.get_packs(self.product)

    # -- Serie danych
    def get_materials_usage_df(self, include_prefixes=False, pricing_factor_df=None):
        # -- Dane zużyć z serializacji
        ser_materials = {
            codename: mat_data
            for group in list(self.serialization.get('materials', {}).values())
            for codename, mat_data in list(group.items())
        }
        # -- Konstruowanie DF
        material_data_df = pd.DataFrame(data=ser_materials).transpose()

        # -- Uzupelnianie brakujących codenames na podstawie aktywnego PricingFactors
        from production_margins.models import PricingFactors

        mu_df = (
            pricing_factor_df
            if pricing_factor_df is not None
            else PricingFactors.get_solo().df
        )
        if 'usage' not in material_data_df:
            material_data_df['usage'] = 0
        mu_df = pd.concat([mu_df, material_data_df['usage']], axis=1, sort=False)

        mu_df[['price_per_unit', 'usage']] = mu_df[['price_per_unit', 'usage']].fillna(
            0
        )

        # -- Uzupelnianie informacji
        mu_df['total'] = mu_df['price_per_unit'] * mu_df['usage']

        if not include_prefixes:
            return mu_df.sort_index()

        mu_df.index = mu_df.apply(
            lambda x: '{}__{}'.format(
                reporting_prefixes[x['C1_category'].lower().replace(' ', '')], x.name
            ),
            axis=1,
        )
        return mu_df.sort_index()

    def get_drawer_widths_in_mm_as_list(self):
        drawer_widths = []
        is_complaint = bool(self.serialization.get('id_complaint', False))
        for element in self.item.get('elements', []):
            if element['elem_type'] == 'T':
                if is_complaint and not element.get('complaint', False):
                    continue
                for component in element['components']:
                    if is_complaint and not component.get('complaint', False):
                        continue
                    if component['type'] == 'T_front':
                        drawer_widths.append(
                            old_div(
                                abs(
                                    component['x_domain'][1] - component['x_domain'][0]
                                ),
                                100,
                            )
                        )
        return drawer_widths

    @property
    def shelf_type(self):
        return self.serialization.get('shelf_type', 0)

    @property
    def rows(self):
        rc = sorted(self.item.get('rows_coor', {}).values())
        return [old_div((rc[i + 1] - rc[i]), 100) for i in range(len(rc) - 1)]

    @property
    def error_message(self):
        return self.serialization.get('errors', None)

    @property
    def id_gallery(self):
        return self.serialization.get('id_gallery')

    @property
    def id_production(self):
        return self.serialization.get('id_production', -1)

    @property
    def id_manufactor(self):
        return self.serialization.get('id_manufactor', -1)

    @property
    def id_pricing_factors(self):
        return self.serialization.get('id_pricing_factors', -1)

    @property
    def serialization_version(self):
        return self.serialization.get('version', 'v-.-')

    @property
    def serialization_date(self):
        return self.serialization.get('serialized_at', '--')

    @property
    def item(self):
        return self.serialization.get('item', {})

    @property
    def hvs_area(self):
        return self.item.get('HVS_area', 0)

    @property
    def packaging_weight(self):
        return sum([p.get('weight', 0) for p in self.item.get('packs', {})])  # noqa: C419


class IvyStatisticMixin(IvyMaterialMixin):
    @property
    def rows_type(self):
        if len(set(self.get_rows()).difference(set(range(200, 800, 100)))) == 0:
            return 1
        if len(set(self.rows).difference({208, 278, 398})) == 0:
            return 0
        return None

    @property
    def has_top_or_bottom_storage(self) -> bool:
        """If the shelf has top or bottom storage, it is Wallstorage+."""
        return self.has_top_storage or self.has_bottom_storage

    @property
    def has_bottom_storage(self) -> bool:
        return self.has_storage(min)

    @property
    def has_top_storage(self) -> bool:
        return self.has_storage(max)

    def has_storage(
        self,
        row_fn_getter: Callable[[Iterable[Any]], Any],
    ) -> bool:
        """Until FPM gives us proper rows, we need to check verticals and doors."""
        if not self.verticals:
            return False

        verticals_by_rows = defaultdict(list)
        for vertical in self.verticals:
            verticals_by_rows[vertical['y1']].append(vertical)
        row_y1 = row_fn_getter(verticals_by_rows.keys())
        c_row_height = 382

        has_high_row = all(
            (vertical['y2'] - vertical['y1']) > c_row_height
            for vertical in verticals_by_rows[row_y1]
        )
        has_doors_in_high_row = any(door['y1'] == row_y1 for door in self.doors)
        return len(verticals_by_rows) >= 2 and has_high_row and has_doors_in_high_row

    def get_elements_by_row(self, with_duplicates=False):
        def filter_by_row(elements, high, low=None):
            if with_duplicates:
                f = [
                    x
                    for x in elements
                    if (low is not None and abs(min(x['y2'], x['y1']) - low) < 10)
                    or abs(max(x['y2'], x['y1']) - high) < 10
                ]
            else:
                f = [x for x in elements if abs(max(x['y2'], x['y1']) - high) < 10]

            return sorted(f, key=lambda x: x['x1'])

        # -- Elements by row sorted by X
        rows = self.get_rows()
        top_coor = [sum(rows[:i]) for i in range(len(rows) + 1)]
        coor = [(0, 0)] + [(top_coor[i], top_coor[i + 1]) for i in range(len(rows))]
        elem = dict(  # noqa: C408
            H=[filter_by_row(self.horizontals, high) for high in top_coor],
            V=[filter_by_row(self.verticals, high, low) for low, high in coor],
            S=[filter_by_row(self.supports, high, low) for low, high in coor],
            B=[filter_by_row(self.backs, high, low) for low, high in coor],
            D=[filter_by_row(self.doors, high, low) for low, high in coor],
            T=[filter_by_row(self.drawers, high, low) for low, high in coor],
        )

        return elem

    def get_openings_by_row(self, opening_sides=True):
        def filter_by_range(elements, x1, x2):
            return [
                x
                for x in elements
                if x1 <= min(x['x1'], x['x2']) <= max(x['x1'], x['x2']) <= x2
            ]

        # -- Elements by row sorted by X
        rows = self.get_rows()
        elem = self.get_elements_by_row(with_duplicates=True)
        s_left, s_right = old_div(-self.width, 2), old_div(self.width, 2)
        # -- Openings by row:
        openings = []
        _height_top = 0
        for row_id, row in enumerate(rows):
            row_id += 1
            _height_top += row
            # -- Elementy
            h, v, s = elem['H'][row_id], elem['V'][row_id], elem['S'][row_id]
            d, b, t = elem['D'][row_id], elem['B'][row_id], elem['T'][row_id]
            # NOTE: Fix for a row with only one double opening
            # if row_id < len(rows) and not h:
            #     h = elem['H'][row_id - 1]
            # -- Otwarcia - zakresy
            op = [
                (v[i]['x1'], v[i + 1]['x1']) for i in range(len(v) - 1)
            ]  # TODO: ogarnac czy ma tak byc
            # openings calculated from the beginning to the beginning of vertical in x
            # and in axes in y
            if opening_sides:
                if abs(op[0][0] - s_left) > 10:
                    op.insert(0, (s_left, op[0][0]))
                if abs(op[-1][1] - s_right) > 10:
                    op.append((op[-1][1], s_right))
            # -- Otwarcia pelna informacja
            openings_ = []
            for x1, x2 in op:
                # -- Podwojne otwarcia przypisane tylko do gornego rzedu
                if not [
                    e
                    for e in h
                    if min(e['x1'], e['x2']) <= x1 < x2 <= max(e['x1'], e['x2'])
                ]:
                    continue
                e = ''.join(
                    [
                        key
                        for key, v in list(
                            dict(  # noqa: C408
                                S=filter_by_range(s, x1, x2),
                                B=filter_by_range(b, x1, x2),
                                D=filter_by_range(d, x1, x2),
                                T=filter_by_range(t, x1, x2),
                            ).items()
                        )
                        if v
                    ]
                )
                e = e.replace('S', '') if 'S' in e and 'B' in e else e
                openings_.append(
                    dict(  # noqa: C408
                        range=(x1, x2),
                        width=(abs(x2 - x1)),
                        height=row,
                        elements=e or None,
                        open=x1 == s_left or x2 == s_right,
                        height_top=_height_top,
                    )
                )
            openings.append(openings_)
        return openings

    def get_area(self):
        return (self.get_width()) / 100.0 * (self.get_height()) / 100.0

    def get_doors_area(self):
        doors_area = sum(
            [  # noqa: C419
                abs(d['x2'] - d['x1']) * abs(d['y2'] - d['y1']) / 1000000.0
                for d in self.doors
            ]
        )
        return doors_area

    def get_drawers_area(self):
        drawers_area = sum(
            [  # noqa: C419
                abs(d['x2'] - d['x1']) * abs(d['y2'] - d['y1']) / 1000000.0
                for d in self.drawers
            ]
        )
        return drawers_area

    def get_fill_area(self):
        return self.get_doors_area() + self.get_drawers_area()

    def get_fill_density(self):
        openings_area = self.get_openings_area()
        # HACK division by zero rare error escaping
        fill_area = self.get_fill_area()
        return old_div(fill_area, openings_area) if openings_area > 0 else 0

    def get_openings_area(self, if_drawers=False):
        if if_drawers:
            return sum(
                [  # noqa: C419
                    (item['height'] / 1000.0) * (item['width'] / 1000.0)
                    for sublist in self.get_openings_by_row(opening_sides=False)
                    for item in sublist
                    if item['height_top'] <= 1500
                ]
            )
        else:
            return sum(
                [  # noqa: C419
                    (item['height'] / 1000.0) * (item['width'] / 1000.0)
                    for sublist in self.get_openings_by_row(opening_sides=False)
                    for item in sublist
                ]
            )
