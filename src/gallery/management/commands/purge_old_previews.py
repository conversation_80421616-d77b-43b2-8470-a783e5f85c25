from os import (
    listdir,
    remove,
)
from os.path import (
    isfile,
    join,
)

from django.conf import settings
from django.core.management.base import BaseCommand

from gallery.models import Jetty


class Command(BaseCommand):
    help = 'Purges orphaned image files'

    def handle(self, *args, **options):
        if settings.USE_AWS_S3_MEDIA_STORAGE:
            raise EnvironmentError(
                'AWS S3 storage in use. Command does not support it.'
            )
        # Single path.
        # Should scan all instances in case of variable upload path in the future
        jetty_upload_to = [  # noqa: RUF015
            f.upload_to for f in Jetty._meta.fields if f.name == 'preview'
        ][0]
        path = '/'.join(
            (
                settings.MEDIA_ROOT,
                jetty_upload_to,
            )
        )

        images_db = [
            j.preview.name.split('/')[-1] for j in Jetty.objects.all().only('preview')
        ]
        images_disk = [f for f in listdir(path) if isfile(join(path, f))]
        images_purge = set(images_disk) - set(images_db)

        for image in images_purge:
            image_path = '/'.join((settings.MEDIA_ROOT, jetty_upload_to, image))
            remove(image_path)
