import logging

from pathlib import Path

from django.conf import settings
from django.core.files.base import ContentFile
from django.core.management.base import BaseCommand

from gallery.enums import ConfiguratorTypeEnum
from gallery.models import CustomDna
from gallery.serializers import CustomDnaSerializer

logger = logging.getLogger('cstm')


DNA_TO_BE_USED = [
    {
        'pattern_slot': 3,
        'shelf_type': 2,
        'new_dna_tools': True,
        'dna_file_path': 'dna_backup/grid_type02_before_fpm.json',
    },
    {
        'pattern_slot': 3,
        'shelf_type': 1,
        'new_dna_tools': True,
        'dna_file_path': 'dna_backup/grid_type02_before_fpm.json',
    },
    {
        'pattern_slot': 3,
        'shelf_type': 2,
        'new_dna_tools': True,
        'dna_file_path': 'dna_backup/grid_veneer01.json',
    },
    {
        'pattern_slot': 3,
        'shelf_type': 1,
        'new_dna_tools': True,
        'dna_file_path': 'dna_backup/grid_type02.json',
    },
    {
        'pattern_slot': 3,
        'shelf_type': 0,
        'new_dna_tools': False,
        'dna_file_path': 'dna_backup/grid_type01.json',
    },
    {
        'pattern_slot': 2,
        'shelf_type': 0,
        'new_dna_tools': False,
        'dna_file_path': 'dna_backup/pattern_type01.json',
    },
    {
        'pattern_slot': 2,
        'shelf_type': 2,
        'new_dna_tools': False,
        'dna_file_path': 'dna_backup/pattern_type02.json',
    },
    {
        'pattern_slot': 2,
        'shelf_type': 1,
        'new_dna_tools': False,
        'dna_file_path': 'dna_backup/pattern_type02.json',
    },
    {
        'pattern_slot': 1,
        'shelf_type': 0,
        'new_dna_tools': False,
        'dna_file_path': 'dna_backup/gradient_type01.json',
    },
    {
        'pattern_slot': 1,
        'shelf_type': 2,
        'new_dna_tools': False,
        'dna_file_path': 'dna_backup/gradient_type02.json',
    },
    {
        'pattern_slot': 1,
        'shelf_type': 1,
        'new_dna_tools': False,
        'dna_file_path': 'dna_backup/gradient_type02.json',
    },
    {
        'pattern_slot': 0,
        'shelf_type': 2,
        'new_dna_tools': False,
        'dna_file_path': 'dna_backup/slant_type02.json',
    },
    {
        'pattern_slot': 0,
        'shelf_type': 0,
        'new_dna_tools': False,
        'dna_file_path': 'dna_backup/slant_type01.json',
    },
    {
        'pattern_slot': 0,
        'shelf_type': 1,
        'new_dna_tools': False,
        'dna_file_path': 'dna_backup/slant_type02.json',
    },
    {
        'pattern_slot': 1921,
        'shelf_type': 0,
        'new_dna_tools': False,
        'dna_file_path': 'dna_backup/mesh_1921.json',
        'configurator_type': ConfiguratorTypeEnum.COLUMN.value,
        'visible_on_web': True,
    },
    {
        'pattern_slot': 1924,
        'shelf_type': 1,
        'new_dna_tools': False,
        'dna_file_path': 'dna_backup/mesh_1924.json',
        'configurator_type': ConfiguratorTypeEnum.COLUMN.value,
        'visible_on_web': True,
    },
    {
        'pattern_slot': 1925,
        'shelf_type': 1,
        'new_dna_tools': False,
        'dna_file_path': 'dna_backup/mesh_1925.json',
        'configurator_type': ConfiguratorTypeEnum.COLUMN.value,
        'visible_on_web': True,
    },
    {
        'pattern_slot': 1927,
        'shelf_type': 0,
        'new_dna_tools': False,
        'dna_file_path': 'dna_backup/mesh_1927.json',
        'configurator_type': ConfiguratorTypeEnum.COLUMN.value,
        'visible_on_web': True,
    },
    {
        'pattern_slot': 1943,
        'shelf_type': 1,
        'new_dna_tools': False,
        'dna_file_path': 'dna_backup/mesh_1943.json',
        'configurator_type': ConfiguratorTypeEnum.COLUMN.value,
        'visible_on_web': True,
    },
    {
        'pattern_slot': 1928,
        'shelf_type': 0,
        'new_dna_tools': False,
        'dna_file_path': 'dna_backup/mesh_1928.json',
        'configurator_type': ConfiguratorTypeEnum.COLUMN.value,
        'visible_on_web': True,
    },
    {
        'pattern_slot': 2122,
        'shelf_type': 1,
        'new_dna_tools': False,
        'dna_file_path': 'dna_backup/mesh_2122.json',
        'configurator_type': ConfiguratorTypeEnum.COLUMN.value,
        'visible_on_web': True,
    },
    {
        'pattern_slot': 2133,
        'shelf_type': 1,
        'new_dna_tools': False,
        'dna_file_path': 'dna_backup/mesh_2133.json',
        'configurator_type': ConfiguratorTypeEnum.COLUMN.value,
        'visible_on_web': True,
    },
    {
        'pattern_slot': 2134,
        'shelf_type': 1,
        'new_dna_tools': False,
        'dna_file_path': 'dna_backup/mesh_2134.json',
        'configurator_type': ConfiguratorTypeEnum.COLUMN.value,
        'visible_on_web': True,
    },
    {
        'pattern_slot': 2142,
        'shelf_type': 1,
        'new_dna_tools': False,
        'dna_file_path': 'dna_backup/mesh_2142.json',
        'configurator_type': ConfiguratorTypeEnum.COLUMN.value,
        'visible_on_web': True,
    },
    {
        'pattern_slot': 2194,
        'shelf_type': 2,
        'new_dna_tools': False,
        'dna_file_path': 'dna_backup/mesh_2194.json',
        'configurator_type': ConfiguratorTypeEnum.COLUMN.value,
        'visible_on_web': True,
    },
    {
        'pattern_slot': 2195,
        'shelf_type': 2,
        'new_dna_tools': False,
        'dna_file_path': 'dna_backup/mesh_2195.json',
        'configurator_type': ConfiguratorTypeEnum.COLUMN.value,
        'visible_on_web': True,
    },
    {
        'pattern_slot': 2196,
        'shelf_type': 2,
        'new_dna_tools': False,
        'dna_file_path': 'dna_backup/mesh_2196.json',
        'configurator_type': ConfiguratorTypeEnum.COLUMN.value,
        'visible_on_web': True,
    },
    {
        'pattern_slot': 2296,
        'shelf_type': 2,
        'dna_file_path': 'dna_backup/mesh_2296.json',
        'configurator_type': ConfiguratorTypeEnum.COLUMN.value,
        'visible_on_web': True,
    },
    {
        'pattern_slot': 2166,
        'shelf_type': 2,
        'dna_file_path': 'dna_backup/mesh_2166.json',
        'configurator_type': ConfiguratorTypeEnum.COLUMN.value,
        'visible_on_web': True,
    },
    {
        'pattern_slot': 2292,
        'shelf_type': 2,
        'dna_file_path': 'dna_backup/mesh_2292.json',
        'configurator_type': ConfiguratorTypeEnum.COLUMN.value,
        'visible_on_web': True,
    },
    {
        'pattern_slot': 2294,
        'shelf_type': 2,
        'dna_file_path': 'dna_backup/mesh_2294.json',
        'configurator_type': ConfiguratorTypeEnum.COLUMN.value,
        'visible_on_web': True,
    },
    {
        'pattern_slot': 2193,
        'shelf_type': 0,
        'dna_file_path': 'dna_backup/mesh_2193.json',
        'configurator_type': ConfiguratorTypeEnum.COLUMN.value,
        'visible_on_web': True,
    },
    {
        'pattern_slot': 2174,
        'shelf_type': 0,
        'dna_file_path': 'dna_backup/mesh_2174.json',
        'configurator_type': ConfiguratorTypeEnum.COLUMN.value,
        'visible_on_web': True,
    },
    {
        'pattern_slot': 2297,
        'shelf_type': 0,
        'dna_file_path': 'dna_backup/mesh_2297.json',
        'configurator_type': ConfiguratorTypeEnum.COLUMN.value,
        'visible_on_web': True,
    },
    {
        'pattern_slot': 2185,
        'shelf_type': 0,
        'dna_file_path': 'dna_backup/mesh_2185.json',
        'configurator_type': ConfiguratorTypeEnum.COLUMN.value,
        'visible_on_web': True,
    },
    {
        'pattern_slot': -1,
        'shelf_type': 0,
        'dna_file_path': 'dna_backup/multi_t01.json',
        'configurator_type': ConfiguratorTypeEnum.COLUMN.value,
        'visible_on_web': True,
    },
    {
        'pattern_slot': -1,
        'shelf_type': 2,
        'dna_file_path': 'dna_backup/multi_t01v.json',
        'configurator_type': ConfiguratorTypeEnum.COLUMN.value,
        'visible_on_web': True,
    },
    {
        'pattern_slot': -1,
        'shelf_type': 1,
        'dna_file_path': 'dna_backup/multi_t02.json',
        'configurator_type': ConfiguratorTypeEnum.COLUMN.value,
        'visible_on_web': True,
    },
    {
        'pattern_slot': -1,
        'shelf_type': 0,
        'dna_file_path': 'dna_backup/shoe_t01.json',
        'configurator_type': ConfiguratorTypeEnum.COLUMN.value,
        'visible_on_web': True,
    },
    {
        'pattern_slot': -1,
        'shelf_type': 2,
        'dna_file_path': 'dna_backup/shoe_t01v.json',
        'configurator_type': ConfiguratorTypeEnum.COLUMN.value,
        'visible_on_web': True,
    },
    {
        'pattern_slot': -1,
        'shelf_type': 1,
        'dna_file_path': 'dna_backup/shoe_t02.json',
        'configurator_type': ConfiguratorTypeEnum.COLUMN.value,
        'visible_on_web': True,
    },
    {
        'pattern_slot': -1,
        'shelf_type': 3,
        'dna_file_path': 'dna_backup/multi_wardrobe.json',
        'configurator_type': ConfiguratorTypeEnum.MIXED_ROW_COLUMN.value,
        'visible_on_web': True,
    },
    {
        'pattern_slot': -1,
        'shelf_type': 3,
        'dna_file_path': 'dna_backup/chest_t03.json',
        'configurator_type': ConfiguratorTypeEnum.COLUMN.value,
        'visible_on_web': True,
    },
    {
        'pattern_slot': -1,
        'shelf_type': 0,
        'dna_file_path': 'dna_backup/wallstorage_t01.json',
        'configurator_type': ConfiguratorTypeEnum.ROW.value,
        'visible_on_web': True,
    },
    {
        'pattern_slot': -1,
        'shelf_type': 2,
        'dna_file_path': 'dna_backup/wallstorage_v01.json',
        'configurator_type': ConfiguratorTypeEnum.ROW.value,
        'visible_on_web': True,
    },
    {
        'pattern_slot': -1,
        'shelf_type': 1,
        'dna_file_path': 'dna_backup/wallstorage_t02.json',
        'configurator_type': ConfiguratorTypeEnum.ROW.value,
        'visible_on_web': True,
    },
    {
        'pattern_slot': -1,
        'shelf_type': 0,
        'dna_file_path': 'dna_backup/vinylstorage_t01.json',
        'configurator_type': ConfiguratorTypeEnum.ROW.value,
        'visible_on_web': True,
    },
    {
        'pattern_slot': -1,
        'shelf_type': 0,
        'dna_file_path': 'dna_backup/1200_T01_1.json',
        'configurator_type': ConfiguratorTypeEnum.COLUMN.value,
        'visible_on_web': True,
    },
    {
        'pattern_slot': -1,
        'shelf_type': 2,
        'dna_file_path': 'dna_backup/1200_T01V_1.json',
        'configurator_type': ConfiguratorTypeEnum.COLUMN.value,
        'visible_on_web': True,
    },
    {
        'pattern_slot': -1,
        'shelf_type': 1,
        'dna_file_path': 'dna_backup/1200_T02_1.json',
        'configurator_type': ConfiguratorTypeEnum.COLUMN.value,
        'visible_on_web': True,
    },
    {
        'pattern_slot': -1,
        'shelf_type': 1,
        'dna_file_path': 'dna_backup/bookcase_t02_30cm.json',
        'configurator_type': ConfiguratorTypeEnum.ROW.value,
        'visible_on_web': True,
    },
    {
        'pattern_slot': -1,
        'shelf_type': 0,
        'dna_file_path': 'dna_backup/bookcase_t01_30cm.json',
        'configurator_type': ConfiguratorTypeEnum.ROW.value,
        'visible_on_web': True,
    },
    {
        'pattern_slot': -1,
        'shelf_type': 2,
        'dna_file_path': 'dna_backup/bookcase_t01v_30cm.json',
        'configurator_type': ConfiguratorTypeEnum.ROW.value,
        'visible_on_web': True,
    },
    {
        'pattern_slot': -1,
        'shelf_type': 0,
        'dna_file_path': 'dna_backup/vinylstorage_t01_30cm.json',
        'configurator_type': ConfiguratorTypeEnum.ROW.value,
        'visible_on_web': True,
    },
    {
        'pattern_slot': -1,
        'shelf_type': 0,
        'dna_file_path': 'dna_backup/desk_t01.json',
        'configurator_type': ConfiguratorTypeEnum.COLUMN.value,
        'visible_on_web': True,
    },
    {
        'pattern_slot': -1,
        'shelf_type': 2,
        'dna_file_path': 'dna_backup/desk_v01.json',
        'configurator_type': ConfiguratorTypeEnum.COLUMN.value,
        'visible_on_web': True,
    },
    {
        'pattern_slot': -1,
        'shelf_type': 1,
        'dna_file_path': 'dna_backup/desk_t02.json',
        'configurator_type': ConfiguratorTypeEnum.COLUMN.value,
        'visible_on_web': True,
    },
    {
        'pattern_slot': -1,
        'shelf_type': 4,
        'dna_file_path': 'dna_backup/cabinet_t13.json',
        'configurator_type': ConfiguratorTypeEnum.MIXED_ROW_COLUMN.value,
        'visible_on_web': True,
    },
    {
        'pattern_slot': -1,
        'shelf_type': 0,
        'dna_file_path': 'dna_backup/Bookcase_t01.json',
        'configurator_type': ConfiguratorTypeEnum.ROW.value,
        'visible_on_web': True,
    },
    {
        'pattern_slot': -1,
        'shelf_type': 1,
        'dna_file_path': 'dna_backup/Bookcase_t02.json',
        'configurator_type': ConfiguratorTypeEnum.ROW.value,
        'visible_on_web': True,
    },
    {
        'pattern_slot': -1,
        'shelf_type': 2,
        'dna_file_path': 'dna_backup/Bookcase_t01v.json',
        'configurator_type': ConfiguratorTypeEnum.ROW.value,
        'visible_on_web': True,
    },
    {
        'pattern_slot': -1,
        'shelf_type': 0,
        'dna_file_path': 'dna_backup/VinylStorage_t01_new.json',
        'configurator_type': ConfiguratorTypeEnum.ROW.value,
        'visible_on_web': True,
    },
    {
        'pattern_slot': -1,
        'shelf_type': 3,
        'dna_file_path': 'dna_backup/wardrobe.json',
        'configurator_type': ConfiguratorTypeEnum.MIXED_ROW_COLUMN.value,
        'visible_on_web': True,
    },
    {
        'pattern_slot': -1,
        'shelf_type': 0,
        'dna_file_path': 'dna_backup/Bookcase_t01_6-pack_sep2023.json',
        'configurator_type': ConfiguratorTypeEnum.ROW.value,
        'visible_on_web': True,
    },
    {
        'pattern_slot': -1,
        'shelf_type': 2,
        'dna_file_path': 'dna_backup/Bookcase_t01v_6-pack_sep2023.json',
        'configurator_type': ConfiguratorTypeEnum.ROW.value,
        'visible_on_web': True,
    },
    {
        'pattern_slot': -1,
        'shelf_type': 1,
        'dna_file_path': 'dna_backup/Bookcase_t02_6-pack_sep2023.json',
        'configurator_type': ConfiguratorTypeEnum.ROW.value,
        'visible_on_web': True,
    },
    {
        'pattern_slot': -1,
        'shelf_type': 0,
        'dna_file_path': 'dna_backup/Sideboard_t01_4-pack_sep2023.json',
        'configurator_type': ConfiguratorTypeEnum.COLUMN.value,
        'visible_on_web': True,
    },
    {
        'pattern_slot': -1,
        'shelf_type': 2,
        'dna_file_path': 'dna_backup/Sideboard_t01v_4-pack_sep2023.json',
        'configurator_type': ConfiguratorTypeEnum.COLUMN.value,
        'visible_on_web': True,
    },
    {
        'pattern_slot': -1,
        'shelf_type': 0,
        'dna_file_path': 'dna_backup/Sideboard_t01_6-pack_nov2023.json',
        'configurator_type': ConfiguratorTypeEnum.COLUMN.value,
        'visible_on_web': True,
    },
    {
        'pattern_slot': -1,
        'shelf_type': 2,
        'dna_file_path': 'dna_backup/Sideboard_t01v_6-pack_nov2023.json',
        'configurator_type': ConfiguratorTypeEnum.COLUMN.value,
        'visible_on_web': True,
    },
    {
        'pattern_slot': -1,
        'shelf_type': 1,
        'dna_file_path': 'dna_backup/Sideboard_t02_6-pack_nov2023.json',
        'configurator_type': ConfiguratorTypeEnum.COLUMN.value,
        'visible_on_web': True,
    },
    {
        'pattern_slot': -1,
        'shelf_type': 0,
        'dna_file_path': 'dna_backup/VinylStorage_t01_2-pack_sep2023.json',
        'configurator_type': ConfiguratorTypeEnum.ROW.value,
        'visible_on_web': True,
    },
    {
        'pattern_slot': -1,
        'shelf_type': 4,
        'dna_file_path': 'dna_backup/cabinet_t13_march24.json',
        'configurator_type': ConfiguratorTypeEnum.MIXED_ROW_COLUMN.value,
        'visible_on_web': True,
    },
    {
        'pattern_slot': -1,
        'shelf_type': 5,
        'dna_file_path': 'dna_backup/Cabinet_t13v.json',
        'configurator_type': ConfiguratorTypeEnum.MIXED_ROW_COLUMN.value,
        'visible_on_web': True,
    },
    {
        'pattern_slot': -1,
        'shelf_type': 4,
        'dna_file_path': 'dna_backup/Bookcase_t13.json',
        'configurator_type': ConfiguratorTypeEnum.MIXED_ROW_COLUMN.value,
        'visible_on_web': True,
    },
    {
        'pattern_slot': -1,
        'shelf_type': 5,
        'dna_file_path': 'dna_backup/Bookcase_t13v.json',
        'configurator_type': ConfiguratorTypeEnum.MIXED_ROW_COLUMN.value,
        'visible_on_web': True,
    },
    {
        'pattern_slot': -1,
        'shelf_type': 4,
        'dna_file_path': 'dna_backup/Chest_t13.json',
        'configurator_type': ConfiguratorTypeEnum.MIXED_ROW_COLUMN.value,
        'visible_on_web': True,
    },
    {
        'pattern_slot': -1,
        'shelf_type': 5,
        'dna_file_path': 'dna_backup/Chest_t13v.json',
        'configurator_type': ConfiguratorTypeEnum.MIXED_ROW_COLUMN.value,
        'visible_on_web': True,
    },
    {
        'pattern_slot': -1,
        'shelf_type': 4,
        'dna_file_path': 'dna_backup/Wallstorage_t13.json',
        'configurator_type': ConfiguratorTypeEnum.MIXED_ROW_COLUMN.value,
        'visible_on_web': True,
    },
    {
        'pattern_slot': -1,
        'shelf_type': 5,
        'dna_file_path': 'dna_backup/Wallstorage_t13v.json',
        'configurator_type': ConfiguratorTypeEnum.MIXED_ROW_COLUMN.value,
        'visible_on_web': True,
    },
    {
        'pattern_slot': -1,
        'shelf_type': 6,
        'dna_file_path': 'dna_backup/Sideboard_t23.json',
        'configurator_type': ConfiguratorTypeEnum.MIXED_ROW_COLUMN.value,
        'visible_on_web': True,
    },
    {
        'pattern_slot': -1,
        'shelf_type': 7,
        'dna_file_path': 'dna_backup/Sideboard_t24.json',
        'configurator_type': ConfiguratorTypeEnum.MIXED_ROW_COLUMN.value,
        'visible_on_web': True,
    },
    {
        'pattern_slot': -1,
        'shelf_type': 8,
        'dna_file_path': 'dna_backup/Sideboard_t25.json',
        'configurator_type': ConfiguratorTypeEnum.MIXED_ROW_COLUMN.value,
        'visible_on_web': True,
    },
    # {
    #     'pattern_slot': -1,
    #     'shelf_type': 9,
    #     'dna_file_path': 'dna_backup/Sideboard_t25v.json',
    #     'configurator_type': ConfiguratorTypeEnum.MIXED_ROW_COLUMN.value,
    #     'visible_on_web': True,
    # },
    {
        'pattern_slot': -1,
        'shelf_type': 10,
        'dna_file_path': 'dna_backup/Sofa_t10.json',
        'configurator_type': ConfiguratorTypeEnum.SOFA.value,
        'visible_on_web': True,
    },
]


class Command(BaseCommand):
    help = 'Recreate dna entries based on saved dna projects'

    def handle(self, **options):
        CustomDna.objects.all().delete()

        for dna_description in DNA_TO_BE_USED:
            dna_file_name = dna_description['dna_file_path'].split('/')[-1]
            dna_file_path = (
                Path(settings.ROOT_PATH)
                / 'gallery'
                / 'static'
                / dna_description['dna_file_path']
            )

            with open(dna_file_path, 'rb') as fp:
                content = fp.read()
                dna = ContentFile(content, name=dna_file_name)

            serializer = CustomDnaSerializer(data={**dna_description, 'dna': dna})
            serializer.is_valid(raise_exception=True)
            serializer.save()
            print(f'{dna_file_name} processed')
