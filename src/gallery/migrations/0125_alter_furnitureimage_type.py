# Generated by Django 4.2.23 on 2025-07-28 16:04

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        ('gallery', '0124_sottyproxyforpresetanalysis'),
    ]

    operations = [
        migrations.AlterField(
            model_name='furnitureimage',
            name='type',
            field=models.CharField(
                choices=[
                    ('feed', 'Feed Image'),
                    ('insta_grid', 'Insta Grid Image'),
                    ('s4l_render', 'S4L Render'),
                    ('s4l_unreal_scene', 'S4L Unreal Scene'),
                    ('s4l_unreal_scene_rectangular', 'S4L Unreal Scene Rectangular'),
                    ('unreal_studio', 'Unreal Studio'),
                    ('unreal_configurator_preview', 'Unreal Configurator Preview'),
                    ('unreal_gallery_desktop', 'Unreal Gallery Desktop'),
                    ('unreal_gallery_mobile', 'Unreal Gallery Mobile'),
                    ('render', 'Render'),
                    ('render_small', 'Render Small'),
                    ('hover_render', 'Hover Render'),
                    ('hover_render_small', 'Hover Render Small'),
                    ('grid_large', 'Grid Large'),
                    ('technical_sketch', 'Technical Sketch'),
                ],
                default='feed',
                max_length=100,
            ),
        ),
    ]
