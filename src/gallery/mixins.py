import typing

from django.db.models import (
    Exists,
    OuterRef,
    Q,
    QuerySet,
)

from custom.enums import Furniture
from gallery.enums import (
    Fabric,
    FurnitureCategory,
)
from gallery.models import (
    SampleBox,
    Sotty,
)
from gallery.types import LineItemType
from services.enums import AdditionalServiceKind
from services.models import AdditionalService

if typing.TYPE_CHECKING:
    from carts.models import CartItem
    from orders.models import OrderItem


class LineItemMixin:
    sellable_item: LineItemType

    @property
    def line_item(self) -> LineItemType:
        return self.sellable_item

    @property
    def is_corduroy(self) -> bool:
        if self._is_sample:
            return False

        elif self.is_service:
            return False

        if (
            isinstance(self.line_item, Sotty)
            and self.line_item.is_s01
            and self.line_item.fabric == Fabric.CORDUROY
        ):
            return True

        return False

    @property
    def is_legit_sotty(self) -> bool:
        return self.is_s01 and self.line_item.shelf_category != FurnitureCategory.COVER

    @property
    def is_old_sofa_collection_service(self) -> bool:
        return self._is_service(kind=AdditionalServiceKind.OLD_SOFA_COLLECTION)

    @property
    def is_s01(self) -> bool:
        if self._is_sample:
            return False

        elif self.is_service:
            return False

        elif self.line_item.is_s01:
            return True

        return False

    @property
    def is_service(self) -> bool:
        return isinstance(self.line_item, AdditionalService)

    @property
    def is_t03(self) -> bool:
        if self._is_sample:
            return False

        elif self.is_service:
            return False

        if self.line_item.is_t03_wardrobe:
            return True

        return False

    @property
    def is_white_gloves_delivery_service(self) -> bool:
        return self._is_service(kind=AdditionalServiceKind.WHITE_GLOVES_DELIVERY)

    @property
    def _is_sample(self) -> bool:
        # Samples don't count
        return isinstance(self.line_item, SampleBox)

    def _is_service(self, kind: AdditionalServiceKind) -> bool:
        if self.is_service:
            return self.line_item.kind == kind
        return False


class CartOrderMixin:
    """Common parts of Cart and Order that are hard to address elsewhere"""

    items: QuerySet['CartItem'] | QuerySet['OrderItem']
    assembly: bool

    @property
    def with_assembly(self):
        return self.items.filter(with_assembly=True).exists()

    @property
    def delivery_service_used(self) -> bool:
        """
        Currently, there are 2 delivery services: assembly and white gloves delivery
        the field should return, if either one is active
        """
        return self.assembly or self.has_white_gloves_delivery

    @property
    def has_s01(self) -> bool:
        return self.items.filter(content_type__model=Furniture.sotty.value).exists()

    @property
    def has_legit_sotty(self) -> bool:
        return self.legit_sotty_items.exists()

    @property
    def has_old_sofa_collection(self) -> bool:
        return self._has_additional_service(
            kind=AdditionalServiceKind.OLD_SOFA_COLLECTION
        )

    @property
    def has_white_gloves_delivery(self) -> bool:
        return self._has_additional_service(
            kind=AdditionalServiceKind.WHITE_GLOVES_DELIVERY
        )

    @property
    def furniture_items(self) -> QuerySet['CartItem'] | QuerySet['OrderItem']:
        # without services and skus
        return self.items.exclude(
            content_type__model__in=['additionalservice', 'skuvariant']
        )

    @property
    def material_items(self) -> QuerySet['CartItem'] | QuerySet['OrderItem']:
        # without services
        return self.items.exclude(content_type__model='additionalservice')

    @property
    def service_items(self) -> QuerySet['CartItem'] | QuerySet['OrderItem']:
        return self.items.filter(content_type__model='additionalservice')

    @property
    def legit_sotty_items(self) -> QuerySet['CartItem'] | QuerySet['OrderItem']:
        """Without covers"""
        covers = Sotty.objects.filter(
            Q(shelf_category=FurnitureCategory.COVER) | Q(covers_only=True)
        ).filter(id=OuterRef('object_id'))[:1]
        return self.items.annotate(is_cover=Exists(covers)).filter(
            content_type__model=Furniture.sotty.value, is_cover=False
        )

    def get_total_legit_sotty_items(self) -> int:
        return sum(item.quantity for item in self.legit_sotty_items)

    def get_all_sofas_modules_number(self) -> int:
        return sum(
            item.sellable_item.modules_number * item.quantity
            for item in self.legit_sotty_items
        )

    def _has_additional_service(self, kind: AdditionalServiceKind) -> bool:
        return self.items.filter(
            content_type__model='additionalservice', additional_service__kind=kind
        ).exists()
