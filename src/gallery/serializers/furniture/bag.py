import base64

from urllib.parse import urljoin

from django.conf import settings
from rest_framework import serializers

from gallery.models import (
    <PERSON>y,
    <PERSON>y,
)


class BagBaseSerializer(serializers.ModelSerializer):
    preview = serializers.SerializerMethodField(read_only=True)
    grid_all_colors = serializers.SerializerMethodField(read_only=True)
    original_url = serializers.SerializerMethodField(read_only=True)
    owner = serializers.SerializerMethodField()

    class Meta:
        abstract = True
        fields = (
            'backs',
            'components',
            'configurator_params',
            'configurator_type',
            'depth',
            'dna_name',
            'dna_object',
            'doors',
            'drawers',
            'grid_all_colors',
            'height',
            'material',
            'original_url',
            'owner',
            'pattern',
            'physical_product_version',
            'preview',
            'shelf_type',
            'width',
        )

    def _get_image_base64(self, obj, image_name):
        try:
            return base64.b64encode(obj.__getattribute__(image_name).file.read())
        except (ValueError, FileNotFoundError):
            return ''

    def get_preview(self, obj):
        return self._get_image_base64(obj, 'preview')

    def get_grid_all_colors(self, obj):
        return self._get_image_base64(obj, 'grid_all_colors')

    def get_original_url(self, obj):
        return urljoin(settings.SITE_URL, obj.get_item_url())

    def get_owner(self, obj):
        if '@' in obj.owner.username:
            return obj.owner.username
        return obj.owner.email


class JettySerializerForBag(BagBaseSerializer):
    class Meta:
        model = Jetty
        fields = BagBaseSerializer.Meta.fields + (  # noqa: RUF005
            'backpanel_styles',
            'base_preset',
            'cable_management',
            'grid_preset',
            'horizontals',
            'inserts',
            'joints',
            'legs',
            'long_legs',
            'max_capacity',
            'modules',
            'plinth',
            'preset_initial_state',
            'property1',
            'row_amount',
            'row_styles',
            'rows',
            'shelf_category',
            'supports',
            'verticals',
        )


class WattySerializerForBag(BagBaseSerializer):
    class Meta:
        model = Watty
        fields = BagBaseSerializer.Meta.fields + (  # noqa: RUF005
            'bars',
            'cable_management',
            'frame',
            'furniture_type',
            'hinges',
            'lighting',
            'masking_bars',
            'slabs',
            'walls',
        )
