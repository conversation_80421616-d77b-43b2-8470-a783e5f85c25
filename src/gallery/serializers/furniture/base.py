from decimal import (
    ROUND_HALF_UP,
    Decimal,
)
from typing import (
    Optional,
    Union,
)

from django.utils.translation import get_language
from rest_framework import serializers
from rest_framework.exceptions import ValidationError

from abtests.constants import EXTENDED_DELIVERY_TEST_NAME
from custom.views import is_ab_test_enabled
from gallery.enums import CapeCollectionType
from gallery.models import (
    <PERSON><PERSON>,
    <PERSON>pleBox,
    Watty,
)
from gallery.models.models import Sotty
from gallery.services.category_assigner import determine_sofa_category
from gallery.services.prices_for_serializers import (
    get_currency_rate,
    get_region_price_in_euro,
    get_region_price_with_discount,
    get_region_price_with_discount_in_euro,
)
from gallery.slugs import get_slug_for_furniture
from pricing_v3.models import SamplePriceSettings
from promotions.utils import strikethrough_promo
from regions.cached_region import (
    CachedRegionData,
    get_region_data_from_request,
)
from regions.models import Region
from warehouse.enums import SampleBoxVariantStatus
from warehouse.models import SampleBoxVariant


def get_serialized_furniture_data(instance: Union[<PERSON>y, <PERSON><PERSON>, SampleBox]) -> dict:
    serializer_class = {
        Jetty: JettySerializer,
        Watty: WattySerializer,
        SampleBox: SampleBoxSerializer,
    }.get(instance.__class__)
    return serializer_class(instance).data


class SellableItemSerializer(serializers.ModelSerializer):
    category = serializers.CharField(source='furniture_category', read_only=True)
    delivery_time = serializers.SerializerMethodField()
    furniture_status = serializers.ReadOnlyField(
        source='get_furniture_status_display',
        help_text='furniture status, text version',
    )
    region_price = serializers.SerializerMethodField()
    region_price_display = serializers.SerializerMethodField()
    region_price_with_discount = serializers.SerializerMethodField()
    region_price_in_euro = serializers.SerializerMethodField()
    region_price_with_discount_in_euro = serializers.SerializerMethodField()
    type = serializers.ReadOnlyField(source='get_furniture_type')
    content_type = serializers.CharField(source='content_type_model', read_only=True)

    class Meta:
        abstract = True
        fields = (
            'id',
            'base_preset',
            'category',
            'components',
            'created_at',
            'created_platform',
            'deleted',
            'delivery_time',
            'furniture_status',
            'furniture_type',
            'grid_preset',
            'preset',
            'preset_initial_state',
            'price',
            'region_price',
            'region_price_display',
            'region_price_with_discount',
            'region_price_in_euro',
            'region_price_with_discount_in_euro',
            'type',
            'updated_at',
            'content_type',
        )

    def _get_region(self, obj) -> CachedRegionData:
        if region := self.context.get('region'):
            return region

        if request := self.context.get('request'):
            return get_region_data_from_request(request)

        # check if furniture is saved to db before accessing profile
        # (e.g. when checking for eco tax)
        if obj.pk and obj.owner and obj.owner.profile.region:
            return obj.owner.profile.cached_region_data

        return Region.get_other().cached_region_data

    def _get_extended_delivery_context(self) -> bool:
        try:
            return is_ab_test_enabled(
                request=self.context['request'], codename=EXTENDED_DELIVERY_TEST_NAME
            )
        except KeyError:
            return False

    def get_delivery_time(self, obj):
        use_extended_delivery = self._get_extended_delivery_context()
        return obj.get_delivery_time_max_week(
            region=self._get_region(obj), use_extended_delivery=use_extended_delivery
        )

    def get_region_price_display(self, obj):
        return obj.get_regionalized_price_display(region=self._get_region(obj))

    def get_region_price(self, obj):
        return str(obj.get_regionalized_price(region=self._get_region(obj)))

    def get_region_price_with_discount(self, obj):
        region = self._get_region(obj)
        return str(
            get_region_price_with_discount(
                obj, region, promotion=strikethrough_promo(region)
            )
        )

    def get_region_price_in_euro(self, obj):
        region = self._get_region(obj)
        currency_rate = get_currency_rate(region)
        return str(get_region_price_in_euro(obj, currency_rate, region))

    def get_region_price_with_discount_in_euro(self, obj):
        region = self._get_region(obj)
        currency_rate = get_currency_rate(region)
        return str(
            get_region_price_with_discount_in_euro(
                obj, currency_rate, region, strikethrough_promo(region)
            ),
        )


class FurnitureBaseSerializer(SellableItemSerializer):
    color_name = serializers.CharField(
        source='translated_material_name', read_only=True
    )
    pattern_name = serializers.CharField(source='get_pattern_name', read_only=True)
    title = serializers.ReadOnlyField(source='default_title')
    additional_images = serializers.ReadOnlyField(source='get_additional_images')
    currency_code = serializers.SerializerMethodField()
    seo_slug = serializers.SerializerMethodField()
    size_txt = serializers.SerializerMethodField(method_name='get_size')
    price_with_discount = serializers.SerializerMethodField()

    class Meta:
        abstract = True
        fields = SellableItemSerializer.Meta.fields + (  # noqa: RUF005
            'additional_images',
            'color_name',
            'configurator_params',
            'configurator_type',
            'currency_code',
            'depth',
            'dna_name',
            'dna_object',
            'grid_all_colors',
            'grid_all_colors_webp',
            'height',
            'material',
            'pattern',
            'physical_product_version',
            'preview',
            'preview_webp',
            'seo_slug',
            'shelf_type',
            'size_txt',
            'title',
            'width',
            'price_with_discount',
        )

    def get_seo_slug(self, obj):
        return get_slug_for_furniture(obj, get_language())

    def get_currency_code(self, obj):
        region = self._get_region(obj)
        if region and region.currency_code:
            return region.currency_code
        if obj.owner.profile.region:
            return obj.owner.profile.region.currency.code
        else:
            return 'EUR'

    def get_size(self, obj):
        return '{} cm x {} cm {} cm'.format(
            obj.get_width(), obj.get_height(), obj.get_depth()
        )

    def get_price_with_discount(self, obj) -> Optional[str]:
        if region := self.context.get('region'):
            price = obj.get_sale_price(
                regular_price=Decimal(obj.price),
                promotion=strikethrough_promo(region),
            )
            return str(price.quantize(Decimal('1.'), rounding=ROUND_HALF_UP))


class SampleBoxSerializer(SellableItemSerializer):
    samples = serializers.JSONField(required=False)
    box_variant = serializers.IntegerField(
        source='box_variant.variant_type',
    )

    class Meta:
        model = SampleBox
        fields = SellableItemSerializer.Meta.fields + (  # noqa: RUF005
            'box_variant',
            'samples',
        )

    def validate_box_variant(self, value: int) -> SampleBoxVariant:
        if not isinstance(value, int):
            raise ValidationError('box_variant is not an integer')
        try:
            return SampleBoxVariant.objects.get(
                variant_type=value,
                status=SampleBoxVariantStatus.ACTIVE,
            )
        except SampleBoxVariant.DoesNotExist:
            raise ValidationError('Invalid box variant')

    def validate(self, attrs):
        validated_data = super().validate(attrs)
        if box_variant := validated_data.get('box_variant'):
            validated_data['box_variant'] = box_variant['variant_type']
        return validated_data

    def create(self, validated_data):
        # determine price
        variant = validated_data['box_variant']
        if variant.is_sofa_sample:
            price = SamplePriceSettings.get_sofa_sample_price_in_euro()
        else:
            price = SamplePriceSettings.get_storage_sample_price_in_euro()
        validated_data['price'] = price
        return super().create(validated_data)


class JettySerializer(FurnitureBaseSerializer):
    pattern_name = serializers.CharField(source='get_pattern_name', read_only=True)

    # TODO: remove after 06.2021 - when all app versions stop using it
    dna_object_id = serializers.ReadOnlyField(source='dna_object')

    class Meta:
        model = Jetty
        fields = FurnitureBaseSerializer.Meta.fields + (  # noqa: RUF005
            'backpanel_styles',
            'backs',
            'cable_management',
            'desk_beams',
            'doors',
            'drawers',
            'dna_object_id',
            'horizontals',
            'inserts',
            'joints',
            'legs',
            'long_legs',
            'max_capacity',
            'modules',
            'pattern_name',
            'plinth',
            'property1',
            'row_amount',
            'row_styles',
            'rows',
            'shelf_category',
            'supports',
            'verticals',
        )


class JettyForPricingSerializer(JettySerializer):
    class Meta:
        model = Jetty
        fields = (
            'horizontals',
            'inserts',
            'joints',
            'legs',
            'long_legs',
            'plinth',
            'shelf_category',
            'supports',
            'verticals',
            'backs',
            'depth',
            'doors',
            'drawers',
            'shelf_type',
            'width',
        )


class SottySerializer(FurnitureBaseSerializer):
    class Meta:
        model = Sotty
        fields = FurnitureBaseSerializer.Meta.fields + (  # noqa: RUF005
            'materials',
            'armrests',
            'chaise_longues',
            'corners',
            'footrests',
            'seaters',
            'covers_only',
        )

    def update(self, instance, validated_data):
        validated_data['category'] = determine_sofa_category(instance)
        return super().update(instance, validated_data)


class WattySerializer(FurnitureBaseSerializer):
    has_multiple_depths = serializers.BooleanField(
        read_only=True,
        source='has_mixed_depth',
    )
    has_multiple_heights = serializers.BooleanField(
        read_only=True,
        source='has_mixed_height',
    )

    class Meta:
        model = Watty
        fields = FurnitureBaseSerializer.Meta.fields + (  # noqa: RUF005
            'backs',
            'bars',
            'cable_management',
            'doors',
            'drawers',
            'frame',
            'hinges',
            'masking_bars',
            'slabs',
            'walls',
            'lighting',
            'legs',
            'has_multiple_depths',
            'has_multiple_heights',
            'shelf_category',
        )

    def create(self, validated_data):
        if not validated_data.get('shelf_category'):
            collection_type = (
                validated_data.get('configurator_params', {})
                .get('additional_parameters', {})
                .get('collection_type', None)
            )
            if collection_type:
                shelf_category = CapeCollectionType(
                    collection_type
                ).get_watty_category_from_collection_type()
                validated_data['shelf_category'] = shelf_category

        return super().create(validated_data)


class WattyForPricingSerializer(WattySerializer):
    class Meta:
        model = Watty
        fields = (
            'bars',
            'cable_management',
            'frame',
            'hinges',
            'masking_bars',
            'slabs',
            'walls',
            'lighting',
            'legs',
            'backs',
            'depth',
            'doors',
            'drawers',
            'shelf_type',
            'width',
        )
