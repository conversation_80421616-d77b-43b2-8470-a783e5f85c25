from decimal import Decimal

from django.utils import translation
from rest_framework import serializers
from rest_framework.serializers import ModelSerializer

from abtests.constants import EXTENDED_DELIVERY_TEST_NAME
from custom.serializers import CamelizeSelectedKeysMixin
from custom.views import is_ab_test_enabled
from ecommerce_api.serializers import BaseSerializerContext
from gallery.enums import FurnitureCategory
from gallery.models import (
    Jetty,
    SampleBox,
    Sotty,
    Watty,
)
from gallery.services.prices_for_serializers import RegionCurrencySerializerMixin
from gallery.services.sotty_single_module import SottySingleModuleRepository
from gallery.types import SellableItemType
from pricing_v3.models import SamplePriceSettings
from warehouse.api.serializers import SampleBoxVariantSerializer


class SellableItemCartSerializerBase(
    BaseSerializerContext,
    CamelizeSelectedKeysMixin,
    serializers.Serializer,
):
    shelf_id = serializers.IntegerField(source='id')
    item_url = serializers.CharField(source='get_item_url')
    item_max_load = serializers.CharField(source='get_max_load')
    item_furniture_type = serializers.CharField(source='product_type')
    color_name = serializers.CharField(source='translated_material_name')
    size_txt = serializers.CharField(source='get_size')
    width = serializers.IntegerField(source='get_width')
    height = serializers.IntegerField(source='get_height')
    depth = serializers.IntegerField(source='get_depth')
    pattern_name = serializers.CharField(source='get_pattern_name')

    category = serializers.SerializerMethodField()
    translated_category_display = serializers.SerializerMethodField()
    translated_item_url = serializers.SerializerMethodField()
    item_image_url = serializers.SerializerMethodField()
    item_description_material = serializers.SerializerMethodField()
    item_description_dimensions = serializers.SerializerMethodField()
    shelf_type = serializers.SerializerMethodField()
    material = serializers.SerializerMethodField()
    configurator_type = serializers.SerializerMethodField()
    physical_product_version = serializers.SerializerMethodField()
    delivery_time = serializers.SerializerMethodField()
    base_preset = serializers.SerializerMethodField()

    density = serializers.SerializerMethodField()
    drawers = serializers.SerializerMethodField()
    doors = serializers.SerializerMethodField()
    lighting = serializers.SerializerMethodField()
    has_multiple_depths = serializers.SerializerMethodField()
    has_multiple_heights = serializers.SerializerMethodField()

    class Meta:
        fields = [
            'shelf_id',
            'item_url',
            'item_max_load',
            'item_furniture_type',
            'color_name',
            'size_txt',
            'width',
            'height',
            'depth',
            'pattern_name',
            'category',
            'translated_category_display',
            'translated_item_url',
            'item_image_url',
            'item_description_material',
            'item_description_dimensions',
            'density',
            'drawers',
            'doors',
            'shelf_type',
            'material',
            'lighting',
            'configurator_type',
            'physical_product_version',
            'delivery_time',
            'base_preset',
            'has_multiple_depths',
            'has_multiple_heights',
        ]

    CAMEL_CASE_FIELDS = [
        'item_url',
        'translated_item_url',
        'item_image_url',
        'item_description_material',
        'item_description_dimensions',
        'item_max_load',
        'shelf_id',
        'item_furniture_type',
        'configurator_type',
        'physical_product_version',
    ]

    def _get_extended_delivery_context(self) -> bool:
        try:
            return is_ab_test_enabled(
                request=self.request, codename=EXTENDED_DELIVERY_TEST_NAME
            )
        except AttributeError:
            return False

    def get_translated_item_url(self, obj: SellableItemType) -> str:
        return obj.get_item_translated_url(self.language)

    @staticmethod
    def get_item_image_url(obj: SellableItemType) -> str:
        return obj.preview.url if obj.preview else ''

    @staticmethod
    def get_item_description_material(obj: SellableItemType) -> str:
        return obj.get_item_description().get('material', '')

    @staticmethod
    def get_item_description_dimensions(obj: SellableItemType) -> str:
        return obj.get_item_description().get('dimensions', '')

    @staticmethod
    def get_category(obj: SellableItemType) -> str:
        furniture_category = obj.furniture_category or ''
        # TODO: refactor this weird replacement with FE
        return furniture_category.replace('_', ' ')

    @staticmethod
    def get_translated_category_display(obj: SellableItemType) -> str:
        # TODO: refactor this translated category not translated
        return (
            obj.furniture_category
            if obj.furniture_category != FurnitureCategory.WARDROBE
            else ''
        )

    @staticmethod
    def get_density(obj: SellableItemType) -> str:
        return str(getattr(obj, 'property1', '') or '')

    @staticmethod
    def get_drawers(obj: SellableItemType) -> int:
        return len(getattr(obj, 'drawers', [])) or 0

    @staticmethod
    def get_doors(obj: SellableItemType) -> int:
        return len(getattr(obj, 'doors', []) or [])

    @staticmethod
    def get_shelf_type(obj: SellableItemType) -> int:
        return getattr(obj, 'shelf_type', 0)

    @staticmethod
    def get_material(obj: SellableItemType) -> str:
        return str(getattr(obj, 'material', '') or '')

    @staticmethod
    def get_lighting(obj: SellableItemType) -> bool:
        return bool(getattr(obj, 'lighting', False))

    @staticmethod
    def get_configurator_type(obj: SellableItemType) -> str:
        return getattr(obj, 'configurator_type', '')

    @staticmethod
    def get_physical_product_version(obj: SellableItemType) -> str:
        return getattr(obj, 'physical_product_version', '')

    def get_delivery_time(self, obj: SellableItemType) -> int:
        use_extended_delivery = self._get_extended_delivery_context()
        return obj.get_delivery_time_max_week(
            use_extended_delivery=use_extended_delivery
        )

    @staticmethod
    def get_base_preset(obj: SellableItemType) -> str:
        return getattr(obj, 'base_preset', '') or ''

    @staticmethod
    def get_has_multiple_depths(obj: SellableItemType) -> bool:
        return getattr(obj, 'has_mixed_depth', False)

    @staticmethod
    def get_has_multiple_heights(obj: SellableItemType) -> bool:
        return getattr(obj, 'has_mixed_height', False)


class JettyCartSerializer(SellableItemCartSerializerBase, ModelSerializer):
    class Meta:
        model = Jetty
        fields = SellableItemCartSerializerBase.Meta.fields


class SottyCartSerializer(SellableItemCartSerializerBase, ModelSerializer):
    name = serializers.SerializerMethodField()

    class Meta:
        model = Sotty
        fields = SellableItemCartSerializerBase.Meta.fields + ['name']  # noqa: RUF005

    def get_name(self, obj: Sotty) -> str | None:
        if obj.shelf_category != FurnitureCategory.COVER:
            # FE decides the name for not covers
            return None
        elif not obj.is_single_module:
            # FE decides the name for not single modules
            return None
        repository = SottySingleModuleRepository(sotty=obj)
        with translation.override(self.context['language']):
            return repository.get_translation()


class WattyCartSerializer(SellableItemCartSerializerBase, ModelSerializer):
    class Meta:
        model = Watty
        fields = SellableItemCartSerializerBase.Meta.fields


class SampleBoxCartSerializer(
    SellableItemCartSerializerBase, RegionCurrencySerializerMixin, ModelSerializer
):
    box_variant = serializers.SerializerMethodField()
    region_price = serializers.SerializerMethodField()
    region_price_with_discount = serializers.SerializerMethodField()
    region_price_in_euro = serializers.SerializerMethodField()
    region_price_with_discount_in_euro = serializers.SerializerMethodField()
    item_price_regionalized = serializers.SerializerMethodField()
    item_price_without_discount = serializers.SerializerMethodField()
    item_price_regionalized_number = serializers.SerializerMethodField()
    item_price_without_discount_regionalized_number = (
        serializers.SerializerMethodField()
    )
    omnibus_price = serializers.SerializerMethodField()

    class Meta:
        model = SampleBox
        fields = SellableItemCartSerializerBase.Meta.fields + [  # noqa: RUF005
            'box_variant',
            'region_price',
            'region_price_with_discount',
            'region_price_in_euro',
            'region_price_with_discount_in_euro',
            'item_price_regionalized',
            'item_price_without_discount',
            'item_price_regionalized_number',
            'item_price_without_discount_regionalized_number',
            'omnibus_price',
        ]

    CAMEL_CASE_FIELDS = SellableItemCartSerializerBase.CAMEL_CASE_FIELDS

    def to_representation(self, instance: SampleBox) -> dict:
        data = super().to_representation(instance)
        box_variant_fields = data.pop('box_variant', {})
        box_variant_fields.pop('region_price')
        data.update(box_variant_fields)
        return data

    def get_box_variant(self, obj: SampleBox) -> dict:
        return SampleBoxVariantSerializer(obj.box_variant).data

    @property
    def _price(self):
        if self.instance.box_variant.is_sofa_sample:
            return SamplePriceSettings.get_sofa_sample_base_price()
        return SamplePriceSettings.get_storage_sample_base_price()

    @property
    def _region_price(self) -> Decimal:
        return self.region_calculations_object.calculate_regionalized(
            base_value=self._price
        )

    @property
    def _sale_price(self):
        if self.instance.box_variant.is_sofa_sample:
            return SamplePriceSettings.get_sofa_sample_price_in_euro()
        return SamplePriceSettings.get_storage_sample_price_in_euro()

    @property
    def _region_sale_price(self) -> Decimal:
        return self.region_calculations_object.calculate_regionalized(
            base_value=self._sale_price
        )

    def get_region_price(self, _: SampleBox) -> Decimal:
        return self._region_price

    def get_region_price_with_discount(self, _: SampleBox) -> Decimal:
        return self._region_sale_price

    def get_region_price_in_euro(self, _: SampleBox) -> Decimal:
        return self._price

    def get_region_price_with_discount_in_euro(self, _: SampleBox) -> Decimal:
        return self._sale_price

    def get_item_price_regionalized(self, _: SampleBox) -> str:
        return self.region.get_format_price(price=self._region_sale_price)

    def get_item_price_without_discount(self, _: SampleBox) -> str:
        return self.region.get_format_price(price=self._region_price)

    def get_item_price_regionalized_number(self, _: SampleBox) -> Decimal:
        return self._region_sale_price

    def get_item_price_without_discount_regionalized_number(
        self, _: SampleBox
    ) -> Decimal:
        return self._region_price

    def get_omnibus_price(self, obj: SampleBox) -> Decimal | str:
        """Returns non-promo price if sample is on promo; else empty string."""
        if (
            obj.box_variant.is_sofa_sample
            and SamplePriceSettings.is_sofa_sample_promo_active()
        ):
            return self._region_price
        elif (
            obj.box_variant.is_storage_sample
            and SamplePriceSettings.is_storage_sample_promo_active()
        ):
            return self._region_price
        return ''
