from rest_framework import serializers

from gallery.constants import SOFA_FURNITURE_CATEGORY
from gallery.models import Sotty
from gallery.serializers.furniture.base import FurnitureBaseSerializer


class SottyConfiguratorSerializer(FurnitureBaseSerializer):
    category = serializers.CharField(default=SOFA_FURNITURE_CATEGORY)

    class Meta:
        model = Sotty
        fields = FurnitureBaseSerializer.Meta.fields + (  # noqa: RUF005
            'materials',
            'armrests',
            'chaise_longues',
            'corners',
            'footrests',
            'seaters',
            'covers_only',
        )
