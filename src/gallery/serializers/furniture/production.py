from typing import Union

from django.core.exceptions import ObjectDoesNotExist
from rest_framework import serializers

from gallery.models import (
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON><PERSON>,
)
from gallery.serializers import (
    JettySerializer,
    SampleBoxSerializer,
    SottySerializer,
    WattySerializer,
)


def furniture_serializer_class_factory(item):
    if isinstance(item, Jetty):
        return JettySerializerForProduction
    elif isinstance(item, SampleBox):
        return SampleBoxSerializer
    elif isinstance(item, Watty):
        return WattySerializerForProduction
    elif isinstance(item, Sotty):
        return SottySerializerForProduction
    return None


class ProductionSerializerMixin:
    assembly_service = serializers.SerializerMethodField()
    country = serializers.SerializerMethodField()

    def get_country(self, obj: Jetty | Watty) -> str:
        """<PERSON><PERSON> needs country to determine which electric plug to use."""
        order = self.context.get('order')
        if order:
            return order.country
        cart = self.context.get('cart')
        if cart:
            return cart.region.country.name

        try:
            return obj.ordered_item.first().order.country
        except (ObjectDoesNotExist, AttributeError):
            return ''

    def get_assembly_service(self, obj: Union[Jetty, Watty]) -> bool:
        order = self.context.get('order')
        if order:
            item = [item for item in order.items.all() if item.object_id == obj.id][0]  # noqa: RUF015
            return True if item.order_item.is_t03_wardrobe else order.assembly
        return obj.assembly_service

    class Meta:
        abstract = True
        fields = (
            'id',
            'assembly_service',
            'country',
            'depth',
            'height',
            'material',
            'pattern',
            'physical_product_version',
            'shelf_type',
            'width',
            'updated_at',
        )


class JettySerializerForProduction(ProductionSerializerMixin, JettySerializer):
    country = serializers.SerializerMethodField()
    assembly_service = serializers.SerializerMethodField()

    class Meta(JettySerializer.Meta):
        fields = ProductionSerializerMixin.Meta.fields + (  # noqa: RUF005
            'backs',
            'cable_management',
            'desk_beams',
            'doors',
            'drawers',
            'horizontals',
            'inserts',
            'joints',  # WYJEBONGO
            'legs',
            'long_legs',
            'modules',  # WYJEBONGO
            'plinth',
            'supports',
            'verticals',
        )


class WattySerializerForProduction(ProductionSerializerMixin, WattySerializer):
    country = serializers.SerializerMethodField()
    assembly_service = serializers.SerializerMethodField()

    class Meta(WattySerializer.Meta):
        fields = ProductionSerializerMixin.Meta.fields + (  # noqa: RUF005
            'backs',
            'bars',
            'cable_management',
            'doors',
            'drawers',
            'frame',
            'hinges',
            'masking_bars',
            'slabs',
            'walls',
            'lighting',
            'legs',
        )


class SottySerializerForProduction(ProductionSerializerMixin, SottySerializer):
    country = serializers.SerializerMethodField()
    assembly_service = serializers.SerializerMethodField()

    class Meta(SottySerializer.Meta):
        fields = ProductionSerializerMixin.Meta.fields + (  # noqa: RUF005
            'materials',
            'armrests',
            'chaise_longues',
            'corners',
            'footrests',
            'seaters',
            'covers_only',
        )
