from typing import Union

from rest_framework import serializers

from gallery.constants import SOFA_FURNITURE_CATEGORY
from gallery.models import (
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON><PERSON>,
)


class UnrealRenderFurnitureSerializer(serializers.ModelSerializer):
    line = serializers.CharField(source='product_line')
    collection = serializers.SerializerMethodField()

    class Meta:
        abstract = True
        fields = [
            'furniture_category',
            'configurator_type',
            'shelf_type',
            'line',
            'collection',
            'configurator_params',
        ]

    @staticmethod
    def get_collection(obj: Union[Jetty, Watty]) -> str:
        return obj.configurator_params.get('additional_parameters', {}).get(
            'collection_type', ''
        )


_all_furniture_fields = (
    'width',
    'height',
    'depth',
    'material',
    'configurator_params',
    'configurator_type',
    'physical_product_version',
    'furniture_category',
    'furniture_type',
    'shelf_category',
    'shelf_type',
    'pattern',
    'components',
)

_storage_fields = _all_furniture_fields + (  # noqa: RUF005
    'backs',
    'cable_management',
    'doors',
    'drawers',
    'legs',
    'pattern',
)


class UnrealJettyGeometrySerializer(serializers.ModelSerializer):
    class Meta:
        model = Jetty
        fields = _storage_fields + (  # noqa: RUF005
            'desk_beams',
            'furniture_type',
            'horizontals',
            'inserts',
            'long_legs',
            'plinth',
            'rows',
            'supports',
            'verticals',
        )


class UnrealSottyGeometrySerializer(serializers.ModelSerializer):
    furniture_category = serializers.CharField(
        default=SOFA_FURNITURE_CATEGORY, read_only=True
    )
    shelf_category = serializers.CharField(
        default=SOFA_FURNITURE_CATEGORY, read_only=True
    )

    class Meta:
        model = Sotty
        fields = _all_furniture_fields + Sotty.GEOMETRY_FIELDS


class UnrealWattyGeometrySerializer(serializers.ModelSerializer):
    class Meta:
        model = Watty
        fields = _storage_fields + (  # noqa: RUF005
            'slabs',
            'walls',
            'hinges',
            'bars',
            'frame',
            'masking_bars',
            'lighting',
        )
