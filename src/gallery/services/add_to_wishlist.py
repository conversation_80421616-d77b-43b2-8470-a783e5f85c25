import logging

from django.contrib.auth import get_user_model
from django.db.models import Q
from django.utils import timezone
from rest_framework.exceptions import ValidationError
from rest_framework.request import Request

from custom.enums import Furniture
from custom.metrics import metrics_client
from custom.shelf_states_interactor import add_jetty_state_to_redis
from custom.utils.url import get_request_source
from gallery.enums import ShelfStatusSource
from gallery.models import Jetty
from gallery.services.saved_item_events import emit_saved_item_related_events
from gallery.types import FurnitureType
from gallery.utils import send_to_facebook_scraper
from mailing.enums import MailingMetricType
from mailing.utils import new_renders_metric_key_for
from regions.constants import OTHER_REGION_NAME
from render_tasks.enums import InteriorType
from render_tasks.utils import generate_unreal_render_task_for_s4l
from user_profile.choices import UserType
from user_profile.models import (
    RetargetingBlacklistToken,
    UserProspect,
)

User = get_user_model()
logger = logging.getLogger('cstm')


class WishlistService:
    def __init__(
        self, request: Request, email: str, marketing_perms: bool, popup_src: str | None
    ) -> None:
        self.request = request
        self.user = self.request.user
        self.profile = self.user.profile
        self.region = self.profile.get_region()
        self.email_address = email
        self.has_marketing_permissions = marketing_perms
        self.popup_src = popup_src

        self.user_from_email = None

    def add_to_wishlist_popup(self, item: FurnitureType) -> None:
        if not self.email_address:
            raise ValidationError('Missing email')

        self.user_from_email = User.objects.filter(
            Q(username=self.email_address) | Q(username=self.email_address.lower())
        ).first()
        if isinstance(item, Jetty):
            add_jetty_state_to_redis(
                user_id=self.user.id,
                jetty=item,
                source=ShelfStatusSource.SAVE_FOR_LATER,
                pagepath=self.request.get_full_path(),
            )

        self._handle_email(item)
        self._create_prospect()

        if item.product_type != Furniture.sotty.value:
            # TODO: include Sotty
            generate_unreal_render_task_for_s4l(item, InteriorType.SCENE)
            send_to_facebook_scraper(self.request, item)
            metrics_client().increment(
                new_renders_metric_key_for(
                    {'watty': 'SavedWattyFlow', 'jetty': 'SavedItemOneFlow'}[
                        item.furniture_type
                    ],
                    MailingMetricType.FILTERED_FOR_PROCESSING,
                ),
                1,
            )

        emit_saved_item_related_events(
            item=item,
            email=self.email_address,
            source=self.popup_src,
            marketing_permission=self.has_marketing_permissions,
        )

    def _handle_email(self, item: FurnitureType) -> None:
        if self.user_from_email:
            if self.user_from_email.email != self.email_address:
                self.user_from_email.email = self.email_address
                self.user_from_email.save(update_fields=['email'])
            item.owner = self.user_from_email
            item.save(update_fields=['owner'])
            self.user_from_email.profile.clear_library_item_number_cache()
            return

        self.user.profile.clear_library_item_number_cache()

        if self.user.profile.user_type == UserType.GUEST_CUSTOMER:
            self.user.email = self.email_address
            self.user.save(update_fields=['email'])
        RetargetingBlacklistToken.objects.get_or_create(
            email=self.email_address,
            defaults={
                'email': self.email_address,
                'token': RetargetingBlacklistToken.create_token(),
            },
        )

    def _create_prospect(self) -> None:
        prospect = UserProspect(
            prospect_source=get_request_source(self.request),
            email=self.email_address,
            origin=self.popup_src
            if self.popup_src
            in ('save for later popup', 'exit popup', 'mobile exit section')
            else None,
            event_date=timezone.now(),
            description=self.request.META.get('HTTP_X_REAL_IP'),
        )
        if prospect.origin and self.region.name == OTHER_REGION_NAME:
            prospect.origin += ' - outside eu'
        prospect.save()
