from django.core.exceptions import ValidationError
from rest_framework.exceptions import ValidationError as DRFValidationError

from complaints.exceptions import ComplaintNotReproductionException
from complaints.serializers import ComplaintExportSerializer
from custom.enums import (
    Furniture,
    LanguageEnum,
)
from gallery.exceptions import NotCreatedAnyObjectsException
from gallery.serializers import (
    JettyImportExportSerializer,
    SottyImportExportSerializer,
    WattyImportExportSerializer,
)
from orders.enums import OrderType
from orders.internal_api.events import OrderRefreshEvent
from orders.models import Order
from regions.models import Region
from vouchers.models import Voucher


class ImportObjectsService:
    """
    Base class for importing objects: Gallery, Product, ProductComplaints
    """

    def __init__(self, json_data, owner_id):
        self.json_data = self.reformat(json_data)
        self.gallery_type = self.get_gallery_type(self.json_data)
        self.is_complaint = self.is_complaint(self.json_data)
        self.owner_id = owner_id
        self.objects_count = len(self.json_data)

    @staticmethod
    def reformat(json_data):
        """Allow processing single or listed Jetty/Sotty/Watty files."""
        if isinstance(json_data, list) and (
            'horizontals' in json_data[0]
            or 'walls' in json_data[0]
            or 'seaters' in json_data[0]
        ):
            return {
                entry_id: {'gallery': entry} for entry_id, entry in enumerate(json_data)
            }
        if isinstance(json_data, dict) and (
            'horizontals' in json_data or 'walls' in json_data or 'seaters' in json_data
        ):
            return {
                0: {'gallery': json_data},
            }
        return json_data

    @staticmethod
    def is_complaint(json_data_from_file):
        return 'complaint' in list(json_data_from_file.values())[0]  # noqa: RUF015

    @staticmethod
    def get_gallery_type(json_data):
        gallery_data = dict(list(json_data.values())[0])  # noqa: RUF015
        if 'horizontals' in gallery_data['gallery'].keys():
            return Furniture.jetty.value
        if 'walls' in gallery_data['gallery'].keys():
            return Furniture.watty.value
        if 'seaters' in gallery_data['gallery'].keys():
            return Furniture.sotty.value
        raise ValueError('Looks like the JSON is misformated, please check it')

    def create_objects_from_json(self, process_to_production, is_influencers):
        self._validate_is_not_cape_serialization()
        objects, errors = self.create_objects()
        if errors:
            raise ValidationError(errors)

        if not objects:
            raise NotCreatedAnyObjectsException

        if not process_to_production:
            return [obj['gallery'].id for obj in objects]

        return self.create_products(objects, is_influencers, errors)

    def create_objects(self):
        """Get Gallery and Complaint objects from JSON data"""
        created_products_objects = []
        errors = []

        for product_id, data in self.json_data.items():
            product_objects = {'product_id': product_id}

            data_gallery = data['gallery']
            try:
                product_objects['gallery'] = self.create_gallery_object(data_gallery)
            except (
                ValidationError,
                DRFValidationError,
            ) as exc:
                errors.append(str(exc))

            created_products_objects.append(product_objects)
        return created_products_objects, errors

    def create_gallery_object(self, data):
        data['owner'] = self.owner_id
        gallery_serializer = self.get_serializer_gallery()
        gallery_object = gallery_serializer(data=data)
        gallery_object.is_valid(raise_exception=True)
        gallery_object = gallery_object.save()
        return gallery_object

    def create_complaint_object(self, complaint_data, product):
        complaint_data['owner'] = self.owner_id
        complaint_data['product'] = product.id
        complaint_serialized = ComplaintExportSerializer(data=complaint_data)
        complaint_serialized.is_valid(raise_exception=True)
        complaint_object = complaint_serialized.save()
        complaint_object.process_complaint_to_production()
        return complaint_object

    def create_products(self, products_objects, is_influencers, errors):
        products_ids_created = []
        for product_data in products_objects:
            gallery_object = product_data['gallery']

            order = self.create_order(
                owner=self.owner_id, is_influencers=is_influencers
            )
            self.create_order_items(order, gallery_object)
            order.move_to_production_service.move()
            OrderRefreshEvent(order)
            product_object = order.product_set.first()

            if self.is_complaint:
                product_id = product_data['product_id']
                complaint_data = self.json_data[product_id]['complaint']

                try:
                    object_complaint = self.create_complaint_object(
                        complaint_data,
                        product_object,
                    )
                except (
                    ComplaintNotReproductionException,
                    ValidationError,
                    DRFValidationError,
                ) as exc:
                    errors.append(str(exc))
                    continue
                products_ids_created.append(object_complaint.reproduction_product_id)
            else:
                products_ids_created.append(product_object.id)

        if errors:
            errors_msg = ', '.join(errors)
            raise ValidationError(
                f'Following errors occurred while importing objects: {errors_msg}'
            )

        if not products_ids_created:
            raise NotCreatedAnyObjectsException
        return products_ids_created

    @staticmethod
    def create_order(owner, is_influencers=False):
        order = Order.objects.create(
            owner_id=owner,
            first_name='TEST IMPORTED FIRST NAME',
            last_name='TEST IMPORTED LAST NAME',
            street_address_1='TEST IMPORTED ADDRESS',
            email='<EMAIL>',
            country='poland',
            city='TEST IMPORTED CITY',
            total_price=0,
            order_type=OrderType.CUSTOM_ORDER,
            region=Region.get_region_for_language(LanguageEnum.PL),
        )
        if is_influencers:
            generic_influencer_voucher = Voucher.objects.get(
                code=Voucher.INFLUENCER_GENERIC_CODE
            )
            order.vouchers.add(generic_influencer_voucher)
        return order

    @staticmethod
    def create_order_items(order, gallery_object):
        order_item_kwargs = {
            'price': 25,
            'region_price': 25,
            'price_net': 20.32,
            'region_price_net': 20.32,
            'vat_amount': 4.68,
            'quantity': 1,
        }
        order.items.create(order_item=gallery_object, **order_item_kwargs)
        order.change_products_to_ordered()

    def get_serializer_gallery(self):
        if self.gallery_type == Furniture.jetty.value:
            return JettyImportExportSerializer
        if self.gallery_type == Furniture.watty.value:
            return WattyImportExportSerializer
        if self.gallery_type == Furniture.sotty.value:
            return SottyImportExportSerializer

    def _validate_is_not_cape_serialization(self):
        if 'superior_object_type' in self.json_data:
            raise ValidationError('Upload the other json')
