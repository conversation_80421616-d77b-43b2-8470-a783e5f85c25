import math

from numbers import Number
from typing import TYPE_CHECKING

from gallery.constants import SEATING_DEPTH_OFFSET
from gallery.enums import SottySeatingDepthCases

if TYPE_CHECKING:
    from gallery.models import Sotty


class SottySeatingDepthHandler:
    def __init__(self, sotty: 'Sotty'):
        self.sotty = sotty

    def _get_sotty_case(self) -> str:
        if (
            self.sotty.has_extended_chaise_longue_module
            and not self.sotty.has_seater_module
        ):
            return SottySeatingDepthCases.SINGLE_EXTENDED_CHAISE_LONGUE
        elif self.sotty.seaters:
            return SottySeatingDepthCases.SEATERS
        elif self.sotty.corners:  # no seaters
            return SottySeatingDepthCases.CORNER
        elif self.sotty.chaise_longues:  # no seaters, corners
            return SottySeatingDepthCases.CHAISE_LONGUE
        elif self.sotty.footrests:  # no seaters, corners, chaise longues
            return SottySeatingDepthCases.SINGLE_FOOTRESTS
        else:
            return SottySeatingDepthCases.SEATERS

    def _get_max_depth(self) -> Number:
        sotty_case = self._get_sotty_case()

        depth_calculators = {
            SottySeatingDepthCases.CORNER: lambda: self._max_depth_from_items(
                self.sotty.corners
            ),
            SottySeatingDepthCases.SINGLE_FOOTRESTS: lambda: self._max_depth_from_items(
                self.sotty.footrests
            ),
            SottySeatingDepthCases.SINGLE_EXTENDED_CHAISE_LONGUE: lambda: (
                self._max_depth_from_items(self.sotty.footrests)
                + self._max_depth_from_items(self.sotty.seaters)
            ),
            SottySeatingDepthCases.CHAISE_LONGUE: lambda: self._max_depth_from_items(
                self.sotty.chaise_longues
            ),
            SottySeatingDepthCases.SEATERS: lambda: self._max_depth_from_items(
                self.sotty.seaters
            ),
        }

        return depth_calculators.get(sotty_case, lambda: None)()

    def _max_depth_from_items(self, items):
        if not items:
            return 0
        return max(item.get('depth', 0) for item in items)

    def get_seating_depth(self) -> int | None:
        """Return value in centimeters (cm)"""
        max_depth = self._get_max_depth()
        return (
            math.ceil((max_depth - SEATING_DEPTH_OFFSET) / 10.0) if max_depth else None
        )
