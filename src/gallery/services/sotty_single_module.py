from typing import (
    Iterable,
    Literal,
    Mapping,
    TypeAlias,
)

from django.utils.functional import Promise
from django.utils.translation import gettext as _

from custom.enums import ShelfType
from gallery.enums import SottyModuleType
from gallery.models import Sotty

SottyModuleNames: TypeAlias = Literal[
    'armrest',
    'chaise_longue',
    'extended_chaise_longue',
    'footrest',
    'left_corner',
    'right_corner',
    'seater',
]


class SottySingleModuleRepository:
    _ATTRS = {'material', 'width', 'depth'}

    def __init__(self, sotty: Sotty):
        self.sotty = sotty
        self.module_type = self.get_module_type(sotty)
        self.material = sotty.material
        self.width = sotty.width
        self.depth = self.get_depth()

        self._siblings = None
        self._extra_depth = None

    @staticmethod
    def get_module_type(sotty: Sotty) -> SottyModuleType:
        return sotty.get_module_types()[0]

    def get_depth(self) -> int:
        if self.module_type != SottyModuleType.EXTENDED_CHAISE_LONGUE:
            return self.sotty.depth

        return self.sotty.seaters[0]['depth']

    @property
    def extra_depth(self) -> int | None:
        if (
            self._extra_depth is None
            and self.module_type == SottyModuleType.EXTENDED_CHAISE_LONGUE
        ):
            self._extra_depth = self.sotty.footrests[0]['width']
        return self._extra_depth

    @property
    def siblings(self) -> int | None:
        if self._siblings is None:
            self._siblings = self.get_module_type_siblings()
        return self._siblings

    def get_module_type_siblings(self) -> Iterable[dict]:
        single_modules_presets = Sotty.objects.get_single_modules_presets(
            covers_only=self.sotty.covers_only,
            module_type=self.module_type,
        )
        if self.module_type == SottyModuleType.CORNER:
            single_modules_presets = [
                single_modules_preset
                for single_modules_preset in single_modules_presets
                if single_modules_preset['orientation'] == self.sotty.orientation
            ]
        return single_modules_presets

    def get_module_name(self) -> SottyModuleNames:
        if self.module_type in SottyModuleType.get_footrest_types():
            name = SottyModuleType.FOOTREST.label
        elif self.module_type == SottyModuleType.CORNER:
            name = f'{self.sotty.orientation} {self.module_type.label}'
        else:
            name = self.module_type.label

        return name.replace(' ', '_').lower()

    def get_translation(self) -> Promise:
        translation_key = self.module_type.translation_key
        if self.sotty.covers_only:
            translation_key += '_cover'
        if self.module_type == SottyModuleType.CORNER:
            translation_key = f'{self.sotty.orientation.value}_{translation_key}'
        return _(translation_key)

    def get_active_attributes(self) -> dict[str, int]:
        active_attributes = {
            'material': self.material,
            'width': self.width,
            'depth': self.depth,
        }
        if self.extra_depth:
            active_attributes['extra_depth'] = self.extra_depth
        return active_attributes

    def get_attributes_map(self) -> dict[str, dict[int, int | None]]:
        """
        Build a mapping of every allowed *material*, *width* and *depth*
        value to the **id** of the first sibling that differs in the other
        two attributes—if such a sibling exists—otherwise ``None``.

        Example:
        -------
        {
            "material": {1: 11, 2: 42},
            "width":    {100: 7, 120: None},
            "depth":    {60: 8, 80: 9},
        }
        """

        candidates = self._get_candidates()
        return {
            attr: {
                v: self._find_variation_id(candidates.keys(), attr, v) for v in values
            }
            for attr, values in candidates.items()
        }

    def _get_candidates(self) -> Mapping[str, Iterable[int]]:
        # values we need to iterate over for each attribute
        candidates = {
            'material': ShelfType.SOFA_TYPE01.colors.get_active_colors(),
        }
        if self.module_type.valid_widths:
            candidates['width'] = self.module_type.valid_widths
        if self.module_type.valid_depths:
            candidates['depth'] = self.module_type.valid_depths
        if self.module_type.valid_extra_depths:
            candidates['extra_depth'] = self.module_type.valid_extra_depths
        return candidates

    def _find_variation_id(
        self, common_attributes: set[str], attribute: str, value: int
    ) -> int | None:
        """
        Return the *id* of the first sibling that matches *attribute=value*
        while differing in every other attribute; otherwise ``None``.
        """
        others = common_attributes - {attribute}

        sibling = next(
            (
                sibling
                for sibling in self.siblings
                if sibling[attribute] == value
                and all(sibling[other] == getattr(self, other) for other in others)
            ),
            None,
        )
        return sibling['id'] if sibling else None
