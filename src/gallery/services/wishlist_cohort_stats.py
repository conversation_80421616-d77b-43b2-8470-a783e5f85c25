import datetime

from typing import Tuple

from django.conf import settings
from django.contrib.auth import get_user_model
from django.utils import timezone

import requests

from dateutil.relativedelta import relativedelta

from gallery.enums import FurnitureStatusEnum
from gallery.models import (
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON><PERSON>,
)
from orders.enums import OrderStatus
from orders.models import Order
from user_profile.choices import UserType

User = get_user_model()

COMPLETED_ORDER_STATUSES = [
    OrderStatus.IN_PRODUCTION,
    OrderStatus.SHIPPED,
    OrderStatus.TO_BE_SHIPPED,
    OrderStatus.DELIVERED,
]

CUSTOMER_TYPES = [UserType.GUEST_CUSTOMER, UserType.CUSTOMER]


def send_s4l_summary_to_slack() -> None:
    """Send a summary of S4L stats to Slack for the last 2 months from last Sunday."""
    now = timezone.now()
    days_since_sunday = (now.weekday() + 1) % 7
    last_sunday = now - datetime.timedelta(days=days_since_sunday)
    two_months_ago = (last_sunday - relativedelta(months=2)).date()
    all_owners_count, converted_orders_count = s4l_cohort_counter(
        two_months_ago,
        last_sunday.date(),
    )

    date_range = f'{two_months_ago} - {last_sunday.date()}'
    _send_s4l_summary_to_slack(date_range, all_owners_count, converted_orders_count)


def s4l_cohort_counter(
    start_date: datetime.date, end_date: datetime.date
) -> Tuple[int, int]:
    """Count new S4L users and their conversions to orders in the given date range."""
    common_filters = {
        'furniture_status': FurnitureStatusEnum.SAVED,
        'created_at__date__gte': start_date,
        'created_at__date__lte': end_date,
        'owner__profile__user_type__in': CUSTOMER_TYPES,
    }

    watty_owner_ids = set(
        Watty.objects_with_soft_deleted.filter(**common_filters).values_list(
            'owner_id', flat=True
        )
    )
    jetty_owner_ids = set(
        Jetty.objects_with_soft_deleted.filter(**common_filters).values_list(
            'owner_id', flat=True
        )
    )
    sotty_owner_ids = set(
        Sotty.objects_with_soft_deleted.filter(**common_filters).values_list(
            'owner_id', flat=True
        )
    )

    all_owner_ids = watty_owner_ids.union(jetty_owner_ids, sotty_owner_ids)

    all_owners = User.objects.filter(
        id__in=all_owner_ids,
    ).exclude(
        email='',
    )

    # Count orders from these users
    converted_orders_count = Order.objects.filter(
        owner_id__in=all_owners.values_list('id', flat=True),
        status__in=COMPLETED_ORDER_STATUSES,
        total_price__gte=100,
    ).count()

    return all_owners.count(), converted_orders_count


def _format_with_spaces(number: int) -> str:
    return format(number, ',').replace(',', ' ')


def _send_s4l_summary_to_slack(
    date_range: str,
    all_owners: int,
    converted_orders: int,
) -> None:
    if not (settings.SLACK_WEBHOOK and settings.IS_PRODUCTION):
        return

    message = (
        f'In date range {date_range} '
        f'{_format_with_spaces(all_owners)} users did S4L\n'
        f'and {_format_with_spaces(converted_orders)} of them converted to orders.'
    )

    response = requests.post(
        settings.SLACK_WEBHOOK,
        json={
            'text': message,
            'channel': 's4l-stats',
            'username': 'Save4Later Stats',
            'icon_emoji': ':hand_with_index_finger_and_thumb_crossed:',
        },
        timeout=10,
    )
    response.raise_for_status()
