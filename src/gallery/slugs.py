from collections import defaultdict
from copy import (
    copy,
    deepcopy,
)
from typing import (
    TYPE_CHECKING,
    Final,
    NamedTuple,
    Optional,
    TypeAlias,
    Union,
)

from django.http import QueryDict
from django.utils.translation import gettext as _

from slugify import slugify

from custom.enums import (
    Furniture,
    LanguageEnum,
    ShelfType,
)
from custom.enums.enums import GenderEnum
from gallery.enums import FurnitureCategory
from gallery.services.sotty_single_module import SottySingleModuleRepository
from gallery.types import FurnitureType

if TYPE_CHECKING:
    from gallery.models import (
        Jetty,
        Sotty,
        Watty,
    )

SHELF_TYPES: TypeAlias = Union['Jetty', 'Watty']
SOFA_TYPES: TypeAlias = 'Sotty'
WARDROBE_TYPES: TypeAlias = 'Watty'
SHELF_TYPE_GROUPS: Final[tuple[set[ShelfType], ...]] = (
    {ShelfType.TYPE01, ShelfType.TYPE02, ShelfType.VENEER_TYPE01},
    {ShelfType.TYPE03},
    {ShelfType.TYPE13, ShelfType.VENEER_TYPE13},
    {ShelfType.TYPE23, ShelfType.TYPE24, Shelf<PERSON>ype.TYPE25},
    {ShelfType.SOFA_TYPE01},
)


class FurnitureSizeConditions(NamedTuple):
    large_threshold: int
    small_height_threshold: int
    small_width_threshold: int
    tall_height_threshold: int
    low_height_threshold: int
    wide_ratio_threshold: float
    wide_width_threshold: int
    slim_ratio_threshold: float
    depth_shallow_threshold: int
    depth_deep_threshold: int


DESK_SIZE_CONDITIONS = FurnitureSizeConditions(
    large_threshold=223,
    small_width_threshold=123,
    small_height_threshold=74,
    tall_height_threshold=75,
    low_height_threshold=0,
    wide_ratio_threshold=1.7,
    wide_width_threshold=210,
    slim_ratio_threshold=1.64,
    depth_shallow_threshold=41,
    depth_deep_threshold=49,
)


size_conditions = {
    FurnitureCategory.BOOKCASE: FurnitureSizeConditions(
        large_threshold=3700,
        small_width_threshold=1310,
        small_height_threshold=1830,
        tall_height_threshold=2130,
        low_height_threshold=1330,
        wide_ratio_threshold=1.30,
        wide_width_threshold=2000,
        slim_ratio_threshold=0.7,
        depth_shallow_threshold=240,
        depth_deep_threshold=400,
    ),
    FurnitureCategory.SIDEBOARD: FurnitureSizeConditions(
        large_threshold=2750,
        small_width_threshold=1310,
        small_height_threshold=440,
        tall_height_threshold=1130,
        low_height_threshold=630,
        wide_ratio_threshold=3.00,
        wide_width_threshold=2200,
        slim_ratio_threshold=0.7,
        depth_shallow_threshold=240,
        depth_deep_threshold=400,
    ),
    FurnitureCategory.TV_STAND: FurnitureSizeConditions(
        large_threshold=3000,
        small_width_threshold=1460,
        small_height_threshold=440,
        tall_height_threshold=830,
        low_height_threshold=430,
        wide_ratio_threshold=2.80,
        wide_width_threshold=2100,
        slim_ratio_threshold=0.7,
        depth_shallow_threshold=240,
        depth_deep_threshold=400,
    ),
    FurnitureCategory.SHOERACK: FurnitureSizeConditions(
        large_threshold=2400,
        small_width_threshold=910,
        small_height_threshold=440,
        tall_height_threshold=830,
        low_height_threshold=430,
        wide_ratio_threshold=2.00,
        wide_width_threshold=1750,
        slim_ratio_threshold=0.7,
        depth_shallow_threshold=240,
        depth_deep_threshold=400,
    ),
    FurnitureCategory.WARDROBE: FurnitureSizeConditions(
        large_threshold=6400,
        small_width_threshold=1510,
        small_height_threshold=2010,
        tall_height_threshold=2370,
        low_height_threshold=1500,
        wide_ratio_threshold=0.80,
        wide_width_threshold=1900,
        slim_ratio_threshold=1.05,
        depth_shallow_threshold=240,
        depth_deep_threshold=630,
    ),
    FurnitureCategory.WALL_STORAGE: FurnitureSizeConditions(
        large_threshold=4400,
        small_width_threshold=1310,
        small_height_threshold=1840,
        tall_height_threshold=2200,
        low_height_threshold=1630,
        wide_ratio_threshold=1.10,
        wide_width_threshold=2400,
        slim_ratio_threshold=0.7,
        depth_shallow_threshold=240,
        depth_deep_threshold=400,
    ),
    FurnitureCategory.CHEST: FurnitureSizeConditions(
        large_threshold=3300,
        small_width_threshold=1010,
        small_height_threshold=830,
        tall_height_threshold=1430,
        low_height_threshold=830,
        wide_ratio_threshold=0.9,
        wide_width_threshold=1850,
        slim_ratio_threshold=0.7,
        depth_shallow_threshold=240,
        depth_deep_threshold=400,
    ),
    FurnitureCategory.VINYL_STORAGE: FurnitureSizeConditions(
        large_threshold=2900,
        small_width_threshold=710,
        small_height_threshold=840,
        tall_height_threshold=1630,
        low_height_threshold=430,
        wide_ratio_threshold=1.60,
        wide_width_threshold=1670,
        slim_ratio_threshold=0.7,
        depth_shallow_threshold=240,
        depth_deep_threshold=410,
    ),
    FurnitureCategory.BEDSIDE_TABLE: FurnitureSizeConditions(
        large_threshold=1100,
        small_width_threshold=410,
        small_height_threshold=440,
        tall_height_threshold=1200,
        low_height_threshold=430,
        wide_ratio_threshold=0.7,
        wide_width_threshold=500,
        slim_ratio_threshold=0.7,
        depth_shallow_threshold=240,
        depth_deep_threshold=400,
    ),
    FurnitureCategory.DESK: DESK_SIZE_CONDITIONS,
    FurnitureCategory.DRESSING_TABLE: DESK_SIZE_CONDITIONS,
}


# TODO: split into methods so each part of the slug can be overriden without hacks
# TODO: add BaseSlugParser
class JettySlugParser:
    def parse_slug(self, furniture, language):
        return slugify(self.parse_shelf_name(furniture, language))

    def slug_replace_material(self, furniture, language, forced_material):
        current_material_name = str(
            furniture.get_item_description()['material']
        ).title()
        new_material_name = (
            ShelfType(furniture.shelf_type)
            .colors(forced_material)
            .translated_material_description.title()
        )
        current_title = self.parse_shelf_name(furniture, language)
        new_title = current_title.replace(current_material_name, new_material_name)
        return slugify(new_title)

    def get_feature_descriptors(
        self,
        furniture: FurnitureType,
        short_version: bool = False,
    ) -> list[str]:
        if short_version:
            # In short version if furniture has a feature with a long descriptor name,
            # return only this feature and skip all the others.
            if long_feature_descriptor := self._get_long_feature_descriptor(furniture):
                return [long_feature_descriptor]

            return self._get_feature_descriptors(furniture)[:2]

        return self._get_feature_descriptors(furniture)

    @staticmethod
    def _get_feature_descriptors(furniture: 'Jetty') -> list[str]:
        category = furniture.furniture_category
        feature_descriptors = []
        if furniture.doors:
            feature_descriptors.append(_('seo_doors'))

        if furniture.drawers and category != FurnitureCategory.CHEST:
            feature_descriptors.append(_('seo_drawers'))

        if furniture.configurator_params and (
            furniture.configurator_params.get('backpanels_rows')
            or furniture.configurator_params.get('backpanels')
        ):
            feature_descriptors.append(_('seo_backpanels'))

        if len(furniture.cable_management) > 0:
            feature_descriptors.append(_('seo_cable_openings'))

        if furniture.long_legs:
            feature_descriptors.append(_('seo_long_legs'))
        elif furniture.plinth:
            feature_descriptors.append(_('seo_plinth_legs'))

        if furniture.has_top_storage and furniture.has_bottom_storage:
            feature_descriptors.append(_('seo_top_and_bottom_storage'))
        elif furniture.has_top_storage:
            feature_descriptors.append(_('seo_top_storage'))
        elif furniture.has_bottom_storage:
            feature_descriptors.append(_('bottom storage'))

        return [feature.title() for feature in feature_descriptors]

    @staticmethod
    def _get_long_feature_descriptor(furniture: 'Jetty') -> Optional[str]:
        long_feature_descriptors = [
            (
                furniture.has_top_storage and furniture.has_bottom_storage,
                _('seo_top_and_bottom_storage'),
            ),
        ]

        for feature, translation in long_feature_descriptors:
            if feature:
                return translation.title()

    @staticmethod
    def _format_case(clause: str) -> str:
        """Transform conjunctions and adverbs to lowercase in a clause."""
        words_to_format = [
            _('seo_with'),
            _('seo_in'),
            _('seo_and'),
            'e',  # another meaning of word 'and' in spanish
            'de',  # 'from' in spanish
        ]
        for word in words_to_format:
            capitalised_word = word.title()
            if f' {capitalised_word} ' in clause:
                clause = clause.replace(capitalised_word, word)

        return clause

    @staticmethod
    def _join_feature_descriptors(feature_descriptors: list[str]) -> str:
        seo_and = _('seo_and')
        seo_with = _('seo_with')

        if not feature_descriptors:
            return ''
        elif len(feature_descriptors) < 3:
            # if only two features, use simple concatenation like "{this} and {that}"
            return _('seo_with') + ' ' + f' {seo_and} '.join(feature_descriptors)
        # otherwise, use more elaborate "{this}, {this}, {this} and {that}"

        return (
            f'{seo_with} {", ".join(feature_descriptors[:-1])} {seo_and} '
            + f'{feature_descriptors[-1]}'
        )

    def get_size_descriptors(self, furniture, language):
        """Calculate "size" adjective(s) based on category specific values."""
        category = furniture.furniture_category
        size_descriptors = []
        category_size_conditions = size_conditions[category]
        category_gender = LanguageEnum(language).get_seo_category_gender(category)
        translations = self._get_size_descriptors_translations()

        # if size is large/small, there is no point in adding other adjectives
        if (
            furniture.width + furniture.height
            > category_size_conditions.large_threshold
        ):
            return translations['seo_large'][category_gender].title()
        elif (
            furniture.width < category_size_conditions.small_width_threshold
            and furniture.height < category_size_conditions.small_height_threshold
        ):
            return translations['seo_small'][category_gender].title()

        # if not, generate some combination of "(tall|low) (wide|slim)""
        if furniture.height >= category_size_conditions.tall_height_threshold:
            size_descriptors.append(translations['seo_tall'][category_gender])
        elif furniture.height < category_size_conditions.low_height_threshold:
            size_descriptors.append(translations['seo_low'][category_gender])

        if (
            furniture.width > category_size_conditions.wide_width_threshold
            and furniture.width / furniture.height
            > category_size_conditions.wide_ratio_threshold
        ):
            size_descriptors.append(translations['seo_wide'][category_gender])
        elif (
            furniture.width / furniture.height
            < category_size_conditions.slim_ratio_threshold
        ):
            size_descriptors.append(translations['seo_slim'][category_gender])

        # if we matched only one adjective earlier, try to add (shallow|deep)
        if len(size_descriptors) < 2:
            if furniture.depth == category_size_conditions.depth_shallow_threshold:
                size_descriptors.append(translations['seo_shallow'][category_gender])
            elif furniture.depth == category_size_conditions.depth_deep_threshold:
                size_descriptors.append(translations['seo_deep'][category_gender])

        return ' '.join(size_descriptors).title()

    @staticmethod
    def _get_size_descriptors_translations():
        return {
            'seo_small': {
                GenderEnum.MASCULINE: _('seo_small_masculine'),
                GenderEnum.FEMININE: _('seo_small_feminine'),
                GenderEnum.NEUTER: _('seo_small'),
                GenderEnum.NONE: _('seo_small'),
            },
            'seo_large': {
                GenderEnum.MASCULINE: _('seo_large_masculine'),
                GenderEnum.FEMININE: _('seo_large_feminine'),
                GenderEnum.NEUTER: _('seo_large'),
                GenderEnum.NONE: _('seo_large'),
            },
            'seo_tall': {
                GenderEnum.MASCULINE: _('seo_tall_masculine'),
                GenderEnum.FEMININE: _('seo_tall_feminine'),
                GenderEnum.NEUTER: _('seo_tall'),
                GenderEnum.NONE: _('seo_tall'),
            },
            'seo_low': {
                GenderEnum.MASCULINE: _('seo_low_masculine'),
                GenderEnum.FEMININE: _('seo_low_feminine'),
                GenderEnum.NEUTER: _('seo_low'),
                GenderEnum.NONE: _('seo_low'),
            },
            'seo_wide': {
                GenderEnum.MASCULINE: _('seo_wide_masculine'),
                GenderEnum.FEMININE: _('seo_wide_feminine'),
                GenderEnum.NEUTER: _('seo_wide'),
                GenderEnum.NONE: _('seo_wide'),
            },
            'seo_slim': {
                GenderEnum.MASCULINE: _('seo_slim_masculine'),
                GenderEnum.FEMININE: _('seo_slim_feminine'),
                GenderEnum.NEUTER: _('seo_slim'),
                GenderEnum.NONE: _('seo_slim'),
            },
            'seo_shallow': {
                GenderEnum.MASCULINE: _('seo_shallow_masculine'),
                GenderEnum.FEMININE: _('seo_shallow_feminine'),
                GenderEnum.NEUTER: _('seo_shallow'),
                GenderEnum.NONE: _('seo_shallow'),
            },
            'seo_deep': {
                GenderEnum.MASCULINE: _('seo_deep_masculine'),
                GenderEnum.FEMININE: _('seo_deep_feminine'),
                GenderEnum.NEUTER: _('seo_deep'),
                GenderEnum.NONE: _('seo_deep'),
            },
        }

    def parse_shelf_name(self, furniture, language):
        template_func = LanguageEnum(language).get_slug_template_callable(
            furniture.furniture_type
        )
        template = template_func(
            furniture=furniture,
            category=furniture.furniture_category,
            features=(
                self._join_feature_descriptors(self.get_feature_descriptors(furniture))
            ),
            length=self.get_length_in_doors(furniture),
            size=self.get_size_descriptors(furniture, language),
            dimensions=self.get_dimensions(furniture),
        )
        return self._concatenate_shelf_elements(elements=template)

    def parse_shelf_name_for_influencer(self, furniture, language):
        template_func = LanguageEnum(language).get_slug_template_callable(
            furniture.furniture_type
        )
        template = template_func(
            furniture=furniture,
            category=furniture.furniture_category,
            features=(
                self._join_feature_descriptors(self.get_feature_descriptors(furniture))
            ),
            length=self.get_length_in_doors(furniture),
            size=self.get_size_descriptors(furniture, language),
            dimensions='',
        )
        return self._concatenate_shelf_elements(elements=template)

    def parse_grid_shelf_name(
        self,
        furniture: 'Jetty',
        language: str,
        without_features: bool = False,
    ) -> str:
        if without_features:
            features = ''
        else:
            features_list = self.get_feature_descriptors(furniture, short_version=True)
            features = self._join_feature_descriptors(features_list)
        template_func = LanguageEnum(language).get_grid_slug_template_callable(
            furniture.furniture_type
        )
        template = template_func(furniture=furniture, features=features)
        return self._concatenate_shelf_elements(elements=template)

    @staticmethod
    def get_length_in_doors(furniture):
        """Return wardrobe length represented in doors count"""
        door_count_per_component = [
            2 if component.get('doors_pair_count', 0) > 0 else 1
            for component in furniture.components
        ]
        return sum(door_count_per_component)

    @staticmethod
    def get_dimensions(furniture):
        return (
            f'{int(furniture.width / 10)}x{int(furniture.height / 10)}x'
            + f'{int(furniture.depth / 10)}cm'
        )

    def _concatenate_shelf_elements(self, elements: list[str]) -> str:
        shelf_name = ''
        for element in elements:
            if not element or element == '- ':
                continue

            shelf_name += f' {self._format_case(element)}'
        return shelf_name.strip()


class SottySlugParser(JettySlugParser):
    def parse_shelf_name(self, furniture: 'Sotty', language):
        template_func = LanguageEnum(language).get_slug_template_callable(
            furniture.furniture_type
        )
        template = template_func(
            furniture=furniture,
            dimensions=self.get_dimensions(furniture),
        )
        return self._concatenate_shelf_elements(elements=template)

    @staticmethod
    def _get_feature_descriptors(furniture: 'Sotty') -> list[str]:
        feature_descriptors = []
        use_title = True

        # Chaise Lounge Sofa with chaise longue
        if (
            furniture.chaise_longues
            and FurnitureCategory(furniture.shelf_category)
            != FurnitureCategory.CHAISE_LONGUE
        ):
            feature_descriptors.append(_('seo_chaise_longue'))

        # Footrest sofa with footrest
        if (
            furniture.has_footrest_module
            and FurnitureCategory(furniture.shelf_category)
            != FurnitureCategory.FOOTREST_AND_MODULES
        ):
            feature_descriptors.append(_('seo_footrest'))

        # Cover
        if FurnitureCategory(furniture.shelf_category) == FurnitureCategory.COVER:
            module_type = SottySingleModuleRepository.get_module_type(sotty=furniture)
            # covers only per single modules
            feature_descriptors = [_('seo_for'), _(module_type.translation_key)]

        return [
            feature.title() if use_title else feature for feature in feature_descriptors
        ]

    @staticmethod
    def _get_long_feature_descriptor(furniture: 'Sotty') -> None:
        # TODO ask business if special long feature description is applied to sotty
        return None


class WattySlugParser(JettySlugParser):
    def _get_feature_descriptors(self, furniture: 'Watty') -> list[str]:
        feature_descriptors = []
        if furniture.furniture_category != FurnitureCategory.CHEST:
            if furniture.has_internal_drawers and furniture.has_external_drawers:
                feature_descriptors.append(_('seo_internal_and_external_drawers'))
            elif furniture.has_internal_drawers:
                feature_descriptors.append(_('seo_internal_drawers'))
            elif furniture.has_external_drawers:
                feature_descriptors.append(_('seo_external_drawers'))

        # wardrobe rail (single/double)
        if len(furniture.bars) > 0:
            feature_descriptors.append(_('seo_rail'))

        return [feature.title() for feature in feature_descriptors]

    @staticmethod
    def _get_long_feature_descriptor(furniture: 'Watty') -> Optional[str]:
        features_with_long_name = [
            (
                furniture.has_internal_drawers and furniture.has_external_drawers,
                _('seo_internal_and_external_drawers'),
            ),
        ]

        for feature, translation in features_with_long_name:
            if feature:
                return translation.title()

    def parse_grid_shelf_name(
        self,
        furniture: 'Watty',
        language: str,
        without_features: bool = False,
    ) -> str:
        if without_features:
            features = ''
        else:
            features_list = self.get_feature_descriptors(furniture, short_version=True)
            features = self._join_feature_descriptors(features_list)

        template_func = LanguageEnum(language).get_grid_slug_template_callable(
            furniture.furniture_type
        )
        template = template_func(furniture=furniture, features=features)
        return ' '.join([self._format_case(elem) for elem in template if elem])


def get_slug_for_furniture(furniture: FurnitureType, language):
    if furniture.furniture_type == Furniture.sotty.value:
        return SottySlugParser().parse_slug(furniture, language)
    elif furniture.furniture_type == Furniture.watty.value:
        return WattySlugParser().parse_slug(furniture, language)
    return JettySlugParser().parse_slug(furniture, language)


def get_slug_for_furniture_material(furniture: FurnitureType, language, material):
    if furniture.furniture_type == Furniture.sotty.value:
        return SottySlugParser().parse_slug(furniture, language)
    elif furniture.furniture_type == Furniture.watty.value:
        return WattySlugParser().slug_replace_material(furniture, language, material)
    return JettySlugParser().slug_replace_material(furniture, language, material)


def get_title_for_furniture(furniture: FurnitureType, language):
    if furniture.furniture_type == Furniture.sotty.value:
        return SottySlugParser().parse_shelf_name(furniture, language)
    elif furniture.furniture_type == Furniture.watty.value:
        return WattySlugParser().parse_shelf_name(furniture, language)
    return JettySlugParser().parse_shelf_name(furniture, language)


def get_title_for_furniture_influencer(furniture, language):
    if furniture.furniture_type == Furniture.watty.value:
        return WattySlugParser().parse_shelf_name_for_influencer(furniture, language)
    return JettySlugParser().parse_shelf_name_for_influencer(furniture, language)


def get_title_for_grid(furniture: FurnitureType, language, without_features=False):
    if furniture.furniture_type == Furniture.sotty.value:
        return SottySlugParser().parse_grid_shelf_name(
            furniture, language, without_features=without_features
        )
    elif furniture.furniture_type == Furniture.watty.value:
        return WattySlugParser().parse_grid_shelf_name(
            furniture, language, without_features=without_features
        )
    return JettySlugParser().parse_grid_shelf_name(
        furniture, language, without_features=without_features
    )


def get_slug_for_all_colors(furniture: FurnitureType, language):
    copied_furniture = deepcopy(furniture)
    colors = ShelfType(furniture.shelf_type).colors.get_active_colors()
    slugs = {}
    for color in colors:
        copied_furniture.set_material(material=color.value, with_save=False)
        slugs[color.value] = get_slug_for_furniture(copied_furniture, language)
    return slugs


def get_seo_title_for_all_colors(
    furniture: FurnitureType, language
) -> dict[str, dict[str, str]]:
    """
    returns:
    {
        "0": {
            "0": "Chest Of Drawers in White with Doors and Backpanels",
            "1": "Chest Of Drawers in Black with Doors and Backpanels",
            ...
        },
        "1": {
            "0": "Chest Of Drawers in White with Doors and Backpanels",
            "1": "Chest Of Drawers in Terracota with Doors and Backpanels",
            ...
        },
        "2": {
            "0": "Chest Of Drawers in White Oak with Doors and Backpanels",
            "1": "Chest Of Drawers in Oak with Doors and Backpanels",
            ...
        }
    }
    """
    furniture_copy = copy(furniture)
    slugs = defaultdict(dict)

    for group in SHELF_TYPE_GROUPS:
        if furniture_copy.shelf_type not in group:
            continue
        for shelf_type in group:
            for color in ShelfType(shelf_type).colors.get_active_colors():
                furniture_copy.set_material(material=color.value, with_save=False)
                furniture_copy.set_shelf_type(shelf_type=shelf_type, with_save=False)
                title = get_title_for_grid(furniture_copy, language)
                slugs[shelf_type][color.value] = title

    return slugs


def get_shelf_slug_default_template(
    furniture: SHELF_TYPES,
    category: FurnitureCategory,
    features: str,
    length: int,
    size: str,
    dimensions: str,
) -> list:
    seo_drawers = _('seo_drawers')
    seo_door = _('seo_door')

    return [
        size,  # large, small, deep, shallow, narrow, wide ...
        str(furniture.get_item_description()['material']).title(),
        (
            f'{len(furniture.drawers)}-{seo_drawers}'
            if category == FurnitureCategory.CHEST
            else None
        ),
        # only for chest of drawers - drawers count as "5 drawers"
        (
            f'{length} {seo_door}'
            if category == FurnitureCategory.WARDROBE and furniture.doors
            else None
        ),
        # only for wardrobes - door count as "5 door"
        furniture.furniture_category.translated_name.title(),
        features,  # concatenated earlier by methodL
        ShelfType(furniture.shelf_type).seo_wooden_materials,
        f'- {dimensions}',
    ]


def get_shelf_slug_template_french(
    furniture: SHELF_TYPES,
    category: FurnitureCategory,
    features: str,
    length: int,
    size: str,
    dimensions: str,
) -> list:
    seo_drawers = _('seo_drawers')
    seo_door = _('seo_door')
    return [
        size,  # large, small, deep, shallow, narrow, wide ...
        (
            f'{len(furniture.drawers)}-{seo_drawers}'
            if category == FurnitureCategory.CHEST
            else None
        ),
        (
            f'{length} {seo_door}'
            if category == FurnitureCategory.WARDROBE and furniture.doors
            else None
        ),
        furniture.furniture_category.translated_name.title(),
        'en',
        str(furniture.get_item_description()['material']).title(),
        features,
        ShelfType(furniture.shelf_type).seo_wooden_materials,
        f'- {dimensions}',
    ]


def get_shelf_slug_template_polish(
    furniture: SHELF_TYPES,
    category: FurnitureCategory,
    features: str,
    length: int,
    size: str,
    dimensions: str,
) -> list:
    seo_with = _('seo_with')
    seo_drawers = _('seo_drawers')
    gender = LanguageEnum.PL.get_seo_category_gender(category)
    return [
        size,  # large, small, deep, shallow, narrow, wide ...
        furniture.color.get_translated_simple_color_with_declination(
            gender=gender
        ).lower(),
        # only for wardrobes - door count as "5 door"
        furniture.furniture_category.translated_name.lower(),
        (
            f'{seo_with} {len(furniture.drawers)} {seo_drawers}'.lower()
            if category == FurnitureCategory.CHEST
            else None
        ),
        # only for chest of drawers - drawers count as "5 drawers"
        features.lower(),  # concatenated earlier by methodL
        ShelfType(furniture.shelf_type).seo_wooden_materials.lower(),
        f'- {dimensions}'.lower(),
    ]


def get_shelf_grid_slug_default_template(
    furniture: SHELF_TYPES,
    features: str,
) -> list:
    return [
        furniture.furniture_category.translated_name.title(),
        _('seo_in'),
        furniture.color.translated_simple_color.title(),
        features,
    ]


def get_shelf_grid_slug_template_polish(
    furniture: SHELF_TYPES,
    features: str,
) -> list:
    gender = LanguageEnum.PL.get_seo_category_gender(furniture.furniture_category)
    return [
        furniture.color.get_translated_simple_color_with_declination(
            gender=gender
        ).title(),
        furniture.furniture_category.translated_name.lower(),
        (
            _('seo_and')
            if furniture.furniture_category == FurnitureCategory.CHEST and features
            else ''
        ),
        features.lower(),
    ]


def get_sofa_slug_default_template(
    furniture: SOFA_TYPES,
    dimensions: str,
) -> list:
    return [
        FurnitureCategory(furniture.shelf_category).translated_name,
        str(furniture.get_item_description()['material']).title(),
        f'- {dimensions}',
    ]


def get_sofa_grid_slug_default_template(furniture: SOFA_TYPES, features: str) -> list:
    if furniture.shelf_category == FurnitureCategory.COVER:
        translation = SottySingleModuleRepository(sotty=furniture).get_translation()
        elements = [_(translation)]
    else:
        elements = [
            FurnitureCategory(furniture.shelf_category).translated_name,
            features,
        ]
    elements += [
        _('seo_in'),
        str(furniture.get_item_description()['material']).title(),
        furniture.fabric.translated_name,
    ]
    return elements


def get_sofa_grid_slug_template_common(furniture: SOFA_TYPES, features: str) -> list:
    if furniture.shelf_category == FurnitureCategory.COVER:
        translation = SottySingleModuleRepository(sotty=furniture).get_translation()
        elements = [_(translation)]
    else:
        elements = [
            FurnitureCategory(furniture.shelf_category).translated_name,
            features,
        ]
    elements += [
        furniture.fabric.translated_name,
        _('seo_in'),
        str(furniture.get_item_description()['material']).title(),
    ]
    return elements


def get_sofa_grid_slug_template_german(furniture: SOFA_TYPES, features: str) -> list:
    return get_sofa_grid_slug_template_common(furniture=furniture, features=features)


def get_sofa_grid_slug_template_spanish(furniture: SOFA_TYPES, features: str) -> list:
    return get_sofa_grid_slug_template_common(furniture=furniture, features=features)


def get_sofa_grid_slug_template_italian(furniture: SOFA_TYPES, features: str) -> list:
    if furniture.shelf_category == FurnitureCategory.COVER:
        translation = SottySingleModuleRepository(sotty=furniture).get_translation()
        elements = [_(translation)]
    else:
        elements = [
            FurnitureCategory(furniture.shelf_category).translated_name,
            features,
        ]
    elements += [
        _('seo_in'),
        furniture.fabric.translated_name,
        str(furniture.get_item_description()['material']).title(),
    ]
    return elements


def get_sofa_grid_slug_template_polish(furniture: SOFA_TYPES, features: str) -> list:
    gender = LanguageEnum.PL.get_seo_category_gender(furniture.furniture_category)
    if furniture.shelf_category == FurnitureCategory.COVER:
        translation = SottySingleModuleRepository(sotty=furniture).get_translation()
        main_part = [_(translation)]
    else:
        main_part = [
            FurnitureCategory(furniture.shelf_category).translated_name,
            features,
        ]
    elements = [
        furniture.color.get_translated_simple_color_with_declination(
            gender=gender
        ).title(),
        *main_part,
        furniture.fabric.translated_name,
    ]
    return elements


def get_wardrobe_slug_default_template(
    furniture: WARDROBE_TYPES,
    category: FurnitureCategory,
    features: str,
    length: int,
    size: str,
    dimensions: str,
) -> list:
    # for now templates are the same as for shelves
    return get_shelf_slug_default_template(
        furniture,
        category,
        features,
        length,
        size,
        dimensions,
    )


def get_wardrobe_slug_template_french(
    furniture: WARDROBE_TYPES,
    category: FurnitureCategory,
    features: str,
    length: int,
    size: str,
    dimensions: str,
) -> list:
    # for now templates are the same as for shelves
    return get_shelf_slug_template_french(
        furniture,
        category,
        features,
        length,
        size,
        dimensions,
    )


def get_wardrobe_slug_template_polish(
    furniture: WARDROBE_TYPES,
    category: FurnitureCategory,
    features: str,
    length: int,
    size: str,
    dimensions: str,
) -> list:
    seo_with = _('seo_with')
    seo_doors = _('seo_doors')
    gender = LanguageEnum.PL.get_seo_category_gender(category)
    return [
        size,  # large, small, deep, shallow, narrow, wide ...
        (f'{seo_with} {length} {seo_doors}'.lower() if furniture.doors else None),
        furniture.furniture_category.translated_name.lower(),
        furniture.color.get_translated_simple_color_with_declination(
            gender=gender
        ).lower(),
        # only for chest of drawers - drawers count as "5 drawers"
        features.lower(),  # concatenated earlier by methodL
        ShelfType(furniture.shelf_type).seo_wooden_materials.lower(),
        f'- {dimensions}'.lower(),
    ]


def get_wardrobe_grid_slug_default_template(
    furniture: WARDROBE_TYPES,
    features: str,
) -> list:
    return [
        furniture.furniture_category.translated_name.title(),
        _('seo_in'),
        furniture.color.translated_simple_color.title(),
        features,
    ]


def get_wardrobe_grid_slug_template_french(
    furniture: WARDROBE_TYPES,
    features: str,
) -> list:
    return [
        furniture.furniture_category.translated_name.title(),
        _('seo_in'),
        furniture.color.translated_simple_color.title(),
        features,
    ]


def get_features_from_filter(features_list: list[str]) -> list[str]:
    """Retrieves the descriptions for a given list of feature names.

    Args:
        features_list (list): A list of strings representing feature names.

    Returns:
        list: A list of feature descriptions.
    """
    feature_mapping = {
        'doors': _('seo_doors'),
        'externalDrawers': _('seo_external_drawers'),
        'internalDrawers': _('seo_internal_drawers'),
        'plinth': _('seo_plinth_legs'),
        'legs': _('seo_long_legs'),
        'cableManagement': _('seo_cable_openings'),
    }
    feature_descriptors = [
        feature_mapping.get(feature, '')
        for feature in features_list
        if feature in feature_mapping
    ]

    if 'externalDrawers' in features_list and 'internalDrawers' in features_list:
        feature_descriptors.append(_('seo_internal_and_external_drawers'))
        feature_descriptors = [
            desc
            for desc in feature_descriptors
            if desc not in (_('seo_external_drawers'), _('seo_internal_drawers'))
        ]

    return feature_descriptors


def generate_title_based_on_filter_data(query_params: QueryDict) -> str:
    """
    Generates a title based on given query parameters. The title includes category,
    color, material, feature, and additional attributes. If there are multiple colors,
    materials, features or additional attributes, these will not be included
    in the title.

    Args:
        query_params (dict): A dictionary of query parameters. Should include
            'category', 'colors', 'materials', 'additional', and 'features' keys.

    Returns:
        str: A string representing the generated title.
    """
    seo_in = _('seo_in')
    seo_and = _('seo_and')
    seo_with = _('seo_with')

    if category := getattr(
        FurnitureCategory, query_params.get('category', '').upper(), ''
    ):
        category = category.translated_name_plural

    color = query_params.get('colors', '')
    if color and len(color.split(',')) == 1:
        color = f' {seo_in} {color.title()}'
    else:
        color = ''

    material = query_params.get('materials', '')
    if material and len(material.split(',')) == 1:
        material = f' - {material.title()}'
    else:
        material = ''

    additional = query_params.get('additional', '')
    if additional and len(additional.split(',')) == 1:
        additional = f' - {additional.title()}'
    else:
        additional = ''

    feature = get_features_from_filter(query_params.get('features', '').split(','))
    if len(feature) in {1, 2}:
        feature_desc = f' {seo_and} '.join(feature)
        feature = f' {seo_with} {feature_desc}'
    else:
        feature = ''

    return f'{category}{color}{feature}{material}{additional}'
