import json

from datetime import timed<PERSON><PERSON>
from unittest.mock import (
    call,
    patch,
)

from django.urls import reverse
from django.utils import timezone
from rest_framework import status

import pytest

from custom.enums import (
    ShelfType,
    Sofa01Color,
)
from events.models import Event
from events.utils import hash_normalized_string
from gallery.enums import (
    CapeCollectionType,
    ShelfStatusSource,
)
from vouchers.enums import VoucherType


@pytest.mark.django_db
class TestJettyConfiguratorView:
    def test_overrides_material_if_cv_is_correct(
        self,
        jetty,
        api_client,
        user,
        regions,
    ):
        valid_color = 3
        url = reverse('jetty-configurator', kwargs={'pk': jetty.id})

        api_client.force_authenticate(user)
        response = api_client.get(url, {'cv': valid_color})

        assert not ShelfType(jetty.shelf_type).colors(valid_color).is_deprecated
        assert response.status_code == status.HTTP_200_OK
        assert response.json().get('material') == valid_color

    def test_returns_original_material_if_cv_is_incorrect(
        self,
        jetty,
        api_client,
        user,
        regions,
    ):
        invalid_color = 17
        url = reverse('jetty-configurator', kwargs={'pk': jetty.id})

        api_client.force_authenticate(user)
        response = api_client.get(url, {'cv': invalid_color})

        assert invalid_color not in ShelfType(jetty.shelf_type).colors.values()
        assert response.status_code == status.HTTP_200_OK
        assert response.json().get('material') == jetty.material


@pytest.mark.django_db
class TestWattyConfiguratorView:
    def test_overrides_material_if_cv_is_correct(
        self,
        watty,
        api_client,
        user,
        regions,
    ):
        valid_color = 3
        url = reverse('watty-configurator', kwargs={'pk': watty.id})

        api_client.force_authenticate(user)
        response = api_client.get(url, {'cv': valid_color})

        assert not ShelfType(watty.shelf_type).colors(valid_color).is_deprecated
        assert response.status_code == status.HTTP_200_OK
        assert response.json().get('material') == valid_color

    def test_returns_original_material_if_cv_is_incorrect(
        self,
        jetty,
        api_client,
        user,
        regions,
    ):
        invalid_color = 17
        url = reverse('jetty-configurator', kwargs={'pk': jetty.id})

        api_client.force_authenticate(user)
        response = api_client.get(url, {'cv': invalid_color})

        assert invalid_color not in ShelfType(jetty.shelf_type).colors.values()
        assert response.status_code == status.HTTP_200_OK
        assert response.json().get('material') == jetty.material


@pytest.mark.django_db
class TestCustomDnaView:
    url = reverse('furniture-dna')

    @pytest.mark.parametrize(
        ('shelf_type', 'collection_type'),
        (  # noqa: PT007
            (ShelfType.TYPE01, CapeCollectionType.BOOKCASE),
            (ShelfType.TYPE01, CapeCollectionType.SIDEBOARD),
            (ShelfType.TYPE02, CapeCollectionType.SHOE_RACK),
            (ShelfType.VENEER_TYPE01, CapeCollectionType.BOOKCASE),
            (ShelfType.VENEER_TYPE01, CapeCollectionType.SIDEBOARD),
            (ShelfType.TYPE02, CapeCollectionType.BOOKCASE),
            (ShelfType.TYPE02, CapeCollectionType.SIDEBOARD),
            (ShelfType.TYPE03, CapeCollectionType.WARDROBE),
        ),
    )
    def test_returns_multi_dna_based_on_query_params(
        self,
        shelf_type,
        collection_type,
        api_client,
        user,
        custom_dna_factory,
    ):
        data = {
            'shelf_type': shelf_type,
            'collection_type': collection_type,
        }
        multi_dna = custom_dna_factory(**data, dna_objects=[1, 2, 3])
        api_client.force_authenticate(user)
        response = api_client.get(
            self.url,
            {'shelf_type': shelf_type.value, 'collection_type': collection_type},
        )
        assert response.status_code == status.HTTP_200_OK
        assert json.loads(response.data)['json'] == multi_dna.dna_json

    def test_returns_last_multi_dna_for_shelf_type_and_collection_type(
        self,
        api_client,
        user,
        custom_dna_factory,
    ):
        first, last = custom_dna_factory.create_batch(  # noqa: RUF059
            size=2,
            dna_objects=[1, 2, 3],
            shelf_type=ShelfType.TYPE01,
            collection_type=CapeCollectionType.BOOKCASE,
        )
        api_client.force_authenticate(user)
        response = api_client.get(
            self.url,
            {
                'shelf_type': ShelfType.TYPE01.value,
                'collection_type': CapeCollectionType.BOOKCASE,
            },
        )
        assert response.status_code == status.HTTP_200_OK
        assert json.loads(response.data)['json'] == last.dna_json


@pytest.mark.django_db
class TestFurniturePDPEndpoint:
    @patch('gallery.ecommerce_api.pdp.metrics_client')
    @patch('gallery.ecommerce_api.pdp.add_jetty_state_to_redis')
    def test_exporting_shelf_to_bigquery(
        self,
        add_jetty_state_to_redis_mock,
        metrics_client_mock,
        settings,
        api_client,
        jetty,
    ):
        settings.IS_PRODUCTION = True
        url = reverse('jetty-pdp', kwargs={'pk': jetty.pk})
        api_client.force_authenticate(jetty.owner)
        response = api_client.get(url)

        assert response.status_code == status.HTTP_200_OK
        assert add_jetty_state_to_redis_mock.call_count == 1
        assert add_jetty_state_to_redis_mock.call_args == call(
            user_id=jetty.owner.id,
            jetty=jetty,
            source=ShelfStatusSource.PRODUCT_VIEW,
            pagepath='missing referer',
        )
        assert metrics_client_mock.call_count == 1

    def test_price_with_discount(
        self,
        user,
        api_client,
        regions,
        jetty,
        promotion_factory,
        promotion_config_factory,
        voucher_factory,
    ):
        voucher = voucher_factory(
            value=100,
            kind_of=VoucherType.PERCENTAGE,
            start_date=timezone.now() - timedelta(days=1),
        )
        promo = promotion_factory(
            strikethrough_pricing=True,
            active=True,
            promo_code=voucher,
        )
        promotion_config_factory(promotion=promo)
        url = reverse('jetty-pdp', kwargs={'pk': jetty.pk})
        api_client.force_authenticate(user)
        response = api_client.get(url)
        assert response.status_code == status.HTTP_200_OK
        assert response.json().get('priceWithDiscount') == 0

    @pytest.mark.parametrize(
        ['factory_name', 'view_name'],  # noqa: PT006
        (  # noqa: PT007
            ('jetty_factory', 'jetty-pdp'),
            ('watty_factory', 'watty-pdp'),
        ),
    )
    def test_last_furniture_view_event_for_jetty(
        self,
        factory_name,
        view_name,
        request,
        user_factory,
        api_client,
    ):
        user = user_factory(email='<EMAIL>')
        furniture_factory = request.getfixturevalue(factory_name)
        instance = furniture_factory(owner=user)

        url = reverse(view_name, kwargs={'pk': instance.pk})
        api_client.force_authenticate(user)
        response = api_client.get(url)
        assert response.status_code == status.HTTP_200_OK

        events = Event.objects.filter(event_name='FurnitureViewEvent')
        event = events.last()

        assert events.count() == 1
        assert event.properties['last_viewed_furniture_id'] == instance.id
        assert event.properties['last_viewed_furniture_type'] == instance.furniture_type
        assert event.properties['external_id'] == hash_normalized_string(user.email)


@pytest.mark.django_db
def test_sotty_corduroy_material_overwrite_configurator(
    user, api_client, sotty_factory, region_uk
):
    sotty = sotty_factory(
        materials=[Sofa01Color.CORDUROY_ECRU],
    )
    user.profile.region = region_uk
    user.profile.save(update_fields=['region'])
    api_client.force_authenticate(user)
    url = reverse('sotty-configurator', kwargs={'pk': sotty.pk})
    response = api_client.get(url)

    assert response.status_code == status.HTTP_200_OK
    assert response.data['materials'] == [Sofa01Color.REWOOL2_BROWN.value]
    assert response.data['configurator_params']['material'] == {
        'material': Sofa01Color.REWOOL2_BROWN.value,
        'material_cushion': Sofa01Color.REWOOL2_BROWN.value,
        'material_backrest': Sofa01Color.REWOOL2_BROWN.value,
    }
    assert response.data['configurator_params']['decoder_params'][
        'default_material'
    ] == {
        'material': Sofa01Color.REWOOL2_BROWN.value,
        'material_cushion': Sofa01Color.REWOOL2_BROWN.value,
        'material_backrest': Sofa01Color.REWOOL2_BROWN.value,
    }


@pytest.mark.django_db
class TestSottyConfiguratorPreloadAPIView:
    def test_basic_response_structure(
        self,
        user,
        api_client,
        sotty_factory,
        regions,
    ):
        sotty = sotty_factory()
        api_client.force_authenticate(user)
        url = reverse('sotty-preload', kwargs={'pk': sotty.pk})
        response = api_client.get(url)

        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        expected_fields = {
            'preview',
            'height',
            'depth',
            'title',
            'width',
            'configuratorType',
            'material',
            'shelfType',
            'omnibusPrice',
            'furnitureCategory',
            'price',
            'priceWithDiscount',
            'pricing',
            'priceWithDiscountInEuro',
            'priceInEuro',
        }
        assert set(response_data.keys()) == expected_fields
        assert response_data['material'] == sotty.materials[0]
        assert response_data['shelfType'] == sotty.shelf_type
        assert response_data['configuratorType'] == sotty.configurator_type

    @patch('gallery.ecommerce_api.pdp.metrics_client')
    @patch('gallery.ecommerce_api.pdp.add_jetty_state_to_redis')
    def test_analytics_events_triggered(
        self,
        add_jetty_state_to_redis_mock,
        metrics_client_mock,
        user,
        api_client,
        sotty_factory,
        regions,
    ):
        sotty = sotty_factory()
        api_client.force_authenticate(user)
        url = reverse('sotty-preload', kwargs={'pk': sotty.pk})
        response = api_client.get(url)

        assert response.status_code == status.HTTP_200_OK
        assert add_jetty_state_to_redis_mock.call_count == 1
        assert add_jetty_state_to_redis_mock.call_args == call(
            user_id=user.id,
            jetty=sotty,
            source=ShelfStatusSource.PRODUCT_VIEW,
            pagepath='missing referer',
        )
        assert metrics_client_mock.call_count == 1

    def test_corduroy_material_overwrite_uk_region(
        self,
        user,
        api_client,
        sotty_factory,
        region_uk,
    ):
        sotty = sotty_factory(
            materials=[Sofa01Color.CORDUROY_ECRU],
        )
        user.profile.region = region_uk
        user.profile.save(update_fields=['region'])
        api_client.force_authenticate(user)
        url = reverse('sotty-preload', kwargs={'pk': sotty.pk})
        response = api_client.get(url)

        assert response.status_code == status.HTTP_200_OK
        assert response.json()['material'] == Sofa01Color.REWOOL2_BROWN.value

    def test_corduroy_material_not_overwritten_non_uk_region(
        self,
        user,
        api_client,
        sotty_factory,
        regions,
    ):
        sotty = sotty_factory(
            materials=[Sofa01Color.CORDUROY_ECRU],
        )
        api_client.force_authenticate(user)
        url = reverse('sotty-preload', kwargs={'pk': sotty.pk})
        response = api_client.get(url)

        assert response.status_code == status.HTTP_200_OK
        assert response.json()['material'] == Sofa01Color.CORDUROY_ECRU.value

    def test_title_generation(
        self,
        user,
        api_client,
        sotty_factory,
        regions,
    ):
        sotty = sotty_factory()
        api_client.force_authenticate(user)
        url = reverse('sotty-preload', kwargs={'pk': sotty.pk})
        response = api_client.get(url)

        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        expected_title = f'{sotty.furniture_category.translated_name} {ShelfType(sotty.shelf_type).translated_name}'  # noqa: E501
        assert response_data['title'] == expected_title

    def test_dimensions_in_centimeters(
        self,
        user,
        api_client,
        sotty_factory,
        regions,
    ):
        sotty = sotty_factory(
            width=3500,  # 3500mm = 350cm
            depth=1600,  # 1600mm = 160cm
            height=830,  # 830mm = 83cm
        )
        api_client.force_authenticate(user)
        url = reverse('sotty-preload', kwargs={'pk': sotty.pk})
        response = api_client.get(url)

        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert response_data['width'] == 350
        assert response_data['depth'] == 160
        assert response_data['height'] == 83

    def test_unauthenticated_access_allowed(
        self,
        api_client,
        sotty_factory,
        regions,
    ):
        sotty = sotty_factory()
        url = reverse('sotty-preload', kwargs={'pk': sotty.pk})
        response = api_client.get(url)

        assert response.status_code == status.HTTP_200_OK

    def test_nonexistent_sotty_returns_404(
        self,
        user,
        api_client,
        regions,
    ):
        api_client.force_authenticate(user)
        url = reverse('sotty-preload', kwargs={'pk': 99999})
        response = api_client.get(url)

        assert response.status_code == status.HTTP_404_NOT_FOUND
