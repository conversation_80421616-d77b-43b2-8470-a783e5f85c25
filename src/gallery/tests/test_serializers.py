import pytest

from pytest_cases import parametrize_with_cases

from custom.enums.colors import Sofa01Color
from gallery.enums import FurnitureCategory
from gallery.serializers import (
    JettyPDPSerializer,
    SottyConfiguratorSerializer,
    SottySerializer,
)
from regions.cached_region import CachedRegionData


@pytest.mark.django_db
class TestJettyPDPSerializer:
    @pytest.mark.parametrize(('shelf_price', 'sale_price'), ((1235, 865), (6196, 4337)))  # noqa: PT007
    def test_promo_price_rounding(
        self,
        shelf_price,
        sale_price,
        mocker,
        region_de,
        currency_rate_factory,
        region_rate_factory,
        global_strikethrough_promo_30,
        jetty,
    ):
        """
        Shelf price and discount values are based on real life example with
        incorrect sale_price rounding.
        """
        region_rate_factory(region=region_de)
        currency_rate_factory(currency=region_de.currency)
        mocker.patch(
            'gallery.models.Jetty.get_regionalized_price',
            return_value=shelf_price,
        )
        serializer_data = JettyPDPSerializer(
            jetty,
            context={'region': region_de.cached_region_data},
        ).data
        assert serializer_data['price_with_discount'] == sale_price


class SottyMaterialCases:
    def case_sotty_in_one_material(self):
        return (Sofa01Color.REWOOL2_BROWN,), Sofa01Color.REWOOL2_BROWN

    def case_sotty_with_multicolor_material(self):
        return (Sofa01Color.REWOOL2_BROWN, Sofa01Color.REWOOL2_OLIVE_GREEN), -1


@pytest.mark.django_db
class TestSottySerializer:
    @parametrize_with_cases('materials, expected_material', cases=SottyMaterialCases)
    def test_to_representation(
        self, sotty_factory, region_factory, materials, expected_material
    ):
        sotty = sotty_factory(materials=materials)
        germany = CachedRegionData(**region_factory(germany=True).get_data_as_dict())
        serialized_data = SottySerializer(sotty, context={'region': germany}).data

        assert serialized_data['id'] == sotty.id
        assert serialized_data['material'] == expected_material
        assert serialized_data['materials'] == sotty.materials


@pytest.mark.django_db
class TestSottyConfiguratorSerializer:
    @pytest.mark.parametrize(
        'furniture_category',
        [
            FurnitureCategory.TWO_SEATER,
            FurnitureCategory.THREE_SEATER,
            FurnitureCategory.FOUR_PLUS_SEATER,
            FurnitureCategory.CORNER,
            FurnitureCategory.CHAISE_LONGUE,
        ],
    )
    def test_to_representation(
        self,
        sotty_factory,
        region_factory,
        furniture_category,
    ):
        sotty = sotty_factory(shelf_category=furniture_category)
        germany = CachedRegionData(**region_factory(germany=True).get_data_as_dict())
        serialized_data = SottyConfiguratorSerializer(
            sotty, context={'region': germany}
        ).data

        assert serialized_data['id'] == sotty.id
        assert serialized_data['category'] == 'sofa'
