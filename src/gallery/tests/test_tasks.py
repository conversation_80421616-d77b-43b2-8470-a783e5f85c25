from unittest.mock import patch

import pytest

from custom.enums.enums import Furniture
from gallery.enums import FurnitureImageType
from gallery.models import FurnitureImage
from gallery.models.furniture_abstract import SottyAbstract
from gallery.tasks import create_sketch_image
from gallery.tests.fixtures.data import TECHNICAL_SKETCH


def sotty_save_without_sketch_side_effect(instance, *args, **kwargs):
    return SottyAbstract.save(instance, *args, **kwargs)


@pytest.mark.django_db
class TestCreateSketchImage:
    @patch(
        'gallery.models.Sotty.save',
        side_effect=sotty_save_without_sketch_side_effect,
        autospec=True,
    )
    @patch('gallery.tasks.generate_technical_sketch', return_value=TECHNICAL_SKETCH)
    def test_create_sketch_image_success(
        self,
        _,  # noqa: PT019
        __,  # noqa: PT019
        sotty_factory,
    ):
        sotty = sotty_factory()

        create_sketch_image(furniture_type=Furniture.sotty.value, furniture_id=sotty.id)

        furniture_image = FurnitureImage.objects.get(
            furniture_object_id=sotty.id,
            furniture_content_type__model='sotty',
            type=FurnitureImageType.TECHNICAL_SKETCH,
            enabled=True,
        )
        assert furniture_image.image is not None
        assert sotty.technical_sketch == furniture_image

    @patch(
        'gallery.models.Sotty.save',
        side_effect=sotty_save_without_sketch_side_effect,
        autospec=True,
    )
    @patch('gallery.tasks.generate_technical_sketch', return_value=TECHNICAL_SKETCH)
    def test_create_sketch_image_if_image_doesnt_exist(
        self,
        _,  # noqa: PT019
        __,  # noqa: PT019
        sotty_factory,
        furniture_image_factory,
    ):
        sotty = sotty_factory()
        furniture_image_factory(
            furniture=sotty, image=None, type=FurnitureImageType.TECHNICAL_SKETCH
        )

        create_sketch_image(furniture_type=Furniture.sotty.value, furniture_id=sotty.id)

        furniture_image = FurnitureImage.objects.get(
            furniture_object_id=sotty.id,
            furniture_content_type__model='sotty',
            type=FurnitureImageType.TECHNICAL_SKETCH,
        )
        assert furniture_image.image is not None
