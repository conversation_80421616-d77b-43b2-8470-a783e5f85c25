from django.urls import (
    include,
    path,
)
from rest_framework import routers

from gallery.views import (
    BagPresetView,
    CustomDnaPreviewView,
    FurnitureImageRenderListView,
    InfluencersLPFurnitureListView,
    JettyViewSet,
    JettyWishlistDetailApiView,
    SampleBoxViewSet,
    SottyViewSet,
    SottyWishlistDetailApiView,
    WattyViewSet,
    WattyWishlistDetailApiView,
    WishlistView,
)

router = routers.SimpleRouter()
router.register('gallery/jetty', JettyViewSet)
router.register('gallery/sample_box', SampleBoxViewSet)
router.register('gallery/sotty', SottyViewSet)
router.register('gallery/watty', WattyViewSet)


urlpatterns = [
    path(
        'renders/',
        FurnitureImageRenderListView.as_view(),
        name='furniture-renders',
    ),
    path(
        'custom_dna/<int:pk>/',
        CustomDnaPreviewView.as_view(),
        name='custom-dna-preview',
    ),
    path(
        'wishlist/',
        WishlistView.as_view(),
        name='wishlist-list',
    ),
    path(
        'wishlist/jetty/<int:pk>/',
        JettyWishlistDetailApiView.as_view(),
        name='jetty-wishlist-detail',
    ),
    path(
        'wishlist/sotty/<int:pk>/',
        SottyWishlistDetailApiView.as_view(),
        name='sotty-wishlist-detail',
    ),
    path(
        'wishlist/watty/<int:pk>/',
        WattyWishlistDetailApiView.as_view(),
        name='watty-wishlist-detail',
    ),
    path('bag/jetty/', BagPresetView.as_view(), name='bag-preset'),
    path(
        'influencers_lp/',
        InfluencersLPFurnitureListView.as_view(),
        name='influencers-lp-api',
    ),
    path('', include('gallery.api_urls')),
    path('', include(router.urls)),
]
