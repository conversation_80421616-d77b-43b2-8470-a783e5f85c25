import io

from collections import defaultdict
from typing import (
    Iterable,
    MutableSequence,
    Optional,
    Tuple,
)

from django.core.files import File
from django.db.models import Q
from django.http import HttpRequest

from PIL import Image

from custom.utils.decorators import production_only
from gallery.enums import (
    CapeCollectionType,
    ConfiguratorTypeEnum,
)
from gallery.models.furniture_abstract import FurnitureAbstract
from orders.choices import OrderSource


def change_mm_to_cm_int(value):
    return int(value // 10.0)


def get_capacity(width: int, default: int) -> int:
    """
    Get capacity for one opening
    :param width: Opening width
    :param default: Value to be returned if opening width over known caps
    :return: Capacity for this opening
    """

    capacity_table = {
        300: 50,
        400: 40,
        500: 35,
        600: 30,
        700: 25,
        800: 20,
        900: 15,
        1000: 10,
    }
    # Get max capacity for the given width
    return next(
        (capacity_table[key] for key in sorted(capacity_table.keys()) if key >= width),
        default,
    )


def round_to(inp: int, r: int) -> int:
    """
    :param inp: Number to be rounded
    :param r: modulus
    :return: Input rounded down to the nearest multiple of r
    """
    return int(inp / r) * r


def clean_horizontals(horizontals: Iterable[dict]) -> dict:
    horizontals_levels = defaultdict(list)
    horizontals_sorted = sorted(horizontals, key=lambda h: h['x1'])
    for horizontal in horizontals_sorted:
        hori_candidate = (horizontal['x1'], horizontal['x2'])
        previous_horis_on_this_level = horizontals_levels[horizontal['y1'] + 9]
        if previous_horis_on_this_level:
            rightmost_end = previous_horis_on_this_level[-1][1]
            candidate_leftmost_end = hori_candidate[0]
            if rightmost_end == candidate_leftmost_end:
                previous_horis_on_this_level[-1] = (
                    previous_horis_on_this_level[-1][0],
                    hori_candidate[1],
                )
            else:
                previous_horis_on_this_level.append(hori_candidate)
        else:
            horizontals_levels[horizontal['y1'] + 9] = [hori_candidate]
    return horizontals_levels


def get_opening_widths(
    verticals: Iterable[dict],
    horizontals: dict,
) -> MutableSequence[int]:
    """
    :param verticals: List of all verticals in the Jetty
    :param horizontals: List of all horizontals in the Jetty
    :return: Sorted list of all opening widths
    """
    vertical_levels = defaultdict(list)
    for vertical in verticals:
        vertical_levels[vertical['y1']].append(vertical['x1'])

    opening_widths = []
    for level, verts in vertical_levels.items():
        sorted_verts = sorted(verts)
        for x1, x2 in zip(sorted_verts, sorted_verts[1:]):
            for horizontal in horizontals[level]:
                if x1 in range(*horizontal) and x2 in range(*horizontal):
                    opening_widths.append(x2 - x1)  # noqa: PERF401
    return sorted(opening_widths, reverse=True)


def get_set_table_for_dna(shelf_type: int, pattern: int) -> dict:
    """
    :param shelf_type: shelf type
    :param pattern: shelf pattern
    :return: capacity parameters for the given DNA
    """
    set_table = {
        0: {
            0: {  # Slant
                'default': 8,
                'subset_len': 0.2,
                'doors': 0.7,
                'param_1': 0.85,
                'param_2': 0.95,
                'param_3': 0.88,
                'p_min': 5,
                'p_max': 60,
                'o_min': 10,
                'o_max': 40,
            },
            1: {  # Gradient
                'default': 7,
                'subset_len': 0.3,
                'doors': 0.9,
                'param_1': 1.2,
                'param_2': 0.96,
                'param_3': 0.88,
                'p_min': 15,
                'p_max': 65,
                'o_min': 20,
                'o_max': 50,
            },
            2: {  # Pattern
                'default': 6,
                'subset_len': 0.25,
                'doors': 0.8,
                'param_1': 1,
                'param_2': 0.95,
                'param_3': 0.88,
                'p_min': 10,
                'p_max': 60,
                'o_min': 20,
                'o_max': 45,
            },
            3: {  # Grid
                'default': 10,
                'subset_len': 0.15,
                'doors': 0.95,
                'param_1': 1,
                'param_2': 0.98,
                'param_3': 0.87,
                'p_min': 20,
                'p_max': 80,
                'o_min': 20,
                'o_max': 60,
            },
        },
        4: {  # Pattern
            'default': 6,
            'subset_len': 0.25,
            'doors': 0.8,
            'param_1': 1,
            'param_2': 0.95,
            'param_3': 0.88,
            'p_min': 10,
            'p_max': 60,
            'o_min': 20,
            'o_max': 45,
        },
        1: {
            0: {
                'default': 8,
                'subset_len': 0.2,
                'doors': 0.7,
                'param_1': 0.85,
                'param_2': 0.95,
                'param_3': 0.88,
                'p_min': 5,
                'p_max': 60,
                'o_min': 10,
                'o_max': 40,
            },
            1: {
                'default': 10,
                'subset_len': 0.15,
                'doors': 0.95,
                'param_1': 1,
                'param_2': 0.98,
                'param_3': 0.87,
                'p_min': 20,
                'p_max': 80,
                'o_min': 20,
                'o_max': 60,
            },
            2: {
                'default': 6,
                'subset_len': 0.25,
                'doors': 0.8,
                'param_1': 1,
                'param_2': 0.95,
                'param_3': 0.88,
                'p_min': 10,
                'p_max': 60,
                'o_min': 20,
                'o_max': 45,
            },
            3: {
                'default': 7,
                'subset_len': 0.3,
                'doors': 0.9,
                'param_1': 1.2,
                'param_2': 0.96,
                'param_3': 0.88,
                'p_min': 15,
                'p_max': 65,
                'o_min': 20,
                'o_max': 50,
            },
            4: {
                'default': 6,
                'subset_len': 0.25,
                'doors': 0.8,
                'param_1': 1,
                'param_2': 0.95,
                'param_3': 0.88,
                'p_min': 10,
                'p_max': 60,
                'o_min': 20,
                'o_max': 45,
            },
        },
    }
    shelf_type_for_capacity = shelf_type if shelf_type != 2 else 1
    set_table_for_type = set_table.get(shelf_type_for_capacity, set_table[0])
    return set_table_for_type.get(
        pattern,
        set_table_for_type[0],
    )


def compute_adjustment(
    set_table: dict, has_doors_or_drawers: bool, row_amount: int
) -> float:
    """
    Computes the adjustment:
    if has doors
    then DNA coefficient (param_1)
    then number of rows (param_2 - exponentially)
    """
    adjustment = set_table['doors'] if has_doors_or_drawers else 1
    adjustment *= set_table['param_1'] * (set_table['param_2'] ** row_amount)
    return adjustment


def get_total_capacity(
    set_table: dict,
    width: int,
    verticals: Iterable[dict],
    cleaned_horizontals: dict,
    has_doors_or_drawers: bool,
) -> int:
    """
    :param set_table: Table with capacity parameters
    :param width: Jetty width
    :param verticals: All jetty's verticals
    :param cleaned_horizontals: Horizontals, joined, to check for openings
    :param has_doors_or_drawers: Does the jetty have doors or drawers
    :return: Maximal capacity for the jetty
    """
    row_amount = len(cleaned_horizontals) - 1
    flattened_width_m = row_amount * width // 1000
    adjustment = compute_adjustment(
        set_table,
        has_doors_or_drawers,
        row_amount,
    )
    # amount of running meters of rows

    all_widths = get_opening_widths(verticals, cleaned_horizontals)
    number_of_openings = len(all_widths)
    number_of_openings_for_avg = int(number_of_openings * set_table['subset_len']) or 1
    widths_for_avg = all_widths[:number_of_openings_for_avg]
    avg_width = sum(widths_for_avg) // len(widths_for_avg)

    # Total capacity: check DNA's min/max, round to the nearest 10
    total = (
        get_capacity(avg_width, set_table['default'])
        * adjustment
        * number_of_openings
        * set_table['param_3']
    )
    total = max(
        min(set_table['p_max'] * flattened_width_m, total),
        set_table['p_min'] * flattened_width_m,
        number_of_openings * set_table['o_min'],
    )
    return total


def prepare_dnas_for_shelf_type(shelf_type: int = 0) -> list[dict]:
    """
    Returns list of single pattern dnas for specific shelf type.
    """
    from gallery.models import CustomDna

    dnas = []
    query = Q(
        visible_on_web=True,
        configurator_type=ConfiguratorTypeEnum.ROW,
        shelf_type=shelf_type,
    )
    exclude_new_collection_types_query = ~Q(
        collection_type__in=[
            CapeCollectionType.BOOKCASE,
            CapeCollectionType.VINYL_STORAGE,
        ]
    )
    queryset = CustomDna.objects.filter(
        query & exclude_new_collection_types_query
    ).order_by('pattern_slot')
    for dna in queryset:
        dnas.append(  # noqa: PERF401
            {
                'configurator_type': ConfiguratorTypeEnum.ROW,
                'json': dna.dna_json,
                'new_dna_tools': dna.new_dna_tools,
            }
        )
    return dnas


def fill_transparent_background(image_file, background=(237, 240, 240)):
    # default color is #edf0f0
    # TODO: remove or create a task when celery is ready
    with Image.open(image_file) as image:
        if image.mode in ('RGBA', 'LA') or (
            image.mode == 'P' and 'transparency' in image.info
        ):
            alpha = image.convert('RGBA').split()[-1]
            background_image = Image.new('RGBA', image.size, background + (255,))  # noqa: RUF005
            background_image.paste(image, mask=alpha)
            return background_image
        return image


def convert_image_to_file(
    image_file: Image,
    file_name: str,
    image_format: str = 'PNG',
) -> File:
    image_bytes = io.BytesIO()
    image_file.save(image_bytes, image_format)
    file = File(image_bytes, file_name)
    return file


def get_openings_load_range(
    verticals: Iterable[dict],
    cleaned_horizontals: dict,
    adjustment: float,
    set_table: dict,
) -> Tuple[Optional[float], float]:
    opening_widths = get_opening_widths(verticals, cleaned_horizontals)
    openings = [
        round_to(int(get_capacity(width, set_table['default']) * adjustment), 5)
        for width in opening_widths
    ]
    capacity_max = min(set_table['o_max'], max(openings + [set_table['o_min']]))  # noqa: RUF005
    capacity_min = max(set_table['o_min'], min(openings))
    if capacity_max == capacity_min:
        capacity_min = None
    return capacity_min, capacity_max


def get_request_platform(request: HttpRequest) -> OrderSource:
    if request.user_agent.is_mobile or request.user_agent.is_tablet:
        return OrderSource.WEB_MOBILE
    else:
        return OrderSource.WEB_DESKTOP


@production_only
def send_to_facebook_scraper(request: HttpRequest, item: FurnitureAbstract) -> None:
    """Send furniture page to facebook scraper to enable sharing with preview"""
    # TODO: investigate why this raises http 400, disable for now
    # from gallery.tasks import send_to_facebook_scrapper
    #
    # send_to_facebook_scrapper.delay(item.get_item_url(), request.get_host())
