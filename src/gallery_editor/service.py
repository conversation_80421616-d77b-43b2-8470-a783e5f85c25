import abc
import json
import typing

from functools import cached_property

from django.shortcuts import get_object_or_404

from requests.exceptions import ConnectionError

from gallery import data_from_ps
from gallery.enums import FurnitureStatusEnum
from gallery.models import (
    <PERSON>y,
    <PERSON><PERSON>,
)
from gallery.serializers import (
    JettySerializer,
    WattySerializer,
)
from producers.models import Product


class GalleryEditorService(abc.ABC):
    geometry_fields: typing.ClassVar[list[str]]
    model: typing.ClassVar[typing.Union[Jetty, Watty]]
    serializer: typing.ClassVar[typing.Union[JettySerializer, WattySerializer]]

    def __init__(self, geometry: typing.Union[Jetty, Watty]) -> None:
        self.gallery_object = geometry

    @cached_property
    def product(self) -> typing.Optional[Product]:
        try:
            return self.gallery_object.order_items.first().product_set.first()
        except AttributeError:
            return

    @property
    def gallery_object_as_json(self) -> str:
        return json.dumps(self.serializer(self.gallery_object).data)

    @property
    def price_as_number(self) -> int:
        return self.gallery_object.get_shelf_price_as_number(region=None, in_pln=True)

    @property
    def ps_errors(self) -> list[str]:
        """Test serialization with PS and return formatted list of errors."""
        try:
            serialized_shelf = data_from_ps.get_serialized_data_from_ps(
                self.gallery_object
            )
            errors = [
                error.get('msg', error) if isinstance(error, dict) else error
                for error in serialized_shelf.get('errors', [])
            ]
        except ConnectionError:
            errors = ['Error connecting to Production System']
        return errors

    def apply_draft(self, draft_gallery_object: typing.Union[Jetty, Watty]):
        """Apply geometry from a draft design to the current gallery object."""
        changed_geometry = {
            field_name: getattr(draft_gallery_object, field_name)
            for field_name in self.geometry_fields
        }
        self.process(changed_geometry, save_original=True)
        self.gallery_object.description = (
            f'{self.gallery_object.description}; '
            f'Applied changes from {self.model.__name__} id {draft_gallery_object.id}'
        )
        self.gallery_object.save(update_fields=['description'])

    def process(
        self,
        changed_geometry: dict,
        save_original: bool = False,
        save_draft: bool = False,
    ) -> int:
        self.apply_changes(changed_geometry)
        if save_original:
            return self.save_changes()
        elif save_draft:
            return self.save_draft()

    def apply_changes(self, changed_geometry: dict) -> None:
        for field, value in changed_geometry.items():
            self.gallery_object.__setattr__(field, value)

    def recalculate_product_front_view(self) -> None:
        """
        If edited Jetty has a Product, re-serialize and update its front view.

        Front view is visible in the CS panel, so we update it right away.
        Other production files will be updated upon batching.
        """
        self.product.serialization_updater.update_product_serialization()
        self.product.details.generate_front_view()

    def add_note_to_product(self) -> None:
        """If edited Jetty has a Product, add a note informing of the edit."""
        message = 'Edytowane przez narzędzie CS'
        if not self.product.notes:
            self.product.notes = message
        elif message not in self.product.notes:
            self.product.notes = f'{message} {self.product.notes}'
        self.product.display_notes_on_front_view = True
        self.product.save()

    def save_changes(self) -> int:
        """Save changes to the edited object and related product, if there is one."""
        backup_id = self.save_backup()
        self.gallery_object.description = (
            f'{self.gallery_object.description or ""} Backup: {backup_id}'
        )
        self.gallery_object.save()
        if self.product:
            self.add_note_to_product()
            self.recalculate_product_front_view()
        return self.gallery_object.id

    def save_backup(self) -> int:
        """Save a backup copy of the original object (as DRAFT) and return its id."""
        gallery_object = get_object_or_404(self.model, id=self.gallery_object.id)
        gallery_object.id = None
        gallery_object.furniture_status = FurnitureStatusEnum.DRAFT
        gallery_object.save()
        return gallery_object.id

    def save_draft(self) -> int:
        """Save changed Jetty or Watty as a new object."""
        original_gallery_object_id = self.gallery_object.id
        self.gallery_object.id = None
        self.gallery_object.furniture_status = FurnitureStatusEnum.DRAFT
        self.gallery_object.description = (
            f'Draft for {self.model.__name__} {original_gallery_object_id}. '
            f'{self.gallery_object.description or ""}'
        )
        self.gallery_object.save()
        return self.gallery_object.id


class JettyEditorService(GalleryEditorService):
    model = Jetty
    serializer = JettySerializer
    geometry_fields = [
        'verticals',
        'horizontals',
        'doors',
        'backs',
        'drawers',
        'supports',
        'modules',
        'legs',
        'inserts',
        'plinth',
        'long_legs',
        'cable_management',
        'desk_beams',
        'width',
        'height',
        'depth',
    ]


class WattyEditorService(GalleryEditorService):
    model = Watty
    serializer = WattySerializer
    geometry_fields = [
        'doors',
        'backs',
        'drawers',
        'walls',
        'slabs',
        'frame',
        'bars',
        'cable_management',
        'hinges',
        'masking_bars',
        'lighting',
        'legs',
        'width',
        'height',
        'depth',
    ]
