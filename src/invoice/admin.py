import csv
import logging
import time
import typing

from datetime import datetime
from subprocess import Popen  # noqa: S404

from django.contrib import admin
from django.contrib.admin.actions import delete_selected
from django.core.paginator import Paginator
from django.db.models import (
    Count,
    Q,
    QuerySet,
    Sum,
    Value,
)
from django.db.models.functions import Coalesce
from django.http import HttpResponse
from django.template.loader import render_to_string
from django.utils import timezone
from django.utils.html import escape
from django.utils.safestring import mark_safe

from django_object_actions import DjangoObjectActions

from custom.admin_action import admin_action_with_form
from custom.constants import VAT_EU
from custom.models import Countries
from custom.utils.mixins import ReadOnlyAdminMixin
from invoice.admin_actions import (
    InvoiceAdminActionsMixin,
    accounting_sales_report,
    create_invoice_correction_for_amount,
    create_invoice_correction_neutralization,
    create_normal_from_proforma_in_sell_month,
    create_normal_invoice_from_proforma,
    create_pdf,
    export_whole_invoices_by_lines,
    export_whole_invoices_by_lines_correcting,
    invoice_items_summary,
    order_summary,
    pdf_link,
    recalculate_invoice_items,
    return_response_with_file,
)
from invoice.admin_filters import (
    InvoicePrettyIdFilter,
    OrderCountryFilter,
    SentInvoiceAtWithCustomNoDateDateFieldListFilter,
)
from invoice.choices import InvoiceStatus
from invoice.forms import InvoiceStornoRevertForm
from invoice.models import (
    Invoice,
    InvoiceCorrectionChange,
    InvoiceDomestic,
    InvoiceItem,
    InvoicePreview,
    InvoiceSequence,
    ProformaCstmInvoice,
    ProformaCustomerServiceInvoice,
    ProformaLogisticInvoice,
    ProformaRetoolInvoice,
    SymfoniaFConfiguration,
)
from invoice.symfonia import (
    export_to_fka,
    storno_adjustments,
    symfonia_queryset,
)
from logger.admin import LoggerMixin
from orders.internal_api.clients import LogisticOrderAPIClient

if typing.TYPE_CHECKING:
    from django.http import HttpRequest


logger = logging.getLogger('invoice')


class InvoiceItemInline(admin.StackedInline):
    model = InvoiceItem
    extra = 0
    raw_id_fields = (
        'invoice',
        'corrected_invoice_item',
        'order_item',
    )

    class Media:
        js = ('invoice/js/admin_extra.js',)


class InvoiceCorrectionChangeInline(admin.StackedInline):
    extra = 1
    model = InvoiceCorrectionChange
    raw_id_fields = ('correcting_invoice',)

    class Media:
        js = ('invoice/js/admin_extra.js',)


class InvoiceAdmin(
    ReadOnlyAdminMixin,
    InvoiceAdminActionsMixin,
    DjangoObjectActions,
    LoggerMixin,
):
    log_actions = True
    list_display = (
        'id',
        'status',
        'pretty_id',
        pdf_link,
        'correction_info',
        'sell_at',
        'issued_at',
        'get_payment_date',
        'get_delivery_date_from_logistic_order',
        'get_sent_to_customer_from_logistic_order',
        'order_as_link',
        order_summary,
        'invoice_sent_at',
        invoice_items_summary,
        'order_items_count',
        'get_package_weight_from_logistic_order',
    )

    list_display_links = (
        'id',
        'pretty_id',
    )

    list_filter = (
        InvoicePrettyIdFilter,
        'created_at',
        'status',
        'source',
        ('sent_invoice_at', SentInvoiceAtWithCustomNoDateDateFieldListFilter),
        OrderCountryFilter,
    )

    search_fields = (
        'id',
        'order__id',
        'pretty_id',
        'order__order_pretty_id',
        'receipt_id',
    )
    raw_id_fields = (
        'order',
        'corrected_invoice',
        'previous_correction',
    )
    date_hierarchy = 'issued_at'
    exclude = (
        'generated_from_order',
        'invoice_history',
        'cached_to_dict',
    )
    inlines = (
        InvoiceItemInline,
        InvoiceCorrectionChangeInline,
    )

    actions = (
        'delete_selected',
        accounting_sales_report,
        'prepare_symfonia_storno',
        'revert_symfonia_storno',
        'export_selected_to_symfonia_f_selected',
        'export_selected_to_symfonia_f_all',
        create_pdf,
        recalculate_invoice_items,
        create_normal_invoice_from_proforma,
        create_normal_from_proforma_in_sell_month,
        export_whole_invoices_by_lines,
        export_whole_invoices_by_lines_correcting,
        create_invoice_correction_neutralization,
        create_invoice_correction_for_amount,
        'export_selected_to_symfonia',
        'export_invoices_intrastat',
        'export_invoices_intrastat_2_months',
        'send_invoice_to_customer',
    )

    change_actions = ('edit_cached_to_dict_and_render_pdf',)

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        qs = qs.exclude(status=InvoiceStatus.CORRECTING_DRAFT)
        return (
            qs.select_related(
                'order',
                'order__owner',
                'corrected_invoice',
                'previous_correction',
            )
            .prefetch_related(
                'invoice_items',
                'order__transactions',
                'corrections',
            )
            .annotate(
                order_items_count=Coalesce(
                    Sum('order__items__quantity', filter=Q(order__items__deleted=None)),
                    Value(0),
                )
            )
        )

    @admin.action(description='Delete selected proforma invoices')
    def delete_selected(self, request, queryset):
        if queryset.exclude(status=InvoiceStatus.PROFORMA).exists():
            self.message_user(
                request,
                'You can delete only proforma invoices',
                level=logging.ERROR,
            )
            return
        return delete_selected(self, request, queryset)

    def has_delete_permission(self, request, obj=None):
        if obj and obj.status != InvoiceStatus.PROFORMA:
            return False
        return super().has_delete_permission(request, obj)

    def order_items_count(self, obj):
        return getattr(obj, 'order_items_count', None)

    @admin.action
    def send_invoice_to_customer(self, request, queryset):
        for invoice in queryset:
            invoice.send_invoice_to_user()
        self.message_user(request, 'Invoices sent to customer')

    @admin.action
    def export_invoices_intrastat_2_months(self, request, queryset):
        return self.export_invoices_intrastat(request, queryset, months_back=2)

    @admin.action
    def export_invoices_intrastat(self, request, queryset, months_back=1):
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = (
            'attachment; filename=invoices_intrastat-{}.csv'.format(
                timezone.now().strftime('%Y_%m_%d'),
            )
        )
        response.write('\ufeff'.encode('utf8'))
        writer = csv.writer(response, delimiter=';')
        written_header = False

        logistic_order_api_client = LogisticOrderAPIClient()
        order_ids = logistic_order_api_client.filter_by_delivered_date(months_back)

        invoices = (
            self.model.objects.filter(
                Q(status=InvoiceStatus.ENABLED) | Q(status=InvoiceStatus.CORRECTING)
            )
            .filter(order__vat_type=VAT_EU)
            .filter(order__in=order_ids)
            .order_by('-issued_at', '-id')
        )

        def pretty_id(inv, logistic):
            return inv.pretty_id

        def items_count(inv, logistic):
            return inv.invoice_items.count()

        def order_id(inv, logistic):
            return inv.order.pk

        def order_vat(inv, logistic):
            return inv.order.invoice_vat if not inv.order.vat else inv.order.vat

        def country_code(inv, logistic):
            country = Countries.get_country_by_name(inv.order.country)
            return country.code if country else inv.order.country

        def transport_type(inv, logistic):
            return 'DAP'

        def total_netto_weight(inv, logistic):
            if logistic:
                return logistic.total_netto_weight
            return '-1'

        def logistic_date(inv, logistic):
            return logistic.delivered_date

        head = (
            ('Numer faktury', 'pretty_id'),
            ('Numer orderu', 'order_id'),
            ('Kod kraju', 'country_code'),
            ('Kod rodzaju transportu', 'transport_type'),
            ('Masa netto', 'total_netto_weight'),
            ('Ilość', 'items_count'),
            ('Vat kontrahenta', 'order_vat'),
            ('Data dostarczenia', 'logistic_date'),
        )
        for page in Paginator(invoices, 100):
            for invoice in page.object_list:
                logistic = invoice.order.logistic_info[-1]

                if written_header is False:
                    writer.writerow([a for a, b in head])
                    written_header = True

                tmp = []
                for a, b in head:  # noqa: B007
                    tmp.append(locals()[b](invoice, logistic))
                writer.writerow(tmp)
        return response

    @admin.action
    def export_selected_to_symfonia(self, request, queryset):
        response = HttpResponse(content_type='text/plain; charset=iso-8859-2')
        invoices = (
            queryset.order_by('id')
            .prefetch_related('corrections')
            .select_related('order')
            .order_by('issued_at')
        )
        invoices.update(exported_to_symfonia=datetime.now())
        template = 'symfonia/symfonia_export.txt'

        data = render_to_string(template, context={'invs': invoices})

        symfonia_out_file = '/tmp/symfonia_{}'.format(  # noqa: S108
            time.mktime(datetime.today().timetuple())
        )
        out_file = open('{0}_tmp'.format(symfonia_out_file), 'w')
        out_file.write(data)
        out_file.close()
        call = Popen(  # noqa: S602
            'iconv -c -f UTF-8 -t cp1250 {0}_tmp >{0}'.format(symfonia_out_file),
            shell=True,
        )
        call.wait()
        invoices.update(exported_to_symfonia=datetime.now())
        response['Content-Disposition'] = 'attachment; filename={}'.format(
            'symfonia_{}.txt'.format('_'.join([str(i.pk) for i in invoices]))
        )
        with open(symfonia_out_file, 'r') as fp:
            data = fp.read()
        response.write(data)
        return response

    @admin.action
    def prepare_symfonia_storno(
        self,
        request: 'HttpRequest',
        queryset: QuerySet['Invoice'],
    ) -> HttpResponse:
        invoices = symfonia_queryset(queryset)
        response_with_file = export_to_fka(self, invoices, request, storno_adjustments)
        return response_with_file

    @admin.action
    def revert_symfonia_storno(
        self,
        request: 'HttpRequest',
        queryset: QuerySet['Invoice'],
    ) -> HttpResponse:
        return admin_action_with_form(
            modeladmin=self,
            request=request,
            queryset=queryset,
            form_class=InvoiceStornoRevertForm,
            success_function=return_response_with_file,
            success_function_kwargs={},
        )

    @admin.action
    def export_selected_to_symfonia_f_selected(self, request, queryset):
        invoices = symfonia_queryset(queryset)
        response_with_file = export_to_fka(self, invoices, request)
        invoices.update(exported_to_symfonia_f=datetime.now())
        return response_with_file

    @admin.action
    def export_selected_to_symfonia_f_all(self, request, queryset):
        statuses_to_export = [
            InvoiceStatus.ENABLED,
            InvoiceStatus.ENABLED_VAT_REGION_CORRECTION,
            InvoiceStatus.CORRECTING,
        ]
        invoices = (
            self.model.objects.filter(
                exported_to_symfonia_f__isnull=True,
                status__in=statuses_to_export,
            )
            .order_by('id')
            .prefetch_related('corrections')
            .select_related('order')
            .order_by('issued_at')
        )
        response_with_file = export_to_fka(self, invoices, request)
        invoices.update(exported_to_symfonia_f=datetime.now())
        return response_with_file

    def get_search_results(self, request, queryset, search_term):
        if ';' in search_term:
            try:
                queryset = queryset.filter(
                    order__id__in=[x for x in search_term.split(';')]  # noqa: C416
                )
                return queryset, False
            except ValueError:
                pass
        return super().get_search_results(request, queryset, search_term)

    @mark_safe  # noqa: S308
    def order_as_link(self, obj):
        return "<a href='/admin/orders/order/{}/'>{} - {}</a>".format(
            obj.order_id,
            obj.order_id,
            escape(obj.order.get_customer_as_string()),
        )

    @mark_safe  # noqa: S308
    def is_company(self, obj):
        if obj.order.is_company():
            return "{} <span class='whole-td-opacity'></span>".format(
                obj.order.is_company(),
            )
        elif obj.order.may_be_a_company():
            return "{} <span class='whole-td-opacity-half'></span>".format(
                obj.order.is_company(),
            )
        else:
            return str(obj.order.is_company())


class InvoiceDomesticAdmin(InvoiceAdmin):
    actions = (
        'delete_selected',
        accounting_sales_report,
        'prepare_symfonia_storno',
        'export_selected_to_symfonia_f_selected',
        'export_selected_to_symfonia_f_all',
        create_pdf,
        export_whole_invoices_by_lines,
        export_whole_invoices_by_lines_correcting,
        'export_selected_to_symfonia',
        'export_invoices_intrastat',
        'export_invoices_intrastat_2_months',
        'send_invoice_to_customer',
    )

    list_filter = (
        InvoicePrettyIdFilter,
        'created_at',
        'status',
        'source',
        'sent_invoice_at',
        OrderCountryFilter,
    )


class InvoicePreviewAdmin(InvoiceAdmin):
    actions = (
        'delete_selected',
        create_pdf,
    )
    list_filter = ('created_at',)

    def get_queryset(self, request):
        qs = super(InvoiceAdmin, self).get_queryset(request)
        return (
            qs.select_related(
                'order',
                'order__owner',
                'corrected_invoice',
                'previous_correction',
            )
            .prefetch_related(
                'invoice_items',
                'order__transactions',
                'corrections',
            )
            .annotate(order_items_count=Count('order__items'))
        )


class ProformaInvoiceAdmin(ReadOnlyAdminMixin, LoggerMixin):
    log_actions = True
    list_display = (
        'id',
        'status',
        'pretty_id',
        pdf_link,
        'correction_info',
        'issued_at',
        'get_payment_date',
        'get_delivery_date_from_logistic_order',
        'get_sent_to_customer_from_logistic_order',
        order_summary,
        'invoice_sent_at',
        invoice_items_summary,
        'get_package_weight_from_logistic_order',
    )

    list_display_links = (
        'id',
        'pretty_id',
    )

    list_filter = (
        'created_at',
        'status',
        'sent_invoice_at',
    )

    search_fields = (
        'id',
        'order__id',
        'pretty_id',
        'order__order_pretty_id',
        'receipt_id',
    )

    raw_id_fields = (
        'order',
        'corrected_invoice',
        'previous_correction',
    )

    date_hierarchy = 'issued_at'

    exclude = ('cached_to_dict',)


class ProformaLogisticInvoiceAdmin(ProformaInvoiceAdmin):
    list_display = (
        'id',
        'status',
        'source',
        'pretty_id',
        pdf_link,
        'correction_info',
        'issued_at',
        'get_payment_date',
        'get_delivery_date_from_logistic_order',
        'get_sent_to_customer_from_logistic_order',
        order_summary,
        'invoice_sent_at',
        invoice_items_summary,
        'get_package_weight_from_logistic_order',
    )

    list_filter = (
        'source',
        'created_at',
        'status',
        'sent_invoice_at',
    )


class ProformaRetoolInvoiceAdmin(ProformaInvoiceAdmin):
    """Retool Proforma Invoices"""


class ProformaCustomerServiceInvoiceAdmin(ProformaInvoiceAdmin):
    """CustomerService Proforma Invoices"""


class InvoiceItemAdmin(ReadOnlyAdminMixin, LoggerMixin):
    log_actions = True
    list_display = (
        'id',
        'invoice',
        'item_type',
        'item_name',
        'item_material',
        'quantity',
        'gross_price',
    )
    list_filter = (
        'item_type',
        'item_name',
        'item_material',
        'gross_price',
    )
    raw_id_fields = (
        'corrected_invoice_item',
        'invoice',
        'order_item',
    )
    search_fields = (
        'invoice__id',
        'invoice__pretty_id',
    )


class SymfoniaFConfigurationAdmin(admin.ModelAdmin):
    list_display = (
        'id',
        'document_type',
        'account',
        'vat_account',
        'transaction_type',
    )


class InvoiceSequenceAdmin(admin.ModelAdmin):
    list_display = (
        'id',
        'numeration_type',
        'country',
        'invoice_type',
        'pretty_id_template',
    )
    list_filter = (
        'numeration_type',
        'country',
    )


admin.site.register(InvoiceItem, InvoiceItemAdmin)
admin.site.register(ProformaCstmInvoice, ProformaInvoiceAdmin)
admin.site.register(ProformaLogisticInvoice, ProformaLogisticInvoiceAdmin)
admin.site.register(ProformaRetoolInvoice, ProformaRetoolInvoiceAdmin)
admin.site.register(ProformaCustomerServiceInvoice, ProformaCustomerServiceInvoiceAdmin)
admin.site.register(SymfoniaFConfiguration, SymfoniaFConfigurationAdmin)
admin.site.register(InvoiceSequence, InvoiceSequenceAdmin)

admin.site.register(Invoice, InvoiceAdmin)
admin.site.register(InvoicePreview, InvoicePreviewAdmin)
admin.site.register(InvoiceDomestic, InvoiceDomesticAdmin)
