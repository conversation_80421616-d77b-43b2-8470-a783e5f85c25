from datetime import (
    UTC,
    datetime,
)

from django.conf import settings

from custom.enums import Furniture
from custom.models import Countries

PLN_IBAN = '****************************'
USD_IBAN = '****************************'
GBP_IBAN = '****************************'
CHF_IBAN = '****************************'
DEFAULT_IBAN = '****************************'

CURRENCY_SYMBOL_TO_IBAN_NUMBER = {
    'zł': PLN_IBAN,
    '$': USD_IBAN,
    '£': GBP_IBAN,
    'CHF': CHF_IBAN,
    '': DEFAULT_IBAN,
}

HTS_CODE = '94036010'
HTS_CODE_FOR_WATTY = '9403500000'
HTS_CODE_FOR_SOTTY = '94016100'

HTS_CODE_BY_FURNITURE_TYPE = {
    Furniture.jetty.value: HTS_CODE,
    Furniture.sample_box.value: HTS_CODE,
    Furniture.watty.value: HTS_CODE_FOR_WATTY,
    Furniture.sotty.value: HTS_CODE_FOR_SOTTY,
}

tzinfo = UTC if settings.USE_TZ else None
FINLAND_VAT_RELEASE_DATETIME = datetime(2024, 9, 1, 0, 0, 0, tzinfo=tzinfo)
SLOVAKIA_VAT_RELEASE_DATETIME = datetime(2025, 1, 1, 0, 0, 0, tzinfo=tzinfo)
ESTONIA_VAT_RELEASE_DATETIME = datetime(2025, 7, 1, 0, 0, 0, tzinfo=tzinfo)
ROMANIA_VAT_RELEASE_DATETIME = datetime(2025, 8, 1, 0, 0, 0, tzinfo=tzinfo)


VAT_CHANGE_DATETIME_BY_COUNTRY = {
    Countries.finland.name: FINLAND_VAT_RELEASE_DATETIME,
    Countries.slovakia.name: SLOVAKIA_VAT_RELEASE_DATETIME,
    Countries.estonia.name: ESTONIA_VAT_RELEASE_DATETIME,
    Countries.romania.name: ROMANIA_VAT_RELEASE_DATETIME,
}

DOMESTIC_RELEASE_DATETIME = datetime(2024, 7, 1, 11, 15, 0, tzinfo=tzinfo)

SEQUENCE_MAJOR_UK_PREFIX = 'INV/'
SEQUENCE_MAJOR_REGION_VAT_PREFIX = 'RV/'

SEQUENCE_MINOR_CORRECTING_PREFIX = 'FKS'
SEQUENCE_MINOR_PROFORMA_PREFIX = 'PROFORMA'
SEQUENCE_MINOR_OTHER_PREFIX = 'DIFF'
