from decimal import Decimal
from typing import Optional

from django.utils.html import format_html_join
from django.utils.safestring import mark_safe

from invoice.dto import InvoiceItemCachedDTO


class InvoiceItemCachedWithServicesPDFAggregator(list):
    def _get_invoice_item_type(self) -> 'InvoiceItemCachedDTO':
        return [item for item in self if item.is_item][0]  # noqa: RUF015

    def _get_invoice_assembly_type(self) -> Optional['InvoiceItemCachedDTO']:
        return next((item for item in self if item.is_assembly), None)

    def _get_invoice_delivery_type(self) -> Optional['InvoiceItemCachedDTO']:
        return next((item for item in self if item.is_delivery), None)

    def _get_invoice_service_types(self) -> list['InvoiceItemCachedDTO']:
        return [item for item in self if item.is_service]

    @property
    def item_name(self) -> str:
        return self._get_invoice_item_type().item_name

    @property
    def assembly_description(self) -> str:
        assembly_item = self._get_invoice_assembly_type()
        if assembly_item is None:
            return ''
        return assembly_item.item_name

    @property
    def delivery_description(self) -> str:
        delivery_item = self._get_invoice_delivery_type()
        if delivery_item is None:
            return ''
        return delivery_item.item_name

    @property
    def service_description(self) -> str:
        service_items = self._get_invoice_service_types()
        item_names = ((service_item.item_name,) for service_item in service_items)
        return format_html_join(mark_safe('<br/>'), '{}', item_names)

    @property
    def item_material(self) -> str:
        return self._get_invoice_item_type().item_material or ''

    @property
    def item_dimensions(self) -> str:
        return self._get_invoice_item_type().item_dimensions or ''

    @property
    def hts_code(self) -> str | None:
        return self._get_invoice_item_type().hts_code

    @property
    def quantity(self) -> int:
        return self._get_invoice_item_type().quantity

    @property
    def vat_rate(self) -> Decimal:
        item = self._get_invoice_item_type()
        return item.vat_rate

    @property
    def net_price(self) -> Decimal:
        return sum((item.net_price for item in self), Decimal('0.0'))

    @property
    def discount_value(self) -> Decimal:
        return sum((item.discount_value for item in self), Decimal('0.0'))

    @property
    def net_value(self) -> Decimal:
        return sum((item.net_value for item in self), Decimal('0.0'))

    @property
    def vat_amount(self) -> Decimal:
        return sum((item.vat_amount for item in self), Decimal('0.0'))

    @property
    def gross_price(self) -> Decimal:
        return sum((item.gross_price for item in self), Decimal('0.0'))

    @property
    def net_weight(self) -> Decimal:
        return self._get_invoice_item_type().net_weight

    @property
    def gross_weight(self) -> Decimal:
        return self._get_invoice_item_type().gross_weight

    @property
    def recycle_tax_value(self) -> Decimal:
        return self._get_invoice_item_type().recycle_tax_value

    @property
    def correction_quantity(self) -> int:
        return self._get_invoice_item_type().correction_quantity()

    @property
    def correction_net_price(self) -> Decimal:
        return sum((item.correction_net_price() for item in self), Decimal('0.0'))

    @property
    def correction_discount_value(self) -> Decimal:
        return sum((item.correction_discount_value() for item in self), Decimal('0.0'))

    @property
    def correction_net_value(self) -> Decimal:
        return sum((item.correction_net_value() for item in self), Decimal('0.0'))

    @property
    def correction_vat_amount(self) -> Decimal:
        return sum((item.correction_vat_amount() for item in self), Decimal('0.0'))

    @property
    def correction_gross_price(self) -> Decimal:
        return sum((item.correction_gross_price() for item in self), Decimal('0.0'))

    @property
    def correction_recycle_tax_value(self) -> Decimal:
        return sum(
            (item.correction_recycle_tax_value() for item in self), Decimal('0.0')
        )

    @property
    def corrected_invoice_item(self) -> Optional['InvoiceItemCachedDTO']:
        return self._get_invoice_item_type().corrected_invoice_item
