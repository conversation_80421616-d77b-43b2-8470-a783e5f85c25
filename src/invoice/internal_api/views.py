import logging
import typing

from subprocess import CalledProcessError  # noqa: S404

from rest_framework import (
    mixins,
    status,
)
from rest_framework.authentication import TokenAuthentication
from rest_framework.decorators import action
from rest_framework.generics import get_object_or_404
from rest_framework.response import Response
from rest_framework.viewsets import GenericViewSet

from custom import permissions
from invoice.internal_api.serializers import (
    CreateLogisticFreeMaterialSampleOutsideEUProformaSerializer,
    CreateProformaInvoiceIgnoredDiscountSerializer,
    CreateProformaInvoiceSerializer,
)
from invoice.models import Invoice
from invoice.serializers import ForLogisticInvoiceSerializer

if typing.TYPE_CHECKING:
    from rest_framework.request import Request


logger = logging.getLogger('invoice')


class InvoiceViewSet(
    mixins.RetrieveModelMixin,
    GenericViewSet,
):
    queryset = Invoice.objects.all()
    authentication_classes = (TokenAuthentication,)
    permission_classes = (permissions.IsLogisticUserPermission,)

    @action(methods=['POST'], url_path='create-proforma', detail=False)
    def create_proforma(self, request, *args, **kwargs):
        serializer = CreateProformaInvoiceSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(
            ForLogisticInvoiceSerializer(serializer.instance).data,
            status=status.HTTP_201_CREATED,
        )

    @action(methods=['POST'], url_path='create-proforma-ignored-discount', detail=False)
    def create_proforma_with_ignored_discount(self, request, *args, **kwargs):
        serializer = CreateProformaInvoiceIgnoredDiscountSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(
            ForLogisticInvoiceSerializer(serializer.instance).data,
            status=status.HTTP_201_CREATED,
        )

    @action(
        methods=['POST'],
        url_path='create-logistic-free-material-sample-proforma',
        detail=False,
    )
    def create_logistic_free_material_sample_outside_eu_proforma(
        self, request: 'Request'
    ) -> Response:
        serializer = CreateLogisticFreeMaterialSampleOutsideEUProformaSerializer(
            data=request.data
        )

        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(
            ForLogisticInvoiceSerializer(serializer.instance).data,
            status=status.HTTP_201_CREATED,
        )

    @action(methods=['GET'], url_path='download-pdf', detail=True)
    def download_pdf(self, request, pk):
        invoice = get_object_or_404(Invoice, id=pk)
        if not invoice.pdf or not invoice.pdf.storage.exists(invoice.pdf.name):
            try:
                invoice.create_pdf()
            except (CalledProcessError, FileNotFoundError, Exception) as e:
                logger.exception(
                    f'Failed to create pdf for invoice(id={invoice.id}) '
                    f'With exception message: {e}'
                )
                return None
        return Response(
            {
                'file_url': invoice.pdf.url,
                'file_name': invoice.pdf.name,
            },
            status=status.HTTP_200_OK,
        )
