from django.core.management.base import BaseCommand

from invoice.choices import (
    InvoiceStatus,
    NumerationType,
)
from invoice.models import InvoiceSequence
from regions.models import Country


class Command(BaseCommand):
    def handle(self, *args, **options):
        for country in Country.objects.all():
            self._generate_invoice_sequences(country, NumerationType.NORMAL)

            if country.region_vat:
                self._generate_invoice_sequences(country, NumerationType.RV)

    @staticmethod
    def _generate_invoice_sequences(country: Country, numeration_type: int) -> None:
        invoice_formats = {
            InvoiceStatus.ENABLED: (
                '{prefix}{invoice_number:0>5}/{issued_date}/{order_id}/{country_code}',
            ),
            InvoiceStatus.CORRECTING: (
                '{prefix}{invoice_number:0>4}/{issued_date}/{order_id}/{country_code}',
            ),
            InvoiceStatus.PROFORMA: (
                '{prefix}{invoice_number:0>4}/{issued_date}/{order_id}/{country_code}',
            ),
        }

        for invoice_type, pretty_id_template in invoice_formats.items():
            InvoiceSequence(
                country=country,
                numeration_type=numeration_type,
                pretty_id_template=pretty_id_template,
                invoice_type=invoice_type,
            ).save()
