import typing

from datetime import <PERSON><PERSON><PERSON>
from decimal import Decimal

from django.db import models
from django.utils import timezone

from invoice.choices import (
    InvoiceSource,
    InvoiceStatus,
)
from invoice.enums import InvoiceItemTag

if typing.TYPE_CHECKING:
    from invoice.models import Invoice


class ProformaCstmInvoiceManager(models.Manager):
    def get_queryset(self):
        return (
            super()
            .get_queryset()
            .filter(
                status__in=[InvoiceStatus.PROFORMA, InvoiceStatus.CORRECTING_PROFORMA],
                source=InvoiceSource.CSTM,
            )
            .order_by('-sell_at')
        )


class ProformaLogisticInvoiceManager(models.Manager):
    def get_queryset(self):
        return (
            super()
            .get_queryset()
            .filter(
                status__in=[InvoiceStatus.PROFORMA, InvoiceStatus.CORRECTING_PROFORMA],
                source__in=[
                    InvoiceSource.LOGISTIC,
                    InvoiceSource.LOGISTIC_FREE_MATERIAL_SAMPLE_OUTSIDE_EU,
                ],
            )
            .order_by('-sell_at')
        )


class ProformaLogisticFreeMaterialSampleOutsideEUInvoiceManager(models.Manager):
    def get_queryset(self):
        return (
            super()
            .get_queryset()
            .filter(
                status=InvoiceStatus.PROFORMA,
                source=InvoiceSource.LOGISTIC_FREE_MATERIAL_SAMPLE_OUTSIDE_EU,
            )
            .order_by('-sell_at')
        )


class ProformaRetoolServiceInvoiceManager(models.Manager):
    def get_queryset(self):
        return (
            super()
            .get_queryset()
            .filter(
                status__in=[InvoiceStatus.PROFORMA, InvoiceStatus.CORRECTING_PROFORMA],
                source=InvoiceSource.RETOOL,
            )
            .order_by('-sell_at')
        )


class ProformaCustomerServiceInvoiceManager(models.Manager):
    def get_queryset(self):
        return (
            super()
            .get_queryset()
            .filter(
                status__in=[InvoiceStatus.PROFORMA, InvoiceStatus.CORRECTING_PROFORMA],
                source=InvoiceSource.CUSTOMER_SERVICE,
            )
            .order_by('-sell_at')
        )


class InvoiceDomesticAllManager(models.Manager):
    def get_queryset(self):
        return models.Manager.get_queryset(self)


class InvoiceDomesticManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset().filter(is_domestic=True)

    def create_domestic_from_normal_or_proforma(self, source_invoice: 'Invoice'):
        domestic_normal = self.model.objects.create(
            order=source_invoice.order,
            source=source_invoice.source,
            status=source_invoice.status,
            pretty_id=f'{source_invoice.pretty_id}/GB',
            sell_at=source_invoice.sell_at,
            show_both_address=source_invoice.show_both_address,
            additional_address_1=source_invoice.additional_address_1,
            additional_address_2=source_invoice.additional_address_2,
            corrected_notes=source_invoice.corrected_notes,
            corrected_sell_at=source_invoice.corrected_sell_at,
            issued_at=source_invoice.issued_at,
            currency_symbol=source_invoice.currency_symbol,
            is_domestic=True,
        )
        return domestic_normal

    def create_domestic_from_correction(self, source_correction_invoice: 'Invoice'):
        corrected_domestic_invoice = self.model.objects.get(
            pretty_id=f'{source_correction_invoice.corrected_invoice.pretty_id}/GB'
        )
        if source_correction_invoice.previous_correction:
            previous_correction_domestic_invoice = self.model.objects.get(
                pretty_id=f'{source_correction_invoice.previous_correction.pretty_id}/GB'
            )
        else:
            previous_correction_domestic_invoice = None

        domestic_correction = self.model.objects.create(
            order=source_correction_invoice.order,
            status=source_correction_invoice.status,
            show_both_address=source_correction_invoice.show_both_address,
            additional_address_1=source_correction_invoice.additional_address_1,
            additional_address_2=source_correction_invoice.additional_address_2,
            corrected_notes=source_correction_invoice.corrected_notes,
            corrected_invoice=corrected_domestic_invoice,
            previous_correction=previous_correction_domestic_invoice,
            pretty_id=f'{source_correction_invoice.pretty_id}/GB',
            sell_at=source_correction_invoice.sell_at,
            corrected_sell_at=source_correction_invoice.corrected_sell_at,
            issued_at=source_correction_invoice.issued_at,
            currency_symbol=source_correction_invoice.currency_symbol,
            is_domestic=True,
        )
        for correction_change in source_correction_invoice.correction_changes.all():
            correction_change.pk = None
            correction_change.correcting_invoice_id = domestic_correction.id
            correction_change.save()

        vat_rate = domestic_correction.get_vat_rate()
        vat_status = domestic_correction.get_vat_type()

        previous_domestic_invoice = (
            previous_correction_domestic_invoice or corrected_domestic_invoice
        )

        for invoice_item in source_correction_invoice.invoice_items.all():
            price_net_number = invoice_item.net_price / (1 + vat_rate)
            region_promo_value_net = invoice_item.discount_value / (1 + vat_rate)
            if invoice_item.quantity == 0:
                region_net = Decimal('0.0')
            else:
                region_net = price_net_number - region_promo_value_net

            region_gross = invoice_item.gross_price
            vat_amount = region_gross - region_net

            invoice_item.pk = None
            invoice_item.invoice_id = domestic_correction.id

            if invoice_item.corrected_invoice_item:
                corrected_invoice_item_id = previous_domestic_invoice.invoice_items.get(
                    order_item=invoice_item.corrected_invoice_item.order_item,
                    item_type=invoice_item.item_type,
                ).id
            else:
                corrected_invoice_item_id = None

            invoice_item.corrected_invoice_item_id = corrected_invoice_item_id
            invoice_item.vat_rate = vat_rate
            invoice_item.vat_status = vat_status
            invoice_item.net_price = price_net_number
            invoice_item.discount_value = region_promo_value_net
            invoice_item.vat_amount = vat_amount
            invoice_item.net_value = region_net
            invoice_item.save()

        domestic_correction.create_pdf()
        return domestic_correction


class InvoicePreviewAllManager(models.Manager):
    def get_queryset(self):
        return models.Manager.get_queryset(self)


class InvoicePreviewManager(models.Manager):
    def get_queryset(self):
        return (
            super()
            .get_queryset()
            .filter(
                status__in=[
                    InvoiceStatus.CORRECTING_PREVIEW,
                    InvoiceStatus.CORRECTING_DRAFT,
                ]
            )
        )


class InvoiceAllManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset().exclude(status=InvoiceStatus.CORRECTING_PREVIEW)


class InvoiceManager(models.Manager):
    def get_queryset(self):
        return (
            super()
            .get_queryset()
            .exclude(status=InvoiceStatus.CORRECTING_PREVIEW)
            .exclude(status=InvoiceStatus.CORRECTING_PROFORMA)
            .exclude(is_domestic=True)
        )

    def create_proforma_neutralization(self, proforma_invoice):
        correction_invoice = self.model(
            issued_at=timezone.now(),
            corrected_invoice=proforma_invoice,
            pretty_id=proforma_invoice._generate_pretty_id(),
            sell_at=proforma_invoice.sell_at,
            currency_symbol=proforma_invoice.currency_symbol,
            status=InvoiceStatus.CORRECTING_PROFORMA,
            order=proforma_invoice.order,
            corrected_notes=InvoiceItemTag.invoice_tag_to_invoice_reason_mapper(
                InvoiceItemTag.CANCELLATION.value
            ),
        )

        correction_invoice.save(
            ignore_create_items=True,
            proforma_invoice_pk=proforma_invoice.pk,
        )

        for invoice_item in proforma_invoice.invoice_items.all():
            old_pk = invoice_item.pk
            invoice_item.pk = None
            invoice_item.invoice_id = correction_invoice.id
            invoice_item.corrected_invoice_item_id = old_pk
            invoice_item.quantity = 0
            invoice_item.net_price = Decimal('0.0')
            invoice_item.gross_price = Decimal('0.0')
            invoice_item.vat_amount = Decimal('0.0')
            invoice_item.net_value = Decimal('0.0')
            invoice_item.discount_value = Decimal('0.0')
            invoice_item.recycle_tax_value = Decimal('0.0')

            invoice_item.net_weight = Decimal('0.0')
            invoice_item.gross_weight = Decimal('0.0')
            invoice_item.tag = InvoiceItemTag.CANCELLATION.value

            invoice_item.save()

        correction_invoice.create_pdf()
        return correction_invoice

    def create_normal_from_proforma(
        self, proforma_invoice, issued_at=None, sell_at=None
    ):
        invoice_items = list(proforma_invoice.invoice_items.all())
        proforma_invoice_pk = proforma_invoice.pk
        proforma_invoice.pk = None

        proforma_invoice.issued_at = issued_at or timezone.now()
        proforma_invoice.status = InvoiceStatus.ENABLED
        proforma_invoice.pretty_id = proforma_invoice._generate_pretty_id()
        proforma_invoice.cached_to_dict = ''
        proforma_invoice.cached_delivery_address_country = ''
        if sell_at:
            proforma_invoice.sell_at = sell_at

        proforma_invoice.save(
            ignore_create_items=True,
            proforma_invoice_pk=proforma_invoice_pk,
        )
        for invoice_item in invoice_items:
            invoice_item.pk = None
            invoice_item.invoice_id = proforma_invoice.id
            invoice_item.save()

        proforma_invoice.create_pdf()

        return proforma_invoice

    def generate_issued_at(self, invoice):
        now = timezone.now()
        if now.month == invoice.issued_at.month and now.year == invoice.issued_at.year:
            return now
        last_invoice_in_month = (
            self.filter(
                issued_at__year=invoice.issued_at.year,
                issued_at__month=invoice.issued_at.month,
            )
            .order_by('issued_at')
            .last()
        )
        if not last_invoice_in_month:
            return invoice.issued_at
        return last_invoice_in_month.issued_at + timedelta(microseconds=1)
