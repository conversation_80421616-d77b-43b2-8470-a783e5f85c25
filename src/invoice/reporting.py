from decimal import (
    ROUND_HALF_UP,
    Decimal,
    localcontext,
)
from typing import TYPE_CHECKING

from past.utils import old_div

from .choices import InvoiceStatus

if TYPE_CHECKING:
    from invoice.models import Invoice


REPORTING_HEADER = [
    'Id zamowienia',
    'Data fv',
    'Data sprzedazy',
    'Nr fv',
    'Nr paragonu',
    'Nazwa odbiorcy',
    'Nazwa firmy',
    'Adres odbiorcy',
    '<PERSON><PERSON>',
    'NIP',
    '<PERSON><PERSON> Dostawy',
    '<PERSON><PERSON>',
    'Wyslane do klienta',
    'Dostarczone do klienta',
    'Produkt',
    'Ilosc',
    'Cena jednostkowa przed rabatem EUR',
    'RABAT EUR',
    'RABAT %',
    'Wartosc netto po rabacie',
    'Stawka vat',
    'Wartosc vat EUR',
    'Cena brutto przed rabatem EUR',
    'Cena brutto po rabacie EUR',
    'Wartość brutto po rabacie EUR',
    '<PERSON><PERSON><PERSON>ć netto po rabacie EUR',
    'Data kursu eur:pln',
    'Wartosc kursu eur:pln',
    'Cena jednostkowa przed rabatem PLN',
    'RABAT PLN',
    'RABAT %',
    'Wartosc netto po rabacie PLN',
    'Stawka vat',
    'Wartosc vat PLN',
    'Cena brutto przed rabatem PLN',
    'Cena brutto po rabacie PLN',
    'Wartość brutto po rabacie PLN',
    'Wartość netto po rabacie PLN',
    'Metoda platnosci',
    'Data otrzymania platnosci',
    'Kanal sprzedazy',
    'Rodzaj faktury',
    'Data dostarczenia do klienta',
    'Korygowana faktura',
    'Numer paragonu',
    'Kod pocztowy',
    'Waga',
]


def get_reporting_dict(invoice: 'Invoice') -> dict:
    invoice_dict = invoice.to_dict()
    exchange_rate = invoice_dict['exchange_rate']
    euro_exchange_rate = None
    reversed_exchange_rate = Decimal(1)
    if (
        'PLN' in invoice_dict['currency_symbol']
    ):  # only for pln right now, westwing case
        with localcontext() as ctx:
            ctx.prec = 4
            reversed_exchange_rate = old_div(Decimal(1), exchange_rate)
            euro_exchange_rate = exchange_rate
            exchange_rate = Decimal(1)

    result = {
        'Id zamowienia': invoice_dict['order'],
        'Data fv': invoice_dict['issue_date'].strftime('%d/%m/%Y')
        if invoice_dict['issue_date'] is not None
        else '-',
        'Data sprzedazy': invoice_dict['sell_date'].strftime('%d/%m/%Y')
        if invoice_dict['sell_date'] is not None
        else '-',
        'Nr fv': invoice_dict['pretty_id'],
        'Nr paragonu': invoice_dict['receipt_id'],
        'Nazwa odbiorcy': '{} {}'.format(
            invoice_dict['delivery_address']['first_name'],
            invoice_dict['delivery_address']['last_name'],
        ),
        'Nazwa firmy': '{} {}'.format(
            invoice_dict['delivery_address']['company_name'],
            invoice_dict['invoice_address']['company_name'],
        )
        if invoice_dict['delivery_address']['company_name']
        or invoice_dict['invoice_address']['company_name']
        or invoice_dict['delivery_address']['vat']
        or invoice_dict['invoice_address']['vat']
        else 'Os. Fizyczna',
        'Adres odbiorcy': '{} {} {} {}'.format(
            invoice_dict['invoice_address']['street_address_1'],
            invoice_dict['invoice_address']['street_address_2'],
            invoice_dict['invoice_address']['postal_code'],
            invoice_dict['invoice_address']['city'],
        ),
        'Kraj': invoice_dict['invoice_address']['country'],
        'NIP': invoice_dict['invoice_address']['vat']
        if invoice_dict['invoice_address']['vat']
        else invoice_dict['delivery_address']['vat'],
        'Adres Dostawy': '{} {} {} {}'.format(
            invoice_dict['delivery_address']['street_address_1'],
            invoice_dict['delivery_address']['street_address_2'],
            invoice_dict['delivery_address']['postal_code'],
            invoice_dict['delivery_address']['city'],
        ),
        'Wyslane do klienta': '\n'.join(
            invoice.get_sent_to_customer_from_logistic_order(html=False)
        ),
        'Dostarczone do klienta': '\n'.join(
            invoice.get_delivery_date_from_logistic_order(html=False)
        ),
        'Kraj Dostawy': invoice_dict['delivery_address']['country'],
        'Produkt': ','.join([x['item_name'] for x in invoice_dict['items']]),
        'Opis': ' + '.join([x['item_name'] for x in invoice_dict['items']]),
        'Ilosc': sum([x['quantity'] for x in invoice_dict['items']]),  # noqa: C419
        'Cena jednostkowa przed rabatem EUR': (
            invoice_dict['net_value'] + invoice_dict['promo_amount']
        )
        * reversed_exchange_rate,
        'RABAT EUR': invoice_dict['promo_amount'] * reversed_exchange_rate,
        'RABAT %': (
            old_div(
                invoice_dict['promo_amount'],
                (invoice_dict['net_value'] + invoice_dict['promo_amount']),
            )
        )
        * 100
        if invoice_dict['promo_amount'] > 0 and invoice_dict['net_value'] > 0
        else 0,
        # rabat %
        'Wartosc netto po rabacie': invoice_dict['net_value'] * reversed_exchange_rate,
        'Wartosc vat EUR': invoice_dict['vat_value'] * reversed_exchange_rate,
        'Cena brutto przed rabatem EUR': invoice_dict['total_value_before_discount']
        * reversed_exchange_rate,
        'Cena brutto po rabacie EUR': invoice_dict['total_value']
        * reversed_exchange_rate,
        'Wartość brutto po rabacie EUR': invoice_dict['total_value']
        * reversed_exchange_rate,
        # wartosc netto po rabacie EUR
        'Wartość netto po rabacie EUR': invoice_dict['net_value']
        * reversed_exchange_rate,
        'Data kursu eur:pln': invoice_dict['exchange_date'].strftime('%d/%m/%Y'),
        'Wartosc kursu eur:pln': invoice_dict['exchange_rate']
        if euro_exchange_rate is None
        else euro_exchange_rate,
        'Cena jednostkowa przed rabatem PLN': invoice_dict['net_value_in_pln']
        + invoice_dict['promo_amount_in_pln'],
        'RABAT PLN': invoice_dict['promo_amount_in_pln'],
        # rabat %
        'Wartosc netto po rabacie PLN': invoice_dict['net_value_in_pln'],
        'Stawka vat': invoice_dict['vat_rate'],
        'Wartosc vat PLN': invoice_dict['vat_in_pln'],
        'Cena brutto przed rabatem PLN': invoice_dict[
            'total_value_before_discount_in_pln'
        ],
        'Cena brutto po rabacie PLN': invoice_dict['total_value_in_pln'],
        'Wartość brutto po rabacie PLN': invoice_dict['total_value_in_pln'],
        'Wartość netto po rabacie PLN': invoice_dict['net_value_in_pln'],
        # wartosc netto
        'Metoda platnosci': invoice_dict['payment_form'],
        'Data otrzymania platnosci': invoice_dict['sell_date'],
        'Kanal sprzedazy': invoice.order.get_order_source_display(),
        'Rodzaj faktury': 'normal'
        if invoice_dict['status'] == InvoiceStatus.ENABLED
        else invoice.get_status_display(),
        'Data dostarczenia do klienta': invoice.get_delivery_date(),
        'Korygowana faktura': '',
        'Numer paragonu': invoice.receipt_id if invoice.receipt_id is not None else '',
        'Kod pocztowy': invoice.order.postal_code,
        'Waga': invoice.get_package_weight_from_logistic_order(),
    }
    for k, v in list(result.items()):
        if isinstance(v, Decimal) and k != 'Wartosc kursu eur:pln':
            result[k] = v.quantize(Decimal('.01'), rounding=ROUND_HALF_UP)
    return result
