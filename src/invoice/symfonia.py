import logging
import re
import typing

from datetime import datetime
from decimal import (
    ROUND_HALF_UP,
    Decimal,
)
from subprocess import Popen  # noqa: S404

from django.core.paginator import Paginator
from django.db.models import QuerySet
from django.http import HttpResponse

from custom.models import Countries
from custom.utils.exports import dump_list_as_csv
from invoice.choices import InvoiceStatus
from invoice.models import SymfoniaFConfiguration

if typing.TYPE_CHECKING:
    from django.contrib.admin import ModelAdmin
    from django.http import HttpRequest

    from invoice.models import Invoice


logger = logging.getLogger('invoice')

UNITED_KINGDOM_DOMESTIC_NUMBER = '844-3-3'
NORMAL_NUMBER = '203-3-1-'
FKS_NUMBER = '201-3-1-'

UNITED_KINGDOM_DOMESTIC_SECOND_NUMBER = '221-7'
DEX_OR_WDT = {'DEX', 'WDT'}

north_ireland = Countries.Country(
    name='north_ireland',
    translated_name='North Ireland',
    code='XI',
    vat=Decimal('0.2'),
    language_code='en',
    locale='en_GB',
    currency='GBP',
)

SYMFONIA_HEADERS = (
    'data_wystawienia',
    'data_sprzedazy',
    'data_platnosci',
    'skrot',
    'typ_dokumentu',
    'numer_ewidencyjny',
    'numer_faktury',
    'tresc_dokumentu',
    'netto_calego_dokumentu',
    'brutto_calego_dokumentu',
    'vat_calego_dokumentu',
    'waluta',
    'kurs',
    'konto_1',
    'kwota_1',
    'konto_2',
    'kwota_2',
    'konto_3',
    'kwota_3',
    'konto_4',
    'kwota_4',
    'konto_5',
    'kwota_5',
    'netto_27',
    'vat_27',
    'netto_26',
    'vat_26',
    'netto_25',
    'vat_25',
    'netto_24',
    'vat_24',
    'netto_23',
    'vat_23',
    'netto_22',
    'vat_22',
    'netto_21',
    'vat_21',
    'netto_20',
    'vat_20',
    'netto_19',
    'vat_19',
    'netto_17',
    'vat_17',
    'netto_16',
    'vat_16',
    'netto_3',
    'vat_3',
    'netto_0',
    'vat_0',
    'dok_powiazany',
    'data_powiazana',
    'rodzaj_transakcji',
    'okres sprawozdawczy',
    'okres rejestru',
    'NIP',
    'Ulica',
    'kod_pocztowy',
    'miasto',
    'kraj',
    'nettopln',
    'vatpln',
)


def symfonia_queryset(invoices: QuerySet['Invoice']) -> QuerySet['Invoice']:
    return (
        invoices.select_related('order', 'order__region', 'order__region__currency')
        .prefetch_related('corrections', 'invoice_items')
        .order_by('issued_at')
    )


def storno_adjustments(unformated_row: dict) -> dict:
    unformated_row['numer_faktury'] += ' - STORNO'
    unformated_row['nettopln'] = unformated_row['nettopln'] * -1
    return unformated_row


def revert_storno_adjustments(
    unformated_row: dict,
    reporting_period: datetime.date,
) -> dict:
    unformated_row['numer_faktury'] += ' - OK'
    unformated_row['okres sprawozdawczy'] = reporting_period.strftime('%Y-%m-%d')
    return unformated_row


def export_to_fka(
    model_admin: 'ModelAdmin',
    invoices: QuerySet['Invoice'],
    request: 'HttpRequest',
    adjustments: typing.Callable | None = None,
) -> HttpResponse:
    configs = (
        SymfoniaFConfiguration.objects.order_by('document_type', '-id')
        .distinct('document_type')
        .values_list(
            'document_type',
            'vat_account',
            'transaction_type',
            'account',
            named=True,
        )
    )
    configs_dict = {config.document_type: config for config in configs}
    doc = []
    invoices = invoices.order_by('id')
    for page in Paginator(invoices, 100):
        for invoice in page.object_list:
            export = generate_date_for_symfonia_export(invoice)
            to_dict = invoice.to_dict()
            vat_rate = to_dict['vat_rate']
            nazwa = export[0]['kodOdKH']
            document_type = export[0]['typ_dk']
            dk = document_type.split('_')
            if document_type.lower() in ['wdt', 'dex']:
                dk = dk[0]
            elif invoice.pretty_id.startswith('FKS'):
                dk = 'F_{}'.format(dk[0])
            else:
                TWOPLACES = Decimal(10) ** -2  # noqa: N806
                vat_rate = Decimal(to_dict['vat_rate']).quantize(TWOPLACES)
                if vat_rate == Decimal(0) or (
                    vat_rate == Decimal('0.23').quantize(TWOPLACES) and dk[0] == 'PL'
                ):
                    dk = 'F_{}'.format(dk[0])
                else:
                    dk = 'F2{}'.format(dk[0])

            dk_fks = False
            net_pln = to_dict['net_value_in_pln']
            total_pln = to_dict['total_value_in_pln']
            if invoice.status == InvoiceStatus.CORRECTING:
                try:
                    attach = model_admin.model.get(
                        order=invoice.order.pk,
                        status=InvoiceStatus.ENABLED,
                    )
                except Exception:
                    attach = invoice.corrected_invoice

                att, att_date = attach.pretty_id, attach.issued_at.strftime('%d.%m.%Y')
                diff = invoice.to_diff_dict()
                netto, brutto, vat = (
                    diff['net_value'],
                    diff['total_value'],
                    diff['total_value'] - diff['net_value'],
                )
                total_pln = (brutto * to_dict['exchange_rate']).quantize(
                    Decimal('.01'), rounding=ROUND_HALF_UP
                )
                vat_in_pln = diff['vat_in_pln']
                net_pln = total_pln - vat_in_pln
                reason = invoice.corrected_notes
                dk_fks = True
                name = 'FKS'
                if dk in DEX_OR_WDT or '2' in dk:
                    name = dk
            else:
                netto, brutto, vat = (
                    to_dict['net_value'],
                    to_dict['total_value'],
                    to_dict['vat_value'],
                )
                reason = ''
                att, att_date = '', ''
            register_date = invoice.issued_at.strftime('%d.%m.%Y')
            fk = configs_dict.get(dk)
            if invoice.is_domestic:
                fk = configs_dict.get('FSGB', fk)
                dk_fks = False
            if not fk:
                model_admin.message_user(
                    request,
                    'No SymfoniaFConfiguration '
                    '(https://tylko.com/admin/invoice/symfoniafconfiguration/) '
                    'for document type {} '
                    'generated by invoice {}'.format(dk, invoice.pretty_id),
                    level=logging.ERROR,
                )
                return
            if not reason:
                opis = 'Order no.{}'.format(invoice.order.pk)
            else:
                opis = reason

            unformated_row = {
                'data_wystawienia': invoice.issued_at.strftime('%d.%m.%Y'),
                'data_sprzedazy': invoice.sell_at.strftime('%d.%m.%Y'),
                'data_platnosci': invoice.sell_at.strftime('%d.%m.%Y'),
                'skrot': nazwa,
                'typ_dokumentu': fk.document_type if not dk_fks else name,
                'numer_faktury': invoice.pretty_id,
                'tresc_dokumentu': opis,
                'netto_calego_dokumentu': netto,
                'brutto_calego_dokumentu': brutto,
                'vat_calego_dokumentu': vat,
                'waluta': export[0]['waluta'],
                'kurs': to_dict['exchange_rate'],
                'konto_1': get_first_account(invoice, dk),
                'kwota_1': vat if invoice.is_domestic else brutto,
                'konto_2': get_second_account(dk, fk, invoice),
                'kwota_2': vat if invoice.is_domestic else netto,
                'konto_3': fk.vat_account,
                'kwota_3': '' if invoice.is_domestic else vat,
                'dok_powiazany': att,
                'data_powiazana': att_date,
                'rodzaj_transakcji': 'sprzedaż',
                'okres sprawozdawczy': invoice.issued_at.strftime('%d.%m.%Y'),
                'okres rejestru': register_date,
                'NIP': (
                    export[1]['nip']
                    if 'nip' in export[1]
                    else ''  # noqa: RUF034
                    if not export[1]['osfiz']
                    else ''
                ),
                'Ulica': export[1]['ulica'],
                'kod_pocztowy': export[1]['kodpocz'],
                'miasto': export[1]['miejscowosc'],
                'kraj': export[0]['khKrajKod'],
                'nettopln': net_pln,
                'vatpln': total_pln - net_pln,
            }
            if adjustments is not None:
                unformated_row = adjustments(unformated_row)

            netto_k, vat_k = 'netto_{0},vat_{0}'.format(int(vat_rate * 100)).split(',')
            unformated_row[netto_k] = netto
            unformated_row[vat_k] = vat
            row = [
                (lambda a: unformated_row[a] if a in unformated_row else '')(x)  # noqa: B023
                for x in SYMFONIA_HEADERS
            ]
            doc.append(row)

    dump_list_as_csv(
        doc,
        output=open('/tmp/symfonia_export.csv_tmp', 'w'),  # noqa: S108
        mail=None,
        headers=SYMFONIA_HEADERS,
        delimiter=';',
    )
    f_name = '/tmp/symfonia_export.csv'  # noqa: S108
    call = Popen('iconv -c -f UTF-8 -t cp1250 {0}_tmp >{0}'.format(f_name), shell=True)  # noqa: S602
    call.wait()
    response = HttpResponse(content_type='text/plain; charset=iso-8859-2')
    filename = f'symfonia_{datetime.today().isoformat()}.csv'
    response['Content-Disposition'] = f'attachment; filename={filename}'

    with open(f_name, 'rb') as fp:
        data = fp.read()
    response.write(data)
    return response


def get_first_account(invoice, dk):
    if invoice.is_domestic:
        return UNITED_KINGDOM_DOMESTIC_NUMBER
    if dk == 'F_PL':
        return FKS_NUMBER
    if invoice.pretty_id.startswith('FKS') and dk not in DEX_OR_WDT:
        return FKS_NUMBER
    return NORMAL_NUMBER


def get_second_account(dk, fk, invoice):
    if invoice.is_domestic and dk == 'DEX':
        return UNITED_KINGDOM_DOMESTIC_SECOND_NUMBER
    return fk.account


def symfonia_document_type_by_country(country_code: str) -> dict:
    return {
        f'G{country_code}F': (
            f'{country_code}_F',
            f'{country_code} Faktura VAT w walucie',
        ),
        f'G{country_code}O': (
            f'{country_code}_O',
            f'{country_code} Faktura VAT w walucie',
        ),
    }


def symfonia_document_types():
    document_types = {
        'f': ('FVS', 'Faktura'),
        'd': ('DEX', 'Dokument eksportowy'),
        'fw': ('FVSW', 'Faktura w walucie'),
        'z': ('ZO', 'Zamówienie obce'),
        'zo': ('ZOW', 'Zamówienie obce w walucie'),
        'wdt': ('WDT', 'Wewnątrzwspólnotowa dostawa towaru'),
        'PL_O': ('PL_O', 'Faktura'),
        'PL_F': ('PL_F', 'Faktura'),
    }
    country_codes = Countries.get_all_country_codes()
    country_codes.append(north_ireland.code)
    for country_code in country_codes:
        country_code = country_code if country_code != 'UK' else 'GB'
        document_types = dict(
            document_types,
            **symfonia_document_type_by_country(country_code),
        )
    return document_types


def get_company_or_private_name_symfonia(invoice_address: dict) -> (dict, str):
    private = '{0} {1}'.format(
        invoice_address['first_name'] or '',
        invoice_address['last_name'] or '',
    ).strip()

    if invoice_address['company_name']:
        company_or_private_name = f"{invoice_address['company_name']} {private}".strip()
    else:
        company_or_private_name = private
    return company_or_private_name, private


def generate_date_for_symfonia_export(invoice: 'Invoice') -> (dict, dict):
    error = False
    document_types = symfonia_document_types()

    payment_method = {'a': 'Adyen', 'p': 'PayPal', 'b': 'Bank Transfer'}

    to_dict = invoice.to_dict()
    symfonia_order = {}
    symfonia_client = {}
    symfonia_order['rejestr_platnosci'] = payment_method['a']

    outside = invoice.is_outside_eu()
    company = (
        to_dict['invoice_address']['company_name'] and to_dict['invoice_address']['vat']
    )

    if str(invoice.order.country.lower()) in Countries.__members__:
        country_code = Countries.__members__[str(invoice.order.country.lower())].code
        country_code = country_code if country_code != 'UK' else 'GB'
    elif invoice.order.country == north_ireland.name:
        country_code = 'XI'
        outside = False
    else:
        error = True
        country_code = ''

    # brexit
    if (
        country_code == 'GB'
        and invoice.order.paid_at
        and invoice.order.paid_at.year < 2021
        and invoice.order.logistic_info
        and (
            (
                invoice.order.logistic_info[-1].sent_to_customer
                and invoice.order.logistic_info[-1].sent_to_customer.year < 2021
            )
            or not invoice.order.logistic_info[-1].sent_to_customer
        )
        and invoice.issued_at.year < 2021
    ):
        outside = False

    symfonia_client['typks'] = 'Zagraniczny'
    to_dict['invoice_address']['vat'] = (
        None
        if not to_dict['invoice_address']['vat']
        or len(to_dict['invoice_address']['vat']) < 5
        else to_dict['invoice_address']['vat']
    )
    if (
        not outside
        and (company or to_dict['invoice_address']['vat'])
        and to_dict['vat_rate'] == 0
    ):
        typ_dk = document_types['wdt']
    elif (
        not outside
        and to_dict['vat_rate'] != 0
        and to_dict['currency_symbol'] not in ('PLN', 'zł')
        and not error
    ):
        if company:
            typ_dk = document_types['G{0}F'.format(country_code)]
        else:
            typ_dk = document_types['G{0}O'.format(country_code)]
    elif outside:
        typ_dk = document_types['d']
    elif to_dict['currency_symbol'] not in ('PLN', 'zł'):
        typ_dk = document_types['wdt']
    else:
        if company:
            typ_dk = document_types['PL_F']
        else:
            typ_dk = document_types['PL_O']
        symfonia_client['typks'] = 'Krajowy'

    symfonia_order['typ_dk'] = typ_dk[0]
    symfonia_order['seria'] = 's{0}'.format(typ_dk[0])
    symfonia_order['nazwa'] = typ_dk[1]
    symfonia_order['opis'] = '{0}'.format(invoice.order.id)
    symfonia_order['data'] = invoice.issued_at.date().isoformat()
    symfonia_order['datasp'] = invoice.sell_at.date().isoformat()

    invoice_address = to_dict['invoice_address']

    company_or_private_name, private = get_company_or_private_name_symfonia(
        invoice_address
    )

    country_code = country_code if country_code != 'UK' else 'GB'
    symfonia_order['khKrajKod'] = country_code
    symfonia_order['kodOdKH'] = company_or_private_name
    symfonia_order['waluta'] = invoice.get_currency_code()

    if symfonia_order['waluta'] != 'PLN':
        symfonia_order['kurs'] = to_dict['exchange_rate']
        symfonia_order['kursDoch'] = to_dict['exchange_rate']

    symfonia_order['rozlmg'] = 0
    symfonia_order['kodMag'] = 'MAG'
    symfonia_order['kod'] = invoice.pretty_id
    symfonia_order['grupacen'] = 2

    if typ_dk[0] in ['DEX']:  # if jak dex zeby dobrze sie wystawialo
        symfonia_order['grupacen'] = 3  # 3 zaplacone
    elif typ_dk[0] in ['PL_O', 'PL_F']:
        symfonia_order['grupacen'] = 2
    elif typ_dk[0][-1] == 'F':
        symfonia_order['grupacen'] = 2
    else:
        symfonia_order['grupacen'] = 2  # 0 zaplacone

    symfonia_order['od_brutto'] = 1
    symfonia_order['flag'] = 512
    symfonia_order['rodzaj_dok'] = 'sprzedaży'

    symfonia_client['kod'] = company_or_private_name
    symfonia_client['nazwa'] = company_or_private_name
    symfonia_client['miejscowosc'] = to_dict['invoice_address']['city']
    symfonia_client['ulica'] = '{0} {1}'.format(
        to_dict['invoice_address']['street_address_1'],
        to_dict['invoice_address']['street_address_2']
        if to_dict['invoice_address']['street_address_2']
        else '',
    )
    symfonia_client['kodpocz'] = to_dict['invoice_address']['postal_code']
    symfonia_client['osfiz'] = 0 if company else 1

    if symfonia_client['osfiz'] == 1 or private:
        symfonia_client['nazwisko'] = to_dict['invoice_address']['last_name']
        symfonia_client['imie'] = to_dict['invoice_address']['first_name']

    if to_dict['invoice_address']['vat']:
        symfonia_client['nip'] = (
            to_dict['invoice_address']['vat'].replace(' ', '').replace('.', '')
        )
        if not re.match(r'(^[A-Z]+)', symfonia_client['nip']):
            if typ_dk == document_types['wdt']:
                symfonia_client['nip'] = '{}{}'.format(
                    country_code, symfonia_client['nip']
                )
                symfonia_client['typ_rejestru_vat'] = 1
                symfonia_client['statusUE'] = 1

    return symfonia_order, symfonia_client
