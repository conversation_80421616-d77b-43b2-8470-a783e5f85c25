{% load invoice_tags %}
<tr {% if invoice_object.order.country == 'france' and invoice_item.correction_recycle_tax_value %}class="no-border-bottom"{% endif %}>
    <td>
        {{ invoice_item.item_name }}
        {% if not is_complaint_proforma %}
            <br/>{{ invoice_item.item_material }}
        {% endif %}
        <br/>{{ invoice_item.item_dimensions }}
        {% if invoice_item.assembly_description %}
            <br/>{{ invoice_item.assembly_description }}
        {% endif %}
        {% if invoice_item.delivery_description %}
            <br/>{{ invoice_item.delivery_description }}
        {% endif %}
        {% if invoice_item.service_description %}
            <br/>{{ invoice_item.service_description }}
        {% endif %}
    </td>
    {% if invoice.delivery_address.country == 'united_kingdom' %}
        <td class="nowrap">
            <em>{{ invoice_item.hts_code }}</em>
        </td>
    {% endif %}
    <td class="nowrap">
        <em>{{ invoice_item.correction_quantity }}</em>
    </td>
    <td class="nowrap">
        <em>{{ invoice_item.correction_net_price }}{{ invoice.currency_symbol }}</em>
    </td>
    <td class="nowrap">
        <em>{{ invoice_item.correction_discount_value }}{{ invoice.currency_symbol }}</em>
    </td>
    <td class="nowrap">
        <em>{{ invoice_item.correction_net_value }}{{ invoice.currency_symbol }}</em>
    </td>
    <td class="nowrap">
        {% if invoice_item.corrected_invoice_item.vat_rate %}
            <em>{% as_percentage invoice_item.corrected_invoice_item.vat_rate %}%</em>
        {% else %}
            <em>{% as_percentage invoice_item.vat_rate %}%</em>
        {% endif %}
    </td>
    <td class="nowrap">
        <em>{{ invoice_item.correction_vat_amount }}{{ invoice.currency_symbol }}</em>
    </td>
    {% if invoice.delivery_address.outside_eu %}
        <td>{{ invoice_item.net_weight }} kg</td>
        <td>{{ invoice_item.gross_weight }} kg</td>
    {% endif %}
    <td class="nowrap">
        <em>{{ invoice_item.correction_gross_price }}{{ invoice.currency_symbol }}</em>
    </td>
</tr>

{% if invoice_object.order.country == 'france' and invoice_item.correction_recycle_tax_value %}
    <tr>
        <td>Eco-fee included</td>
        <td class="nowrap"></td>
        <td class="nowrap"></td>
        <td class="nowrap"></td>
        <td class="nowrap"></td>
        <td class="nowrap"></td>
        <td class="nowrap"></td>
        {% if invoice.delivery_address.outside_eu %}
            <td></td>
            <td></td>
        {% endif %}
        <td class="nowrap">
            <em>{{ invoice_item.correction_recycle_tax_value }}{{ invoice.currency_symbol }}</em>
        </td>
    </tr>
{% endif %}
