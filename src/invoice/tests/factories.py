from decimal import Decimal

from django.utils import timezone

import factory

from factory import fuzzy

from invoice.choices import InvoiceItemType


class InvoiceFactory(factory.django.DjangoModelFactory):
    order = factory.SubFactory('orders.tests.factories.OrderFactory')
    pretty_id = ''
    sell_at = timezone.now()
    issued_at = timezone.now()
    exchange_date = timezone.now().date()
    exchange_rate = Decimal('4.20')
    pdf = factory.django.FileField(filename='the_file.pdf', data=b'abc')

    class Meta:
        model = 'invoice.Invoice'


class InvoiceSequenceFactory(factory.django.DjangoModelFactory):
    pretty_id_template = (
        '{prefix}{invoice_number:0>5}/{issued_date}/{order_id}/{country_code}'
    )
    country = factory.SubFactory('regions.tests.factories.CountryFactory')
    numeration_type = 0
    invoice_type = 0

    class Meta:
        model = 'invoice.InvoiceSequence'


class InvoiceItemFactory(factory.django.DjangoModelFactory):
    invoice = factory.SubFactory('invoice.tests.factories.InvoiceFactory')
    order_item = factory.SubFactory('orders.tests.factories.OrderItemFactory')
    item_type = InvoiceItemType.ITEM
    item_name = fuzzy.FuzzyText()
    quantity = fuzzy.FuzzyInteger(low=1)
    net_price = fuzzy.FuzzyDecimal(low=0.0)
    net_value = fuzzy.FuzzyDecimal(low=0.0)
    vat_amount = fuzzy.FuzzyDecimal(low=0.0)
    gross_price = fuzzy.FuzzyDecimal(low=0.0)

    class Meta:
        model = 'invoice.InvoiceItem'
