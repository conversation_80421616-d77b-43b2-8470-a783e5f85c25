import json

from datetime import datetime
from decimal import Decimal

from django.utils import timezone

from invoice.encoder import (
    TypedJSONEncoder,
    decode_typed_strings,
)


class TestTypedJSONEncoder:
    encoder_class = TypedJSONEncoder

    def test_decimal(self):
        test_dict = {'test': Decimal('1.3333')}

        encoded_json = json.dumps(test_dict, cls=self.encoder_class)

        assert 'Decimal_1.3333' in encoded_json

    def test_datetime(self):
        actual_datetime = timezone.now()
        test_dict = {'test': actual_datetime}

        encoded_json = json.dumps(test_dict, cls=self.encoder_class)

        actual_datetime = actual_datetime.isoformat()[:23] + 'Z'
        assert f'Datetime_{actual_datetime}' in encoded_json

    def test_date(self):
        actual_date = timezone.now().date()
        test_dict = {'test': actual_date}

        encoded_json = json.dumps(test_dict, cls=self.encoder_class)

        assert f'Date_{actual_date}' in encoded_json


def test_decode_typed_strings():
    raw_json = (
        '{"decimal": "Decimal_1.2222", "datetime": "Datetime_2021-08-18T13:08:00"}'
    )
    excepted_values = {
        'decimal': Decimal('1.2222'),
        'datetime': datetime(2021, 8, 18, 13, 8),
    }

    converted_json = json.loads(
        raw_json,
        object_hook=decode_typed_strings,
    )

    assert converted_json == excepted_values
