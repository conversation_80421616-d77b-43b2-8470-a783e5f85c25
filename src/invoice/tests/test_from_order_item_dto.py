from decimal import Decimal
from unittest import mock

from django.utils.translation import gettext_lazy as _

import pytest

from custom.enums import (
    SampleBoxVariantEnum,
    ShelfType,
    Sofa01Color,
    Type01Color,
)
from custom.models import Countries
from gallery.enums import FurnitureCategory
from invoice.choices import InvoiceStatus
from invoice.from_order_item_dto import (
    FreeSampleInvoiceItemFromOrderItem,
    InvoiceItemFromOrderItem,
)


@pytest.mark.django_db
class TestInvoiceItemFromOrderItem:
    def test_get_descriptions_should_return_no_dimension_no_pl_suffix_when_wooden_sample_germany(  # noqa: E501
        self,
        order_factory,
        order_item_factory,
        sample_box_factory,
    ):
        order = order_factory(country=Countries.germany.name, items=[])

        wooden_sample_box = sample_box_factory(
            box_variant__shelf_type=ShelfType.TYPE01,
            box_variant__variant_type=SampleBoxVariantEnum.TYPE01_CLASSIC,
        )
        order_item = order_item_factory(order_item=wooden_sample_box)
        invoice_item_dto = InvoiceItemFromOrderItem(
            order_item=order_item,
            order=order,
            invoice=mock.Mock(),
            ignore_promo_on_invoice=True,
        )

        dimension, material, name = invoice_item_dto.get_descriptions()

        assert dimension == ''
        assert material == _('ola_comparison_sample_set1_name')
        assert name == _('Sample Set')

    def test_get_descriptions_should_return_no_dimension_with_pl_suffix_when_wooden_sample_united_kingdom(  # noqa: E501
        self,
        order_factory,
        order_item_factory,
        sample_box_factory,
    ):
        order = order_factory(country=Countries.united_kingdom.name, items=[])

        wooden_sample_box = sample_box_factory(
            box_variant__shelf_type=ShelfType.TYPE01,
            box_variant__variant_type=SampleBoxVariantEnum.TYPE01_CLASSIC,
        )
        order_item = order_item_factory(order_item=wooden_sample_box)
        invoice_item_dto = InvoiceItemFromOrderItem(
            order_item=order_item,
            order=order,
            invoice=mock.Mock(),
            ignore_promo_on_invoice=True,
        )

        dimension, material, name = invoice_item_dto.get_descriptions()

        assert dimension == ''
        assert material == _('ola_comparison_sample_set1_name')
        assert name == f"{_('Sample Set')} / Próbki materiałowe"

    def test_get_descriptions_should_return_no_dimension_no_pl_suffix_when_material_sample_germany(  # noqa: E501
        self,
        order_factory,
        order_item_factory,
        sample_box_factory,
    ):
        order = order_factory(country=Countries.germany.name, items=[])

        wooden_sample_box = sample_box_factory(
            box_variant__shelf_type=ShelfType.SOFA_TYPE01,
            box_variant__variant_type=200,
        )
        order_item = order_item_factory(order_item=wooden_sample_box)
        invoice_item_dto = InvoiceItemFromOrderItem(
            order_item=order_item,
            order=order,
            invoice=mock.Mock(),
            ignore_promo_on_invoice=True,
        )

        dimension, material, name = invoice_item_dto.get_descriptions()

        assert dimension == ''
        assert material == _('sample_set_200_name')
        assert name == _('Sample Materials')

    def test_get_descriptions_should_return_no_dimension_with_pl_suffix_when_material_sample_united_kingdom(  # noqa: E501
        self,
        order_factory,
        order_item_factory,
        sample_box_factory,
    ):
        order = order_factory(country=Countries.united_kingdom.name, items=[])

        wooden_sample_box = sample_box_factory(
            box_variant__shelf_type=ShelfType.SOFA_TYPE01,
            box_variant__variant_type=200,
        )
        order_item = order_item_factory(order_item=wooden_sample_box)
        invoice_item_dto = InvoiceItemFromOrderItem(
            order_item=order_item,
            order=order,
            invoice=mock.Mock(),
            ignore_promo_on_invoice=True,
        )

        dimension, material, name = invoice_item_dto.get_descriptions()

        assert dimension == ''
        assert material == _('sample_set_200_name')
        assert name == f"{_('Sample Materials')} / Próbki materiałowe"

    def test_get_descriptions_should_return_without_pl_suffix_when_watty_germany(
        self,
        watty_factory,
        order_item_factory,
        order_factory,
    ):
        order = order_factory(country=Countries.germany.name, items=[])
        watty_item = watty_factory(
            shelf_type=ShelfType.TYPE03, material=0, height=300, width=200, depth=320
        )
        order_item = order_item_factory(order_item=watty_item)
        invoice_item_dto = InvoiceItemFromOrderItem(
            order_item=order_item,
            order=order,
            invoice=mock.Mock(),
            ignore_promo_on_invoice=True,
        )

        dimension, material, name = invoice_item_dto.get_descriptions()

        assert dimension == 'H30cm, W20cm, D32cm'
        assert material == _('White')
        assert name == f"{_('Wardrobe')}"

    def test_get_descriptions_should_return_with_pl_suffix_when_watty_united_kingdom(
        self,
        watty_factory,
        order_item_factory,
        order_factory,
    ):
        order = order_factory(country=Countries.united_kingdom.name, items=[])
        watty_item = watty_factory(
            shelf_type=ShelfType.TYPE03, material=0, height=300, width=200, depth=320
        )
        order_item = order_item_factory(order_item=watty_item)
        invoice_item_dto = InvoiceItemFromOrderItem(
            order_item=order_item,
            order=order,
            invoice=mock.Mock(),
            ignore_promo_on_invoice=True,
        )

        dimension, material, name = invoice_item_dto.get_descriptions()

        assert dimension == 'H30cm, W20cm, D32cm'
        assert material == _('White')
        assert name == f"{_('Wardrobe')} / Szafa Tylko"

    @mock.patch(
        'gallery.models.models.determine_sofa_category',
        return_value=FurnitureCategory.COVER,
    )
    def test_get_descriptions_should_return_without_pl_suffix_when_sotty_germany(
        self,
        mocked_determine_sofa_category,
        sotty_factory,
        order_item_factory,
        order_factory,
    ):
        order = order_factory(country=Countries.germany.name, items=[])
        sotty_item = sotty_factory(
            shelf_category=FurnitureCategory.COVER,
            height=350,
            width=250,
            depth=320,
        )
        order_item = order_item_factory(order_item=sotty_item)
        invoice_item_dto = InvoiceItemFromOrderItem(
            order_item=order_item,
            order=order,
            invoice=mock.Mock(),
            ignore_promo_on_invoice=True,
        )

        dimension, material, name = invoice_item_dto.get_descriptions()  # noqa: RUF059

        assert dimension == 'H35cm, W25cm, D32cm'
        assert name == f"{_('martin_common_cover_singular')}"

    @mock.patch(
        'gallery.models.models.determine_sofa_category',
        return_value=FurnitureCategory.COVER,
    )
    @pytest.mark.parametrize(
        ('material', 'expected_material'),
        [
            (Sofa01Color.CORDUROY_ECRU, 'sztruksowa'),
            (Sofa01Color.REWOOL2_BROWN, 'wełniana'),
        ],
    )
    def test_get_descriptions_should_return_with_pl_suffix_when_sotty_united_kingdom(
        self,
        mocked_determine_sofa_category,
        material,
        expected_material,
        sotty_factory,
        order_item_factory,
        order_factory,
    ):
        order = order_factory(country=Countries.united_kingdom.name, items=[])
        sotty_item = sotty_factory(
            height=350,
            width=250,
            depth=320,
            materials=[material],
        )
        order_item = order_item_factory(order_item=sotty_item)
        invoice_item_dto = InvoiceItemFromOrderItem(
            order_item=order_item,
            order=order,
            invoice=mock.Mock(),
            ignore_promo_on_invoice=True,
        )

        dimension, material, name = invoice_item_dto.get_descriptions()

        assert dimension == 'H35cm, W25cm, D32cm'

        cover_name = _('martin_common_cover_singular')
        expected = (
            f'{cover_name} / Modułowy zestaw sof tapicerowanych, '
            f'rama: drewniana, tapicerka: {expected_material}, do użytku domowego'
        )
        assert name == expected

    def test_get_descriptions_should_return_without_pl_suffix_when_jetty_germany(
        self,
        jetty_factory,
        order_item_factory,
        order_factory,
    ):
        order = order_factory(country=Countries.germany.name, items=[])
        jetty_item = jetty_factory(
            shelf_type=ShelfType.TYPE01,
            material=Type01Color.WHITE,
            height=350,
            width=250,
            depth=320,
        )
        order_item = order_item_factory(order_item=jetty_item)
        invoice_item_dto = InvoiceItemFromOrderItem(
            order_item=order_item,
            order=order,
            invoice=mock.Mock(),
            ignore_promo_on_invoice=True,
        )

        dimension, material, name = invoice_item_dto.get_descriptions()  # noqa: RUF059

        assert dimension == 'H35cm, W25cm, D32cm'
        assert name == f"{_('Ivy Shelf')}"

    def test_get_descriptions_should_return_with_pl_suffix_when_jetty_united_kingdom(
        self,
        jetty_factory,
        order_item_factory,
        order_factory,
    ):
        order = order_factory(country=Countries.united_kingdom.name, items=[])
        jetty_item = jetty_factory(
            shelf_type=ShelfType.TYPE01,
            material=Type01Color.WHITE,
            height=350,
            width=250,
            depth=320,
        )
        order_item = order_item_factory(order_item=jetty_item)
        invoice_item_dto = InvoiceItemFromOrderItem(
            order_item=order_item,
            order=order,
            invoice=mock.Mock(),
            ignore_promo_on_invoice=True,
        )

        dimension, material, name = invoice_item_dto.get_descriptions()  # noqa: RUF059

        assert dimension == 'H35cm, W25cm, D32cm'
        assert name == f"{_('Ivy Shelf')} / Regał Tylko"


class TestFreeSampleInvoiceItemFromOrderItem:
    @pytest.mark.parametrize(
        ('country', 'expected_item_gross'),
        [
            (Countries.united_kingdom, Decimal('1.00')),
            (Countries.switzerland, Decimal('1.00')),
            (Countries.norway, Decimal('5.00')),
        ],
    )
    def test_item_gross_should_return_minimal_by_country_when_sample_box_non_eu(
        self,
        country,
        expected_item_gross,
        invoice_factory,
        order_factory,
        order_item_factory,
    ):
        order = order_factory(country=country.name, items=[])

        order_item = order_item_factory(order=order, is_sample_box=True)
        invoice = invoice_factory(
            order=order,
            status=InvoiceStatus.ENABLED,
            pretty_id='00007/03/2024/183875625/1',
        )
        invoice_item_dto = FreeSampleInvoiceItemFromOrderItem(
            order_item=order_item,
            order=order,
            invoice=invoice,
            ignore_promo_on_invoice=True,
        )

        assert invoice_item_dto.item_gross == expected_item_gross
        assert invoice_item_dto.item_gross_promo == Decimal('0.00')
