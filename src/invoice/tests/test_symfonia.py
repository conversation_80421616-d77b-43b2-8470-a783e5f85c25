from decimal import Decimal
from unittest import mock

from django.utils import timezone

import pytest

from custom.models import Countries
from invoice.choices import InvoiceStatus
from invoice.symfonia import (
    generate_date_for_symfonia_export,
    get_company_or_private_name_symfonia,
    get_first_account,
    get_second_account,
    symfonia_document_types,
)


class TestSymfonia:
    def test_get_first_account_should_return_domestic_when_domestic(
        self, invoice_factory
    ):
        invoice = invoice_factory(
            is_domestic=True,
            pretty_id='00001/03/2024/*********/UK/GB',
        )
        first_account = get_first_account(invoice, '')
        assert first_account == '844-3-3'

    def test_get_first_account_should_return_fks_when_f_pl(self, invoice_factory):
        invoice = invoice_factory(
            is_domestic=False,
            pretty_id='00001/03/2024/*********/DE',
        )
        first_account = get_first_account(invoice, 'F_PL')
        assert first_account == '201-3-1-'

    def test_get_first_account_should_return_fks_when_pretty_id_fks_and_not_dex_wdt(
        self, invoice_factory
    ):
        invoice = invoice_factory(
            is_domestic=False,
            pretty_id='FKS/00001/03/2024/*********/DE',
        )
        first_account = get_first_account(invoice, '')
        assert first_account == '201-3-1-'

    @pytest.mark.parametrize('dk', ['DEX', 'WDT'])
    def test_get_first_account_should_return_normal_when_pretty_id_fks_and_dex_wdt(
        self, dk, invoice_factory
    ):
        invoice = invoice_factory(
            is_domestic=False,
            pretty_id='FKS/00001/03/2024/*********/DE',
        )
        first_account = get_first_account(invoice, dk)
        assert first_account == '203-3-1-'

    def test_get_first_account_should_return_normal_when_other(self, invoice_factory):
        invoice = invoice_factory(
            is_domestic=False,
            pretty_id='00001/03/2024/*********/DE',
        )
        first_account = get_first_account(invoice, '')
        assert first_account == '203-3-1-'

    def test_get_second_account_should_return_domestic_when_domestic_and_dex(
        self, invoice_factory
    ):
        invoice = invoice_factory(
            is_domestic=True,
            pretty_id='00001/03/2024/*********/DE',
        )
        fk = mock.Mock(account='123')
        first_account = get_second_account('DEX', fk, invoice)
        assert first_account == '221-7'

    def test_get_second_account_should_return_fk_account_when_domestic_and_not_dex(
        self, invoice_factory
    ):
        invoice = invoice_factory(
            is_domestic=True,
            pretty_id='00001/03/2024/*********/DE',
        )
        fk = mock.Mock(account='123')
        first_account = get_second_account('WDT', fk, invoice)
        assert first_account == '123'

    def test_get_second_account_should_return_fk_account_when_non_domestic_and_dex(
        self, invoice_factory
    ):
        invoice = invoice_factory(
            is_domestic=False,
            pretty_id='00001/03/2024/*********/DE',
        )
        fk = mock.Mock(account='123')
        first_account = get_second_account('DEX', fk, invoice)
        assert first_account == '123'


@pytest.mark.django_db
class TestSymfoniaExporter:
    def test_generate_date_for_symfonia_export_should_return_data_when_germany(
        self, order_factory, invoice_factory
    ):
        jetty_order = order_factory(
            country=Countries.germany.name,
            region_total_price=Decimal('500.0'),
            paid_at=timezone.now(),
            vat_rate=Decimal('0.2'),
        )
        invoice = invoice_factory(
            is_domestic=False,
            pretty_id='00001/03/2024/*********/DE',
            status=InvoiceStatus.ENABLED,
            order=jetty_order,
        )
        order, client = generate_date_for_symfonia_export(invoice)  # noqa: RUF059
        assert order['typ_dk'] == 'DE_O'

    def test_generate_date_for_symfonia_export_should_return_data_when_xi(
        self, order_factory, invoice_factory
    ):
        jetty_order = order_factory(
            country='north_ireland',
            region_total_price=Decimal('500.0'),
            paid_at=timezone.now(),
            vat_rate=Decimal('0.2'),
        )
        invoice = invoice_factory(
            is_domestic=False,
            pretty_id='00001/03/2024/*********/XI',
            status=InvoiceStatus.ENABLED,
            order=jetty_order,
        )
        order, client = generate_date_for_symfonia_export(invoice)  # noqa: RUF059
        assert order['typ_dk'] == 'XI_O'

    def test_symfonia_document_types_should_return_all_types(self):
        document_types = symfonia_document_types()
        expected_document_types = {
            'GATF': ('AT_F', 'AT Faktura VAT w walucie'),
            'GATO': ('AT_O', 'AT Faktura VAT w walucie'),
            'GBEF': ('BE_F', 'BE Faktura VAT w walucie'),
            'GBEO': ('BE_O', 'BE Faktura VAT w walucie'),
            'GBGF': ('BG_F', 'BG Faktura VAT w walucie'),
            'GBGO': ('BG_O', 'BG Faktura VAT w walucie'),
            'GCHF': ('CH_F', 'CH Faktura VAT w walucie'),
            'GCHO': ('CH_O', 'CH Faktura VAT w walucie'),
            'GCZF': ('CZ_F', 'CZ Faktura VAT w walucie'),
            'GCZO': ('CZ_O', 'CZ Faktura VAT w walucie'),
            'GDEF': ('DE_F', 'DE Faktura VAT w walucie'),
            'GDEO': ('DE_O', 'DE Faktura VAT w walucie'),
            'GDKF': ('DK_F', 'DK Faktura VAT w walucie'),
            'GDKO': ('DK_O', 'DK Faktura VAT w walucie'),
            'GEEF': ('EE_F', 'EE Faktura VAT w walucie'),
            'GEEO': ('EE_O', 'EE Faktura VAT w walucie'),
            'GESF': ('ES_F', 'ES Faktura VAT w walucie'),
            'GESO': ('ES_O', 'ES Faktura VAT w walucie'),
            'GFIF': ('FI_F', 'FI Faktura VAT w walucie'),
            'GFIO': ('FI_O', 'FI Faktura VAT w walucie'),
            'GFRF': ('FR_F', 'FR Faktura VAT w walucie'),
            'GFRO': ('FR_O', 'FR Faktura VAT w walucie'),
            'GGBF': ('GB_F', 'GB Faktura VAT w walucie'),
            'GGBO': ('GB_O', 'GB Faktura VAT w walucie'),
            'GGRF': ('GR_F', 'GR Faktura VAT w walucie'),
            'GGRO': ('GR_O', 'GR Faktura VAT w walucie'),
            'GHRF': ('HR_F', 'HR Faktura VAT w walucie'),
            'GHRO': ('HR_O', 'HR Faktura VAT w walucie'),
            'GHUF': ('HU_F', 'HU Faktura VAT w walucie'),
            'GHUO': ('HU_O', 'HU Faktura VAT w walucie'),
            'GIEF': ('IE_F', 'IE Faktura VAT w walucie'),
            'GIEO': ('IE_O', 'IE Faktura VAT w walucie'),
            'GITF': ('IT_F', 'IT Faktura VAT w walucie'),
            'GITO': ('IT_O', 'IT Faktura VAT w walucie'),
            'GLTF': ('LT_F', 'LT Faktura VAT w walucie'),
            'GLTO': ('LT_O', 'LT Faktura VAT w walucie'),
            'GLUF': ('LU_F', 'LU Faktura VAT w walucie'),
            'GLUO': ('LU_O', 'LU Faktura VAT w walucie'),
            'GLVF': ('LV_F', 'LV Faktura VAT w walucie'),
            'GLVO': ('LV_O', 'LV Faktura VAT w walucie'),
            'GNLF': ('NL_F', 'NL Faktura VAT w walucie'),
            'GNLO': ('NL_O', 'NL Faktura VAT w walucie'),
            'GNOF': ('NO_F', 'NO Faktura VAT w walucie'),
            'GNOO': ('NO_O', 'NO Faktura VAT w walucie'),
            'GPLF': ('PL_F', 'PL Faktura VAT w walucie'),
            'GPLO': ('PL_O', 'PL Faktura VAT w walucie'),
            'GPTF': ('PT_F', 'PT Faktura VAT w walucie'),
            'GPTO': ('PT_O', 'PT Faktura VAT w walucie'),
            'GROF': ('RO_F', 'RO Faktura VAT w walucie'),
            'GROO': ('RO_O', 'RO Faktura VAT w walucie'),
            'GSEF': ('SE_F', 'SE Faktura VAT w walucie'),
            'GSEO': ('SE_O', 'SE Faktura VAT w walucie'),
            'GSIF': ('SI_F', 'SI Faktura VAT w walucie'),
            'GSIO': ('SI_O', 'SI Faktura VAT w walucie'),
            'GSKF': ('SK_F', 'SK Faktura VAT w walucie'),
            'GSKO': ('SK_O', 'SK Faktura VAT w walucie'),
            'GXIF': ('XI_F', 'XI Faktura VAT w walucie'),
            'GXIO': ('XI_O', 'XI Faktura VAT w walucie'),
            'PL_F': ('PL_F', 'Faktura'),
            'PL_O': ('PL_O', 'Faktura'),
            'd': ('DEX', 'Dokument eksportowy'),
            'f': ('FVS', 'Faktura'),
            'fw': ('FVSW', 'Faktura w walucie'),
            'wdt': ('WDT', 'Wewnątrzwspólnotowa dostawa towaru'),
            'z': ('ZO', 'Zamówienie obce'),
            'zo': ('ZOW', 'Zamówienie obce w walucie'),
        }
        assert document_types == expected_document_types

    def test_get_company_or_private_name_symfonia_should_return_private_when_no_company(
        self,
    ):
        invoice_address = {
            'first_name': 'First Name',
            'last_name': 'Last Name',
            'company_name': None,
        }
        company_or_private_name, private = get_company_or_private_name_symfonia(
            invoice_address
        )

        assert company_or_private_name == 'First Name Last Name'
        assert private == 'First Name Last Name'

    def test_get_company_or_private_name_symfonia_should_return_sanitized_when_none(
        self,
    ):
        invoice_address = {
            'first_name': None,
            'last_name': None,
            'company_name': None,
        }
        company_or_private_name, private = get_company_or_private_name_symfonia(
            invoice_address
        )

        assert company_or_private_name == ''
        assert private == ''

    def test_get_company_or_private_name_symfonia_should_return_with_company_when_company(  # noqa: E501
        self,
    ):
        invoice_address = {
            'first_name': 'First',
            'last_name': 'Last',
            'company_name': 'Company',
        }
        company_or_private_name, private = get_company_or_private_name_symfonia(
            invoice_address
        )

        assert company_or_private_name == 'Company First Last'
        assert private == 'First Last'

    def test_get_company_or_private_name_symfonia_should_return_sanitized_when_company(
        self,
    ):
        invoice_address = {
            'first_name': None,
            'last_name': None,
            'company_name': 'Company',
        }
        company_or_private_name, private = get_company_or_private_name_symfonia(
            invoice_address
        )

        assert company_or_private_name == 'Company'
        assert private == ''
