import datetime

from decimal import Decimal

import pytest

from invoice.choices import InvoiceStatus
from invoice.enums import InvoiceItemTag
from invoice.utils import (
    calculate_vat_amount,
    first_working_day_before,
    update_invoice_items_and_corrected_notes,
)


class TestUtils:
    @pytest.mark.parametrize(
        ('date', 'expected_date'),
        [
            (datetime.date(2024, 11, 1), datetime.date(2024, 10, 31)),
            (datetime.date(2024, 11, 2), datetime.date(2024, 10, 31)),
            (datetime.date(2024, 11, 3), datetime.date(2024, 10, 31)),
            (datetime.date(2024, 11, 16), datetime.date(2024, 11, 15)),
            (datetime.date(2024, 11, 17), datetime.date(2024, 11, 15)),
            (datetime.date(2024, 11, 11), datetime.date(2024, 11, 8)),
        ],
    )
    def test_first_working_day_before_should_back_when_weekend_or_pl_holidays(
        self, date, expected_date
    ):
        exchange_date = first_working_day_before(date)
        assert exchange_date == expected_date

    def test_calculate_vat_amount_should_calculate_amount_when_given_vat_rate(self):
        vat_amount = calculate_vat_amount(Decimal('100.0'), Decimal('0.2'))
        assert vat_amount == Decimal('16.67')

    def test_update_invoice_items_and_corrected_notes_should_update_items_and_corrected_notes(  # noqa: E501
        self,
        invoice_factory,
        invoice_item_factory,
    ):
        normal_invoice = invoice_factory(
            pretty_id='normal/1/2/DE',
            status=InvoiceStatus.ENABLED,
            order__country='germany',
        )

        invoice = invoice_factory(
            pretty_id='correction/1/2/DE',
            status=InvoiceStatus.CORRECTING,
            order__country='germany',
            corrected_invoice=normal_invoice,
            corrected_notes='Discount applied / Naliczono rabat',
        )

        invoice_item_factory(
            invoice=invoice,
            tag=InvoiceItemTag.DISCOUNT_QUALITY_DISSATISFACTION.value,
        )

        correction_invoice = update_invoice_items_and_corrected_notes(
            invoice,
            tag=InvoiceItemTag.CANCELLATION.value,
        )

        assert (
            correction_invoice.corrected_notes
            == 'Order cancelled / Zamówienie anulowane'
        )
        for invoice_item in correction_invoice.invoice_items.all():
            assert invoice_item.tag == InvoiceItemTag.CANCELLATION.value
