import logging

from datetime import timed<PERSON><PERSON>
from decimal import Decimal
from unittest import mock
from unittest.mock import patch

from django.db.models.query_utils import Q
from django.utils import timezone

import pytest

from custom.constants import (
    VAT_EU,
    VAT_NORMAL,
)
from gallery.enums import FurnitureCategory
from gallery.serializers import JettySerializer
from invoice.choices import (
    InvoiceChangeScope,
    InvoiceStatus,
)
from invoice.models import (
    Invoice,
    InvoiceCorrectionChange,
    InvoiceHistory,
)
from orders.enums import (
    OrderStatus,
    OrderType,
)
from pricing_v3.services.price_calculators import OrderPriceCalculator
from producers.models import Product

logger = logging.getLogger('invoice')


@pytest.mark.nbp
@pytest.mark.django_db
class TestInvoice:
    @staticmethod
    def get_jetty(owner):
        jetty_data = {
            'shelf_category': FurnitureCategory.BOOKCASE,
            'base_preset': 317186,
            'grid_all_colors_webp': None,
            'horizontals': [
                {'y1': 2100, 'x2': 1575, 'x1': -1575, 'y2': 2100},
                {'y1': 1800, 'x2': 1575, 'x1': -1575, 'y2': 1800},
                {'y1': 1400, 'x2': -548, 'x1': -1575, 'y2': 1400},
                {'y1': 1400, 'x2': 1575, 'x1': -152, 'y2': 1400},
                {'y1': 1000, 'x2': 1575, 'x1': -1575, 'y2': 1000},
                {'y1': 800, 'x2': -1056, 'x1': -1575, 'y2': 800},
                {'y1': 800, 'x2': 737, 'x1': -738, 'y2': 800},
                {'y1': 800, 'x2': 1575, 'x1': 1112, 'y2': 800},
                {'y1': 400, 'x2': -317, 'x1': -1575, 'y2': 400},
                {'y1': 400, 'x2': 1575, 'x1': 15, 'y2': 400},
                {'y1': 0, 'x2': 1575, 'x1': -1575, 'y2': 0},
            ],
            'joints': [],
            'updated_at': '2019-08-13T14:30:13.247694',
            'backs': [
                {'y2': 391, 'x2': -1098, 'y1': 9, 'x1': -1557, 'z1': 12, 'z2': 0},
                {'y2': 391, 'x2': -663, 'y1': 9, 'x1': -1080, 'z1': 12, 'z2': 0},
                {'y2': 391, 'x2': -335, 'y1': 9, 'x1': -645, 'z1': 12, 'z2': 0},
                {'y2': 391, 'x2': 503, 'y1': 9, 'x1': 33, 'z1': 12, 'z2': 0},
                {'y2': 391, 'x2': 1286, 'y1': 9, 'x1': 799, 'z1': 12, 'z2': 0},
                {'y2': 991, 'x2': -1074, 'y1': 809, 'x1': -1432, 'z1': 12, 'z2': 0},
                {'y2': 991, 'x2': -405, 'y1': 809, 'x1': -720, 'z1': 12, 'z2': 0},
                {'y2': 991, 'x2': 218, 'y1': 809, 'x1': -387, 'z1': 12, 'z2': 0},
                {'y2': 991, 'x2': 719, 'y1': 809, 'x1': 236, 'z1': 12, 'z2': 0},
                {'y2': 991, 'x2': 1557, 'y1': 809, 'x1': 1130, 'z1': 12, 'z2': 0},
                {'y2': 1791, 'x2': -915, 'y1': 1409, 'x1': -1432, 'z1': 12, 'z2': 0},
                {'y2': 1791, 'x2': 1557, 'y1': 1409, 'x1': 971, 'z1': 12, 'z2': 0},
                {'y2': 2091, 'x2': 373, 'y1': 1809, 'x1': -107, 'z1': 12, 'z2': 0},
            ],
            'verticals': [
                {'y1': 9, 'x2': 24, 'x1': 24, 'y2': 391},
                {'y1': 409, 'x2': 24, 'x1': 24, 'y2': 791},
                {'y1': 9, 'x2': -326, 'x1': -326, 'y2': 391},
                {'y1': 409, 'x2': -326, 'x1': -326, 'y2': 791},
                {'y1': 9, 'x2': -1089, 'x1': -1089, 'y2': 391},
                {'y1': 9, 'x2': 1295, 'x1': 1295, 'y2': 391},
                {'y1': 9, 'x2': 790, 'x1': 790, 'y2': 391},
                {'y1': 9, 'x2': 512, 'x1': 512, 'y2': 391},
                {'y1': 9, 'x2': -654, 'x1': -654, 'y2': 391},
                {'y1': 9, 'x2': -1566, 'x1': -1566, 'y2': 391},
                {'y1': 409, 'x2': -1065, 'x1': -1065, 'y2': 791},
                {'y1': 809, 'x2': -1065, 'x1': -1065, 'y2': 991},
                {'y1': 409, 'x2': -729, 'x1': -729, 'y2': 791},
                {'y1': 809, 'x2': -729, 'x1': -729, 'y2': 991},
                {'y1': 409, 'x2': 728, 'x1': 728, 'y2': 791},
                {'y1': 809, 'x2': 728, 'x1': 728, 'y2': 991},
                {'y1': 409, 'x2': 1121, 'x1': 1121, 'y2': 791},
                {'y1': 809, 'x2': 1121, 'x1': 1121, 'y2': 991},
                {'y1': 409, 'x2': 347, 'x1': 347, 'y2': 791},
                {'y1': 409, 'x2': -1566, 'x1': -1566, 'y2': 791},
                {'y1': 809, 'x2': -1441, 'x1': -1441, 'y2': 991},
                {'y1': 1409, 'x2': -1441, 'x1': -1441, 'y2': 1791},
                {'y1': 809, 'x2': -396, 'x1': -396, 'y2': 991},
                {'y1': 809, 'x2': 227, 'x1': 227, 'y2': 991},
                {'y1': 1009, 'x2': -557, 'x1': -557, 'y2': 1391},
                {'y1': 1409, 'x2': -557, 'x1': -557, 'y2': 1791},
                {'y1': 1009, 'x2': -143, 'x1': -143, 'y2': 1391},
                {'y1': 1409, 'x2': -143, 'x1': -143, 'y2': 1791},
                {'y1': 1009, 'x2': -1141, 'x1': -1141, 'y2': 1391},
                {'y1': 1009, 'x2': 243, 'x1': 243, 'y2': 1391},
                {'y1': 1009, 'x2': 728, 'x1': 728, 'y2': 1391},
                {'y1': 1009, 'x2': -1566, 'x1': -1566, 'y2': 1391},
                {'y1': 1409, 'x2': -906, 'x1': -906, 'y2': 1791},
                {'y1': 1409, 'x2': 296, 'x1': 296, 'y2': 1791},
                {'y1': 1409, 'x2': 962, 'x1': 962, 'y2': 1791},
                {'y1': 1809, 'x2': -758, 'x1': -758, 'y2': 2091},
                {'y1': 1809, 'x2': 382, 'x1': 382, 'y2': 2091},
                {'y1': 1809, 'x2': -1108, 'x1': -1108, 'y2': 2091},
                {'y1': 1809, 'x2': -1566, 'x1': -1566, 'y2': 2091},
                {'y1': 1809, 'x2': 800, 'x1': 800, 'y2': 2091},
                {'y1': 1809, 'x2': -116, 'x1': -116, 'y2': 2091},
                {'y1': 409, 'x2': 1566, 'x1': 1566, 'y2': 791},
                {'y1': 809, 'x2': 1566, 'x1': 1566, 'y2': 991},
                {'y1': 1009, 'x2': 1441, 'x1': 1441, 'y2': 1391},
                {'y1': 1809, 'x2': 1566, 'x1': 1566, 'y2': 2091},
                {'y1': 9, 'x2': 1566, 'x1': 1566, 'y2': 391},
                {'y1': 1409, 'x2': 1566, 'x1': 1566, 'y2': 1791},
            ],
            'created_platform': 0,
            'shelf_type': 0,
            'size_txt': '315.0 cm x 213.0 cm 40.0 cm',
            'price': 3516.0,
            'id': 728928,
            'category': 'wallstorage',
            'property1': 67.0,
            'rows': [400, 400, 200, 400, 400, 300, 300, 300, 300, 300, 300, 300, 300],
            'title': 'Tylko Shelf',
            'pattern': 2,
            'region_price_display': '\u20ac3516',
            'region_price': '3516',
            'grid_all_colors': None,
            'color': 0,
            'doors': [
                {
                    'y2': 391,
                    'flip': 0,
                    'x2': -1098,
                    'y1': 9,
                    'x1': -1557,
                    'type': 0,
                    'z1': 400,
                    'z2': 381,
                },
                {
                    'y2': 391,
                    'flip': 0,
                    'x2': -663,
                    'y1': 9,
                    'x1': -1080,
                    'type': 0,
                    'z1': 400,
                    'z2': 381,
                },
                {
                    'y2': 391,
                    'flip': 0,
                    'x2': -335,
                    'y1': 9,
                    'x1': -645,
                    'type': 0,
                    'z1': 400,
                    'z2': 381,
                },
                {
                    'y2': 391,
                    'flip': 0,
                    'x2': 503,
                    'y1': 9,
                    'x1': 33,
                    'type': 0,
                    'z1': 400,
                    'z2': 381,
                },
                {
                    'y2': 391,
                    'flip': 0,
                    'x2': 1286,
                    'y1': 9,
                    'x1': 799,
                    'type': 0,
                    'z1': 400,
                    'z2': 381,
                },
                {
                    'y2': 1791,
                    'flip': 0,
                    'x2': -915,
                    'y1': 1409,
                    'x1': -1432,
                    'type': 0,
                    'z1': 400,
                    'z2': 381,
                },
                {
                    'y2': 1791,
                    'flip': 1,
                    'x2': 1264,
                    'y1': 1409,
                    'x1': 971,
                    'type': 1,
                    'z1': 400,
                    'z2': 381,
                },
                {
                    'y2': 1791,
                    'flip': 0,
                    'x2': 1557,
                    'y1': 1409,
                    'x1': 1264,
                    'type': 0,
                    'z1': 400,
                    'z2': 381,
                },
                {
                    'y2': 2091,
                    'flip': 0,
                    'x2': 373,
                    'y1': 1809,
                    'x1': -107,
                    'type': 0,
                    'z1': 400,
                    'z2': 381,
                },
            ],
            'additional_images': [],
            'row_styles': [13, 11, 31, 11, 12, 12, 1, 1, 1, 1],
            'supports': [
                {'y2': 791, 'y1': 409, 'x2': -1557, 'x1': -1432, 'z1': 0, 'z2': 0},
                {'y2': 1391, 'y1': 1009, 'x2': -1557, 'x1': -1432, 'z1': 0, 'z2': 0},
                {'y2': 791, 'y1': 409, 'x2': 719, 'x1': 594, 'z1': 0, 'z2': 0},
                {'y2': 991, 'y1': 809, 'x2': 1130, 'x1': 1255, 'z1': 0, 'z2': 0},
                {'y2': 1391, 'y1': 1009, 'x2': -566, 'x1': -691, 'z1': 0, 'z2': 0},
                {'y2': 1791, 'y1': 1409, 'x2': -134, 'x1': -9, 'z1': 0, 'z2': 0},
                {'y2': 1391, 'y1': 1009, 'x2': 1432, 'x1': 1307, 'z1': 0, 'z2': 0},
                {'y2': 1391, 'y1': 1009, 'x2': 252, 'x1': 377, 'z1': 0, 'z2': 0},
                {'y2': 2091, 'y1': 1809, 'x2': -749, 'x1': -624, 'z1': 0, 'z2': 0},
                {'y2': 2091, 'y1': 1809, 'x2': -1557, 'x1': -1432, 'z1': 0, 'z2': 0},
                {'y2': 791, 'y1': 409, 'x2': 1557, 'x1': 1432, 'z1': 0, 'z2': 0},
                {'y2': 991, 'y1': 809, 'x2': -1074, 'x1': -1199, 'z1': 0, 'z2': 0},
                {'y2': 2091, 'y1': 1809, 'x2': 809, 'x1': 934, 'z1': 0, 'z2': 0},
            ],
            'cardboards_number': 5,
            'preset_initial_state': False,
            'description': '',
            'deleted': False,
            'backpanel_styles': [],
            'material': 0,
            'preset': False,
            'dna_name': 'PATTERN_10rows_v2d',
            'max_capacity': 740,
            'grid_preset': None,
            'height': 2130,
            'legs': [
                {'y1': -20, 'x1': -1555, 'z1': 20},
                {'y1': -20, 'x1': -1555, 'z1': 380},
                {'y1': -20, 'x1': -1089, 'z1': 20},
                {'y1': -20, 'x1': -1089, 'z1': 380},
                {'y1': -20, 'x1': -654, 'z1': 20},
                {'y1': -20, 'x1': -654, 'z1': 380},
                {'y1': -20, 'x1': -326, 'z1': 20},
                {'y1': -20, 'x1': -326, 'z1': 380},
                {'y1': -20, 'x1': 24, 'z1': 20},
                {'y1': -20, 'x1': 24, 'z1': 380},
                {'y1': -20, 'x1': 512, 'z1': 20},
                {'y1': -20, 'x1': 512, 'z1': 380},
                {'y1': -20, 'x1': 790, 'z1': 20},
                {'y1': -20, 'x1': 790, 'z1': 380},
                {'y1': -20, 'x1': 1295, 'z1': 20},
                {'y1': -20, 'x1': 1295, 'z1': 380},
                {'y1': -20, 'x1': 1555, 'z1': 20},
                {'y1': -20, 'x1': 1555, 'z1': 380},
            ],
            'row_amount': 6,
            'type': 'jetty',
            'created_at': '2019-08-09T00:04:47.955739',
            'furniture_status': 'ordered',
            'modules': [{'sizex': 3132, 'sizey': 2112, 'posx': 3132, 'posy': 0}],
            'drawers': [
                {
                    'y2': 991,
                    'flip': 0,
                    'x2': -1074,
                    'y1': 809,
                    'x1': -1432,
                    'type': 0,
                    'z1': 400,
                    'z2': 381,
                },
                {
                    'y2': 991,
                    'flip': 0,
                    'x2': -405,
                    'y1': 809,
                    'x1': -720,
                    'type': 0,
                    'z1': 400,
                    'z2': 381,
                },
                {
                    'y2': 991,
                    'flip': 0,
                    'x2': 218,
                    'y1': 809,
                    'x1': -387,
                    'type': 0,
                    'z1': 400,
                    'z2': 381,
                },
                {
                    'y2': 991,
                    'flip': 0,
                    'x2': 719,
                    'y1': 809,
                    'x1': 236,
                    'type': 0,
                    'z1': 400,
                    'z2': 381,
                },
                {
                    'y2': 991,
                    'flip': 0,
                    'x2': 1557,
                    'y1': 809,
                    'x1': 1130,
                    'type': 0,
                    'z1': 400,
                    'z2': 381,
                },
            ],
            'depth': 400,
            'color_name': 'ivy_white',
            'pattern_name': 'Pattern',
            'preview': None,
            'similar': 55870,
            'width': 3150,
            'currency_code': 'EUR',
        }
        jetty = JettySerializer(data=jetty_data)
        jetty.is_valid()
        return jetty.save(owner_id=owner.pk)

    @pytest.fixture(autouse=True)
    def set_up_test_data(
        self,
        user_factory,
        order_factory,
        order_item_factory,
        regions,
    ):
        user = user_factory()
        self.regions = regions
        self.invoices = []
        self.orders = []
        self.cc = []

        with mock.patch('gallery.models.Jetty.get_weight', return_value=100):
            with mock.patch(
                'gallery.models.Jetty.get_accurate_weight_gross', return_value=120
            ):
                for i in range(12):
                    # set up if order is b2b or not
                    vat = 'VAT' if (i + 1) % 3 == 0 else ''

                    for region in self.regions:
                        country = region.countries.last()
                        order_status = OrderStatus.IN_PRODUCTION
                        invoice_status = InvoiceStatus.ENABLED
                        jetty = self.get_jetty(user)
                        order = order_factory(
                            status=order_status,
                            owner=user,
                            email=user.profile.email,
                            country=country.name,
                            region=region,
                            region_vat=country.region_vat,
                            updated_at=timezone.now() - timedelta(days=1),
                            vat=vat,
                            vat_type=VAT_EU if vat else VAT_NORMAL,
                            invoice_vat=vat,
                            company_name=vat,
                            invoice_company_name=vat,
                            paid_at=timezone.now(),
                        )
                        order_item = order_item_factory(
                            order=order,
                            quantity=1,
                            order_item=jetty,
                        )
                        order_item.order_item.save()
                        OrderPriceCalculator(order).calculate()
                        self.orders.append(order)
                        inv = Invoice.objects.create(
                            status=invoice_status,
                            order=order,
                            issued_at=timezone.now(),
                            sell_at=timezone.now(),
                            currency_symbol=order.get_region().currency.symbol,
                        )
                        correction = Invoice.objects.create(
                            status=InvoiceStatus.CORRECTING,
                            order=order,
                            issued_at=timezone.now(),
                            sell_at=timezone.now() - timedelta(hours=1),
                            currency_symbol=order.get_region().currency.symbol,
                            corrected_invoice=inv,
                        )
                        self.invoices.append(inv)
                        self.invoices.append(correction)
                        cc = InvoiceCorrectionChange()
                        cc.correcting_invoice = correction
                        cc.correction_type = InvoiceChangeScope.OTHER
                        cc.name = 'Customer`s Address/ Adres kontrahenta'
                        cc.save()
                        self.cc.append(cc)
                        for item in inv.invoice_items.all():
                            old_pk = item.pk
                            item.pk = None
                            item.invoice_id = correction.id
                            item.corrected_invoice_item_id = old_pk
                            item.save()

    def test_pretty_id_polish(self, subtests):
        # polish invoices normal plus b2b
        prev = (
            int(
                Invoice.objects.filter(status=InvoiceStatus.ENABLED)
                .filter(
                    Q(order__region_vat=False)
                    | Q(Q(order__region_vat=True) & Q(order__vat_type=VAT_EU))
                )
                .order_by('id')
                .first()
                .pretty_id.split('/')[0]
            )
            - 1
        )
        for invoice in (
            Invoice.objects.filter(status=InvoiceStatus.ENABLED)
            .filter(
                Q(order__region_vat=False)
                | Q(Q(order__region_vat=True) & Q(order__vat_type=VAT_EU))
            )
            .order_by('id')
        ):
            pretty_id = invoice.pretty_id.split('/')
            current = int(pretty_id[0])
            with subtests.test():
                assert current == prev + 1
            prev = current

        prev = 0

        for invoice in (
            Invoice.objects.filter(status=InvoiceStatus.CORRECTING)
            .filter(
                Q(order__region_vat=False)
                | Q(Q(order__region_vat=True) & Q(order__vat_type=VAT_EU))
            )
            .order_by('id')
        ):
            pretty_id = invoice.pretty_id.split('/')
            current = int(pretty_id[0].replace('FKS', ''))
            with subtests.test():
                assert current == prev + 1
            prev = current

    def test_pretty_id_region_vat(self, subtests):
        # not polish invoices
        self.regions.reverse()
        for region in self.regions:
            country = region.countries.last()
            prev = 0
            if country.region_vat:
                for invoice in (
                    Invoice.objects.filter(status=InvoiceStatus.ENABLED)
                    .filter(
                        Q(
                            Q(order__region_vat=True)
                            & Q(order__vat_type=VAT_NORMAL)
                            & Q(order__country=country.name)
                        )
                    )
                    .order_by('id')
                ):
                    pretty_id = invoice.pretty_id.split('/')
                    current = int(pretty_id[1])
                    with subtests.test():
                        assert current == prev + 1
                    prev = current
        # corrections
        for region in self.regions:
            country = region.countries.last()
            prev = 0
            if country.region_vat:
                for invoice in (
                    Invoice.objects.filter(status=InvoiceStatus.CORRECTING)
                    .filter(
                        Q(
                            Q(order__region_vat=True)
                            & Q(order__vat_type=VAT_NORMAL)
                            & Q(order__country=country.name)
                        )
                    )
                    .order_by('id')
                ):
                    pretty_id = invoice.pretty_id.split('/')
                    current = int(pretty_id[1].replace('FKS', ''))
                    with subtests.test():
                        assert current == prev + 1
                    prev = current

    def test_order_total_equal_invoice_total(self, subtests):
        for inv in self.invoices:
            with subtests.test():
                assert inv.is_total_the_same()

    def test_invoicing_history_new_invoice(self):
        inv = Invoice.objects.all()[0]
        history = InvoiceHistory.objects.filter(invoice_raw_id=inv.pk).last()
        assert len(history.invoice_history) == 1
        assert len(history.generated_from_order) == 1

    def test_invoicing_history_creating_invoice_history_after_changes(self):
        inv = Invoice.objects.all()[1]
        history = InvoiceHistory.objects.filter(invoice_raw_id=inv.pk).last()
        assert len(history.invoice_history) == 1
        inv.save()  # should create new history
        inv.save()  # should not create history because there is not changes
        history.refresh_from_db()
        assert len(history.invoice_history) == 2
        inv.corrected_notes = 'TEST notes'
        inv.save()  # should create new history
        inv.save()  # should not create history because there is not changes
        inv.save()  # should not create history because there is not changes
        history.refresh_from_db()
        assert len(history.invoice_history) == 3

    def test_invoicing_history_changes_in_invoice(self):
        inv = Invoice.objects.all()[2]
        inv.corrected_notes = 'Test notes'
        inv.save()
        history = InvoiceHistory.objects.filter(invoice_raw_id=inv.pk).last()
        assert len(history.invoice_history) == 2
        assert len(history.generated_from_order) == 1

    def test_invoicing_history_changes_in_order(self):
        inv = Invoice.objects.all()[3]
        order = inv.order
        order.notes = 'Test notes'
        order.save()
        inv.save()
        history = InvoiceHistory.objects.filter(invoice_raw_id=inv.pk).last()
        assert len(history.invoice_history) == 2
        assert len(history.generated_from_order) == 2

    def test_invoicing_validation_country_in_order(self):
        order = self.orders[0]

        with mock.patch('gallery.models.Jetty.get_weight', return_value=100):
            with mock.patch(
                'gallery.models.Jetty.get_accurate_weight_gross', return_value=120
            ):
                inv = Invoice.objects.create(
                    status=InvoiceStatus.ENABLED,
                    order=order,
                    issued_at=timezone.now(),
                    sell_at=timezone.now() - timedelta(hours=1),
                    currency_symbol=order.get_region().currency.symbol,
                )
                order.country = self.regions[-1].countries.last().name

                with pytest.raises(ValueError):  # noqa: PT011
                    Invoice.objects.create(
                        status=InvoiceStatus.CORRECTING,
                        order=order,
                        issued_at=timezone.now(),
                        sell_at=timezone.now() - timedelta(hours=1),
                        currency_symbol=order.get_region().currency.symbol,
                        corrected_invoice=inv,
                    )
        # cleaning
        inv.delete()

    def test_invoicing_validation_changing_currency_in_order(self):
        order = self.orders[0]

        with mock.patch('gallery.models.Jetty.get_weight', return_value=100):
            with mock.patch(
                'gallery.models.Jetty.get_accurate_weight_gross', return_value=120
            ):
                inv = Invoice.objects.create(
                    status=InvoiceStatus.ENABLED,
                    order=order,
                    issued_at=timezone.now(),
                    sell_at=timezone.now() - timedelta(hours=1),
                    currency_symbol='R',
                )

                with pytest.raises(ValueError):  # noqa: PT011
                    Invoice.objects.create(
                        status=InvoiceStatus.CORRECTING,
                        order=order,
                        issued_at=timezone.now(),
                        sell_at=timezone.now() - timedelta(hours=1),
                        currency_symbol='F',
                        corrected_invoice=inv,
                    )
        # cleaning
        inv.delete()

    @patch.object(Product, 'get_weight_brutto', return_value=Decimal('9.99'))
    @patch.object(Product, 'get_weight_netto', return_value=Decimal('15.12'))
    def test_weight_from_product_when_complaint_order(
        self, netto_weight_mock, brutto_weight_mock, product, invoice_factory
    ):
        order = product.order
        order.order_type = OrderType.COMPLAINT
        order.save()
        invoice = invoice_factory(
            status=InvoiceStatus.ENABLED,
            pretty_id='test/DE',
            order=product.order,
        )
        netto_weight_mock.assert_called_once()
        brutto_weight_mock.assert_called_once()
        assert invoice.get_total_net_weight() == netto_weight_mock()
        assert invoice.get_total_gross_weight() == brutto_weight_mock()
