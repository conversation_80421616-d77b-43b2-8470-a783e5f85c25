import datetime
import logging
import typing

from decimal import Decimal

from django.utils import (
    dateparse,
    timezone,
)

from past.utils import old_div

from custom.constants import POLISH_HOLIDAYS

from .choices import (
    InvoiceItemType,
    InvoiceStatus,
)
from .enums import InvoiceItemTag

if typing.TYPE_CHECKING:
    from .models import Invoice

logger = logging.getLogger('invoice')


def create_normal_from_proforma_after_success_klarna_payment(order):
    from .models import Invoice

    try:
        invoice = Invoice.objects.get(order=order, status=InvoiceStatus.PROFORMA)
    except Invoice.DoesNotExist:
        logger.error('Proforma invoice for %s does not exist', order)
    except Invoice.MultipleObjectsReturned:
        logger.error('More than one proforma invoice for %s', order)
    else:
        delivery_date = dateparse.parse_date(order.get_delivery_date())
        sell_at = datetime.datetime(
            delivery_date.year,
            delivery_date.month,
            delivery_date.day,
            hour=12,
            tzinfo=datetime.UTC,
        )

        invoice.issued_at = timezone.now()
        Invoice.objects.create_normal_from_proforma(
            invoice,
            issued_at=Invoice.objects.generate_issued_at(invoice),
            sell_at=sell_at,
        )


def get_corrected_invoice_item_by_type_and_order_item(source_invoice, invoice_item):
    return source_invoice.invoice_items.get(
        order_item=invoice_item.order_item,
        item_type=invoice_item.item_type,
    )


def get_corrected_invoice_item_by_type_and_order_item_and_service(
    source_invoice, invoice_item
):
    invoice_items = source_invoice.invoice_items.filter(
        order_item=invoice_item.order_item,
        item_type=invoice_item.item_type,
    )
    if invoice_item.item_type == InvoiceItemType.SERVICE:
        invoice_items = invoice_items.filter(service_name=invoice_item.service_name)
    return invoice_items.first()


def get_corrected_invoice_item_by_item_type_and_service_type(
    correction_request, invoice_item
):
    invoice_items_by_type = correction_request.deleted_invoice_items.filter(
        item_type=invoice_item.item_type
    )
    if invoice_item.item_type == InvoiceItemType.SERVICE:
        invoice_items_by_type = invoice_items_by_type.filter(
            service_name=invoice_item.service_name
        )
    return invoice_items_by_type.first()


def first_working_day_before(exchange_date: datetime.date) -> datetime.date:
    while exchange_date.weekday() >= 5 or exchange_date in POLISH_HOLIDAYS:
        exchange_date -= datetime.timedelta(days=1)

    return exchange_date


def calculate_vat_amount(brutto: Decimal, vat_rate: Decimal) -> Decimal:
    vat_amount = brutto * (vat_rate / (1 + vat_rate))
    return vat_amount.quantize(Decimal('0.01'))


def update_invoice_items_and_corrected_notes(
    correction_invoice: 'Invoice', tag: int
) -> 'Invoice':
    for invoice_item in correction_invoice.invoice_items.all():
        invoice_item.tag = tag
        invoice_item.save()
    correction_invoice.corrected_notes = (
        InvoiceItemTag.invoice_tag_to_invoice_reason_mapper(int(tag))
    )
    correction_invoice.save(update_fields=['corrected_notes'])
    return correction_invoice


def gross_to_net(gross: Decimal, vat_rate: Decimal) -> Decimal:
    return old_div(gross, (1 + vat_rate))
