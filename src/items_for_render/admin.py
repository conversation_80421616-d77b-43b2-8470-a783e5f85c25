import json

from datetime import datetime
from io import BytesIO
from zipfile import ZipFile

from django.contrib import (
    admin,
    messages,
)
from django.http import FileResponse
from django.utils.html import format_html
from django.utils.safestring import mark_safe

from .models import (
    Item,
    ItemGroup,
    ItemTextureVariant,
    Strategy,
    StrategyGroupRule,
)
from .serializers import ItemSerializer


class StrategyRuleInline(admin.StackedInline):
    model = StrategyGroupRule
    extra = 1
    min_num = 0


class StrategyAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'description')
    inlines = [
        StrategyRuleInline,
    ]


class TextureInline(admin.StackedInline):
    model = ItemTextureVariant
    extra = 0
    min_num = 1


class ItemAdmin(admin.ModelAdmin):
    raw_id_fields = ('owner',)
    list_display = ('id', 'name', 'enabled', 'min_y', 'max_y')
    list_filter = ('enabled',)
    inlines = [
        TextureInline,
    ]
    actions = ['create_group_from_items', 'toggle_enabled', 'export_items']
    fieldsets = (
        (
            None,
            {
                'fields': (
                    'name',
                    'enabled',
                    'owner',
                    'model',
                    'shadow_model',
                    'shadow_texture',
                    'min_y',
                    'max_y',
                    'margin_min_left',
                    'margin_min_right',
                    'margin_min_top',
                    'can_be_on_top_horizontal',
                    'snapping',
                )
            },
        ),
        (
            'not used yet',
            {
                'classes': ('collapse',),
                'fields': (
                    'categories',
                    'scale_x_min',
                    'scale_x_max',
                    'scale_y_min',
                    'scale_y_max',
                    'scale_z_min',
                    'scale_z_max',
                    'default_x',
                    'default_y',
                    'default_z',
                    'outside',
                    'corner_to_snap',
                    'snapping_corner_offset_x',
                    'snapping_corner_offset_y',
                    'snapping_corner_offset_z',
                ),
            },
        ),
    )

    def create_group_from_items(self, request, queryset):
        item_group = ItemGroup(
            name='CHANGEME, Automatic Group from: {}'.format(
                ', '.join([x.name for x in queryset])
            )[:240]
        )  # to keep it in field length limit (+10 chars JUST TO BE SURE
        item_group.save()
        item_group.items.add(*queryset)
        self.message_user(
            request=request,
            message='Added {} items to new group'.format(queryset.count()),
        )

    def toggle_enabled(self, request, queryset):
        for item in queryset:
            item.enabled = not item.enabled
            item.save(
                update_fields=[
                    'enabled',
                ]
            )

    def export_items(self, request, queryset):
        if queryset.count() >= 80:
            self.message_user(
                request,
                'This export should be used only for max 80 items, try smaller packs',
                level=messages.ERROR,
            )
            return
        stream = BytesIO()
        with ZipFile(stream, 'w') as zipfile:
            for item in queryset:
                for fileField in ['shadow_model', 'shadow_texture', 'model']:  # noqa: N806
                    if getattr(item, fileField):
                        zipfile.writestr(
                            getattr(item, fileField).name,
                            getattr(item, fileField).read(),
                        )
                for texture in item.textures.all():
                    zipfile.writestr(
                        texture.texture_file.name,
                        texture.texture_file.read(),
                    )
            items_data = ItemSerializer(queryset, many=True).data
            zipfile.writestr('items_serialization.json', json.dumps(items_data))

        stream.seek(0)
        filename = f'models_{datetime.now().strftime("%Y_%m_%d")}.zip'
        return FileResponse(stream, as_attachment=True, filename=filename)


class ItemGroupAdmin(admin.ModelAdmin):
    filter_horizontal = ('items',)
    list_display = (
        'id',
        'name',
        'number_of_items',
        'number_of_enabled_items',
        'get_links',
    )

    @mark_safe  # noqa: S308
    def get_links(self, obj):
        return format_html(
            '<a href="/shelf/bookcases/329890/?cv=0&items_conf=1&items_group={}"> '
            'show config with only this group </a>'.format(obj.id)
        )


admin.site.register(Item, ItemAdmin)
admin.site.register(ItemGroup, ItemGroupAdmin)
admin.site.register(ItemTextureVariant)
admin.site.register(Strategy, StrategyAdmin)
