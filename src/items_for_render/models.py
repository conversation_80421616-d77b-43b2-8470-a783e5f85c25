from django.db import models

from custom.enums import ShelfType
from custom.models import Choice<PERSON>rray<PERSON>ield
from items_for_render.choices import (
    CornerSnap,
    FreeSpaceSelectionApproach,
    FurnitureCategory,
    ItemsForRenderApproach,
    ItemsSnapping,
)

TYPE_COLOUR_CHOICES = []
for entry in ShelfType:
    colors = [x for x in entry.colors]  # noqa: C416
    for color in colors:
        TYPE_COLOUR_CHOICES.append(  # noqa: PERF401
            ['ShelfType:{}_Material:{}'.format(entry.value, color)] * 2
        )


class Item(models.Model):
    name = models.CharField(max_length=256)
    enabled = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    owner = models.ForeignKey('auth.User', on_delete=models.PROTECT)

    model = models.FileField(upload_to='items_for_render/item/%Y/%m')
    shadow_model = models.FileField(
        upload_to='items_for_render/item/%Y/%m', null=True, blank=True
    )
    shadow_texture = models.FileField(
        upload_to='items_for_render/item/%Y/%m', null=True, blank=True
    )

    categories = ChoiceArrayField(
        models.CharField(choices=FurnitureCategory.choices, max_length=25, blank=True),
        blank=True,
        default=list,
    )

    scale_x_min = models.IntegerField(default=100, help_text='100 == 100%')
    scale_x_max = models.IntegerField(default=100, help_text='100 == 100%')
    scale_y_min = models.IntegerField(default=100, help_text='100 == 100%')
    scale_y_max = models.IntegerField(default=100, help_text='100 == 100%')
    scale_z_min = models.IntegerField(default=100, help_text='100 == 100%')
    scale_z_max = models.IntegerField(default=100, help_text='100 == 100%')

    default_x = models.IntegerField(default=0, help_text='in cm')
    default_y = models.IntegerField(default=0, help_text='in cm')
    default_z = models.IntegerField(default=0, help_text='in cm')

    max_y = models.IntegerField(default=500, help_text='in cm')
    min_y = models.IntegerField(default=0, help_text='in cm')

    margin_min_left = models.IntegerField(default=0)
    margin_min_right = models.IntegerField(default=0)
    margin_min_top = models.IntegerField(default=0)

    can_be_on_top_horizontal = models.BooleanField(default=False)
    snapping = models.IntegerField(
        choices=ItemsSnapping.choices,
        default=ItemsSnapping.NO_SNAPPING,
    )

    outside = models.BooleanField(
        default=False, help_text='May be rendered outside shelf/wardrobe'
    )

    corner_to_snap = models.CharField(
        max_length=50,
        choices=CornerSnap.choices,
        default=CornerSnap.BOTTOM_CENTER,
    )

    snapping_corner_offset_x = models.IntegerField(default=0)
    snapping_corner_offset_y = models.IntegerField(default=0)
    snapping_corner_offset_z = models.IntegerField(default=0)

    def __str__(self):
        return 'Item: {}'.format(self.name)


class ItemGroup(models.Model):
    name = models.CharField(max_length=250)
    items = models.ManyToManyField(Item)
    created_at = models.DateTimeField(auto_now_add=True)

    categories = ChoiceArrayField(
        models.CharField(choices=FurnitureCategory.choices, max_length=25, blank=True),
        blank=True,
        default=list,
    )

    def __str__(self):
        return 'Item group {}'.format(
            self.name,
        )

    @property
    def number_of_items(self):
        return self.items.all().count()

    @property
    def number_of_enabled_items(self):
        return self.items.filter(enabled=True).count()


class ItemTextureVariant(models.Model):
    name = models.CharField(max_length=250, default='default')
    created_at = models.DateTimeField(auto_now_add=True)
    item = models.ForeignKey(Item, related_name='textures', on_delete=models.CASCADE)
    texture_file = models.FileField(
        upload_to='items_for_render/item_texture_variant/%Y/%m'
    )
    is_transparent = models.BooleanField(default=False)
    availability = ChoiceArrayField(
        models.CharField(choices=TYPE_COLOUR_CHOICES, max_length=25, blank=True),
        blank=True,
        default=list,
    )

    def __str__(self):
        return 'Texture variant: {}, {}'.format(self.name, self.item)


class Strategy(models.Model):
    name = models.CharField(max_length=250)
    description = models.TextField(default='', blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    number_of_items = models.IntegerField(default=0, help_text='absolute, or %')
    number_of_items_approach = models.IntegerField(
        choices=ItemsForRenderApproach.choices,
        default=ItemsForRenderApproach.ABSOLUTE,
    )
    free_space_selection_approach = models.IntegerField(
        choices=FreeSpaceSelectionApproach.choices,
        default=FreeSpaceSelectionApproach.DISTANCE_FROM_START,
    )

    def __str__(self):
        return 'Strategy: {}, {}'.format(self.name, self.description)


class StrategyGroupRule(models.Model):
    description = models.TextField(default='', blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    strategy = models.ForeignKey(
        'Strategy', related_name='rules', on_delete=models.CASCADE
    )
    item_group = models.ForeignKey(
        'ItemGroup', related_name='used_in_strategies', on_delete=models.CASCADE
    )
    slot_number = models.IntegerField(default=0)
    min_number_of_items = models.IntegerField(default=0)
    max_number_of_items = models.IntegerField(default=64)

    proposed_number_of_items = models.IntegerField(default=1)

    def __str__(self):
        return 'Rule id:{} for: {}. Info:  {}'.format(
            self.id, self.strategy, self.description
        )

    class Meta:  # noqa: DJ012
        ordering = ('slot_number', 'id')
