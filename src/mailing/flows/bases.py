import abc
import functools
import inspect
import logging

from django.contrib.contenttypes.models import ContentType
from django.db.utils import Error
from django.utils import timezone

from custom.internal_api.dto import LogisticOrderDTO
from custom.metrics import metrics_client
from custom.utils.mixins import ClassNameHashableMixin
from mailing.enums import (
    MailingMetricType,
    MailingStatus,
)
from mailing.flows.controllers import EmailTimeIntervalController
from mailing.models import (
    MailingFlowSettings,
    MailingFlowStatus,
)
from mailing.utils import mailing_metric_key_for

logger = logging.getLogger('cstm')

MAILING_FLOWS_REGISTRY = {}


def rgetattr(obj, attr, *args):
    def _getattr(obj, attr):
        return getattr(obj, attr, *args)

    return functools.reduce(_getattr, [obj] + attr.split('.'))  # noqa: RUF005


class ShouldNotProcessError(Error):
    """Object shouldn't be processed by mailing flow."""


class AbstractMailingFlow(ClassNameHashableMixin, metaclass=abc.ABCMeta):
    """Abstract class defining a mailing flow.

    Instances of this class subclasses are responsible for filtering objects,
    sending emails.

    """

    @classmethod
    def __init_subclass__(cls, **kwargs):
        """Register mailing flow classes."""
        super().__init_subclass__(**kwargs)
        if not inspect.isabstract(cls):
            MAILING_FLOWS_REGISTRY.setdefault(cls.__name__, cls)

    @abc.abstractmethod
    def interested_in(self):
        """Model processed in mailing flow."""
        raise NotImplementedError

    @abc.abstractmethod
    def filter_interested(self, candidate_objects, **kwargs):
        """Return queryset of ``interested_in`` objects ready to process."""
        raise NotImplementedError

    @abc.abstractmethod
    def get_mail_template(self, **kwargs):
        """``BaseMail`` subclass used to send emails."""
        raise NotImplementedError

    @abc.abstractmethod
    def get_success_candidates(self):
        """``QuerySet`` of recently and successfully processed flows statuses."""
        raise NotImplementedError

    @abc.abstractmethod
    def _get_mail_from_status(self, mailing_flow_status):
        """Return email address from ``MailingFlowStatus`` instance."""
        raise NotImplementedError

    @abc.abstractmethod
    def _get_user_from_status(self, mailing_flow_status):
        """``User`` instance extracted from ``MailingFlowStatus`` intance."""
        raise NotImplementedError

    @abc.abstractmethod
    def _process_object(self, mailing_flow_status):
        """Send email after final verification."""
        raise NotImplementedError

    @abc.abstractmethod
    def _process_success_candidate(self, success_candidate):
        """Update status of related instance of ``MailingFlowStatus``.

        Called after applying changes on related flow object.

        """
        raise NotImplementedError

    # TODO: make it abstract property
    @property
    def _interested_object_time_before_td_designation(self):
        raise NotImplementedError

    def get_mail_from_status(self, mailing_flow_status):
        """Return email address from ``MailingFlowStatus`` instance."""
        try:
            return self._get_mail_from_status(mailing_flow_status)
        except Exception as e:
            logger.error('Could not find the email because: {}'.format(e))
        return None

    @classmethod
    def get_or_create_mailing_flow_settings(cls):
        mailing_flow_name = cls.__name__
        try:
            mfs = MailingFlowSettings.objects.get(flow_designation=mailing_flow_name)
        except MailingFlowSettings.DoesNotExist:
            logger.debug(
                'Did not find MailingFlowSettings for designation {}. '
                'Creating default.'.format(mailing_flow_name)
            )
            mfs = MailingFlowSettings.objects.create(flow_designation=mailing_flow_name)
        if mfs.active:
            return mfs

    def process(self, limit):
        self.process_success_candidates()

        if not self.interested_in:
            logger.debug(
                '%s has no specified interested_in attribute', self.__class__.__name__
            )
            interested_in_all = []
        else:
            interested_in_all = (
                []
                if self.interested_in is LogisticOrderDTO
                else self.interested_in.objects.all()
            )

        filter_interested = self.filter_interested(interested_in_all)
        filter_interested_len = len(filter_interested)
        if filter_interested_len > limit:
            logger.debug(
                'MailingFlow %s filter_interested_len=%s more than limit. '
                'Sending first %s objects.',
                self.__class__.__name__,
                filter_interested_len,
                limit,
            )
            filter_interested = filter_interested[:limit]

        content_type = None
        for object_of_interest in filter_interested:
            if not content_type:
                if isinstance(object_of_interest, LogisticOrderDTO):
                    content_type = ContentType.objects.get(
                        app_label='logistic',
                        model='logisticorder',
                    )
                else:
                    content_type = ContentType.objects.get_for_model(object_of_interest)
            self.process_object_of_interest(object_of_interest, content_type)

    def process_success_candidates(self):
        # TODO mailingperfo refactor after main refactor based on
        #  https://github.com/tylkocom/cstm/pull/2185
        success_candidates = self.get_success_candidates()
        for success_candidate in success_candidates:
            self.get_and_process_success_candidates(success_candidate)

    def process_object_of_interest(self, object_of_interest, content_type):
        logistic_order_content_type = ContentType.objects.get(
            app_label='logistic',
            model='logisticorder',
        )
        defaults = {
            'flow_designation': self.designation,
            'status': MailingStatus.PROCESSING,
            'time': timezone.now(),
        }
        if content_type.id != logistic_order_content_type.id:
            defaults['flow_object'] = object_of_interest

        mailing_flow_status, created = MailingFlowStatus.objects.get_or_create(  # noqa: RUF059
            flow_designation=self.designation,
            content_type=content_type,
            object_id=object_of_interest.id,
            status=MailingStatus.PROCESSING,
            defaults=defaults,
        )
        if not EmailTimeIntervalController().allow(self, mailing_flow_status):
            return

        mailing_flow_status = self.process_object(mailing_flow_status)

        if not mailing_flow_status:
            return

        if mailing_flow_status.status == MailingStatus.SUCCESS:
            # TODO mailingperfo add data dog metrics
            pass

        try:
            self.log_flow(mailing_flow_status)
        except TypeError:
            logger.exception('There was an error during log_flow', exc_info=True)

        if mailing_flow_status.is_logistic_flow():
            object_id = mailing_flow_status.object_id
        else:
            object_id = mailing_flow_status.flow_object.id

        existing_mfs = MailingFlowStatus.objects.filter(
            flow_designation=mailing_flow_status.flow_designation,
            content_type_id=mailing_flow_status.content_type_id,
            object_id=object_id,
        ).last()

        if existing_mfs:
            attrs_to_update = (
                'status',
                'subject',
                'email_address',
                'email_language',
                'additional_data',
                'template_class',
                'version',
            )
            for attr_name in attrs_to_update:
                setattr(
                    existing_mfs,
                    attr_name,
                    getattr(mailing_flow_status, attr_name),
                )
            existing_mfs.save()
        else:
            mailing_flow_status.save()

    def process_object(self, mailing_flow_status):
        """Send email after final verification."""
        if (
            not mailing_flow_status.is_logistic_flow()
            and not mailing_flow_status.flow_object
        ):
            mailing_flow_status.status = MailingStatus.OBJECT_GONE
        else:
            try:
                processed = self._process_object(mailing_flow_status)
                if processed is not False:
                    if mailing_flow_status.status == MailingStatus.PROCESSING:
                        mailing_flow_status.status = MailingStatus.SUCCESS
            except ShouldNotProcessError:
                logger.debug(
                    'Object should not be processed %s', mailing_flow_status.id
                )
                return None
            except Exception:
                logger.exception(
                    '{} Error while processing {}'.format(
                        self.designation, mailing_flow_status
                    )
                )
                mailing_flow_status.status = MailingStatus.FAILURE
        return mailing_flow_status

    def get_and_process_success_candidates(self, success_candidate):
        """Update status of related instance of ``MailingFlowStatus``.

        Called after applying changes on related flow object.

        """
        try:
            if (
                not success_candidate.is_logistic_flow()
                and success_candidate.flow_object is None
            ):
                success_candidate.status = MailingStatus.OBJECT_GONE
                success_candidate.save()
            else:
                self._process_success_candidate(success_candidate)
        except AttributeError:
            logger.debug(
                (
                    '{} One of the flow objects seems to be gone. '
                    'Setting GONE status on MailingFlowStatus id={}.'
                ).format(self.designation, success_candidate.id),
            )
            success_candidate.status = MailingStatus.OBJECT_GONE
            success_candidate.save()
            metrics_client().increment(
                mailing_metric_key_for(self.designation, MailingMetricType.ERROR), 1
            )
        except Exception:
            logger.exception(
                '{} Error while processing success candidate {}'.format(
                    self.designation,
                    success_candidate.id,
                ),
            )
            metrics_client().increment(
                mailing_metric_key_for(self.designation, MailingMetricType.ERROR), 1
            )

    def get_mail_subject(self, mail_template_class):
        """Mail subject's template extracted from ``BaseMail`` subclass."""
        mailing_subject = None
        if mail_template_class and mail_template_class.topic:
            # We prefer them lazy
            if type(mail_template_class.topic).__name__ == '__proxy__':
                mailing_subject = mail_template_class.topic.__dict__['_proxy____args'][
                    0
                ]
            else:
                # Not good. Already translated key is returned.
                mailing_subject = mail_template_class.topic
        return mailing_subject

    def flows_for_objects_qs(self, object_ids, content_type):
        """Return ``QuerySet`` with already processed ``MailingFlowStatus``."""
        mfs_qs = MailingFlowStatus.objects.filter(
            object_id__in=object_ids,
            flow_designation=self.designation,
            status__in=[
                MailingStatus.SUCCESS,
                MailingStatus.GOAL,
                MailingStatus.OBJECT_GONE,
            ],
        )
        if content_type is not None:
            mfs_qs = mfs_qs.filter(content_type=content_type)
        return mfs_qs

    def was_processed(self, object_id, content_type=None):
        """Info if ``MailingFlowStatus`` for ``object_id`` was processed."""
        content_type = (
            content_type
            if content_type is not None
            else self.interested_in_content_type
        )
        mfss = self.flows_for_objects_qs([object_id], content_type)
        return mfss.exist()

    def set_flow_object_as_gone(self, mailing_flow_status, object_id, message):
        """Set ``MailingFlowStatus`` status as ``STATUS_OBJECT_GONE``.

        This method is called in ``process_object`` method for all flow
        objects that doesn't fullfill final verification.

        """
        mailing_flow_status.status = MailingStatus.OBJECT_GONE
        metrics_client().increment(
            mailing_metric_key_for(self.designation, MailingMetricType.OBJECT_GONE), 1
        )
        mailing_flow_status.save()

    def get_processed(self, object_ids, content_type=None):
        """List of ``object_id`` of already processed ``MailingFlowStatus``."""
        mfss = self.flows_for_objects_qs(object_ids, content_type)
        return mfss.values_list('object_id', flat=True)

    def get_not_yet_processed(self, object_ids, content_type=None):
        """List of ``object_id`` of unprocessed ``MailingFlowStatus``."""
        content_type = (
            content_type
            if content_type is not None
            else self.interested_in_content_type
        )
        processed_ids = self.get_processed(object_ids, content_type)
        return set(object_ids) - set(processed_ids)

    def log_flow(self, mailing_flow_status):
        """Send information about flow to external services.

        Update some legacy metrics in datadog.

        """
        if not isinstance(mailing_flow_status, MailingFlowStatus):
            raise TypeError(
                (
                    '`mailing_flow_status` should be instance of `{0}`, '
                    'but got `{1}` instance'
                ).format(
                    MailingFlowStatus.__name__,
                    type(mailing_flow_status),
                ),
            )
        if mailing_flow_status.is_logistic_flow():
            return False
        if mailing_flow_status.flow_object is None:
            return False
        if self.interested_in is not None and not isinstance(
            mailing_flow_status.flow_object, self.interested_in
        ):
            raise TypeError(
                (
                    '`mailing_flow_status.flow_object` should be instance of '
                    '`{0}`, but got `{1}` instance'
                ).format(
                    self.interested_in.__name__,
                    type(mailing_flow_status.flow_object),
                ),
            )
        metrics_client().increment(
            'backend.mail.flow_processed',
            1,
            tags=[
                'flow_designation:{}'.format(
                    mailing_flow_status.flow_designation,
                ),
            ],
        )

    @property
    def designation(self):
        """Identifier of the flow."""
        return self.__class__.__name__

    @property
    def interested_in_content_type(self):
        """``ContentType`` of ``interested_in`` model.

        ``None`` is returned when there is no ``interested_in`` model.

        """
        if self.interested_in is None:
            return None
        return ContentType.objects.get_for_model(self.interested_in)

    @property
    def interested_object_time_before_td_designation(self):
        """Time unit passed to ``datetime.timedelta`` used in filtering."""
        return self._interested_object_time_before_td_designation

    @staticmethod
    def _filter_helper(element, field_values, exclude=False):
        """Return filter predicate result."""
        result = []
        for field, value in field_values:
            field_value = rgetattr(element, field)
            if not field_value:
                result.append(False)
            else:
                result.append(field_value in value)
        result = all(result)
        return not result if exclude else result

    @staticmethod
    def _filter(queryset, fields, values, exclude=False):
        """Perform filtering of ``field_values`` on ``element``.

        Filters objects in Python instead of SQL, because it's sometimes
        more performant.

        """
        field_values = list(zip(fields, values))

        def iterable(element):
            return AbstractMailingFlow._filter_helper(element, field_values, exclude)

        return [element for element in queryset if iterable(element)]
