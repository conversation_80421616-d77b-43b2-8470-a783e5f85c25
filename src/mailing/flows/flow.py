import logging

from datetime import timedelta
from functools import lru_cache

from django.conf import settings
from django.contrib.auth.models import User
from django.core.cache import cache
from django.db import IntegrityError
from django.db.models.query_utils import Q
from django.utils import timezone

from custom.metrics import metrics_client
from mailing.enums import (
    MailingMetricType,
    MailingStatus,
)
from mailing.flows.controllers import AbstractMailingFlowController
from mailing.models import (
    MailingFlowStatus,
    RetargetingBlacklist,
)
from mailing.templates import (
    BankTransferReminderMail,
    ProductionDelayEmail,
)
from mailing.utils import mailing_metric_key_for
from orders.enums import (
    OrderStatus,
    OrderType,
)
from orders.models import (
    Order,
    PaidOrders,
)
from user_profile.models import (
    LoginAccessToken,
    RetargetingBlacklistToken,
)

from .bases import (
    MAILING_FLOWS_REGISTRY,
    AbstractMailingFlow,
)

logger = logging.getLogger('cstm')


class EmailTimeIntervalController(AbstractMailingFlowController):
    """
    Flow controller not allowing the same objects of interest to be processed
    more than once an interval within the context of one flow.

    """

    INTERVAL = (
        timedelta(minutes=1)
        if settings.IS_TESTING or settings.IS_DEV
        else timedelta(hours=1)
    )

    def _allow(self, mailing_flow, mailing_flow_status):
        email_address = mailing_flow.get_mail_from_status(mailing_flow_status)
        if (
            email_address
            and MailingFlowStatus.objects.filter(
                email_address=email_address,
                flow_designation=mailing_flow_status.flow_designation,
                time__gt=timezone.now() - self.INTERVAL,
            ).exists()
        ):
            return False
        return True

    def disallow_strategy(self, mailing_flow, mailing_flow_status):
        mailing_flow_status.status = MailingStatus.FAILURE
        try:
            mailing_flow_status.save()
        except IntegrityError:
            mailing_flow_status.status = MailingStatus.OBJECT_GONE
            mailing_flow_status.save()


class MailingFlowFactory(object):
    @lru_cache(typed=True)  # noqa: B019
    def _get_interesting_classes(self, mail_template):
        return [
            c
            for c in MAILING_FLOWS_REGISTRY.values()
            if c().mail_template == mail_template
        ]

    def get_mailing_flow(self, mailing_flow):
        mailing_flow_classes = self._get_interesting_classes(mailing_flow)
        return mailing_flow_classes[0] if len(mailing_flow_classes) > 0 else None


class RetargetingFlowMixin(object):
    def get_blacklisted_emails(self):
        if not hasattr(self, '_blacklisted_emails'):
            self._blacklisted_emails = list(
                RetargetingBlacklist.objects.values_list('email', flat=True)
            ) + list(
                User.objects.filter(
                    username__startswith='user_created_by_cs'
                ).values_list('username', flat=True)
            )
        return self._blacklisted_emails

    def get_users_with_paid_orders(self):
        users_with_paid_orders = cache.get('users_with_paid_orders')
        if not users_with_paid_orders:
            month_ago = timezone.now() - timedelta(weeks=4)
            users_with_paid_orders = PaidOrders.objects.filter(
                updated_at__gt=month_ago
            ).values_list('owner', flat=True)
            cache.set(
                'users_with_paid_orders',
                users_with_paid_orders,
                settings.MAILING_FLOW_PERIOD * 60,
            )
        return users_with_paid_orders


class FailedPaymentBankTransferFlowByOrders(AbstractMailingFlow, RetargetingFlowMixin):
    def get_mail_template(self, **kwargs):
        return BankTransferReminderMail

    @property
    def interested_in(self):
        return Order

    def filter_interested(self, candidate_objects, **kwargs):
        objects_filtered = (
            candidate_objects.filter(
                status__in=[
                    OrderStatus.PAYMENT_PENDING,
                ],
                updated_at__lt=timezone.now() - timedelta(hours=72),
                paid_at=None,
                updated_at__gte='2018-12-20',
                email__isnull=False,
            )
            .filter(
                Q(
                    chosen_payment_method__startswith='bankTransfer',
                )
                | Q(
                    chosen_payment_method='dotpay',
                )
            )
            .exclude(transactions__notification__success=True)
            .distinct('id')
        )
        filtered = objects_filtered.filter(
            id__in=self.get_not_yet_processed(
                object_ids=[o.id for o in objects_filtered]
            )
        )
        metrics_client().increment(
            mailing_metric_key_for(
                self.designation, MailingMetricType.FILTERED_FOR_PROCESSING
            ),
            filtered.count(),
        )
        return filtered

    def _get_mail_from_status(self, mailing_flow_status):
        return mailing_flow_status.flow_object.email

    def _get_user_from_status(self, mailing_flow_status):
        return mailing_flow_status.flow_object.owner

    def _process_object(self, mailing_flow_status):
        order = Order.objects.get(pk=mailing_flow_status.flow_object.pk)
        login_access_token = LoginAccessToken.get_or_create_for_user(order.owner)
        if order.status in [OrderStatus.PAYMENT_FAILED, OrderStatus.PAYMENT_PENDING]:
            order.change_status(OrderStatus.DRAFT)
        else:
            logger.exception(
                '[{}] Error while processing order {} status order {}'.format(
                    self.designation, order.id, order.status
                )
            )
            mailing_flow_status.status = MailingStatus.FAILURE
            metrics_client().increment(
                mailing_metric_key_for(self.designation, MailingMetricType.ERROR), 1
            )
            return
        mail_template = self.get_mail_template()
        mt = mail_template(
            order.email,
            {
                'order': order,
                'login_access_token': login_access_token,
                'blacklist_token': RetargetingBlacklistToken.get_or_create_for_email(
                    email=order.email
                ).token,
            },
        )
        mt.send(language=order.owner.profile.language, user_id=order.owner_id)
        metrics_client().increment(
            mailing_metric_key_for(self.designation, MailingMetricType.PROCESSED), 1
        )
        mailing_flow_status.template_class = mail_template.__name__
        mailing_flow_status.email_address = order.email
        mailing_flow_status.email_language = order.owner.profile.language
        mailing_flow_status.subject = self.get_mail_subject(mail_template)

    def get_success_candidates(self):
        return MailingFlowStatus.objects.filter(
            flow_designation=self.designation, status=MailingStatus.SUCCESS
        )

    def _process_success_candidate(self, success_candidate):
        if success_candidate.flow_object.paid_at:
            success_candidate.status = MailingStatus.GOAL
            success_candidate.save()


class ProductionDelayFlow(AbstractMailingFlow):
    def get_mail_template(self, **kwargs):
        return ProductionDelayEmail

    @property
    def interested_in(self):
        return Order

    def filter_interested(self, candidate_objects, **kwargs):
        import dateutil

        objects_filtered = (
            candidate_objects.filter(
                status=OrderStatus.IN_PRODUCTION,
                estimated_delivery_time__isnull=False,
                email__isnull=False,
            )
            .exclude(order_type=OrderType.COMPLAINT)
            .exclude(estimated_delivery_time_log=[])
        )
        interested = []
        for order in objects_filtered:
            last_date_before_change = dateutil.parser.parse(
                order.estimated_delivery_time_log[-1]
            )
            multiple_dates = False
            if len(order.estimated_delivery_time_log) > 1:
                multiple_dates = True
            if (order.estimated_delivery_time - last_date_before_change).days >= 7:
                if multiple_dates:
                    last_date_before_change_back = dateutil.parser.parse(
                        order.estimated_delivery_time_log[-2]
                    )
                    if (
                        last_date_before_change_back - last_date_before_change_back
                    ).days < 7:
                        continue
                processed = MailingFlowStatus.objects.filter(
                    object_id=order.pk, flow_designation=self.designation
                ).exclude(status=MailingStatus.FAILURE)
                if processed.count() == 0:
                    interested.append(order.pk)
                elif (
                    processed[0].additional_data
                    and len(order.estimated_delivery_time_log)
                    != processed[0].additional_data['estimated_delivery_time_log']
                ):
                    interested.append(order.pk)
                elif not processed[0].additional_data and len(
                    order.estimated_delivery_time_log
                ):
                    interested.append(order.pk)
        filtered = objects_filtered.filter(id__in=interested)
        metrics_client().increment(
            mailing_metric_key_for(
                self.designation, MailingMetricType.FILTERED_FOR_PROCESSING
            ),
            filtered.count(),
        )
        return filtered

    def _get_mail_from_status(self, mailing_flow_status):
        return mailing_flow_status.flow_object.email

    def _get_user_from_status(self, mailing_flow_status):
        return mailing_flow_status.flow_object.owner

    def _process_object(self, mailing_flow_status):
        order = mailing_flow_status.flow_object
        mail_template = self.get_mail_template()
        mt = mail_template(
            order.email,
            {
                'order': order,
                'user_name': order.first_name,
                'order_id': order.order_pretty_id,
                'date': order.estimated_delivery_time.date().isoformat(),
            },
            topic_variables={
                'order_id': order.order_pretty_id,
            },
        )
        mt.send(language=order.owner.profile.language, user_id=order.owner_id)
        metrics_client().increment(
            mailing_metric_key_for(self.designation, MailingMetricType.PROCESSED), 1
        )
        mailing_flow_status.email_address = order.email
        mailing_flow_status.template_class = mail_template.__name__
        mailing_flow_status.email_language = order.owner.profile.language
        mailing_flow_status.subject = self.get_mail_subject(mail_template)
        mailing_flow_status.additional_data = {
            'order': order.pk,
            'estimated_delivery_time_log': len(order.estimated_delivery_time_log),
        }

    def get_success_candidates(self):
        return MailingFlowStatus.objects.filter(
            flow_designation=self.designation, status=MailingStatus.SUCCESS
        )

    def _process_success_candidate(self, success_candidate):
        if success_candidate.flow_object.status == OrderStatus.DELIVERED:
            success_candidate.status = MailingStatus.GOAL
            success_candidate.save()
