from datetime import datetime
from zoneinfo import ZoneInfo

from django.conf import settings
from django.contrib.contenttypes.models import ContentType

from custom.internal_api.dto import LogisticOrderDTO
from custom.logistic_enums import Shipper
from custom.metrics import metrics_client
from mailing.enums import (
    MailingMetricType,
    MailingStatus,
)
from mailing.flows.bases import AbstractMailingFlow
from mailing.models import MailingFlowStatus
from mailing.templates_logistic import (
    ComplaintsReplacementShipped,
    ProductShippedRegularMail,
    ProductShippedRegularMailDPD,
    ProductShippedRegularMailFedex,
    ProductShippedRegularMailSampleSet,
    ProductShippedRegularMailSampleSetDPD,
    ProductShippedRegularMailSampleSetFedex,
    ProductShippedRegularMailUPS,
    ProductToBeShippedMail,
    ProductToBeShippedMailUPS,
)
from mailing.utils import mailing_metric_key_for
from orders.internal_api.clients import LogisticOrderAP<PERSON>lient
from orders.models import Order
from user_profile.models import RetargetingBlacklistToken


class ProductShippedFlow(AbstractMailingFlow):
    def get_mail_template(self, logistic_order_dto=None, order=None, **kwargs):
        if order.contains_only_samples:
            if logistic_order_dto.shipper:
                if logistic_order_dto.shipper.name == Shipper.FEDEX.value:
                    return ProductShippedRegularMailSampleSetFedex
                if logistic_order_dto.shipper.name == Shipper.DPD.value:
                    return ProductShippedRegularMailSampleSetDPD
            return ProductShippedRegularMailSampleSet

        if logistic_order_dto.shipper:
            if logistic_order_dto.shipper.name == Shipper.UPS.value:
                return ProductShippedRegularMailUPS
            if logistic_order_dto.shipper.name == Shipper.DPD.value:
                return ProductShippedRegularMailDPD
            if logistic_order_dto.shipper.name == Shipper.FEDEX.value:
                return ProductShippedRegularMailFedex

        return ProductShippedRegularMail

    @property
    def interested_in_content_type(self):
        return ContentType.objects.get(app_label='logistic', model='logisticorder')

    @property
    def interested_in(self):
        return LogisticOrderDTO

    def filter_interested(self, candidate_objects, **kwargs):
        now = datetime.now(tz=ZoneInfo('UTC'))
        if not settings.IS_TESTING and now.hour != 13:
            return []

        logistic_order_api_client = LogisticOrderAPIClient()
        logistic_orders = logistic_order_api_client.get_product_shipped_flow()

        not_yet_processed = self.get_not_yet_processed(
            object_ids=[logistic_order.id for logistic_order in logistic_orders]
        )
        objects_filtered = [
            logistic_order
            for logistic_order in logistic_orders
            if logistic_order.id in not_yet_processed
        ]
        amount_of_filtered_for_processing = len(objects_filtered)
        metrics_client().increment(
            mailing_metric_key_for(
                self.designation, MailingMetricType.FILTERED_FOR_PROCESSING
            ),
            amount_of_filtered_for_processing,
        )
        return objects_filtered

    def _get_mail_from_status(self, order):
        return order.email

    def _get_user_from_status(self, order):
        return order.owner

    def _process_object(self, mailing_flow_status):
        logistic_order_id = mailing_flow_status.object_id
        order = Order.objects.get(
            serialized_logistic_info__contains=[{'id': logistic_order_id}]
        )
        logistic_order_dto = [  # noqa: RUF015
            lo for lo in order.logistic_info if lo.id == logistic_order_id
        ][0]
        furniture_type = (
            order.items.all().order_by('-region_price').first().order_item.default_title
        )
        order_language = order.owner.profile.language
        language_code = order.owner.profile.get_language_code()
        context = {
            'furniture_type': furniture_type,
            'logistic_order': logistic_order_dto,
            'language_code': language_code,
            'blacklist_token': RetargetingBlacklistToken.get_or_create_for_email(
                email=order.email
            ).token,
            'country': order.country,
        }

        if logistic_order_dto.tracking_link:
            context['tracking_link'] = logistic_order_dto.tracking_link

        email = self._get_mail_from_status(order)
        mail_template = self.get_mail_template(logistic_order_dto, order)
        mt = mail_template(email, context)
        mt.send(language=order_language, user_id=order.owner.id)
        mailing_flow_status.email_address = email
        mailing_flow_status.email_language = order_language
        mailing_flow_status.template_class = mail_template.__name__
        metrics_client().increment(
            mailing_metric_key_for(self.designation, MailingMetricType.PROCESSED), 1
        )
        mailing_flow_status.subject = self.get_mail_subject(mail_template)
        mailing_flow_status.save()

    def get_success_candidates(self):
        return []

    def _process_success_candidate(self, success_candidate):
        pass


class ComplaintReproductionShippedFlow(AbstractMailingFlow):
    @property
    def mail_templates(self):
        return [
            ComplaintsReplacementShipped,
        ]

    @property
    def _interested_object_time_before_td_designation(self):
        return 'weeks'

    def get_success_candidates(self):
        return []

    def _process_success_candidate(self, success_candidate):
        pass

    def get_mail_template(self, **kwargs):
        return self.mail_templates[0]

    @property
    def interested_in_content_type(self):
        return ContentType.objects.get(app_label='logistic', model='logisticorder')

    @property
    def interested_in(self):
        return LogisticOrderDTO

    def filter_interested(self, candidate_objects, **kwargs):
        logistic_order_api_client = LogisticOrderAPIClient()
        logistic_orders = (
            logistic_order_api_client.get_complaint_reproduction_shipped_flow()
        )

        not_yet_processed = self.get_not_yet_processed(
            object_ids=[logistic_order.id for logistic_order in logistic_orders]
        )
        objects_filtered = [
            logistic_order
            for logistic_order in logistic_orders
            if logistic_order.id in not_yet_processed
        ]

        amount_of_filtered_for_processing = len(objects_filtered)
        metrics_client().increment(
            mailing_metric_key_for(
                self.designation, MailingMetricType.FILTERED_FOR_PROCESSING
            ),
            amount_of_filtered_for_processing,
        )
        return objects_filtered

    def _get_mail_from_status(self, order):
        return order.email

    def _get_user_from_status(self, order):
        return order.owner

    def is_already_sent_for_mailing_flow(self, mailing_flow_status):
        content_type = ContentType.objects.get(
            app_label='logistic',
            model='logisticorder',
        )
        return MailingFlowStatus.objects.filter(
            flow_designation=ComplaintReproductionShippedFlow.__name__,
            content_type=content_type,
            object_id=mailing_flow_status.object_id,
            status__in=[
                MailingStatus.SUCCESS,
                MailingStatus.GOAL,
                MailingStatus.OBJECT_GONE,
            ],
        ).exists()

    def _process_object(self, mailing_flow_status):
        if self.is_already_sent_for_mailing_flow(mailing_flow_status):
            return
        logistic_order_complaint_id = mailing_flow_status.object_id
        order = Order.objects.get(
            serialized_logistic_info__contains=[{'id': logistic_order_complaint_id}]
        )
        logistic_order_dto = [  # noqa: RUF015
            lo for lo in order.logistic_info if lo.id == logistic_order_complaint_id
        ][0]
        context = {
            'user': order.first_name,
            'tracking_number': logistic_order_dto.tracking_number,
            'carrier': logistic_order_dto.carrier.upper(),
            'tracking_link': logistic_order_dto.tracking_link,
        }
        email = self._get_mail_from_status(order)
        mail_template = self.get_mail_template()
        order_language = order.owner.profile.language
        mt = mail_template(email, context)
        mt.send(language=order_language, user_id=order.owner.id)
        mailing_flow_status.email_address = email
        mailing_flow_status.email_language = order_language
        mailing_flow_status.template_class = mail_template.__name__
        metrics_client().increment(
            mailing_metric_key_for(self.designation, MailingMetricType.PROCESSED), 1
        )
        mailing_flow_status.subject = self.get_mail_subject(mail_template)
        mailing_flow_status.status = MailingStatus.SUCCESS
        mailing_flow_status.save()


class ProductToBeShippedFlow(AbstractMailingFlow):
    def get_mail_template(self, logistic_order=None, **kwargs):
        if (
            logistic_order
            and logistic_order.shipper
            and logistic_order.shipper.name == Shipper.UPS.value
        ):
            return ProductToBeShippedMailUPS
        return ProductToBeShippedMail

    @property
    def interested_in_content_type(self):
        return ContentType.objects.get(app_label='logistic', model='logisticorder')

    @property
    def interested_in(self):
        return LogisticOrderDTO

    def filter_interested(self, candidate_objects, **kwargs):
        logistic_order_api_client = LogisticOrderAPIClient()
        logistic_orders = logistic_order_api_client.get_product_to_be_shipped_flow()

        not_yet_processed = self.get_not_yet_processed(
            object_ids=[logistic_order.id for logistic_order in logistic_orders]
        )
        objects_filtered = [
            logistic_order
            for logistic_order in logistic_orders
            if logistic_order.id in not_yet_processed
        ]

        amount_of_filtered_for_processing = len(objects_filtered)
        metrics_client().increment(
            mailing_metric_key_for(
                self.designation, MailingMetricType.FILTERED_FOR_PROCESSING
            ),
            amount_of_filtered_for_processing,
        )
        return objects_filtered

    def _get_mail_from_status(self, mailing_flow_status):
        return mailing_flow_status.flow_object.order.email

    def _get_user_from_status(self, mailing_flow_status):
        return mailing_flow_status.flow_object.order.owner

    def _process_object(self, mailing_flow_status):
        logistic_order = mailing_flow_status.flow_object
        order = logistic_order.order
        if not order.order_pretty_id:
            order.create_pretty_id()
        packages = []
        for batch in logistic_order.get_packages():
            packages.extend(batch.get('packages', []))
        total_weight = logistic_order.total_brutto_weight_string()
        mail_template = self.get_mail_template(logistic_order=logistic_order)
        mt = mail_template(
            order.email,
            {
                'order': order,
                'packages': packages,
                'total_weight': total_weight,
            },
        )
        mt.send(language=order.owner.profile.language, user_id=order.owner_id)
        metrics_client().increment(
            mailing_metric_key_for(self.designation, MailingMetricType.PROCESSED), 1
        )
        mailing_flow_status.email_address = self._get_mail_from_status(
            mailing_flow_status
        )
        mailing_flow_status.template_class = mail_template.__name__
        user = self._get_user_from_status(mailing_flow_status)
        mailing_flow_status.email_language = user.profile.language
        mailing_flow_status.subject = self.get_mail_subject(mail_template)

    def get_success_candidates(self):
        return MailingFlowStatus.objects.filter(
            flow_designation=self.designation, status=MailingStatus.SUCCESS
        )

    def _process_success_candidate(self, success_candidate):
        pass
