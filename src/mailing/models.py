import copy
import logging

from collections import OrderedDict
from datetime import timed<PERSON><PERSON>
from decimal import Decimal
from secrets import token_urlsafe

from django.conf import settings
from django.contrib.auth import get_user_model
from django.contrib.contenttypes.fields import GenericForeignKey
from django.contrib.contenttypes.models import ContentType
from django.db import models
from django.db.models.aggregates import Sum
from django.utils import timezone

from custom.enums import LanguageEnum
from mailing.enums import (
    CustomerTypeChoices,
    MailChimpSatus,
    MailingScheduleStatus,
    MailingStatus,
)
from mailing.managers import (
    CustomerManager,
    MailingCustomerManager,
)
from orders.enums import OrderStatus
from vouchers.enums import (
    VoucherOrigin,
    VoucherType,
)
from vouchers.models import Voucher

User = get_user_model()

logger = logging.getLogger('cstm')


def generate_token():
    return token_urlsafe(32)


class MailingFlowStatus(models.Model):
    time = models.DateTimeField(auto_now_add=True, db_index=True)
    flow_designation = models.CharField(max_length=64, db_index=True)
    content_type = models.ForeignKey(
        ContentType,
        on_delete=models.CASCADE,
    )
    object_id = models.PositiveIntegerField()
    flow_object = GenericForeignKey('content_type', 'object_id')
    status = models.PositiveSmallIntegerField(
        choices=MailingStatus.choices, db_index=True
    )
    email_address = models.EmailField(blank=True, null=True, db_index=True)  # noqa: DJ001
    email_language = models.CharField(  # noqa: DJ001
        choices=LanguageEnum.choices,
        db_index=True,
        max_length=2,
        null=True,
    )
    template_class = models.CharField(db_index=True, max_length=64, null=True)  # noqa: DJ001
    subject = models.CharField(max_length=256, blank=True, null=True)  # noqa: DJ001
    opens = models.PositiveSmallIntegerField(blank=True, null=True, db_index=True)
    clicks = models.PositiveSmallIntegerField(blank=True, null=True, db_index=True)
    version = models.PositiveIntegerField(default=0, db_index=True)
    additional_data = models.JSONField(blank=True, null=True)

    class Meta:
        verbose_name_plural = 'Mailing flow statuses'
        get_latest_by = 'time'

    def __str__(self):
        return (
            'MailingFlowStatus[id={} flow={} status={} object_id={} object_type={} '
            'time={}]'.format(
                self.id,
                self.flow_designation,
                self.get_status_display(),
                self.object_id,
                self.content_type,
                self.time,
            )
        )

    def is_logistic_flow(self):
        logistic_order_content_type = ContentType.objects.get(
            app_label='logistic',
            model='logisticorder',
        )
        return self.content_type_id == logistic_order_content_type.id

    @property
    def mailing_flow_settings(self):
        flow_designation = self.flow_designation

        try:
            return MailingFlowSettings.objects.get(flow_designation=flow_designation)
        except MailingFlowSettings.DoesNotExist:
            logger.error('MailingFlowSettings for %s does not exist.', flow_designation)

    @staticmethod
    def get_mailing_statistics(queryset):
        # TODO: Optimize me, currently generates timeouts
        result = OrderedDict()
        return result

        qs = queryset.filter(
            email_address__isnull=False,
            status__in=[
                MailingStatus.SUCCESS,
                MailingStatus.GOAL,
            ],
        )
        for flow_designation in MailingFlowSettings.objects.filter(
            active=True,
        ).values_list(
            'flow_designation',
            flat=True,
        ):
            result[flow_designation] = {}
            fd_qs = qs.filter(flow_designation=flow_designation)
            for template in (
                fd_qs.order_by('template_class')
                .distinct('template_class')
                .values_list('template_class', flat=True)
            ):
                result[flow_designation][template] = {}
                t_qs = fd_qs.filter(template_class=template)
                for lang in (
                    t_qs.order_by('email_language')
                    .distinct('email_language')
                    .values_list('email_language', flat=True)
                ):
                    l_qs = t_qs.filter(email_language=lang)
                    result[flow_designation][template][lang] = {
                        'sent': l_qs.count(),
                        'opened': l_qs.aggregate(opens_count=Sum('opens'))[
                            'opens_count'
                        ],
                        'clicked': l_qs.aggregate(clicks_count=Sum('clicks'))[
                            'clicks_count'
                        ],
                        'goals': l_qs.filter(status=MailingStatus.GOAL).count(),
                        'emails_count': l_qs.order_by('email_address')
                        .distinct('email_address')
                        .count(),
                    }
        return result


class MailingFlowSettings(models.Model):
    flow_designation = models.CharField(max_length=128, db_index=True, unique=True)
    active = models.BooleanField(default=False)

    class Meta:
        verbose_name = 'Mailing flow settings'
        verbose_name_plural = verbose_name

    def __str__(self):
        return 'MailingFlow[name={} active={}]'.format(
            self.flow_designation, self.active
        )


class RetargetingBlacklist(models.Model):
    email = models.EmailField(max_length=200, unique=True)
    source = models.CharField(max_length=200, null=True, blank=True)  # noqa: DJ001
    date_added = models.DateTimeField(auto_now=True)

    def __str__(self):
        return 'RetargetingBlacklist[email={} date_added={}]'.format(
            self.email, self.date_added
        )

    def save(self, from_mailchimp=False, *args, **kwargs):
        super(RetargetingBlacklist, self).save(*args, **kwargs)
        # also revoke any planned flows
        email = self.email
        MailingSchedule.revoke_all_for_email(email)


class MailingSchedule(models.Model):
    flow_designation = models.CharField(max_length=64, db_index=True)
    origin = models.ForeignKey(
        MailingFlowStatus,
        on_delete=models.CASCADE,
    )
    scheduled_at = models.DateTimeField()
    status = models.CharField(
        max_length=10, choices=MailingScheduleStatus.choices, default='waiting'
    )

    class Meta:
        get_latest_by = 'scheduled_at'

    @staticmethod
    def filter_designated(designation):
        now = timezone.now()
        return MailingSchedule.objects.filter(flow_designation=designation).filter(
            status=MailingScheduleStatus.WAITING,
            scheduled_at__lte=now,
            scheduled_at__gt=now - timedelta(hours=2),
        )

    @staticmethod
    def filtered_for_processing(designation, max_length=None):
        qs = MailingSchedule.filter_designated(designation).values_list('id', flat=True)
        if max_length:
            qs = qs[:max_length]
        filtered_ids = list(qs)
        filtered = MailingSchedule.objects.filter(id__in=filtered_ids)
        filtered.update(status=MailingScheduleStatus.PROCESSING)
        return filtered

    @staticmethod
    def revoke_for_email(designation, email):
        return MailingSchedule.objects.filter(
            flow_designation=designation,
            status__in=(
                MailingScheduleStatus.WAITING,
                MailingScheduleStatus.PROCESSING,
            ),
            origin__email_address=email,
        ).update(status=MailingScheduleStatus.REVOKED)

    @staticmethod
    def set_sent_for_email(designation, email):
        return MailingSchedule.objects.filter(
            flow_designation=designation,
            status__in=(
                MailingScheduleStatus.WAITING,
                MailingScheduleStatus.PROCESSING,
            ),
            origin__email_address=email,
        ).update(status=MailingScheduleStatus.SENT)

    @staticmethod
    def set_sent_for_origin_id(designation, origin_id):
        return MailingSchedule.objects.filter(
            flow_designation=designation,
            status__in=(
                MailingScheduleStatus.WAITING,
                MailingScheduleStatus.PROCESSING,
            ),
            origin__id=origin_id,
        ).update(status=MailingScheduleStatus.SENT)

    def save(self, *args, **kwargs):  # noqa: DJ012
        # ATTENTION to this overwrite below
        if settings.IS_DEV or settings.IS_TESTING:
            self.scheduled_at = timezone.now() + timedelta(minutes=2)
        super(MailingSchedule, self).save(*args, **kwargs)

    @staticmethod
    def revoke_all_for_email(email):
        MailingSchedule.objects.filter(
            origin__email_address=email, status=MailingScheduleStatus.WAITING
        ).update(status=MailingScheduleStatus.REVOKED)


class ProductDeliveredBlacklistedEmail(models.Model):
    email = models.EmailField()

    def __str__(self):
        return self.email


class MailChimpTaskRetry(models.Model):
    RETRY_EVERY_SEC = [129600, 43200, 18000, 3600, 300, 60]

    scheduled_at = models.DateTimeField(db_index=True, null=True)
    status = models.IntegerField(
        choices=MailChimpSatus.choices, default=MailChimpSatus.RETRY
    )
    retry = models.IntegerField(default=len(RETRY_EVERY_SEC) - 1)
    kwargs = models.JSONField(blank=True, null=False)
    fail_info = models.JSONField(blank=True, null=True, default=None)
    created_at = models.DateTimeField(
        'Created at', default=timezone.now, editable=False, db_index=True
    )

    class Meta:
        verbose_name = 'Mailchimp task retry'
        verbose_name_plural = 'Mailchimp task retries'

    def __str__(self):
        return 'MailChimpTaskRetry'


class MandrillReportHistory(models.Model):
    for_date = models.DateField()
    report_id = models.CharField(max_length=128)
    is_imported = models.BooleanField(default=False)

    class Meta:
        verbose_name = 'Mandrill report history'
        verbose_name_plural = verbose_name


class MandrillTagReportHistory(models.Model):
    tag = models.CharField(max_length=64)
    last_record_time = models.DateTimeField()

    class Meta:
        verbose_name = 'Mandrill tag report history'
        verbose_name_plural = verbose_name


class MandrillRejectReportHistory(models.Model):
    last_record_time = models.DateTimeField()

    class Meta:
        verbose_name = 'Mandrill reject report history'
        verbose_name_plural = verbose_name


def orders_data_default():
    return {
        'customer_order_details': {
            'ids': [],
            'orders_count': 0,
            'assembly': [],
            'order_source': [],
            'countries': [],
        }
    }


# TODO maybe move it to Custom? but we need do about 4 migration files
class Customer(models.Model):
    email = models.EmailField(unique=True, help_text='Email matched from Order email')
    email_owner = models.EmailField(
        help_text='Email matched from Order owner email',
        blank=True,
    )
    customer_type = models.PositiveSmallIntegerField(
        choices=CustomerTypeChoices.choices,
    )
    orders_data = models.JSONField(null=True, blank=True, default=orders_data_default)

    objects = CustomerManager()

    @classmethod
    def can_be_cs_customer(cls, order):
        from orders.models import Order
        from orders.utils import build_or_query_nullable_excluded

        order_email = order.email
        order_owner_email = order.owner.email

        if cls.objects.filter(
            build_or_query_nullable_excluded(
                {'email': order_email, 'email_owner': order_owner_email}
            )
        ).exists():
            return True
        # exists() because actual order is still cart. So it isn't in corrected statuses
        # If someone wanna use that in other situation must change query
        return (
            Order.objects.filter_with_proxy_by_query(
                build_or_query_nullable_excluded(
                    {'email': order_email, 'owner_email': order_owner_email}
                )
            )
            .filter(status__in=cls.get_correct_statuses())
            .exclude(id=order.id)
            .exists()
        )

    @staticmethod
    def get_correct_statuses():
        return {
            OrderStatus.IN_PRODUCTION,
            OrderStatus.TO_BE_SHIPPED,
            OrderStatus.SHIPPED,
            OrderStatus.DELIVERED,
        }

    def generate_newsletter_voucher(self):
        voucher = Voucher.objects.create(
            kind_of=VoucherType.PERCENTAGE,
            code=Voucher.generate_code(
                inside_string='pfi25',
                inside_string_at_beginning=True,
                character_count=9,
            ),
            creator=User.objects.get(username='admin'),
            origin=VoucherOrigin.MAILING,
            start_date=timezone.now(),
            end_date=timezone.now() + timedelta(days=14),
            value=Decimal('25'),
            active=True,
            quantity=1,
            quantity_left=1,
            for_email=self.email,
            amount_starts=Decimal('1'),
            amount_limit=Decimal('100000'),
        )
        return voucher

    def __str__(self):  # noqa: DJ012
        return self.email

    def add_order_data(self, order):
        if 'customer_order_details' not in self.orders_data:
            self.orders_data = copy.deepcopy(orders_data_default())

        if self.is_all_data_filled(order):
            return

        customer_order_details = self.orders_data['customer_order_details']
        if order.id not in customer_order_details['ids']:
            customer_order_details['ids'].append(order.id)

        orders_count = len(customer_order_details['ids'])
        customer_order_details['orders_count'] = orders_count
        customer_order_details['assembly'].append(order.assembly)
        customer_order_details['order_source'].append(order.order_source)
        customer_order_details['countries'].append(order.country)
        self.save(update_fields=['orders_data'])

    def is_all_data_filled(self, order):
        customer_order_details = self.orders_data['customer_order_details']
        return all(
            [
                order.id in customer_order_details['ids'],
                customer_order_details['assembly'],
                customer_order_details['countries'],
                customer_order_details['order_source'],
            ]
        )


class MailingCustomer(Customer):
    objects = MailingCustomerManager()

    class Meta:
        proxy = True
