import csv
import io
import json
import logging
import os
import re
import subprocess  # noqa: S404
import tempfile
import urllib.error
import urllib.parse
import urllib.request

from datetime import (
    datetime,
    timedelta,
)
from zipfile import ZipFile

from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.mail import send_mail
from django.utils import timezone

import requests

from celery import shared_task
from celery.utils.log import get_task_logger
from google.cloud import bigquery
from google.cloud.exceptions import BadRequest

from custom.metrics import task_metrics
from custom.utils.decorators import notify_after
from custom.utils.exports import dump_list_as_txt
from kpi.big_query import (
    export_list_to_big_query,
    upload_list_to_big_query,
)
from mailing.enums import MailchimpListStatus
from mailing.models import (
    MailingCustomer,
    MandrillRejectReportHistory,
    MandrillReportHistory,
    MandrillTagReportHistory,
)
from mailing.serializers import MailingReportSerializer
from mailing.utils import (
    create_export_for_date,
    get_class_from_module_name,
    hash_email,
    mandrill_request,
)
from regions.constants import OTHER_REGION_NAME

task_logger = get_task_logger(__name__)
mailer_logger = logging.getLogger('django_mailer')
User = get_user_model()


@shared_task
@task_metrics
# add to celery schedule if needed
def mailchimp_subscribe_to_waiting_list(
    language, email, location, region_name, other_countries=False
):
    data = {
        'email_address': email,
        'language': language,
        'status': MailchimpListStatus.SUBSCRIBED.value,
    }
    if location:
        latitude, longitude = location.split(',')
        data['location'] = {
            'latitude': float(latitude),
            'longitude': float(longitude),
        }
    if other_countries:
        list_id = settings.MAILCHIMP_T03_OTHER_COUNTRIES_LIST
        interest = settings.MAILCHIMP_T03_COUNTRIES_INTERESTS.get(
            region_name,
            settings.MAILCHIMP_T03_COUNTRIES_INTERESTS[OTHER_REGION_NAME],
        )
        data['interests'] = {
            interest: True,
        }
    else:
        list_id = settings.MAILCHIMP_WAITING_LIST
    subscriber_hash = hash_email(email)
    url = (
        f'https://us10.api.mailchimp.com/3.0/lists/{list_id}/members/{subscriber_hash}'
    )
    response = requests.put(
        url,
        data=json.dumps(data),
        auth=('apikey', settings.MAILCHIMP_API_KEY),
        verify=True,
    )
    if response.status_code != 200:
        task_logger.error('Unable to subscribe %s to waiting list', email)


@shared_task
@task_metrics
def mandrill_activity_fetch_export():
    for i in range(1, settings.MANDRILL_REPORT_CHECK_DAYS_BACK):
        day = timezone.now() - timedelta(days=i)
        if not MandrillReportHistory.objects.filter(for_date=day).exists():
            create_export_for_date(day)


def get_mandrill_tag_list():
    payload = {
        'key': settings.EMAIL_HOST_PASSWORD,
    }
    resp = mandrill_request('/tags/list.json', payload=payload)
    pattern = re.compile(r'^(z_)?([0-9]+)$')
    resp_json = resp.json()
    return [tag['tag'] for tag in resp_json if not re.match(pattern, tag['tag'])]


def mandrill_tag_time_series_import_to_bigquery(tag_name):
    payload = {'key': settings.EMAIL_HOST_PASSWORD, 'tag': tag_name}
    resp = mandrill_request('/tags/time-series.json', payload=payload)
    resp_json = resp.json()
    try:
        last_report = MandrillTagReportHistory.objects.filter(tag=tag_name).latest(
            'last_record_time'
        )
    except MandrillTagReportHistory.DoesNotExist:
        last_report = None

    data_batch = []
    date_format = '%Y-%m-%d %H:%M:%S'
    report_date = None
    for record in resp_json:
        record_date = datetime.strptime(record['time'], date_format)
        if not last_report or record_date > last_report.last_record_time:
            record['tag'] = tag_name
            data_batch.append(record)
            if not report_date or record_date > report_date:
                report_date = record_date
    if report_date:
        export_list_to_big_query(
            settings.BQ_MAILING_REPORT_DATASET,
            settings.BQ_MAILING_TAG_REPORT_TABLE,
            data_batch,
            write=bigquery.WriteDisposition.WRITE_APPEND,
        )
        MandrillTagReportHistory.objects.create(
            tag=tag_name, last_record_time=report_date
        )


@shared_task
@task_metrics
def mandrill_rejects_import_to_bigquery():
    payload = {
        'key': settings.EMAIL_HOST_PASSWORD,
        'include_expired': True,
    }
    resp = mandrill_request('/rejects/list.json', payload=payload)
    resp_json = resp.json()
    try:
        last_report = MandrillRejectReportHistory.objects.latest('last_record_time')
    except MandrillRejectReportHistory.DoesNotExist:
        last_report = None

    date_format = '%Y-%m-%d %H:%M:%S'
    data_batch = []
    report_date = None

    for record in resp_json:
        record_date = datetime.strptime(record['last_event_at'], date_format)
        if not last_report or record_date > last_report.last_record_time:
            data_batch.append(record)
            if not report_date or record_date > report_date:
                report_date = record_date

    if report_date:
        export_list_to_big_query(
            settings.BQ_MAILING_REPORT_DATASET,
            settings.BQ_MAILING_REJECTS_REPORT_TABLE,
            data_batch,
            write=bigquery.WriteDisposition.WRITE_APPEND,
        )
        MandrillRejectReportHistory.objects.create(last_record_time=report_date)


@shared_task
@task_metrics
def mandrill_tags_report_import():
    mailing_tags = get_mandrill_tag_list()
    for tag in mailing_tags:
        mandrill_tag_time_series_import_to_bigquery(tag)


@shared_task
@task_metrics
def mandrill_activity_import_to_bigquery():
    for report in MandrillReportHistory.objects.filter(is_imported=False):
        try:
            load_report_to_biguery(report.report_id)
        except BadRequest as ex:  # noqa: PERF203
            for er in ex.errors:
                task_logger.exception(
                    'Error while importing bigquery from google.cloud {} {}'.format(
                        'mandrill_activity_import', er
                    )
                )
        else:
            report.is_imported = True
            report.save()


def load_report_to_biguery(report_id):
    headers = {'Content-Type': 'application/json; charset=utf-8'}
    payload = {'key': settings.EMAIL_HOST_PASSWORD, 'id': report_id}
    resp = requests.post(
        settings.MANDRILL_API_URL + '/exports/info.json',
        json=payload,
        verify=False,  # noqa: S501
        headers=headers,
    )

    try:
        resp.raise_for_status()
    except requests.HTTPError:
        return

    response_json = resp.json()
    report_url = response_json['result_url']
    save_and_unzip_report(report_url)

    big_query_batch = []
    csv_path = tempfile.gettempdir() + '/activity.csv'
    with open(csv_path, 'r') as report_file:
        for row in csv.reader(report_file):
            data = {
                'created_at': row[0],
                'email': row[1],
                'sender': row[2],
                'subject': row[3],
                'status': row[4],
                'tags': row[5],
                'subaccount': row[6],
                'opens': row[7],
                'clicks': row[8],
                'bounce_detail': row[9],
            }
            if len(row) > 10:
                data['metadata'] = ';'.join(row[10:])
            serializer = MailingReportSerializer(data=data)
            if serializer.is_valid():
                big_query_batch.append(serializer.validated_data)
    upload_list_to_big_query(
        settings.BQ_MAILING_REPORT_DATASET,
        settings.BQ_MAILING_REPORT_TABLE,
        big_query_batch,
        write=bigquery.WriteDisposition.WRITE_APPEND,
    )
    try:
        os.remove(csv_path)
    except OSError:
        pass


def save_and_unzip_report(report_url):
    result_file = urllib.request.urlopen(report_url, timeout=5)  # noqa: S310
    with tempfile.NamedTemporaryFile(suffix='.zip') as named_tmp_file:
        named_tmp_file.write(result_file.read())
        with ZipFile(named_tmp_file) as zip_file:
            zip_file.extractall(tempfile.gettempdir())


@shared_task
@task_metrics
def logwatch_report():
    output = subprocess.getstatusoutput(  # noqa: S605
        'sudo rm -rf /tmp/logwatch.* && sudo logwatch --detail Med --range today'  # noqa: S607
    )
    send_mail(
        'Logwatch report',
        output[1],
        'Logwatch <<EMAIL>>',
        [a[1] for a in settings.ADMINS],
        fail_silently=False,
    )


@shared_task
def export_mailing_statistics(mailing_stats, notify_to):
    @notify_after(
        body='',
        attachment_base_name='mailing_stats',
        attachment_extension='csv',
        attachment_mime='text/csv',
    )
    def _export_mailing_statistics(mailing_stats):
        rows = []
        for flow_designation, templates in list(mailing_stats.items()):
            for template_class, languages in list(templates.items()):
                for language, row in list(languages.items()):
                    rows.append(
                        [
                            flow_designation,
                            template_class,
                            language,
                            row['sent'],
                            row['opened'],
                            row['clicked'],
                            row['goals'],
                            row['emails_count'],
                        ]
                    )
        header_row = [
            'flow',
            'template',
            'language',
            'sent',
            'opened',
            'clicked',
            'goals',
            'emails',
        ]
        string_buffer = io.StringIO()
        writer = csv.writer(string_buffer, delimiter=';')
        writer.writerow(header_row)
        for r in rows:
            writer.writerow(r)
        return string_buffer

    subject = 'Mailing statistics %s' % timezone.now().strftime('%d/%m/%Y %H:%M')
    _export_mailing_statistics.delay(mailing_stats, __subject=subject, __to=[notify_to])


@shared_task
def mailing_flow_run(mailing_flow_class_name):
    mailing_flow_class = get_class_from_module_name(
        settings.MAILING_FLOWS_MODULE, mailing_flow_class_name
    )
    if mailing_flow_class and mailing_flow_class.get_or_create_mailing_flow_settings():
        mailing_flow_class().process(limit=settings.MAILING_FLOW_THRESHOLD)


@shared_task
def generate_file_with_clients_emails(email):
    """
    Task to generate and send file with clients (owner of at least one delivered
    shelf or wardrobe - only sample box owners are excluded) emails.
    """
    today = datetime.now().date()
    results = MailingCustomer.objects.values_list('email', flat=True)
    filename = f'_exclude_{today}.txt'
    mail_body = f'File with clients emails. Situation as at {today}.'
    dump_list_as_txt(
        results,
        output=filename,
        mail=(email,),
        mail_body=mail_body,
        mail_subject='Tylko clients emails.',
    )
