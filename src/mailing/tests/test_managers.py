import pytest

from mailing.enums import CustomerTypeChoices
from mailing.models import Customer


@pytest.mark.django_db
class TestCustomerManager:
    @pytest.mark.parametrize(
        'customer_type',
        [CustomerTypeChoices.CS_CUSTOMER, CustomerTypeChoices.MAILING_CUSTOMER],
    )
    def test_update_or_create_customer_set_type_when_object_created(
        self, customer_type, customer_factory, order_factory
    ):
        email = '<EMAIL>'
        order = order_factory(email=email)
        customer = customer_factory(email=email, customer_type=customer_type)

        Customer.objects.update_or_create_customer_and_set_type(order=order)
        customer.refresh_from_db()

        assert customer.customer_type == CustomerTypeChoices.MAILING_AND_CS_CUSTOMER
