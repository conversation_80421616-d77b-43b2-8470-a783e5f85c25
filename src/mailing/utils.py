import hashlib
import importlib
import logging
import os
import re

from datetime import datetime
from email.mime.base import MIMEBase

from django.conf import settings
from django.core.mail import (
    EmailMultiAlternatives,
    SafeMIMEMultipart,
)
from django.db.models import (
    <PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    Exists,
    F,
    OuterRef,
    Q,
    When,
)
from django.utils import timezone
from django.utils.encoding import force_bytes
from rest_framework import status

import requests

from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

from gallery.enums import FurnitureStatusEnum
from gallery.models import Jetty
from mailing.models import (
    MailingCustomer,
    MandrillReportHistory,
)
from orders.enums import OrderStatus
from orders.models import (
    Order,
    PaidOrders,
)
from user_profile.choices import SubscriptionSources
from user_profile.models import UserProfile

logger = logging.getLogger('cstm')


def mailing_metric_key_for(designation, metric_type):
    return 'backend.mailing.{}.{}'.format(designation, metric_type.value)


def new_renders_metric_key_for(designation, metric_type):
    return 'backend.mailing.new_renders.{}.{}'.format(designation, metric_type.value)


def mailing_metric_perfo_key_for(designation, metric_type):
    return 'backend.mailing_perf.{}.{}'.format(designation, metric_type.value)


def add_any_email(queryset):
    """Used with UserProfile qs"""
    return queryset.annotate(
        any_email=Case(
            When(email__exact='', then=None),
            When(email__isnull=False, then=F('email')),
            When(user__email__exact='', then=None),
            When(user__email__isnull=False, then=F('user__email')),
            When(user__order__email__exact='', then=None),
            When(user__order__email__isnull=False, then=F('user__order__email')),
            default=None,
            output_field=CharField(),
        )
    )


def has_paid_orders(queryset):
    """Used with UserProfile qs"""
    paid_orders_subquery = PaidOrders.objects.filter(owner__profile=OuterRef('id'))
    return queryset.annotate(has_paid_orders=Exists(paid_orders_subquery))


def has_paid_orders_by_email(queryset, outer_ref_name):
    """Used with UserProfile qs"""
    paid_orders_subquery = PaidOrders.objects.filter(email=OuterRef(outer_ref_name))
    return queryset.annotate(has_paid_orders=Exists(paid_orders_subquery))


def has_saved_items(queryset):
    """Used with UserProfile qs"""
    saved_jetties_subquery = Jetty.objects.filter(
        owner__profile=OuterRef('id'), furniture_status=FurnitureStatusEnum.SAVED
    )
    return queryset.annotate(has_saved_items=Exists(saved_jetties_subquery))


def has_items_in_cart(queryset):
    items_subquery = Order.objects.filter(
        owner__profile=OuterRef('id'), status=OrderStatus.CART, items__isnull=False
    )
    return queryset.annotate(has_items_in_cart=Exists(items_subquery))


def items_in_wishlist_or_cart():
    qs = UserProfile.objects.filter(
        user__date_joined__gte=timezone.datetime(2017, 1, 1)
    )
    qs = add_any_email(qs)
    qs = has_paid_orders(qs)
    qs = has_saved_items(qs)
    qs = has_items_in_cart(qs)
    qs = qs.filter(has_paid_orders=False, any_email__isnull=False).filter(
        Q(has_saved_items=True) | Q(has_items_in_cart=True)
    )
    return qs.values_list('id', 'any_email').distinct()


def mandrill_request(url, payload=None):
    session = requests.Session()
    # Mandrill sometimes returns 500 for known errors(eg. reports queue is full)
    retry = Retry(
        total=5,
        read=5,
        connect=5,
        backoff_factor=2,
        status_forcelist=[status.HTTP_500_INTERNAL_SERVER_ERROR],
    )
    adapter = HTTPAdapter(max_retries=retry)
    session.mount('http://', adapter)
    session.mount('https://', adapter)
    headers = {'Content-Type': 'application/json; charset=utf-8'}
    full_url = '{}{}'.format(settings.MANDRILL_API_URL, url)
    resp = session.post(full_url, json=payload, verify=False, headers=headers)
    try:
        resp.raise_for_status()
    except requests.HTTPError as ex:
        logger.error(
            ('Got HTTP error requesting Mandrill %s endpoint: %s [%s]'),
            url,
            resp.text,
            resp.status_code,
        )
        raise ex
    return resp


def create_export_for_date(report_date):
    try:
        start_date = datetime(
            day=report_date.day,
            month=report_date.month,
            year=report_date.year,
            hour=0,
            minute=0,
            second=0,
        )
        end_date = datetime(
            day=report_date.day,
            month=report_date.month,
            year=report_date.year,
            hour=23,
            minute=59,
            second=59,
        )
        date_format = '%Y-%m-%d %H:%M:%S'
        payload = {
            'key': settings.EMAIL_HOST_PASSWORD,
            'date_from': start_date.strftime(date_format),
            'date_to': end_date.strftime(date_format),
            'senders': settings.MANDRILL_REPORT_SENDER_LIST,
            'states': ['sent'],
        }
        resp = mandrill_request('/exports/activity.json', payload=payload)
        resp_json = resp.json()
        MandrillReportHistory.objects.create(
            for_date=start_date.date(), report_id=resp_json['id']
        )
    except Exception as exc:
        logger.exception('Error during activity export')
        raise exc


class EmailMultiRelated(EmailMultiAlternatives):
    """
    A version of EmailMessage that makes it easy to send multipart/related
    messages. For example, including text and HTML versions with inline images.
    """

    related_subtype = 'related'

    def __init__(
        self,
        subject='',
        body='',
        from_email=None,
        to=None,
        bcc=None,
        connection=None,
        attachments=None,
        headers=None,
        alternatives=None,
    ):
        # self.related_ids = []
        self.related_attachments = []
        super(EmailMultiRelated, self).__init__(
            subject,
            body,
            from_email,
            to,
            bcc,
            connection,
            attachments,
            headers,
            alternatives,
        )

    def attach_related(self, filename=None, content=None, mimetype=None):
        """
        Attaches a file with the given filename and content. The filename can
        be omitted and the mimetype is guessed, if not provided.

        If the first parameter is a MIMEBase subclass it is inserted directly
        into the resulting message attachments.
        """
        if isinstance(filename, MIMEBase):
            assert content == mimetype is None
            self.related_attachments.append(filename)
        else:
            assert content is not None
            self.related_attachments.append((filename, content, mimetype))

    def attach_related_file(self, path, mimetype=None):
        """Attaches a file from the filesystem."""
        filename = os.path.basename(path)
        content = open(path, 'rb').read()
        self.attach_related(filename, content, mimetype)

    def _create_message(self, msg):
        return self._create_attachments(
            self._create_related_attachments(self._create_alternatives(msg))
        )

    def _create_alternatives(self, msg):
        for i, (content, mimetype) in enumerate(self.alternatives):
            if mimetype == 'text/html':
                for filename, _, _ in self.related_attachments:
                    content = re.sub(
                        r'(?<!cid:){}'.format(re.escape(filename)),
                        'cid:{}'.format(filename),
                        content,
                    )
                self.alternatives[i] = (content, mimetype)

        return super(EmailMultiRelated, self)._create_alternatives(msg)

    def _create_related_attachments(self, msg):
        encoding = self.encoding or settings.DEFAULT_CHARSET
        if self.related_attachments:
            body_msg = msg
            msg = SafeMIMEMultipart(_subtype=self.related_subtype, encoding=encoding)
            if self.body:
                msg.attach(body_msg)
            for related in self.related_attachments:
                msg.attach(self._create_related_attachment(*related))
        return msg

    def _create_related_attachment(self, filename, content, mimetype=None):
        """
        Convert the filename, content, mimetype triple into a MIME attachment
        object. Adjust headers to use Content-ID where applicable.
        Taken from http://code.djangoproject.com/ticket/4771
        """
        attachment = super(EmailMultiRelated, self)._create_attachment(
            filename, content, mimetype
        )
        if filename:
            mimetype = attachment['Content-Type']
            del attachment['Content-Type']
            del attachment['Content-Disposition']
            attachment.add_header('Content-Disposition', 'inline', filename=filename)
            attachment.add_header('Content-Type', mimetype, name=filename)
            attachment.add_header('Content-ID', '<{}>'.format(filename))
        return attachment


def get_class_from_module_name(module_name, class_name):
    try:
        module_ = importlib.import_module(module_name)
        try:
            return getattr(module_, class_name)
        except AttributeError:
            return None
    except ImportError:
        return None


def get_customer(newsletter_source, email):
    if newsletter_source != SubscriptionSources.NEWSLETTER_FOR_CUSTOMERS:
        return

    try:
        return MailingCustomer.objects.get(email__iexact=email)
    except MailingCustomer.DoesNotExist:
        return


def should_use_new_renders(items_list):
    return all([getattr(item.s4l_render, 'url', None) for item in items_list])  # noqa: C419


def hash_email(email):
    return hashlib.md5(force_bytes(email.lower())).hexdigest()  # noqa: S324


def email_exists_in_list(list_id, email):
    subscriber_hash = hash_email(email)
    response = requests.get(
        'https://us10.api.mailchimp.com/3.0/lists/{}/members/{}'.format(
            list_id,
            subscriber_hash,
        ),
        auth=('apikey', settings.MAILCHIMP_API_KEY),
        verify=True,
    )
    return response.status_code == 200
