from django.urls import (
    include,
    path,
)
from rest_framework.routers import Default<PERSON>out<PERSON>

from material_recovery.api.views import (
    MaterialRecoverySampleBoxViewSet,
    MaterialRecoveryWarehouseViewSet,
    ProductMaterialRecoveryAppConfigurationView,
    ProductMaterialRecoveryReportViewSet,
    ProductMaterialRecoveryViewSet,
    ShelfMarketViewSet,
)

router = DefaultRouter()


router.register(
    'samples',
    MaterialRecoverySampleBoxViewSet,
    basename='product-material-recovery-samples',
)
router.register(
    'shelf_market',
    ShelfMarketViewSet,
    basename='product-material-recovery-shelf-market',
)
router.register(
    'reports',
    ProductMaterialRecoveryReportViewSet,
    basename='product-material-recovery-report',
)

router.register(
    'warehouse',
    MaterialRecoveryWarehouseViewSet,
    basename='product-material-recovery-warehouse',
)
router.register(
    '',
    ProductMaterialRecoveryViewSet,
    basename='product-material-recovery',
)
urlpatterns = [
    path(
        'product_material_recovery_configuration/',
        ProductMaterialRecoveryAppConfigurationView.as_view(),
        name='product-material-recovery-configuration',
    ),
    path('', include(router.urls)),
]
