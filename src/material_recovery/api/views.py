from django.utils import timezone
from rest_framework import (
    mixins,
    status,
    viewsets,
)
from rest_framework.decorators import action
from rest_framework.generics import get_object_or_404
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.settings import api_settings
from rest_framework.views import APIView

from django_filters.rest_framework import DjangoFilterBackend

from material_recovery.api.filter_sets import ProductMaterialRecoveryFilterSet
from material_recovery.api.serializers import (
    BulkIdsParamSerializer,
    CreateProductMaterialRecoveryReportSerializer,
    CreateSampleBoxMaterialRecoveryReportSerializer,
    CreateShelfMarketReportSerializer,
    CreateShelfMarketSerializer,
    DecreaseAmountSerializer,
    MaterialRecoveryReportSerializer,
    MaterialRecoverySampleBoxSerializer,
    MaterialRecoveryWarehouseSerializer,
    ProductColorNameSerializer,
    ProductMaterialRecoverySerializer,
    ShelfMarketSerializer,
)
from material_recovery.models import (
    MaterialRecoveryReport,
    MaterialRecoverySampleBox,
    MaterialRecoveryWarehouse,
    ShelfMarket,
)
from material_recovery.permissions import MaterialRecoveryPanelPermission
from producers.models import Product


class ProductMaterialRecoveryAppConfigurationView(APIView):
    def get(self, request, format=None):
        return Response(
            {
                'color_options': ProductColorNameSerializer.from_choices().data,
            }
        )


class ProductMaterialRecoveryViewSet(
    mixins.ListModelMixin,
    mixins.RetrieveModelMixin,
    viewsets.GenericViewSet,
):
    filter_backends = [DjangoFilterBackend]
    filter_fields = ('is_recovered',)
    filterset_class = ProductMaterialRecoveryFilterSet
    permission_classes = (
        IsAuthenticated,
        MaterialRecoveryPanelPermission,
    )
    serializer_class = ProductMaterialRecoverySerializer

    def get_queryset(self):
        return (
            Product.objects.filter(
                manufactor__isnull=False,
            )
            .select_related(
                'manufactor',
                'order',
                'order_item',
                'product_details_jetty',
                'product_details_watty',
            )
            .prefetch_related(
                'order_item__order_item',
            )
            .order_by('-recovered_at')
        )

    @action(
        methods=['post'],
        detail=True,
        url_path='mark_as_recovered',
        url_name='mark-as-recovered',
    )
    def mark_as_recovered(self, request, pk):
        product = get_object_or_404(Product, id=pk)
        if product.is_recovered:
            return Response(
                {api_settings.NON_FIELD_ERRORS_KEY: ['Product is already added']},
                status=status.HTTP_400_BAD_REQUEST,
            )
        if ShelfMarket.objects.filter(product=product).exists():
            return Response(
                {
                    api_settings.NON_FIELD_ERRORS_KEY: [
                        'Product is already added to shelf market'
                    ]
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        if product.order_item.free_return is None:
            return Response(
                {api_settings.NON_FIELD_ERRORS_KEY: ['Only Free returns can be added']},
                status=status.HTTP_400_BAD_REQUEST,
            )

        product.is_recovered = True
        product.recovered_at = timezone.now()
        product.save(update_fields=['is_recovered', 'recovered_at'])
        return Response(status=status.HTTP_202_ACCEPTED)

    @action(
        methods=['post'],
        detail=False,
        url_path='mark_as_not_recovered',
        url_name='mark-as-not-recovered',
    )
    def mark_as_not_recovered(self, request):
        query_params_serializer = BulkIdsParamSerializer(
            data=request.query_params,
            context={'queryset': Product.objects.filter(recovery_report=None)},
        )
        query_params_serializer.is_valid(raise_exception=True)

        products = query_params_serializer.validated_data['ids']
        for product in products:
            product.is_recovered = False
            product.recovered_at = None
            product.save(update_fields=['is_recovered', 'recovered_at'])

        return Response(status=status.HTTP_202_ACCEPTED)

    @action(
        methods=['get'],
        detail=True,
        url_path='product_info',
        url_name='product-info',
    )
    def get_product_info(self, request, pk):
        product = get_object_or_404(Product, id=pk)
        serializer = ProductMaterialRecoverySerializer(product)
        return Response(serializer.data, status.HTTP_200_OK)

    @action(
        methods=['post'],
        detail=False,
        url_path='create_report',
        url_name='create-report',
    )
    def create_report(self, request):
        serializer = CreateProductMaterialRecoveryReportSerializer(
            data=request.data, many=True
        )
        serializer.is_valid(raise_exception=True)
        report = serializer.save()
        return Response(
            MaterialRecoveryReportSerializer(report).data, status=status.HTTP_200_OK
        )


class MaterialRecoverySampleBoxViewSet(viewsets.ModelViewSet):
    filter_backends = [DjangoFilterBackend]
    serializer_class = MaterialRecoverySampleBoxSerializer
    permission_classes = (
        IsAuthenticated,
        MaterialRecoveryPanelPermission,
    )

    def get_queryset(self):
        return MaterialRecoverySampleBox.objects.all().order_by('-id')

    def delete(self, request, *args, **kwargs):
        query_params_serializer = BulkIdsParamSerializer(
            data=request.query_params,
            context={
                'queryset': MaterialRecoverySampleBox.objects.filter(
                    recovery_report=None
                )
            },
        )
        query_params_serializer.is_valid(raise_exception=True)

        sample_boxes = query_params_serializer.validated_data['ids']
        for sample_box in sample_boxes:
            sample_box.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)

    @action(
        methods=['post'],
        detail=False,
        url_path='create_report',
        url_name='create-report',
    )
    def create_report(self, request):
        serializer = CreateSampleBoxMaterialRecoveryReportSerializer(
            data=request.data, many=True
        )
        serializer.is_valid(raise_exception=True)
        report = serializer.save()
        return Response(
            MaterialRecoveryReportSerializer(report).data, status=status.HTTP_200_OK
        )


class ShelfMarketViewSet(viewsets.ModelViewSet):
    filter_backends = [DjangoFilterBackend]
    permission_classes = (
        IsAuthenticated,
        MaterialRecoveryPanelPermission,
    )

    def get_serializer_class(self):
        if self.action in ['list', 'retrieve']:
            return ShelfMarketSerializer
        if self.action in ['create', 'update', 'partial_update', 'destroy']:
            return CreateShelfMarketSerializer
        return self.serializer_class

    def delete(self, request, *args, **kwargs):
        query_params_serializer = BulkIdsParamSerializer(
            data=request.query_params,
            context={'queryset': ShelfMarket.objects.filter(recovery_report=None)},
        )
        query_params_serializer.is_valid(raise_exception=True)

        shelf_markets = query_params_serializer.validated_data['ids']
        for sample_box in shelf_markets:
            sample_box.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)

    def get_queryset(self):
        return (
            ShelfMarket.objects.all()
            .select_related(
                'product__manufactor',
                'product__order',
                'product__order_item',
                'product__product_details_jetty',
                'product__product_details_watty',
            )
            .prefetch_related(
                'product__order_item__order_item',
            )
            .order_by('-id')
        )

    @action(
        methods=['post'],
        detail=False,
        url_path='create_report',
        url_name='create-report',
    )
    def create_report(self, request):
        serializer = CreateShelfMarketReportSerializer(data=request.data, many=True)
        serializer.is_valid(raise_exception=True)
        report = serializer.save()
        return Response(
            MaterialRecoveryReportSerializer(report).data, status=status.HTTP_200_OK
        )


class ProductMaterialRecoveryReportViewSet(
    mixins.ListModelMixin,
    mixins.DestroyModelMixin,
    viewsets.GenericViewSet,
):
    serializer_class = MaterialRecoveryReportSerializer
    permission_classes = (
        IsAuthenticated,
        MaterialRecoveryPanelPermission,
    )

    def delete(self, request, *args, **kwargs):
        query_params_serializer = BulkIdsParamSerializer(
            data=request.query_params,
            context={'queryset': self.get_queryset()},
        )
        query_params_serializer.is_valid(raise_exception=True)

        reports = query_params_serializer.validated_data['ids']
        for report in reports:
            report.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)

    def get_queryset(self):
        return MaterialRecoveryReport.objects.all().order_by('-id')


class MaterialRecoveryWarehouseViewSet(
    mixins.ListModelMixin,
    mixins.RetrieveModelMixin,
    viewsets.GenericViewSet,
):
    filter_backends = [DjangoFilterBackend]
    filter_fields = ('color', 'type')
    permission_classes = (
        IsAuthenticated,
        MaterialRecoveryPanelPermission,
    )
    serializer_class = MaterialRecoveryWarehouseSerializer

    def get_queryset(self):
        return MaterialRecoveryWarehouse.objects.all().order_by('-id')

    @action(
        methods=['post'],
        detail=False,
        url_path='decrease_amount',
        url_name='decrease-amount',
    )
    def decrease_amount(self, request):
        serializer = DecreaseAmountSerializer(data=request.data, many=True)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(status=status.HTTP_202_ACCEPTED)
