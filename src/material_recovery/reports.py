from dataclasses import dataclass
from decimal import Decimal
from typing import Dict

from django.core.files.base import ContentFile


@dataclass
class RecoveredItemWithCost:
    amount: int
    cost: Decimal


def calculate_recovered_total_waste_recycling(
    elements_by_color_and_name: Dict, fittings_by_name: Dict
) -> Decimal:
    total_waste_recycling = Decimal('0.0')
    for color, elements in elements_by_color_and_name.items():  # noqa: B007, PERF102
        for element_name, recovered_item in elements.items():  # noqa: B007, PERF102
            total_waste_recycling += recovered_item.cost

    for fitting_name, recovered_item in fittings_by_name.items():  # noqa: B007, PERF102
        total_waste_recycling += recovered_item.cost

    return total_waste_recycling


def buffer_to_csv_file(csv_buffer):
    return ContentFile(csv_buffer.getvalue().encode('utf-8'))


def format_decimal_with_comma(value: Decimal) -> str:
    return str(value).replace('.', ',')
