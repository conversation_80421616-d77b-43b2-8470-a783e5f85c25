import csv

from collections import defaultdict
from decimal import Decimal
from io import <PERSON><PERSON>
from typing import (
    TYPE_CHECKING,
    Dict,
    List,
)

from django.core.files.base import ContentFile

from gallery.models import SampleBox
from material_recovery.constants import SAMPLE_BOX_COLOR_TO_MATERIAL_CODENAMES
from material_recovery.reports import (
    RecoveredItemWithCost,
    buffer_to_csv_file,
    format_decimal_with_comma,
)
from producers.cost_calculations import get_average_price

if TYPE_CHECKING:
    from material_recovery.models import MaterialRecoveryReport


def calculate_samples_total_waste_recycling(samples_by_color: dict) -> Decimal:
    return sum([samples.cost for samples in samples_by_color.values()])  # noqa: C419


def count_waste_recycling(amount, color) -> Decimal:
    codename = SAMPLE_BOX_COLOR_TO_MATERIAL_CODENAMES.get(color)
    if codename is None:
        return Decimal('0.0')
    return round(amount * SampleBox.SAMPLE_AREA * get_average_price(codename), 2)


def group_by_color(samples_by_id: List[Dict]) -> dict:
    samples_by_color = defaultdict(
        lambda: RecoveredItemWithCost(amount=0, cost=Decimal('0.0'))
    )

    for sample_by_id in samples_by_id:
        sample_box = sample_by_id['sample_box_id']
        samples_by_color[sample_box.color].amount += sample_box.amount
        samples_by_color[sample_box.color].cost += count_waste_recycling(
            sample_box.amount, sample_box.color
        )
    return samples_by_color


def create_sample_box_csv_manufactor_report(
    report: 'MaterialRecoveryReport', samples_by_id: List[Dict]
) -> ContentFile:
    csv_buffer = StringIO()
    csv_writer = csv.writer(csv_buffer, delimiter=';')
    csv_writer.writerow([f'Raport nr {report.pk}'])
    csv_writer.writerow(['Data utworzenia', report.created_at.strftime('%Y-%m-%d')])

    headers = ['LP', 'Kolor', 'Ilość sampli']
    csv_writer.writerow(headers)

    samples_by_color = group_by_color(samples_by_id)
    for idx, (color, samples) in enumerate(samples_by_color.items(), 1):
        csv_writer.writerow([idx, color, samples.amount])

    return buffer_to_csv_file(csv_buffer)


def create_sample_box_csv_admin_report(
    report: 'MaterialRecoveryReport', samples_by_id: List[Dict]
) -> ContentFile:
    csv_buffer = StringIO()
    csv_writer = csv.writer(csv_buffer, delimiter=';')
    csv_writer.writerow([f'Raport nr {report.pk}'])
    csv_writer.writerow(['Data utworzenia', report.created_at.strftime('%Y-%m-%d')])

    samples_by_color = group_by_color(samples_by_id)

    total_waste_recycling = calculate_samples_total_waste_recycling(samples_by_color)
    csv_writer.writerow(
        ['Zysk z odzysku', format_decimal_with_comma(total_waste_recycling)]
    )
    csv_writer.writerow([])

    headers = ['LP', 'Kolor', 'Ilość sampli', 'Odzysk']
    csv_writer.writerow(headers)

    for idx, (color, samples) in enumerate(samples_by_color.items(), 1):
        csv_writer.writerow(
            [idx, color, samples.amount, format_decimal_with_comma(samples.cost)]
        )

    return buffer_to_csv_file(csv_buffer)
