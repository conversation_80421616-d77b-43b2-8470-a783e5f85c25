import csv

from decimal import Decimal
from io import String<PERSON>
from typing import (
    TYPE_CHECKING,
    Dict,
    List,
)

from django.core.files.base import ContentFile

from material_recovery.constants import SHELF_MARKET_PRICING_IN_PLN
from material_recovery.reports import (
    buffer_to_csv_file,
    format_decimal_with_comma,
)
from material_recovery.reports_product_recovery import total_production_cost

if TYPE_CHECKING:
    from material_recovery.models import MaterialRecoveryReport
    from producers.models import Product


class ShelfMarketCost:
    PRICEABLE_ELEMENT_CODES = ['T', 'Ih', 'Iv', 'Px', 'Pz', 'Pe', 'Pf', 'Pb', 'Ll', 'G']

    def __init__(self, product: 'Product'):
        self.product = product

    def calculate_cost(self) -> Decimal:
        if not self.product:
            return Decimal('0.00')

        product_info = self.product.get_serialized_product_info()['item']
        elements_cost = self.get_elements_cost(product_info)
        height_width_cost = self.get_height_width_cost(product_info)
        doors_cost = self.get_doors_cost(product_info['elements'])
        total = sum([elements_cost, height_width_cost, doors_cost])
        costs = total * self.product.shelfmarket.quality_factor
        return Decimal(costs).quantize(Decimal('0.01'))

    def get_height_width_cost(self, product_info: dict) -> Decimal:
        return (
            self.milimeters_to_meters(product_info['height'])
            * self.milimeters_to_meters(product_info['width'])
            * SHELF_MARKET_PRICING_IN_PLN[self.product.cached_shelf_type]
        )

    def get_doors_cost(self, product_elements: dict) -> Decimal:
        return sum(  # type: ignore  # yes, it is Decimal
            SHELF_MARKET_PRICING_IN_PLN[element.get('elem_type')][
                element.get('height_name', '')
            ]
            for element in product_elements
            if element.get('elem_type') in {'D'}
        )

    def milimeters_to_meters(self, value: int) -> Decimal:
        return Decimal(value / (1000 * 100)) if value else Decimal(0.0)  # noqa: RUF032

    def get_elements_cost(self, product_info: dict) -> Decimal:
        cost = sum(
            SHELF_MARKET_PRICING_IN_PLN.get(element_code) * amount
            for element_code, amount in product_info.get('elements_amount', {}).items()
            if element_code in self.PRICEABLE_ELEMENT_CODES
        )
        return Decimal(cost)


def create_shelf_market_csv_manufactor_report(
    report: 'MaterialRecoveryReport', shelf_markets_by_id: List[Dict]
) -> ContentFile:
    csv_buffer = StringIO()
    csv_writer = csv.writer(csv_buffer, delimiter=';')
    csv_writer.writerow([f'Raport nr {report.pk}'])
    csv_writer.writerow(['Data utworzenia', report.created_at.strftime('%Y-%m-%d')])
    headers = ['LP', 'Id regału', 'Producent', 'Kolor', 'Głębokość']
    csv_writer.writerow(headers)

    for shelf_market_by_id in shelf_markets_by_id:
        shelf_market = shelf_market_by_id['shelf_market_id']
        csv_writer.writerow(
            [
                shelf_market.id,
                shelf_market.product_id,
                shelf_market.product.manufactor,
                shelf_market.product.color_with_shelf_name,
                shelf_market.product.cached_depth,
            ]
        )

    return buffer_to_csv_file(csv_buffer)


def create_shelf_market_csv_admin_report(
    report: 'MaterialRecoveryReport', shelf_market_by_ids: List[Dict]
) -> ContentFile:
    csv_buffer = StringIO()
    csv_writer = csv.writer(csv_buffer, delimiter=';')
    csv_writer.writerow([f'Raport nr {report.pk}'])
    csv_writer.writerow(['Data utworzenia', report.created_at.strftime('%Y-%m-%d')])
    headers = [
        'LP',
        'Id regału',
        'Producent',
        'Kolor',
        'Głębokość',
        'Typ Wysyłki',
        'Współczynnik jakościowy',
        'Koszt Odzysku Materiału',
        'Cena Tylko',
    ]
    csv_writer.writerow(headers)

    for shelf_market_by_id in shelf_market_by_ids:
        shelf_market = shelf_market_by_id['shelf_market_id']
        csv_writer.writerow(
            [
                shelf_market.id,
                shelf_market.product_id,
                shelf_market.product.manufactor,
                shelf_market.product.color_with_shelf_name,
                shelf_market.product.cached_depth,
                shelf_market.product.get_carrier_name(),
                shelf_market.quality_factor,
                format_decimal_with_comma(
                    round(total_production_cost(shelf_market.product), 2)
                ),
                format_decimal_with_comma(
                    ShelfMarketCost(shelf_market.product).calculate_cost()
                ),
            ]
        )

    return buffer_to_csv_file(csv_buffer)
