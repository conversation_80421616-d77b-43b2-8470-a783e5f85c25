import json

from decimal import Decimal

from django.conf import settings
from django.urls import reverse
from django.utils import timezone
from rest_framework.authtoken.models import Token

import pytest

from custom.enums import Type01Color
from material_recovery.enums import (
    MaterialRecoveryPricingFactorType,
    MaterialRecoveryReportTypeEnum,
)
from material_recovery.models import MaterialRecoveryReport
from user_profile.choices import UserType


def load_serialization():
    with open(
        str(
            settings.APPS_DIR.path(
                'material_recovery/tests/test_api/responses/cached_serialization.json'
            )
        )
    ) as f:
        cached_serialization = json.load(f)
    return cached_serialization


@pytest.mark.django_db
def test_list(
    product_factory,
    product_details_jetty_factory,
    manufactor_factory,
    user_factory,
    api_client,
):
    product = product_factory(manufactor=manufactor_factory(name='Drewtur'))
    cached_serialization = load_serialization()
    product_details_jetty_factory(
        product=product,
        cached_serialization=cached_serialization,
    )

    user_recovery_producer = user_factory(
        is_staff=True, is_superuser=True, profile__user_type=UserType.STAFF
    )
    token = Token.objects.create(user=user_recovery_producer)
    api_client.force_authenticate(user_recovery_producer)

    url = reverse('product-material-recovery-list')
    response = api_client.get(
        url,
        format='json',
        HTTP_AUTHORIZATION=f'Token {token.key}',
    )
    data = response.data
    assert data and data[0]  # noqa: PT018
    response_product = data[0]
    assert response_product['id'] == product.id
    assert response_product['manufactor'] == 'Drewtur'


@pytest.mark.django_db
def test_get_details(
    product_factory,
    product_details_jetty_factory,
    user_factory,
    manufactor_factory,
    api_client,
):
    manufactor = manufactor_factory(name='Drewtur')
    product = product_factory(manufactor=manufactor)
    cached_serialization = load_serialization()

    product_details_jetty_factory(
        product=product, cached_serialization=cached_serialization
    )

    user_recovery_producer = user_factory(
        is_staff=True, is_superuser=True, profile__user_type=UserType.STAFF
    )
    token = Token.objects.create(user=user_recovery_producer)
    api_client.force_authenticate(user_recovery_producer)

    url = reverse('product-material-recovery-detail', args=(product.id,))
    response = api_client.get(
        url,
        format='json',
        HTTP_AUTHORIZATION=f'Token {token.key}',
    )
    data = response.json()
    assert data['id'] == product.id
    assert data['color'] == Type01Color.WHITE.color_name
    expected = {
        'verticals': [
            {'name': 'vertical_320_C', 'amount': 1},
            {'name': 'vertical_320_A', 'amount': 8},
        ],
        'supports': [{'name': 'support_A', 'amount': 4}],
        'fittings': [
            {'name': 'fitting_hinge_black_mounting-plate', 'amount': 6},
            {'name': 'fitting_slide_indaux_left-rail-drawer-270', 'amount': 3},
            {'name': 'fitting_slide_indaux_right-rail-drawer-270', 'amount': 3},
            {'name': 'fitting_hinge_black_door-bluemotion', 'amount': 3},
            {'name': 'fitting_hinge_black_door-spring', 'amount': 3},
            {'name': 'fitting_hinge_black_cover-cap-hinge', 'amount': 6},
            {'name': 'fitting_slide_indaux_drawer-locking-mechanism', 'amount': 3},
            {'name': 'tool_board', 'amount': 1},
        ],
    }
    assert sorted(data['elements']['fittings'], key=lambda x: x['name']) == sorted(
        expected['fittings'], key=lambda x: x['name']
    )
    assert sorted(data['elements']['verticals'], key=lambda x: x['name']) == sorted(
        expected['verticals'], key=lambda x: x['name']
    )
    assert sorted(data['elements']['supports'], key=lambda x: x['name']) == sorted(
        expected['supports'], key=lambda x: x['name']
    )


@pytest.mark.django_db
def test_create_report(
    product_factory,
    product_details_jetty_factory,
    manufactor_factory,
    user_factory,
    material_recovery_pricing_factor_factory,
    api_client,
):
    manufactor = manufactor_factory(name='Drewtur')
    product = product_factory(
        manufactor=manufactor, is_recovered=True, recovered_at=timezone.now()
    )
    cached_serialization = load_serialization()

    product_details_jetty_factory(
        product=product, cached_serialization=cached_serialization
    )
    material_recovery_pricing_factor_factory(
        codename='Vertical_320_A',
        color=product.color_with_shelf_name,
        type=MaterialRecoveryPricingFactorType.ELEMENTS.value,
        price=Decimal('1.13'),
    )
    material_recovery_pricing_factor_factory(
        codename='Support_C',
        color=product.color_with_shelf_name,
        type=MaterialRecoveryPricingFactorType.ELEMENTS.value,
        price=Decimal('1.13'),
    )
    material_recovery_pricing_factor_factory(
        codename='fitting_hinge_black_cover-cap-hinge',
        type=MaterialRecoveryPricingFactorType.FITTINGS.value,
        price=Decimal('2.15'),
    )

    url = reverse('product-material-recovery-create-report')
    user_recovery_producer = user_factory(
        is_staff=True,
        is_superuser=True,
        profile__user_type=UserType.RECOVERY_PRODUCER,
    )
    token = Token.objects.create(user=user_recovery_producer)
    api_client.force_authenticate(user_recovery_producer)
    request_data = [
        {
            'product_id': product.id,
            'elements': [
                {'name': 'Vertical_320_A', 'amount': 3},
                {'name': 'Support_C', 'amount': 2},
                {'name': 'fitting_hinge_black_cover-cap-hinge', 'amount': 4},
            ],
        }
    ]

    response = api_client.post(
        url,
        format='json',
        data=request_data,
        HTTP_AUTHORIZATION=f'Token {token.key}',
    )
    assert response.status_code == 200
    product_report = response.data
    product_report['product'] = product.id
    report_type = product_report['get_report_type_display']
    assert report_type == 'Elements'

    report = MaterialRecoveryReport.objects.filter(
        report_type=MaterialRecoveryReportTypeEnum.ELEMENTS
    ).first()
    product.refresh_from_db()
    assert product.recovery_report == report
    assert report.product_data == [
        {
            'elements': [
                {'name': 'Vertical_320_A', 'amount': 3},
                {'name': 'Support_C', 'amount': 2},
                {'name': 'fitting_hinge_black_cover-cap-hinge', 'amount': 4},
            ],
            'product_id': product.id,
        }
    ]
