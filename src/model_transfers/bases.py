import json

from datetime import datetime
from os import path
from pathlib import Path

from django.core.files.storage import FileSystemStorage
from django.core.serializers import serialize
from django.db import (
    DEFAULT_DB_ALIAS,
    connections,
)
from django.db.migrations.executor import MigrationExecutor
from django.utils.module_loading import import_string

from fixture_magic.utils import (
    serialize_fully,
    serialize_me,
)


class BaseExportManager:
    static_dir = 'static_files'
    files_config = None
    migration_plan_file = 'migration_plan.json'

    def __init__(self, directory='fixtures', file_name='cstm_data.json'):
        self.files_config = self.files_config or {}
        self.directory = directory
        self.file_name = file_name
        self.storage = FileSystemStorage(path.join(self.directory, self.static_dir))
        self.connection = connections[DEFAULT_DB_ALIAS]

        Path(self.directory).mkdir(parents=True, exist_ok=True)

    def export(self):
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] Start export')
        self.serialize_data()
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] End serialize_data')
        self.export_migration_plan_to_file()
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] End migration plan')
        with open(path.join(self.directory, self.file_name), 'w+') as f:
            f.write(
                serialize(
                    'cstm_json',
                    [instance for instance in serialize_me if instance is not None],
                    indent=4,
                    use_natural_foreign_keys=False,
                    use_natural_primary_keys=False,
                )
            )
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] File saved')
        self.export_files()
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] End export_files')

    def get_migration_plan(self):
        executor = MigrationExecutor(self.connection)
        plan = executor.migration_plan(
            executor.loader.graph.leaf_nodes(), clean_start=True
        )
        return [
            (migration.app_label, migration.name)
            for migration, unapplied in plan
            if not unapplied
        ]

    def export_migration_plan_to_file(self):
        with open(path.join(self.directory, self.migration_plan_file), 'w+') as f:
            json.dump(self.get_migration_plan(), f)

    def serialize_data(self):
        serialize_fully()

    def export_files(self):
        for obj in serialize_me:
            file_fields = self.files_config.get(obj._meta.label, {})
            for field_name, file_config in file_fields.items():
                file_field = getattr(obj, field_name, None)
                if file_field:
                    handler = file_config.get('handler', None)
                    if handler:
                        handler_instance = import_string(handler)()
                        file_field = handler_instance.handle(file_field)
                    self.save_file(file_field)

    def create_files_manifest(self, file_path):
        """Creates a file with files needed for data dump

        Args:
        file_path: Path where to create an output file

        """
        print(
            f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] '
            f'Start create_files_manifest'
        )
        self.serialize_data()
        files = []
        for obj in serialize_me:
            file_fields = self.files_config.get(obj._meta.label, {})
            for field_name, file_config in file_fields.items():  # noqa: B007, PERF102
                file_field = getattr(obj, field_name, None)
                if file_field:
                    files.append(file_field.name)
        with open(file_path, 'w+') as file:
            for file_path in files:
                file.write('{}\n'.format(file_path))
        print(
            f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] '
            f'End create_files_manifest'
        )

    def save_file(self, file):
        try:
            self.storage.save(file.name, file)
        except (FileNotFoundError, OSError):
            pass
