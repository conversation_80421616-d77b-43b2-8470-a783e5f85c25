from django.contrib.auth.models import AnonymousUser
from rest_framework.authentication import BasicAuthentication
from rest_framework.exceptions import AuthenticationFailed

from custom.utils.adyen import get_current_payment_settings


class AdyenWebhookAuthentication(BasicAuthentication):
    """
    To avoid creating a specific user in every DB with adyen specific credentials we use
    basic login/password auth to compare them with configuration data
    """

    def authenticate_credentials(self, userid, password, request=None):
        adyen_settings = get_current_payment_settings()
        if userid != adyen_settings.get(
            'WEBHOOK_USERNAME'
        ) or password != adyen_settings.get('WEBHOOK_PASSWORD'):
            raise AuthenticationFailed('Invalid username/password.')
        return AnonymousUser(), True
