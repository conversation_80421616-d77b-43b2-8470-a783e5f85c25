import contextlib
import logging

from decimal import Decimal

from django.conf import settings
from django.contrib.auth.models import AnonymousUser
from django.db import models
from django.db.models.query_utils import Q
from django.http import HttpRequest
from django.utils.safestring import mark_safe

from b2b.tasks import update_pipedrive_account_after_order_payment
from carts.services.cart_service import CartService
from checkout.enums import AdyenResultCodeEnum
from custom.metrics import metrics_client
from events.domain_events.transact_events import PaymentFailedEvent
from mailing.templates import (
    OrderPaymentAuthorisedMail,
    OrderPaymentAuthorisedMailSampleSet,
    PaymentChargebackForTylko,
    PaymentFailedInfoForTylko,
    get_referral_context_for_email,
)
from orders.enums import OrderStatus
from orders.models import Order
from payments.choices import (
    KlarnaStatus,
    NotificationStatuses,
)
from payments.constants import KLARNA_PAYMENT_METHODS
from pricing_v3.services.price_calculators import Order<PERSON>riceCalculator
from producers.choices import ProductPriority
from user_profile.models import RetargetingBlacklistToken

logger = logging.getLogger('cstm')


class PrimerNotification(models.Model):
    event_type = models.CharField(max_length=255)
    date = models.DateTimeField()
    notification_config_id = models.CharField(max_length=255, blank=True)
    notification_config_description = models.CharField(max_length=255, blank=True)
    version = models.CharField(max_length=7, blank=True)
    payment_id = models.CharField(max_length=255)
    payment_date = models.DateTimeField()
    payment_amount = models.IntegerField()
    payment_currency_code = models.CharField(max_length=3, blank=True)
    payment_customer_id = models.CharField(max_length=255, blank=True)
    payment_order_id = models.CharField(max_length=255, unique=True)
    code = models.CharField(max_length=255, blank=True)
    payment_method_token = models.CharField(max_length=255, blank=True)
    payment_method_analytics_id = models.CharField(max_length=255, blank=True)
    payment_method_type = models.CharField(max_length=255, blank=True)
    payment_method_data = models.JSONField(default=dict, blank=True)
    three_ds_secure_authentication_response_code = models.CharField(  # noqa: DJ001
        max_length=255,
        blank=True,
        null=True,
    )
    processor = models.JSONField(default=dict, blank=True)
    success = models.BooleanField(default=False)

    create_date = models.DateTimeField(auto_now_add=True)
    update_date = models.DateTimeField(auto_now=True)

    exported_to_big_query = models.DateTimeField(null=True, blank=True)

    class Meta:
        verbose_name = 'Primer Notification'
        verbose_name_plural = 'Primer Notifications'

    @property
    def payment_method(self):
        return self.payment_method_type


class Transaction(models.Model):
    """
    statuses:
    AUTHORISATION - successfully completed
    REFUSED - Payment was refused / payment authorisation was unsuccessful.
    CANCELLED - Payment attempt was cancelled by the shopper or the shopper requested
                to return to the merchant by pressing the back button
                on the initial page.
    PENDING - Final status of the payment attempt could not be established immediately.
              This can happen if the systems providing final payment status are
              unavailable or the shopper needs to take further action
              to complete the payment.
    ERROR -An error occurred during the payment processing.
    """

    order = models.ForeignKey(
        Order,
        related_name='transactions',
        verbose_name='Source order',
        on_delete=models.CASCADE,
    )
    amount = models.IntegerField('Amount', null=True, blank=True)
    reference = models.CharField(
        'Payment reference - pspReference',
        max_length=128,
        blank=True,
    )
    merchant_reference = models.CharField(
        'Merchant reference - merchantReference',
        max_length=128,
        blank=True,
    )
    skin_code = models.CharField(
        'Skin code (adyen)',
        max_length=128,
        blank=True,
    )
    payment_method = models.CharField(
        'Payment method',
        max_length=128,
        blank=True,
    )
    status = models.CharField('Status', max_length=128, blank=True)
    return_data = models.TextField('Return data', blank=True)
    live = models.BooleanField('Live', default=False)

    created_at = models.DateTimeField('Date Created', auto_now_add=True)
    updated_at = models.DateTimeField('Date Updated', auto_now=True)

    primer_notification = models.ForeignKey(
        'payments.PrimerNotification',
        on_delete=models.CASCADE,
        related_name='transactions',
        null=True,
        blank=True,
        default=None,
    )

    class Meta(object):
        verbose_name = 'Transaction'
        verbose_name_plural = 'Transactions'

    def __str__(self):
        return f'Transaction for {self.order} '

    def _handle_chargeback(self, notification, order):
        logger.warning(
            f'[transaction_id={self.id} notification_id={notification.id}'
            f'order_id={self.order.id}] '
            f'CHARGEBACK INFO RECEIVED: {notification}'
        )
        for name, receipt in settings.PAYMENT_CHARGEBACK_RECIPIENTS:  # noqa: B007
            mail = PaymentChargebackForTylko(
                receipt, {'order': self.order, 'notification': notification}
            )
            mail.send(language=self.order.owner.profile.language)
        order.is_chargeback = True
        if not any(order.product_set.all().values_list('batch', flat=True)):
            for product in order.product_set.all():
                product.priority_updater.change_priority(
                    ProductPriority.ON_HOLD, owner=order.owner
                )
        order.save()

    def _handle_success(self, notification, order, request):
        if not request:
            # usually this method is triggered by a notification without any meaningful
            # data in the request, but it's needed by following methods to proceed
            request = HttpRequest()
            request.user = AnonymousUser()
        self._update_payment_method_and_status(notification=notification, success=True)
        if order.status in [OrderStatus.DRAFT, OrderStatus.CART] or (
            order.status == OrderStatus.PAYMENT_PENDING
            and order.status_previous in [OrderStatus.DRAFT, OrderStatus.CART]
        ):
            if order.should_split_order_by_samples_and_furniture():
                samples_order = order.split_orders_for_sample_and_furniture()
                order.refresh_from_db()
                if samples_order.should_be_updated_to_paid():
                    is_klarna_payment = (
                        samples_order.is_klarna_payment() or self.is_klarna_payment()
                    )
                    samples_order.update_to_paid(is_klarna_payment=is_klarna_payment)
                    self._create_and_send_payment_authorised_mail(samples_order)

            order.change_status(OrderStatus.PAYMENT_PENDING)
            if order.is_parent_for_split():
                calculator = OrderPriceCalculator(order)
                calculator.calculate()
            order.change_products_to_ordered()
            vouchers = order.vouchers.all()
            for voucher in vouchers:
                voucher.subtract_quantity_left()
            order.estimated_delivery_time = order.get_estimated_delivery_time()
            if not order.order_pretty_id:
                order.create_pretty_id()

        # let's put order in production status,
        # its possible only from payment-pending

        # Adding extra check with extra query
        o = Order.objects.get(pk=self.order.pk)
        count = o.product_set.all().count()
        if count != 0:
            logger.warning(
                'Notification success, but order have already products Order id=%s ',
                order.id,
            )
        if order.should_be_updated_to_paid(count):
            with contextlib.suppress(Order.cart.RelatedObjectDoesNotExist):
                CartService(order.cart).handle_paid_order()

            is_klarna_payment = order.is_klarna_payment() or self.is_klarna_payment()
            order.update_to_paid(is_klarna_payment=is_klarna_payment)
            if settings.IS_PRODUCTION:
                # circular imports
                from orders.serializers import GTMPurchaseEventSerializer
                from orders.tasks import send_purchase_event_data_to_gtm_task

                serializer = GTMPurchaseEventSerializer(order)
                send_purchase_event_data_to_gtm_task.delay(serializer.data)
                update_pipedrive_account_after_order_payment.delay(order.id)

            self._create_and_send_payment_authorised_mail(order)
        else:
            logger.error(
                'Notification success, but order was not in '
                'payment pending status,FIX IT Order id=%s ',
                order.id,
            )

    @classmethod
    def create_or_update_transaction_based_on_order(cls, order):
        transaction, _ = cls.objects.update_or_create(
            order=order,
            merchant_reference=order.adyen_reference,
        )
        return transaction

    @classmethod
    def sync_with_order(cls, order: 'Order') -> None:
        query = Q(
            order=order,
            merchant_reference=order.adyen_reference,
            status=AdyenResultCodeEnum.PENDING,
        )
        try:
            transaction = cls.objects.get(query)
        except cls.DoesNotExist:
            return
        except cls.MultipleObjectsReturned:
            transaction = cls.objects.filter(query).order_by('-created_at').first()

        transaction.amount = order.adyen_price
        transaction.save(update_fields=['amount'])

    def _handle_not_success(self, notification, order):
        metrics_client().increment(
            f'backend.{order.chosen_payment_method}.paymentmethod.failure',
            1,
        )
        # emit event about failed payment
        PaymentFailedEvent(user=order.owner, order_id=order.id, email=order.email)

        self._update_payment_method_and_status(notification=notification, success=False)
        if order.status == OrderStatus.PAYMENT_PENDING:
            order.change_status(OrderStatus.DRAFT)
        for name, receipt in settings.PAYMENT_FAILED_RECIPIENTS:  # noqa: B007
            mail = PaymentFailedInfoForTylko(
                receipt, {'order': order, 'notification': notification}
            )
            mail.send(language=self.order.owner.profile.language)

    def _update_payment_method_and_status(self, notification, success):
        self.status = notification.code
        self.payment_method = notification.payment_method
        self.save(update_fields=['payment_method', 'status'])

        # temporary logger to track payment method scheme
        # https://cstm-tasks.atlassian.net/browse/ECO-3708
        if (
            self.payment_method == 'scheme'
            or self.order.chosen_payment_method == 'scheme'
        ):
            logger.warning(
                'Spying payment method scheme'
                ' - update transaction after notification (AdyenAcceptNotification)\n'
                'order: %s\n'
                'notification code: %s\n'
                'success: %s\n'
                'payment method in order: %s\n'
                'payment method in notification: %s\n',
                self.order.id,
                notification.code,
                success,
                self.order.chosen_payment_method,
                notification.payment_method,
            )

        if success and (
            not self.order.chosen_payment_method
            or self.order.chosen_payment_method != notification.payment_method
        ):
            # update order payment method if we're lacking it
            self.order.chosen_payment_method = self.payment_method
            self.order.save(update_fields=['chosen_payment_method'])

    def update_after_notification(self, notification, request=None):
        order = self.order
        # 1. transaction was already authorised
        if self.status in {
            NotificationStatuses.AUTHORISATION,
            NotificationStatuses.AUTHORIZED,
        }:
            # 1.1 Authorised transaction might change into chargeback
            previous_notification = self.notification_set.last()
            if notification.code == 'CHARGEBACK':
                self._handle_chargeback(notification, order)

            # 1.2 Previous notification was also authorised, but wasn't successful
            elif previous_notification:
                # if previous notification was unsuccessful but this one is - then
                # payment is completed
                if not previous_notification.success and notification.success:
                    self._handle_success(notification, order, request)
                    return True
            return False

        # Payment was failed and notification was receive from primer service
        # see more information about handle failure payment here
        # TODO add link (https://cstm-tasks.atlassian.net/browse/ECOM-1660)
        if notification.code in NotificationStatuses.failure_statuses():
            self._handle_not_success(notification, order)
            return False

        # 2. Transaction is authorised

        if notification.code in NotificationStatuses.success_statuses():
            if notification.success is True:
                self._handle_success(notification, order, request)
            else:
                self._handle_not_success(notification, order)

        # 3. Transaction is not authorised
        else:
            metrics_client().increment(
                f'backend.{order.chosen_payment_method}.paymentmethod.failure',
                1,
            )
            logger.warning(
                f'Notification but not authorization for order {order.id} failed'
            )
            return False
        return True

    def _get_delivery_time(self, order):
        if order.estimated_delivery_time:
            return order.estimated_delivery_time

        delivery_time = order.get_estimated_delivery_time()
        logger.warning('[order_id=%s] No estimated delivery time! ', order.id)
        return delivery_time

    def _create_and_send_payment_authorised_mail(self, order):
        if not settings.PROCESS_TRANSACT_FLOWS_ON_BE:
            return

        delivery_time = self._get_delivery_time(order)
        email_template = self.get_email_template(order)
        blacklist_token = RetargetingBlacklistToken.get_or_create_for_email(
            email=order.email
        ).token
        email_template(
            order.email,
            {
                'order': order,
                'blacklist_token': blacklist_token,
                'delivery_date': delivery_time,
                **get_referral_context_for_email(order.owner.profile.region),
            },
            topic_variables={'order_pretty_id': order.order_pretty_id},
        ).send(language=order.owner.profile.language)

    def regionalize_value(self, voucher, order):
        return (
            voucher.get_regionalized_value_display(order.owner.profile.region)
            if voucher
            else ''
        )

    def get_email_template(self, order):
        if order.contains_only_samples:
            return OrderPaymentAuthorisedMailSampleSet
        return OrderPaymentAuthorisedMail

    def is_klarna_payment(self):
        return self.payment_method in KLARNA_PAYMENT_METHODS

    def admin_order(self):
        return mark_safe(  # noqa: S308
            '<a href="/admin/orders/order/{}" target="blank">{}</a>'.format(
                self.order.id, self.order
            )
        )

    admin_order.short_description = 'Order'

    def admin_notifications(self):
        return mark_safe('<br/>'.join([str(n) for n in self.notification_set.all()]))  # noqa: S308

    admin_notifications.short_description = 'Notifications'

    @property
    def order_status(self):
        return self.order.get_status_display()

    @property
    def order_phone(self):
        prefix = self.order.phone_prefix or ''
        return f'{prefix} {self.order.phone}'.replace('phone', '')

    @property
    def amount_in_major_units(self) -> Decimal:
        minor_major_ratio = 100
        return Decimal(self.amount) / minor_major_ratio


class KlarnaCaptureRequest(models.Model):
    """
    Object created after sending a capture request to adyen.
    Should be updated by adyen notification.
    """

    psp_reference = models.CharField(
        'Payment reference - pspReference',
        max_length=128,
        blank=True,
    )
    transaction = models.ForeignKey(
        Transaction,
        null=True,
        blank=True,
        related_name='klarna_captures',
        on_delete=models.CASCADE,
    )
    status = models.PositiveSmallIntegerField(
        choices=KlarnaStatus.choices, default=KlarnaStatus.PENDING
    )
    error_message = models.CharField(null=True, blank=True, max_length=1024)  # noqa: DJ001


class Notification(models.Model):
    """
    eventCode
    AUTHORISATION - everything is ok
    Modification Payment Events
    - CANCELLATION.
    - REFUND.
    - CANCEL_OR_REFUND.
    - CAPTURE.
    - REFUNDED_REVERSED.
    - CAPTURE_FAILED.
    - REFUND_FAILED.
    Dispute Events
    - REQUEST_FOR_INFORMATION.
    - NOTIFICATION_OF_CHARGEBACK.
    - ADVICE_OF_DEBIT.
    - CHARGEBACK.
    - CHARGEBACK_REVERSED.
    Other Events
     REPORT_AVAILABLE.
    """

    transaction = models.ForeignKey(
        Transaction,
        null=True,
        blank=True,
        on_delete=models.CASCADE,
    )
    klarna_capture = models.OneToOneField(
        KlarnaCaptureRequest,
        null=True,
        blank=True,
        on_delete=models.CASCADE,
    )
    live = models.BooleanField(default=False)

    merchant_reference = models.CharField(
        'Merchant reference - merchantReference',
        max_length=128,
        blank=True,
    )
    psp_reference = models.CharField(
        'Psp reference - pspReference',
        max_length=128,
        blank=True,
    )
    code = models.CharField(max_length=128)
    success = models.BooleanField(default=False)
    event_date = models.DateTimeField()
    payment_method = models.CharField(
        'Payment method',
        max_length=128,
        blank=True,
    )
    operations = models.CharField(max_length=1024)
    reason = models.TextField(blank=True)
    amount_value = models.IntegerField(blank=True, null=True)
    amount_currency = models.CharField(max_length=10)

    merchant_account_code = models.CharField(  # noqa: DJ001
        max_length=128,
        blank=True,
        null=True,
        default=None,
    )
    original_reference = models.CharField(  # noqa: DJ001
        max_length=128,
        blank=True,
        null=True,
        default=None,
    )

    class Meta(object):
        verbose_name = 'Notification'
        verbose_name_plural = 'Notification'

    def __str__(self):
        return f'Notification for {self.transaction} '

    def get_payment_form(self):
        payment_methods = {
            'mc': 'credit card',
            'visa': 'credit card',
            'ideal': 'online payment',
            'paypal': 'online payment',
            'dotpay': 'online payment',
            'bankTransfer_IBAN': 'bank transfer',
            'sepadirectdebit': 'sepa direct',
        }
        return payment_methods.get(self.payment_method, self.payment_method)
