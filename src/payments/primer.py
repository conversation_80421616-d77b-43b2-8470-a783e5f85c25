import base64
import hashlib
import hmac
import json
import logging
import time

from urllib.parse import urljoin

from django.conf import settings

import requests

logger = logging.getLogger('cstm')


class IncorrectMethodError(Exception):
    pass


class PrimerClient:
    ALLOWED_METHODS = ['GET', 'POST', 'PATCH']

    def __init__(
        self,
        api_url: str,
        api_key: str,
        api_version: str = None,  # noqa: RUF013
    ):
        self._api_url = api_url
        self._api_key = api_key
        self._api_version = api_version

    def _get_headers(self):
        headers = {
            'Content-Type': 'application/json',
            'X-API-KEY': self._api_key,
        }
        if self._api_version:
            headers['X-API-VERSION'] = self._api_version

        return headers

    def call_api(self, path, method, data):
        if method not in self.ALLOWED_METHODS:
            raise IncorrectMethodError(
                f'Method {method} is not correct method for this client'
            )
        url = urljoin(self._api_url, path)
        request_kwargs = {'params': data} if method == 'GET' else {'json': data}
        return requests.request(
            method=method,
            url=url,
            headers=self._get_headers(),
            **request_kwargs,
        )

    @staticmethod
    def validate_webhook_signature(payload: dict, webhook_signature: str) -> bool:
        signature_timestamp = payload['signedAt']

        current_unix_timestamp = int(time.time())

        if (current_unix_timestamp - int(signature_timestamp)) > (3 * 60):
            logger.error('Timestamp is not within threshold (3 min)')
            return False

        payload_str = json.dumps(payload)

        mac = hmac.new(
            key=settings.PRIMER_WEBHOOK_SECRET.encode('utf-8'),
            msg=payload_str.encode('utf-8'),
            digestmod=hashlib.sha256,
        )
        payload_hash = base64.b64encode(mac.digest()).decode('utf-8').strip()
        return payload_hash == webhook_signature


class BasePrimerService:
    def __init__(self):
        self._client = PrimerClient(
            api_url=settings.PRIMER_API_HOST,
            api_key=settings.PRIMER_API_KEY,
            api_version=settings.PRIMER_API_VERSION,
        )


class PrimerClientSessionService(BasePrimerService):
    session_endpoint_path = 'client-session'

    def create_client_session(self, data):
        return self._client.call_api(
            path=self.session_endpoint_path,
            data=data,
            method='POST',
        )

    def retrieve_client_session(self, data):
        return self._client.call_api(
            path=self.session_endpoint_path,
            data=data,
            method='GET',
        )

    def update_client_session(self, data):
        return self._client.call_api(
            path=self.session_endpoint_path,
            data=data,
            method='PATCH',
        )


class PrimerKlarnaService(BasePrimerService):
    def capture_klarna_payment(self, transaction):
        return self._client.call_api(
            path=f'/payments/{transaction.primer_notification.payment_id}/capture',
            method='POST',
            data={'final': True},
        )
