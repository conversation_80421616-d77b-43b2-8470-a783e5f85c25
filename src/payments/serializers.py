from django.forms.fields import <PERSON><PERSON><PERSON><PERSON>
from rest_framework import serializers

from custom.models import Countries
from orders.models import Order
from payments.models import PrimerNotification
from payments.utils import create_order_id_with_adyen_schema


class AdyenPaymentSetupSerializer(serializers.Serializer):
    orderID = serializers.IntegerField()  # noqa: N815
    returnUrl = CharField(min_length=4, max_length=255)  # noqa: N815
    token = CharField(min_length=1, max_length=255)


class PrimerOrderItemRequestSerializer(serializers.Serializer):
    item_id = serializers.SerializerMethodField()
    amount = serializers.SerializerMethodField()
    description = serializers.CharField(source='get_furniture_type')

    def get_item_id(self, obj):
        return f'{obj.content_type.model}-{obj.object_id}'

    def get_amount(self, obj):
        return int(obj.aggregate_region_price * 100)


class PrimerOrderSerialier(serializers.Serializer):
    line_items = serializers.SerializerMethodField()
    country_code = serializers.SerializerMethodField()

    def get_line_items(self, obj):
        return PrimerOrderItemRequestSerializer(obj.items.all(), many=True).data

    def get_country_code(self, obj):
        if not obj.country:
            return
        return Countries.get_country_by_name(obj.country).iso_alpha_2_country_code


class PrimerAddressBaseSerializer(serializers.ModelSerializer):
    class Meta:
        model = Order
        fields = (
            'first_name',
            'last_name',
            'address_line_1',
            'address_line_2',
            'city',
            'state',
            'country_code',
            'postal_code',
        )


class BillingAddressPrimerSerializer(PrimerAddressBaseSerializer):
    first_name = serializers.CharField(source='invoice_first_name')
    last_name = serializers.CharField(source='invoice_last_name')
    # we switch address lines due to Primer-Adyen fields mapping
    address_line_1 = serializers.CharField(source='invoice_street_address_2')
    address_line_2 = serializers.CharField(source='invoice_street_address_1')
    city = serializers.CharField(source='invoice_city')
    state = serializers.CharField(source='invoice_country')
    country_code = serializers.SerializerMethodField()
    postal_code = serializers.CharField(source='invoice_postal_code')

    def get_country_code(self, obj):
        if not obj.invoice_country:
            return
        return Countries.get_country_by_name(
            obj.invoice_country
        ).iso_alpha_2_country_code


class ShippingAddressPrimerSerializer(PrimerAddressBaseSerializer):
    # we switch address lines due to Primer-Adyen fields mapping
    address_line_1 = serializers.CharField(source='street_address_2')
    address_line_2 = serializers.CharField(source='street_address_1')
    state = serializers.CharField(source='country_area')
    country_code = serializers.ReadOnlyField(source='iso_alpha_2_country_code')


class PrimerCustomerOrderSerializer(serializers.ModelSerializer):
    email_address = serializers.EmailField(source='email')
    mobile_number = serializers.CharField(source='cleaned_phone_number')
    billing_address = BillingAddressPrimerSerializer(source='*')
    shipping_address = ShippingAddressPrimerSerializer(source='*')
    tax_id = serializers.CharField(source='vat')

    class Meta:
        model = Order
        fields = (
            'email_address',
            'mobile_number',
            'first_name',
            'last_name',
            'billing_address',
            'shipping_address',
            'tax_id',
        )


class PrimerMetadataSerializer(serializers.ModelSerializer):
    """All primer custom data goes here"""

    region_country_code = serializers.ReadOnlyField(source='iso_alpha_2_country_code')
    paypal_custom_id = serializers.SerializerMethodField()

    class Meta:
        model = Order
        fields = ('region_country_code', 'paypal_custom_id')

    def get_paypal_custom_id(self, obj):
        return create_order_id_with_adyen_schema(obj)


class PrimerClientSessionRequestSerializer(serializers.ModelSerializer):
    """
    Main primer serializer.
    Primer expects a nested obj with data that are by far kept in out Order model, but
    to fit their expectations we're using multiple serializers.
    """

    amount = serializers.IntegerField(source='primer_price')
    customer = PrimerCustomerOrderSerializer(source='*')
    customer_id = serializers.SerializerMethodField()
    currency_code = serializers.SerializerMethodField()
    metadata = PrimerMetadataSerializer(source='*')
    order = PrimerOrderSerialier(source='*')
    order_id = serializers.SerializerMethodField()

    class Meta:
        model = Order
        fields = (
            'amount',
            'customer',
            'customer_id',
            'currency_code',
            'metadata',
            'order',
            'order_id',
        )

    @property
    def user(self):
        return self.context.get('request').user

    def get_currency_code(self, obj):
        return self.user.profile.region.get_currency().code

    def get_customer_id(self, obj):
        return str(self.user.id)

    def get_order_id(self, obj):
        return create_order_id_with_adyen_schema(obj)


class PrimerNotificationBigQuerySerializer(serializers.ModelSerializer):
    class Meta:
        model = PrimerNotification
        fields = (
            'event_type',
            'date',
            'notification_config_id',
            'notification_config_description',
            'version',
            'payment_id',
            'payment_date',
            'payment_amount',
            'payment_currency_code',
            'payment_customer_id',
            'payment_order_id',
            'code',
            'payment_method_token',
            'payment_method_analytics_id',
            'payment_method_type',
            'payment_method_data',
            'three_ds_secure_authentication_response_code',
            'processor',
            'success',
            'create_date',
            'update_date',
            'exported_to_big_query',
        )
