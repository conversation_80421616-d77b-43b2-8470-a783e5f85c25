import logging

from typing import (
    Optional,
    Tuple,
)

from django.core.exceptions import ObjectDoesNotExist
from django.db.transaction import atomic
from django.http import HttpRequest

from custom.metrics import metrics_client
from custom.utils.retries import retry
from orders.enums import OrderStatus
from orders.models import Order
from payments.choices import NotificationStatuses
from payments.models import (
    PrimerNotification,
    Transaction,
)
from payments.utils import decode_order_id_from_adyen_schema_notification

logger = logging.getLogger('cstm')


class PrimerNotificationHandler:
    def __init__(self, notification_data: dict) -> None:
        self.notification_data = notification_data
        self.order_id = notification_data.get('payment', {}).get('orderId', None)
        self.notification, self.created = self._get_notification()
        self.transactions = []
        self.transaction = None

    @atomic
    def _get_notification(self) -> Tuple[Optional[PrimerNotification], bool]:
        if not self.order_id:
            return None, False

        notification, created = PrimerNotification.objects.update_or_create(
            payment_order_id=self.order_id,
            defaults=self._map_raw_notification(),
        )
        return notification, created

    def _map_raw_notification(self) -> dict:
        primer_payment_object = self.notification_data.get('payment', {})
        notification_config_description = self.notification_data.get(
            'notificationConfig',
            {},
        ).get('description', '')
        payment_currency_code = primer_payment_object.get('currencyCode', '')
        primer_payment_method_object = primer_payment_object.get('paymentMethod', {})
        payment_method_token = primer_payment_method_object.get(
            'paymentMethodToken',
            '',
        )
        payment_method_analytics_id = primer_payment_method_object.get(
            'analyticsId',
            '',
        )
        payment_method_type = primer_payment_method_object.get('paymentMethodType', '')
        payment_method_data = primer_payment_object.get('paymentMethodData', {})
        ds_secure_code = primer_payment_method_object.get(
            'threeDSecureAuthentication',
            '',
        )
        notification_config_id = self.notification_data.get(
            'notificationConfig',
            {},
        ).get('id', '')

        return {
            'event_type': self.notification_data['eventType'],
            'date': self.notification_data['date'],
            'notification_config_description': notification_config_description,
            'version': self.notification_data['version'],
            'payment_id': self.notification_data['payment']['id'],
            'payment_date': self.notification_data['payment']['date'],
            'payment_amount': self.notification_data['payment']['amount'],
            'payment_currency_code': payment_currency_code,
            'payment_customer_id': primer_payment_object.get('customerId', ''),
            'code': self.notification_data.get('payment', {}).get('status', ''),
            'payment_order_id': self.order_id,
            'payment_method_token': payment_method_token,
            'payment_method_analytics_id': payment_method_analytics_id,
            'payment_method_type': payment_method_type,
            'payment_method_data': payment_method_data,
            'three_ds_secure_authentication_response_code': ds_secure_code,
            'processor': self.notification_data['payment']['processor'],
            'notification_config_id': notification_config_id,
        }

    @atomic
    @retry(3, 5, ObjectDoesNotExist)
    def handle(self) -> None:
        self._update_if_success()
        transactions_ids = [
            transaction['processorTransactionId']
            for transaction in self.notification_data['payment']['transactions']
        ]
        # Take most fresh transaction (The latest transaction is the most
        # relevant transaction and contain the last state of order)
        self.transaction = (
            Transaction.objects.filter(reference__in=transactions_ids)
            .order_by('-updated_at')
            .last()
        )

        if not self.transaction:
            order_id = decode_order_id_from_adyen_schema_notification(self.order_id)

            self.transaction = (
                Order.objects.get(id=order_id)
                .transactions.order_by('-updated_at')
                .last()
            )

        if not self.transaction:
            raise ObjectDoesNotExist(
                f'Missing transaction ({transactions_ids}) object for notification'
                f"{self.notification_data['payment']['id']}"
            )

        self.transaction.primer_notification = self.notification
        self.transaction.save(update_fields=['primer_notification'])

    def update_transaction_if_paypal(self, request: HttpRequest) -> None:
        if self.notification.payment_method_type != 'PAYPAL_ORDER':
            return
        self.transaction.update_after_notification(
            self.notification,
            request,
        )

    def process_failed_transaction(self, transaction: dict, order: Order) -> None:
        order.change_status(OrderStatus.PAYMENT_FAILED)
        msg = transaction.get('processorStatusReason', {}).get(
            'message', str(transaction)
        )  # pass 'message' or whole obj
        logger.warning(f'Primer payment failed: {msg}')
        metrics_client().event(
            'Primer payment failed',
            text=msg,
        )

    def _update_if_success(self) -> None:
        if self.notification.code in NotificationStatuses.success_statuses():
            self.notification.success = True
            self.notification.save(update_fields=['success'])
