import copy

BASE_TRANSACTIONS = [
    {
        'date': '2022-05-26T11:14:11.946300',
        'amount': 3000,
        'currencyCode': 'GBP',
        'transactionType': 'SALE',
        'processorTransactionId': 'pi_3L3edsGZasdasdc1iget38p',
        'processorName': 'STRIPE',
        'processorMerchantId': 'acct_1GORasvasdNWFwi8c',
        'processorStatus': 'SETTLED',
    }
]

WEBHOOKS_BASE_RESULTS = {
    'eventType': 'PAYMENT.STATUS',
    'date': '2021-02-21T15:36:16.367687',
    'notificationConfig': {
        'id': 'cc51f9f0-7e1c-492b-9d37-f83a818f6070',
        'description': 'Payment webhook',
    },
    'version': '2.1',
    'payment': {
        'id': 'DdRZ6YY0',
        'date': '2022-01-01T12:12:12.000000',
        'amount': 3000,
        'currencyCode': 'GBP',
        'customerId': 'cust-123',
        'orderId': 'order-123',
        'status': 'AUTHORIZED',
        'paymentMethod': {
            'paymentMethodToken': '-lcWjvBAAs2DnIRXwxNjUzNTYzNjIy',
            'analyticsId': 'LUi5pETUaVsdSEamK25L',
            'paymentMethodType': 'PAYMENT_CARD',
            'paymentMethodData': {
                'paymentMethodType': 'PRIMER_TEST_CARD',
                'last4Digits': '1111',
                'expirationMonth': '03',
                'expirationYear': '2030',
                'cardholderName': 'ADYEN',
                'network': 'Visa',
                'isNetworkTokenized': False,
                'binData': {
                    'network': 'VISA',
                    'issuerCountryCode': 'US',
                    'issuerName': 'JPMORGAN CHASE BANK, N.A.',
                    'regionalRestriction': 'UNKNOWN',
                    'accountNumberType': 'UNKNOWN',
                    'accountFundingType': 'UNKNOWN',
                    'prepaidReloadableIndicator': 'NOT_APPLICABLE',
                    'productUsageType': 'UNKNOWN',
                    'productCode': 'UNKNOWN',
                    'productName': 'UNKNOWN',
                },
                'cvvAvailable': True,
            },
            'threeDSecureAuthentication': {'responseCode': 'NOT_PERFORMED'},
        },
        'processor': {
            'name': 'STRIPE',
            'processorMerchantId': 'acct_1GORasdasqNWFwi8c',
            'amountCaptured': 3000,
            'amountRefunded': 0,
        },
        'transactions': BASE_TRANSACTIONS,
    },
}
WEBHOOKS_CARD_RESULTS = copy.deepcopy(WEBHOOKS_BASE_RESULTS)

WEBHOOKS_PAYPALL_RESULTS_PENNDING = copy.deepcopy(WEBHOOKS_BASE_RESULTS)
WEBHOOKS_PAYPALL_RESULTS_PENNDING['payment']['status'] = 'PENDING'
WEBHOOKS_PAYPALL_RESULTS_PENNDING['payment']['paymentMethod']['paymentMethodType'] = (
    'PAYPAL_ORDER'
)
WEBHOOKS_PAYPALL_RESULTS_PENNDING['payment']['transactions'] = []

WEBHOOKS_PAYPALL_RESULTS = copy.deepcopy(WEBHOOKS_PAYPALL_RESULTS_PENNDING)
WEBHOOKS_PAYPALL_RESULTS['payment']['status'] = 'AUTHORIZED'
WEBHOOKS_PAYPALL_RESULTS['payment']['transactions'] = copy.deepcopy(BASE_TRANSACTIONS)
WEBHOOKS_PAYPALL_RESULTS['payment']['transactions'][0]['processorTransactionId'] = (
    'pi_3L3edsGZasdasdc1iget38x'
)
WEBHOOKS_PAYPALL_RESULTS['payment']['orderId'] = 'order-123'

WEBHOOKS_PAYPALL_RESULTS_2 = copy.deepcopy(WEBHOOKS_PAYPALL_RESULTS)
WEBHOOKS_PAYPALL_RESULTS_2['payment']['transactions'] = copy.deepcopy(BASE_TRANSACTIONS)

WEBHOOKS_PAYPALL_RESULTS_3 = copy.deepcopy(WEBHOOKS_PAYPALL_RESULTS)
WEBHOOKS_PAYPALL_RESULTS_3['payment']['transactions'] = copy.deepcopy(BASE_TRANSACTIONS)
WEBHOOKS_PAYPALL_RESULTS_3['payment']['transactions'][0]['processorTransactionId'] = (
    'SomeTestTransactionId'
)
WEBHOOKS_PAYPALL_RESULTS_3['payment']['orderId'] = 'order-1234'

WEBHOOKS_PAYPALL_PAYMENT_FAILED_RESULTS = copy.deepcopy(WEBHOOKS_PAYPALL_RESULTS)
WEBHOOKS_PAYPALL_PAYMENT_FAILED_RESULTS['payment']['status'] = 'DECLINED'
