from datetime import (
    UTC,
    datetime,
)
from unittest import mock

from django.db.models import Sum
from django.urls import reverse

import pytest

from freezegun import freeze_time

from carts.services.cart_service import CartService
from custom.models import Countries
from mailing.templates import (
    OrderPaymentAuthorisedMail,
    OrderPaymentAuthorisedMailSampleSet,
)
from orders.enums import OrderStatus
from orders.models import (
    Order,
    OrderToProduction,
)
from vouchers.enums import VoucherType


@pytest.mark.django_db
class TestTransaction:
    ESTIMATED_DELIVERY_TIME = datetime(2021, 4, 3, 12, 0, 0, tzinfo=UTC)

    @freeze_time(ESTIMATED_DELIVERY_TIME)
    def test__get_delivery_time_should_return_estimated_delivery_time_when_set_in_order(
        self, order_factory, transaction_factory
    ):
        order = order_factory(estimated_delivery_time=self.ESTIMATED_DELIVERY_TIME)
        transaction = transaction_factory(order=order)

        delivery_time = transaction._get_delivery_time(order)

        assert delivery_time == self.ESTIMATED_DELIVERY_TIME

    @mock.patch(
        'orders.models.Order.get_estimated_delivery_time',
        return_value=datetime(2021, 1, 3, 12, 0, 0, tzinfo=UTC),
    )
    def test__get_delivery_time_should_call_get_estimated_delivery_time_when_no_estimated_delivery_time(  # noqa: E501
        self, mock_get_estimated_delivery_time, order_factory, transaction_factory
    ):
        order = order_factory(estimated_delivery_time=None)
        transaction = transaction_factory(order=order)

        delivery_time = transaction._get_delivery_time(order)

        assert delivery_time == datetime(2021, 1, 3, 12, 0, 0, tzinfo=UTC)

    def test_get_email_template_should_return_orderpaymentauthorisedmailsampleset_when_contains_only_samples(  # noqa: E501
        self, order_factory, order_item_factory, transaction_factory
    ):
        order = order_factory(items=[])
        order_item_factory(order=order, is_sample_box=True)
        transaction = transaction_factory(order=order)

        email_template = transaction.get_email_template(order)

        assert email_template is OrderPaymentAuthorisedMailSampleSet

    def test_get_email_template_should_return_orderpaymentauthorisedmail_when_not_contains_only_samples(  # noqa: E501
        self, order_factory, order_item_factory, transaction_factory
    ):
        order = order_factory(items=[])
        order_item_factory(order=order, is_sample_box=True)
        order_item_factory(order=order, is_jetty=True)
        transaction = transaction_factory(order=order)

        email_template = transaction.get_email_template(order)

        assert email_template is OrderPaymentAuthorisedMail

    @mock.patch('payments.models.Transaction._create_and_send_payment_authorised_mail')
    def test_update_after_notification_authorization_should_call_split_orders_for_sample_and_furniture_when_should_be_split(  # noqa: E501
        self,
        mock__create_and_send_payment_authorised_mail,
        rf,
        order_factory,
        order_item_factory,
        transaction_factory,
        notification_factory,
        region_factory,
    ):
        order = order_factory(
            items=[], status=OrderStatus.CART, country=Countries.united_kingdom.name
        )
        region_factory(name=Countries.united_kingdom.name)
        order_item_factory(order=order, is_sample_box=True)
        order_item_factory(order=order, is_sample_box=True)
        order_item_factory(order=order, is_jetty=True)
        transaction = transaction_factory(order=order, status='PENDING')
        notification = notification_factory(
            code='AUTHORISATION', success=True, transaction=transaction
        )
        request = rf.post(reverse('payment-notifications'))
        request.user = order.owner
        request.session = {}

        updated = transaction.update_after_notification(notification, request)

        order.refresh_from_db()
        sample_order = Order.objects.get(parent_order=order)

        assert (
            OrderToProduction.objects.filter(
                original_order_id__in=[order.id, sample_order.id]
            ).count()
            == 2
        )
        assert mock__create_and_send_payment_authorised_mail.call_count == 2
        assert updated
        assert order.items.count() == 1
        assert order.paid_at
        assert sample_order.items.count() == 2
        assert sample_order.paid_at

    def test_regionalize_value_should_return_empty_when_no_voucher(
        self, order_factory, transaction_factory
    ):
        order = order_factory(
            items=[], status=OrderStatus.CART, country=Countries.united_kingdom.name
        )
        transaction = transaction_factory(order=order)
        regionalized_value = transaction.regionalize_value(None, order)

        assert regionalized_value == ''

    def test_regionalize_value_should_return_regionalized_when_voucher_exists(
        self, order_factory, transaction_factory, voucher_factory
    ):
        order = order_factory(
            items=[], status=OrderStatus.CART, country=Countries.united_kingdom.name
        )
        voucher = voucher_factory(value=25, kind_of=VoucherType.ABSOLUTE)
        transaction = transaction_factory(order=order)
        regionalized_value = transaction.regionalize_value(voucher, order)

        assert regionalized_value == voucher.get_regionalized_value_display(
            order.owner.profile.region
        )

    @pytest.mark.parametrize(
        (
            'chosen_payment_method',
            'payment_method',
            'success',
            'expected_payment_method',
        ),
        [
            ('mc', 'klarna', False, 'mc'),
            ('mc', 'klarna', True, 'klarna'),
            ('mc', 'mc', True, 'mc'),
            ('mc', 'mc', False, 'mc'),
        ],
    )
    def test_update_payment_method_and_status(
        self,
        transaction_factory,
        notification_factory,
        chosen_payment_method,
        payment_method,
        success,
        expected_payment_method,
    ):
        transaction = transaction_factory(
            order__chosen_payment_method=chosen_payment_method
        )
        notification = notification_factory(payment_method=payment_method)
        transaction._update_payment_method_and_status(notification, success=success)

        assert transaction.order.chosen_payment_method == expected_payment_method

    def test_handle_success_subtract_voucher_quantity(
        self,
        voucher_factory,
        transaction_factory,
        notification_factory,
    ):
        transaction = transaction_factory(
            order__status=OrderStatus.PAYMENT_PENDING,
            order__vouchers=[voucher_factory(quantity_left=10)],
        )
        notification = notification_factory(
            transaction=transaction,
        )

        transaction._handle_success(notification, transaction.order, None)

        assert (
            transaction.order.vouchers.aggregate(Sum('quantity_left')).get(
                'quantity_left__sum'
            )
            == 9
        )


@pytest.mark.django_db
class TestTransactionUpdateAfterNotification:
    @pytest.fixture
    def setup(self, transaction_factory, notification_factory):
        transaction = transaction_factory(
            status='PENDING',
            order__status=OrderStatus.PAYMENT_PENDING,
        )
        success_notification = notification_factory(
            code='AUTHORISATION',
            success=True,
            transaction=transaction,
        )
        unsuccessful_notification = notification_factory(
            code='AUTHORISATION',
            success=False,
            transaction=transaction,
        )

        return (
            transaction,
            success_notification,
            unsuccessful_notification,
        )

    def test_first_payment_successful_second_not(self, setup):
        """That's an interesting corner case. Payment notifications are coming to us
        asynchronously, so sometime it happens that the order of received notifications
        is not accurate with the real payments. Only scenario that this may affect us is
        when user's first transaction was unsuccessful, second did succeed, but
        notifications came in reversed order, so first notification mark order as paid,
        second reversed it. This test ensure us that this will never happen.
        """

        transaction, notification_ok, notification_nok = setup

        assert transaction.order.status == OrderStatus.PAYMENT_PENDING

        transaction.update_after_notification(notification_ok)
        transaction.order.refresh_from_db()
        assert OrderToProduction.objects.count() == 1

        transaction.update_after_notification(notification_nok)
        transaction.order.refresh_from_db()
        assert OrderToProduction.objects.count() == 1

    def test_first_unsuccessful_second_successful(self, setup):
        transaction, notification_ok, notification_nok = setup

        assert transaction.order.status == OrderStatus.PAYMENT_PENDING
        transaction.update_after_notification(notification_nok)
        transaction.order.refresh_from_db()
        assert OrderToProduction.objects.count() == 0

        transaction.update_after_notification(notification_ok)
        transaction.order.refresh_from_db()
        assert OrderToProduction.objects.count() == 1


@pytest.fixture
def order_with_sofa_and_old_sofa_collection(cart_factory, cart_item_factory, region_de):
    cart = cart_factory(region=region_de, items=[])
    cart_item_factory(cart=cart, is_sofa_wool=True)
    service = CartService(cart)
    service.change_old_sofa_collection(activate=True)
    service.sync_with_order()
    return cart.order


@pytest.mark.django_db
def test_handel_success_with_additional_services(
    order_with_sofa_and_old_sofa_collection,
    transaction_factory,
    notification_factory,
):
    """
    Check if order with sofa and additional service goes smoothly through payment flow
    """
    order = order_with_sofa_and_old_sofa_collection
    order.change_status(OrderStatus.PAYMENT_PENDING)
    transaction = transaction_factory(
        order=order,
        status='PENDING',
    )
    notification = notification_factory(
        code='AUTHORISATION',
        success=True,
        transaction=transaction,
    )
    assert not OrderToProduction.objects.filter(original_order=order).exists()

    transaction._handle_success(notification, order, None)
    assert OrderToProduction.objects.filter(original_order=order).exists()
