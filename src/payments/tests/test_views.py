from copy import copy
from unittest import mock
from unittest.mock import Mock

from django.urls import reverse
from rest_framework import status

import pytest

from orders.enums import OrderStatus
from orders.models import OrderToProduction
from payments.choices import AdyenResponseStatus
from payments.models import (
    PrimerNotification,
    Transaction,
)
from payments.tests.mocks.primer_api_response import (
    WEBHOOKS_CARD_RESULTS,
    WEBHOOKS_PAYPALL_PAYMENT_FAILED_RESULTS,
    WEBHOOKS_PAYPALL_RESULTS,
    WEBHOOKS_PAYPALL_RESULTS_2,
    WEBHOOKS_PAYPALL_RESULTS_3,
    WEBHOOKS_PAYPALL_RESULTS_PENNDING,
)
from payments.views import (
    AdyenCreateSessionView,
    PrimerClientSessionView,
)


@pytest.mark.django_db
@pytest.mark.nbp
class TestPayment:
    notification_url = reverse('payment-notifications')

    def test_update_transaction_after_adyen_notification(
        self,
        api_client,
        test_order,
        notification_success_data,
    ):
        api_client.force_authenticate(user=test_order.owner)

        url = reverse('order-payment-url-live', args=[test_order.id])

        api_client.get(url)

        assert test_order.transactions.count() == 1

        transaction = test_order.transactions.first()
        assert transaction.status == ''

        notification_success = notification_success_data
        notification_success['merchantReference'] = transaction.merchant_reference

        api_client.post(self.notification_url, notification_success)

        assert transaction.notification_set.count() == 1
        assert transaction.notification_set.first().success

        assert transaction.reference == ''
        assert transaction.payment_method == ''
        assert transaction.order.chosen_payment_method is None

        assert test_order.transactions.first().status == 'AUTHORISATION'
        assert OrderToProduction.objects.filter(original_order=test_order).count() == 1

    def test_get_success_notification(
        self,
        api_client,
        test_order,
        notification_success_data,
    ):
        api_client.force_authenticate(user=test_order.owner)

        url = reverse('order-payment-url-live', args=[test_order.id])

        api_client.get(url)

        assert test_order.transactions.count() == 1

        transaction = test_order.transactions.first()
        assert transaction.status == ''

        notification_success = notification_success_data
        notification_success['merchantReference'] = transaction.merchant_reference

        api_client.post(self.notification_url, notification_success)

        assert transaction.notification_set.count() == 1
        assert transaction.notification_set.first().success
        assert test_order.transactions.first().status == 'AUTHORISATION'
        assert OrderToProduction.objects.filter(original_order=test_order).count() == 1

    def test_get_2_success_notifications(
        self,
        api_client,
        order_data,
        test_order,
        notification_success_data,
    ):
        """
        Strange situation when adyen sends notification twice.
        Apparently such case used to happen long time ago.
        """
        api_client.force_authenticate(user=test_order.owner)
        url = reverse('order-payment-url-live', args=[test_order.id])
        api_client.get(url)
        transaction = test_order.transactions.first()
        assert test_order.transactions.count() == 1
        assert transaction.status == ''

        notification_success_1 = notification_success_data
        notification_success_2 = notification_success_data
        notification_success_1['merchantReference'] = transaction.merchant_reference
        notification_success_2['merchantReference'] = transaction.merchant_reference

        api_client.post(self.notification_url, notification_success_1)
        api_client.post(self.notification_url, notification_success_2)

        assert transaction.notification_set.count() == 2
        assert transaction.notification_set.first().success
        assert test_order.transactions.first().status == 'AUTHORISATION'
        assert OrderToProduction.objects.filter(original_order=test_order).count() == 1

    def test_get_2_success_notifications_for_2_transactions(
        self,
        api_client,
        order_data,
        test_order,
        notification_success_data,
    ):
        """
        Strange case that used to happen long time ago.
        Probably to avoid duplicates in production.
        """
        api_client.force_authenticate(user=test_order.owner)
        url = reverse('order-payment-url-live', args=[test_order.id])

        response1 = api_client.get(url)
        response2 = api_client.get(url)
        transaction_1 = test_order.transactions.all()[0]
        transaction_2 = test_order.transactions.all()[1]

        assert response1.status_code == status.HTTP_200_OK
        assert response2.status_code == status.HTTP_200_OK
        assert test_order.transactions.count() == 2
        assert transaction_1.status == ''
        assert transaction_2.status == ''

        notification_success_1 = notification_success_data
        notification_success_2 = notification_success_data
        notification_success_1['merchantReference'] = transaction_1.merchant_reference
        notification_success_2['merchantReference'] = transaction_2.merchant_reference

        response = api_client.post(self.notification_url, notification_success_1)

        assert response.status_code == status.HTTP_200_OK

        response = api_client.post(self.notification_url, notification_success_2)

        assert response.status_code == status.HTTP_200_OK
        assert test_order.transactions.first().notification_set.count() == 1
        assert test_order.transactions.first().notification_set.first().success
        assert test_order.transactions.first().status == 'AUTHORISATION'
        assert OrderToProduction.objects.filter(original_order=test_order).count() == 1

    def test_get_failure_notification(
        self,
        api_client,
        order_data,
        test_order,
        notification_failed_data,
    ):
        api_client.force_authenticate(user=test_order.owner)
        url = reverse('order-payment-url-live', args=[test_order.id])

        api_client.get(url)

        assert test_order.transactions.count() == 1
        assert test_order.transactions.first().status == ''

        notification_failed = copy(notification_failed_data)
        notification_failed['merchantReference'] = test_order.adyen_reference

        api_client.post(self.notification_url, notification_failed)

        assert test_order.transactions.first().notification_set.count() == 1
        assert not test_order.transactions.first().notification_set.first().success
        assert test_order.transactions.first().status == 'AUTHORISATION'
        assert (
            test_order.transactions.first().notification_set.first().reason
            == '3d-secure: Authentication failed'
        )
        assert OrderToProduction.objects.filter(original_order=test_order).count() == 0


@pytest.mark.django_db
@pytest.mark.nbp
class TestPrimerNotifications:
    def test_primer_notification_flow_for_not_paypal_payment(
        self,
        api_client,
        user,
        country_factory,
        order_factory,
        transaction_factory,
    ):
        """Test case which test successful notification after payment by any other
        method except of paypal which has a different flow than others method
        you can see payment flow details here:
        (https://cstm-tasks.atlassian.net/wiki/spaces/PAYM/pages/2273804326/Primer)
        """

        number_of_notification_object = PrimerNotification.objects.count()
        country = country_factory(germany=True)
        order = order_factory(
            status=OrderStatus.PAYMENT_PENDING,
            owner=user,
            region=country.region,
        )
        transaction_factory(
            order=order,
            reference=WEBHOOKS_CARD_RESULTS['payment']['transactions'][0][
                'processorTransactionId'
            ],
            status='PENDING',
        )
        url = reverse('payment-primer-notifications')
        with mock.patch(
            'payments.primer.PrimerClient.validate_webhook_signature', return_value=True
        ):
            response = api_client.post(
                url,
                WEBHOOKS_CARD_RESULTS,
                format='json',
                HTTP_x_signature_primary='test',
            )

        assert 200 == response.status_code
        assert number_of_notification_object == PrimerNotification.objects.count() - 1
        assert OrderToProduction.objects.count() == 0

    def test_primer_notification_flow_broken_signature(
        self,
        api_client,
        user,
        country_factory,
        order_factory,
        transaction_factory,
    ):
        number_of_notification_object = PrimerNotification.objects.count()
        country = country_factory(germany=True)
        order = order_factory(
            status=OrderStatus.PAYMENT_PENDING,
            owner=user,
            region=country.region,
        )
        transaction_factory(
            order=order,
            reference=WEBHOOKS_CARD_RESULTS['payment']['transactions'][0][
                'processorTransactionId'
            ],
            status='PENDING',
        )
        url = reverse('payment-primer-notifications')
        with mock.patch(
            'payments.primer.PrimerClient.validate_webhook_signature',
            return_value=False,
        ):
            response = api_client.post(
                url,
                WEBHOOKS_CARD_RESULTS,
                format='json',
                HTTP_x_signature_primary='test',
            )

        assert 401 == response.status_code
        assert number_of_notification_object == PrimerNotification.objects.count()
        assert OrderToProduction.objects.count() == 0

    def test_primer_notification_flow_for_paypal_payment(
        self,
        api_client,
        user,
        country_factory,
        order_factory,
        transaction_factory,
    ):
        number_of_notification_object = PrimerNotification.objects.count()

        country = country_factory(germany=True)
        order = order_factory(
            status=OrderStatus.PAYMENT_PENDING,
            owner=user,
            region=country.region,
        )
        transaction_factory(
            order=order,
            reference='test',
            status='PENDING',
        )
        WEBHOOKS_PAYPALL_RESULTS['payment']['orderId'] = f'order-{order.pk}'
        url = reverse('payment-primer-notifications')
        with mock.patch(
            'payments.primer.PrimerClient.validate_webhook_signature', return_value=True
        ):
            response = api_client.post(
                url,
                WEBHOOKS_PAYPALL_RESULTS,
                format='json',
                HTTP_x_signature_primary='test',
            )
        assert 200 == response.status_code
        assert number_of_notification_object == PrimerNotification.objects.count() - 1
        assert OrderToProduction.objects.filter(original_order=order).count() == 1

    def test_primer_notification_flow_for_paypal_payment_multiple_notification(
        self,
        api_client,
        user,
        country_factory,
        order_factory,
        transaction_factory,
    ):
        """Test case which test notification after paypal payment by primer:
        in this test primer send two notification:
        (first notification is in pending status and the secound one is authorized)
        """
        number_of_notification_object = PrimerNotification.objects.count()
        country = country_factory(germany=True)
        order = order_factory(
            status=OrderStatus.PAYMENT_PENDING,
            owner=user,
            region=country.region,
        )
        transaction_factory(
            order=order,
            reference=WEBHOOKS_PAYPALL_RESULTS_2['payment']['transactions'][0][
                'processorTransactionId'
            ],
            status='PENDING',
        )
        WEBHOOKS_PAYPALL_RESULTS_2['payment']['orderId'] = f'order-{order.pk}'
        WEBHOOKS_PAYPALL_RESULTS_PENNDING['payment']['orderId'] = f'order-{order.pk}'

        url = reverse('payment-primer-notifications')
        with mock.patch(
            'payments.primer.PrimerClient.validate_webhook_signature', return_value=True
        ):
            response = api_client.post(
                url,
                WEBHOOKS_PAYPALL_RESULTS_PENNDING,
                format='json',
                HTTP_x_signature_primary='test',
            )

        assert 200 == response.status_code
        assert number_of_notification_object == PrimerNotification.objects.count() - 1
        assert OrderToProduction.objects.filter(original_order=order).count() == 0

        with mock.patch(
            'payments.primer.PrimerClient.validate_webhook_signature', return_value=True
        ):
            response = api_client.post(
                url,
                WEBHOOKS_PAYPALL_RESULTS_2,
                format='json',
                HTTP_x_signature_primary='test',
            )
        assert 200 == response.status_code
        assert number_of_notification_object == PrimerNotification.objects.count() - 1
        assert OrderToProduction.objects.filter(original_order=order).count() == 1

    def test_primer_notification_flow_for_paypal_payment_catch_by_order_id(
        self,
        api_client,
        user,
        country_factory,
        order_factory,
        transaction_factory,
    ):
        """Test case which test successful notification after paypal payment
        by primer. different for this test case is that transaction is catch by
        order which is include in notification data
        """

        number_of_notification_object = PrimerNotification.objects.count()
        country = country_factory(germany=True)
        order = order_factory(
            status=OrderStatus.PAYMENT_PENDING,
            owner=user,
            region=country.region,
        )
        transaction_factory(
            order=order,
            reference='some_wrong_refernce_code',
            status='PENDING',
        )
        url = reverse('payment-primer-notifications')
        WEBHOOKS_PAYPALL_RESULTS_3['payment']['orderId'] = f'order-{order.pk}'

        with mock.patch(
            'payments.primer.PrimerClient.validate_webhook_signature', return_value=True
        ):
            response = api_client.post(
                url,
                WEBHOOKS_PAYPALL_RESULTS_3,
                format='json',
                HTTP_x_signature_primary='test',
            )
        assert 200 == response.status_code
        assert number_of_notification_object == PrimerNotification.objects.count() - 1
        assert OrderToProduction.objects.filter(original_order=order).count() == 1


@pytest.mark.django_db
class TestPaymentStatusAfterAdyenDropIn:
    @pytest.mark.parametrize(
        ('adyen_status', 'url_to_redirect'),
        [
            (AdyenResponseStatus.AUTHORISED, 'confirmation'),
            (AdyenResponseStatus.RECEIVED, 'confirmation_pending'),
            (AdyenResponseStatus.PENDING, 'confirmation_pending'),
            (AdyenResponseStatus.REFUSED, 'payment_methods'),
            (AdyenResponseStatus.CANCELLED, 'payment_methods'),
            (AdyenResponseStatus.ERROR, 'payment_methods'),
        ],
    )
    def test_payment_redirect(
        self,
        adyen_status,
        url_to_redirect,
        user,
        api_client,
        order_factory,
        transaction_factory,
        region_de,
        country_factory,
    ):
        region_de.countries.add(country_factory(germany=True))
        order = order_factory(owner=user, region=region_de)
        transaction_factory(order=order)
        url = (
            f'{reverse("adyen-redirect", kwargs={"pk": order.pk})}'
            f'?uuid={order.id}&redirectResult=kakademona'
        )
        with mock.patch(
            'payments.services.adyen_result_handler.AdyenPaymentResultHandler.'
            'get_status',
            return_value=adyen_status,
        ):
            response = api_client.get(url)
        assert response.status_code == status.HTTP_302_FOUND
        assert url_to_redirect in response.url


@pytest.mark.django_db
class TestPrimerPayment:
    @mock.patch(
        'payments.views.BaseCreateSessionMixin.validate_order', return_value=None
    )
    def test_create_transaction_after_create_payment_session(
        self,
        validate_order_mock,
        user,
        api_client,
        order_factory,
        user_factory,
        region_de,
    ):
        transaction_count = Transaction.objects.count()
        user = user_factory(profile__region=region_de)
        order = order_factory(owner=user, country='germany', invoice_country='germany')
        api_client.force_authenticate(user=order.owner)
        assert transaction_count == 0

        with mock.patch(
            'payments.views.PrimerClientSessionService.create_client_session'
        ) as mock_create_client_session:
            response_from_primer = Mock()
            response_from_primer.status_code = 200
            response_from_primer.raise_for_status.return_value = None
            response_from_primer.json.return_value = {
                'test': 'ok',
                'orderId': f'order-{order.id}/test.com',
            }
            mock_create_client_session.return_value = response_from_primer
            mock_create_client_session.status_code = 200

            api_client.post(reverse('primer'))

        assert transaction_count == Transaction.objects.count() - 1

    @pytest.mark.parametrize(
        'reverse_url',
        [
            reverse('primer'),
        ],
    )
    def test_create_session_when_order_is_empty(
        self,
        user,
        api_client,
        order_factory,
        reverse_url,
    ):
        order = order_factory(
            owner=user,
            first_name='Test',
            last_name='Test',
            email='<EMAIL>',
            phone='123456789',
            street_address_1='test street',
            city='test city',
            postal_code='12345',
            country='PL',
            phone_prefix='+48',
            invoice_country='germany',
            status=9,
            items=[],  # empty order
        )
        api_client.force_authenticate(user=order.owner)
        response = api_client.post(reverse_url)

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.json()['detail'][0] == 'Cart is empty'

    @pytest.mark.parametrize(
        'reverse_url',
        [
            reverse('primer'),
        ],
    )
    def test_create_session_when_order_is_missing_require_data(
        self,
        user,
        api_client,
        order_factory,
        reverse_url,
    ):
        order = order_factory(
            owner=user,
            first_name='Test',
            last_name='Test',
            email='<EMAIL>',
            phone='123456789',
            street_address_1='test street',
            city=None,
            postal_code='12345',
            country='PL',
            phone_prefix='+48',
            invoice_country='germany',
            status=9,
            items=[],  # empty order
        )
        api_client.force_authenticate(user=order.owner)
        response = api_client.post(reverse_url)

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.json()['detail']['city'][0] == 'This field may not be null.'

    @mock.patch('payments.models.Transaction._handle_not_success')
    def test_sending_email_when_payment_failed_paypal(
        self,
        _handle_not_success_mock,  # noqa: PT019
        api_client,
        user,
        country_factory,
        order_factory,
        transaction_factory,
    ):
        number_of_notification_object = PrimerNotification.objects.count()
        country = country_factory(germany=True)
        order = order_factory(
            status=OrderStatus.PAYMENT_PENDING,
            owner=user,
            region=country.region,
        )
        transaction_factory(
            order=order, reference='some_wrong_refernce_code', status='PENDING'
        )
        url = reverse('payment-primer-notifications')
        WEBHOOKS_PAYPALL_PAYMENT_FAILED_RESULTS['payment']['orderId'] = (
            f'order-{order.pk}'
        )
        with mock.patch(
            'payments.primer.PrimerClient.validate_webhook_signature', return_value=True
        ):
            response = api_client.post(
                url,
                WEBHOOKS_PAYPALL_PAYMENT_FAILED_RESULTS,
                format='json',
                HTTP_x_signature_primary='test',
            )
        assert 200 == response.status_code
        assert number_of_notification_object == PrimerNotification.objects.count() - 1
        assert _handle_not_success_mock.call_count == 1


@pytest.mark.django_db
class TestPaymentRequiredOrderValues:
    @pytest.mark.parametrize(
        'view_class',
        [
            PrimerClientSessionView,
            AdyenCreateSessionView,
        ],
    )
    def test_order_payment_required_values(self, order_factory, view_class):
        order = order_factory(
            first_name='Test',
            last_name='Test',
            email='<EMAIL>',
            phone='123456789',
            street_address_1='test street',
            city='test city',
            postal_code='12345',
            country='PL',
            phone_prefix='+48',
        )
        assert view_class().validate_order(order) is None

    @pytest.mark.parametrize(
        'view_class',
        [
            PrimerClientSessionView,
            AdyenCreateSessionView,
        ],
    )
    def test_order_with_missing_required_field(self, order_factory, view_class):
        order = order_factory(
            first_name='Test1',
            last_name='Test',
            email='<EMAIL>',
            phone=None,
            street_address_1='test street',
            city='test city',
            postal_code='12345',
            country='PL',
            phone_prefix='+48',
        )

        result = view_class().validate_order(order)
        assert result.status_code == status.HTTP_400_BAD_REQUEST
        assert result.data['response'] == 'error'

    @pytest.mark.parametrize(
        'view_class',
        [
            PrimerClientSessionView,
            AdyenCreateSessionView,
        ],
    )
    def test_order_without_items(self, order_factory, view_class):
        order = order_factory(
            first_name='Test2',
            last_name='Test',
            email='<EMAIL>',
            phone='123456789',
            street_address_1='test street',
            city='test city',
            postal_code='12345',
            country='PL',
            phone_prefix='+48',
        )

        order.items.all().delete()
        result = view_class().validate_order(order)
        assert result.status_code == status.HTTP_400_BAD_REQUEST
        assert result.data['response'] == 'error'
