import logging

from django.conf import settings

from orders.models import Order
from payments.models import Transaction

logger = logging.getLogger('cstm')


def decode_order_id_from_adyen_schema_notification(order_id: str) -> str:
    """This Method exctract order_id value from adyen 'order id format':
    Adyen order_id format example: 'order-123456'
        or for not production env: order-123456/emhyr.ecom.oklyt.pl

    And result of this function always will be only id for Order:
        so from example above: 123456
    """
    if '/' in order_id:
        return order_id.split('/')[0].replace('order-', '')
    return order_id.replace('order-', '')


def create_order_id_with_adyen_schema(obj: Order) -> str:
    if settings.SITE_URL and not settings.IS_PRODUCTION:
        adyen_schema_url = settings.SITE_URL.replace('https://', '').replace('/', '')
        return f'order-{obj.pk}/{adyen_schema_url}'
    return f'order-{obj.pk}'


def update_dependencies(data: dict) -> None:
    """
    This function is used to update dependencies (transaction and order) after
    getting notification from Adyen DropIn.

    Args:
        data: Data provided by adyen notification (https://docs.adyen.com/api-explorer/)

    Returns:
        After execution of this function, transaction and order will be updated.
        updated Transaction field:
            - payment_method
            - reference
        and updated Order field:
            - chosen_payment_method
    """
    if data.get('merchantAccountCode') != 'CSTMCO_Primer':
        return None

    psp_reference = data.get('pspReference')
    merchant_reference = data.get('merchantReference')
    payment_method = data.get('paymentMethod')
    success = data.get('success', 'false') == 'true'
    paid_by_link = data.get('additionalData.paymentLinkId')

    try:
        transaction = Transaction.objects.select_related('order').get(
            merchant_reference=merchant_reference
        )
    except Transaction.DoesNotExist:
        return None

    order = transaction.order
    transaction_fields_to_update = []
    order_fields_to_update = []

    # temporary logger to track payment method scheme
    # https://cstm-tasks.atlassian.net/browse/ECO-3708
    if payment_method == 'scheme' or order.chosen_payment_method == 'scheme':
        logger.warning(
            'Spying payment method scheme'
            ' - update_dependencies (AdyenAcceptNotification)\n'
            'order: %d\n'
            'success: %s\n'
            'payment method in transaction: %s\n'
            'payment method in order: %s\n'
            'payment method in notification: %s\n',
            order.id,
            success,
            transaction.payment_method,
            order.chosen_payment_method,
            payment_method,
        )

    if not transaction.reference:
        transaction.reference = psp_reference
        transaction_fields_to_update.append('reference')

    if not transaction.payment_method or transaction.payment_method != payment_method:
        transaction.payment_method = payment_method
        transaction_fields_to_update.append('payment_method')

    if transaction_fields_to_update:
        transaction.save(update_fields=transaction_fields_to_update)

    if success and (
        not order.chosen_payment_method or order.chosen_payment_method != payment_method
    ):
        order.chosen_payment_method = payment_method
        order_fields_to_update.append('chosen_payment_method')

    if paid_by_link:
        info_pbl = f'\n\nPaid with PayByLink: {paid_by_link}'
        order.notes = order.notes + info_pbl if order.notes else info_pbl
        order_fields_to_update.append('notes')

    if order_fields_to_update:
        order.save(update_fields=order_fields_to_update)
