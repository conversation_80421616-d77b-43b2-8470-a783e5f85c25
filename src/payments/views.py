import logging

from typing import Optional
from urllib.parse import urljoin

from django.http import (
    Http404,
    HttpResponse,
    HttpResponseRedirect,
)
from django.shortcuts import get_object_or_404
from django.utils import timezone
from django.views.decorators.csrf import csrf_exempt
from django.views.generic import View
from rest_framework import (
    permissions,
    status,
)
from rest_framework.exceptions import ValidationError
from rest_framework.generics import RetrieveAPIView
from rest_framework.request import Request
from rest_framework.response import Response
from rest_framework.views import APIView

from djangorestframework_camel_case.util import camelize

from carts.services.cart_service import CartService
from checkout.enums import AdyenResultCodeEnum
from checkout.serializers import CheckoutAddressSerializer
from checkout.services.adyen_session import AdyenSessionHandler
from ecommerce_api.mixins import EcommerceAPIMixin
from orders.enums import OrderStatus
from orders.models import Order
from payments.serializers import PrimerClientSessionRequestSerializer
from payments.tasks import accept_notification
from regions.utils import reverse_with_region

from .authentications import AdyenWebhookAuthentication
from .choices import AdyenResponseStatus
from .constants import PRIMER_TEST_MESSAGE
from .models import Transaction
from .permissions import PrimerWebhookPermission
from .primer import PrimerClientSessionService
from .services.adyen_result_handler import AdyenPaymentResultHandler
from .services.gtm_purchase_event_data_handler import GTMPurchaseEventDataHandler
from .services.primer_notification_handler import PrimerNotificationHandler
from .utils import (
    decode_order_id_from_adyen_schema_notification,
    update_dependencies,
)

logger = logging.getLogger('cstm')


class AdyenAcceptNotification(APIView):
    authentication_classes = [AdyenWebhookAuthentication]
    permission_classes = [permissions.AllowAny]

    def post(self, request, *args, **kwargs):
        update_dependencies(request.POST)
        accept_notification.delay(request.POST)
        return HttpResponse('[accepted]')


class PrimerAcceptNotification(APIView):
    permission_classes = [PrimerWebhookPermission]

    def post(self, request, *args, **kwargs):
        if self.is_webhook_test(request):
            return Response()
        handler_service = PrimerNotificationHandler(request.data)
        if not handler_service.notification:
            logger.error(
                f'Broken Primer notification '
                f"(Payment ID: {request.data.get('payment', {}).get('id', 'unknown')},)"
            )
            return Response('Broken notification', status=status.HTTP_400_BAD_REQUEST)
        handler_service.handle()
        handler_service.update_transaction_if_paypal(request)
        return Response('[accepted]')

    @staticmethod
    def is_webhook_test(request: Request) -> bool:
        return request.data.get('message') == PRIMER_TEST_MESSAGE


class AdyenRedirectView(View):
    http_method_names = ['get']

    def get(self, request, *args, **kwargs):
        order = get_object_or_404(Order, id=kwargs.get('pk'))
        redirect_result = request.GET.get('redirectResult')
        payload = request.GET.get('payload')
        if not any([redirect_result, payload]):
            raise Http404
        handler = AdyenPaymentResultHandler(
            redirect_result or payload,
            order,
        )
        redirect_status = handler.get_status()
        self.update_psp_reference(order, handler)
        return self.get_redirect(redirect_status, order)

    def get_redirect(
        self,
        adyen_status: AdyenResponseStatus,
        order: Order,
    ) -> HttpResponse:
        region_code = order.region.get_country().code.lower()
        if adyen_status == AdyenResponseStatus.AUTHORISED:
            return HttpResponseRedirect(
                reverse_with_region(
                    'front-confirmation',
                    region_code,
                    args=[order.id],
                )
            )
        elif adyen_status in {
            AdyenResponseStatus.PENDING,
            AdyenResponseStatus.RECEIVED,
        }:
            return HttpResponseRedirect(
                reverse_with_region(
                    'front-confirmation-pending',
                    region_code,
                    args=[order.id],
                )
            )
        elif adyen_status in {
            AdyenResponseStatus.REFUSED,
            AdyenResponseStatus.CANCELLED,
            AdyenResponseStatus.ERROR,
        }:
            return HttpResponseRedirect(
                urljoin(
                    reverse_with_region(
                        'front-payment-method',
                        region_code,
                        args=[order.id],
                    ),
                    '?error=true',
                )
            )

    def update_psp_reference(
        self,
        order: Order,
        handler: AdyenPaymentResultHandler,
    ) -> None:
        """
        `psp_reference` can be accessed only after payment and is required for Klarna
        related actions. This is the only access point despite webhooks.
        """
        transaction = order.transactions.order_by('-created_at').first()
        if psp_reference := handler.transaction_details.get('pspReference'):
            transaction.reference = psp_reference
            transaction.save(update_fields=['reference'])
        else:
            logger.warning(
                'Lacking psp_reference for transaction with merchant_reference '
                f'{transaction.merchant_reference}'
            )


class BaseCreateSessionMixin:
    @property
    def order_id(self) -> Optional[int]:
        try:
            return int(self.request.query_params.get('order_id'))
        except (ValueError, TypeError):
            return None

    def get_order(self) -> Order:
        if order_id := self.order_id:
            return get_object_or_404(
                Order,
                id=order_id,
                owner=self.request.user,
                status__in=(
                    OrderStatus.DRAFT,
                    OrderStatus.CART,
                    OrderStatus.PAYMENT_FAILED,
                    OrderStatus.PAYMENT_PENDING,
                ),
            )

        return CartService.get_cart_related_order(self.request.user)

    def validate_order(self, order: Order) -> Optional[Response]:
        """Validate the order before creating a session

        Creating a session means that we inform our external payment provider about
        order, and they expect that the order contains all required fields to finalize
        the transaction. That's why we need to validate the order before creating a
        session.

        Args:
            order: order object to validate

        Returns:
            Response: -> Response (400) with error message if order is not valid
            None: -> If order is valid
        """
        try:
            CheckoutAddressSerializer.validate_for(order, raise_exception=True)
            if not order.items.exists():
                raise ValidationError('Cart is empty')
        except ValidationError as e:
            return Response(
                {'response': 'error', 'detail': e.detail},
                status=status.HTTP_400_BAD_REQUEST,
            )

    def set_data_for_gtm(self, order: Order) -> None:
        GTMPurchaseEventDataHandler(order).set_data_to_redis(self.request)


class AdyenCreateSessionView(BaseCreateSessionMixin, APIView):
    http_method_names = ['post']

    def create_or_update_transaction(self, order):
        try:
            Transaction.objects.update_or_create(
                order=order,
                merchant_reference=order.adyen_reference,
                defaults={
                    'status': AdyenResultCodeEnum.PENDING,
                    'amount': order.adyen_price,
                },
            )
        except Transaction.MultipleObjectsReturned:
            transaction = (
                Transaction.objects.filter(
                    order=order, merchant_reference=order.adyen_reference
                )
                .order_by('-created_at')
                .first()
            )
            transaction.status = AdyenResultCodeEnum.PENDING
            transaction.amount = order.adyen_price
            transaction.save(update_fields=['status', 'amount'])

    def post(self, request, *args, **kwargs):
        order = self.get_order()
        handler = AdyenSessionHandler(order)
        self.create_or_update_transaction(order)
        order.placed_at = timezone.now()
        order.save(update_fields=['placed_at'])
        self.set_data_for_gtm(order)
        return Response(
            status=status.HTTP_201_CREATED,
            data=handler.get_response_data(),
        )


class PrimerAPIBaseView(EcommerceAPIMixin, RetrieveAPIView):
    serializer_class = PrimerClientSessionRequestSerializer

    def get_object(self):
        return CartService.get_cart_related_order(self.request.user)

    def get_queryset(self):
        return ()

    def post(self, request, *args, **kwargs):
        raise NotImplementedError()


class PrimerClientSessionView(PrimerAPIBaseView, BaseCreateSessionMixin):
    def post(self, request, *args, **kwargs):
        order = self.get_order()
        if error_result := self.validate_order(order):
            return error_result

        data = camelize(self.get_serializer(instance=order).data)
        # paypal_custom_id shall not be camelized
        data['metadata']['paypal_custom_id'] = data['metadata'].pop('paypalCustomId')

        response = PrimerClientSessionService().create_client_session(data)
        response.raise_for_status()
        json_response = response.json()
        json_response['orderId'] = decode_order_id_from_adyen_schema_notification(
            json_response['orderId']
        )
        order = CartService.get_cart_related_order(self.request.user)
        Transaction.create_or_update_transaction_based_on_order(order)
        order.copy_address_to_profile()
        order.placed_at = timezone.now()
        order.save(update_fields=['placed_at'])
        self.set_data_for_gtm(order)
        return Response(json_response, status=status.HTTP_201_CREATED)


class PaymentPendingView(PrimerAPIBaseView):
    def post(self, request, *args, **kwargs):
        order = CartService.get_cart_related_order(self.request.user)
        order.change_status(OrderStatus.PAYMENT_PENDING)
        if not order.order_pretty_id:
            order.create_pretty_id()
        return Response(status=status.HTTP_201_CREATED)


adyenNotificationView = csrf_exempt(AdyenAcceptNotification.as_view())
