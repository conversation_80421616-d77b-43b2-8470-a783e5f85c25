# Generated by Django 4.2.23 on 2025-07-25 14:50

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        ('producers', '0131_batchingsettings_separate_extended'),
    ]

    operations = [
        migrations.AlterField(
            model_name='product',
            name='previous_status',
            field=models.IntegerField(
                choices=[
                    (0, 'Aborted'),
                    (1, 'Accepted to production'),
                    (2, 'Utilization'),
                    (3, 'Utilization Done'),
                    (4, 'Sent to production'),
                    (5, 'In production'),
                    (6, 'Sent to customer'),
                    (7, 'Finished product'),
                    (8, 'Quality Blocker'),
                    (9, 'Internal use'),
                    (10, 'To be shipped'),
                    (11, 'Quality Control'),
                    (12, 'Aborted done'),
                    (13, 'Shelfmarket'),
                    (14, 'Shelfmarket Done'),
                    (15, 'Delivered to Customer'),
                    (16, 'CM Utilization Done'),
                    (17, 'To be shipped awization'),
                    (18, 'Internal Shipment'),
                    (19, 'Return'),
                ],
                default=1,
            ),
        ),
        migrations.AlterField(
            model_name='product',
            name='status',
            field=models.IntegerField(
                choices=[
                    (0, 'Aborted'),
                    (1, 'Accepted to production'),
                    (2, 'Utilization'),
                    (3, 'Utilization Done'),
                    (4, 'Sent to production'),
                    (5, 'In production'),
                    (6, 'Sent to customer'),
                    (7, 'Finished product'),
                    (8, 'Quality Blocker'),
                    (9, 'Internal use'),
                    (10, 'To be shipped'),
                    (11, 'Quality Control'),
                    (12, 'Aborted done'),
                    (13, 'Shelfmarket'),
                    (14, 'Shelfmarket Done'),
                    (15, 'Delivered to Customer'),
                    (16, 'CM Utilization Done'),
                    (17, 'To be shipped awization'),
                    (18, 'Internal Shipment'),
                    (19, 'Return'),
                ],
                db_index=True,
                default=1,
            ),
        ),
        migrations.AlterField(
            model_name='productstatushistory',
            name='previous_status',
            field=models.IntegerField(
                choices=[
                    (0, 'Aborted'),
                    (1, 'Accepted to production'),
                    (2, 'Utilization'),
                    (3, 'Utilization Done'),
                    (4, 'Sent to production'),
                    (5, 'In production'),
                    (6, 'Sent to customer'),
                    (7, 'Finished product'),
                    (8, 'Quality Blocker'),
                    (9, 'Internal use'),
                    (10, 'To be shipped'),
                    (11, 'Quality Control'),
                    (12, 'Aborted done'),
                    (13, 'Shelfmarket'),
                    (14, 'Shelfmarket Done'),
                    (15, 'Delivered to Customer'),
                    (16, 'CM Utilization Done'),
                    (17, 'To be shipped awization'),
                    (18, 'Internal Shipment'),
                    (19, 'Return'),
                ]
            ),
        ),
        migrations.AlterField(
            model_name='productstatushistory',
            name='status',
            field=models.IntegerField(
                choices=[
                    (0, 'Aborted'),
                    (1, 'Accepted to production'),
                    (2, 'Utilization'),
                    (3, 'Utilization Done'),
                    (4, 'Sent to production'),
                    (5, 'In production'),
                    (6, 'Sent to customer'),
                    (7, 'Finished product'),
                    (8, 'Quality Blocker'),
                    (9, 'Internal use'),
                    (10, 'To be shipped'),
                    (11, 'Quality Control'),
                    (12, 'Aborted done'),
                    (13, 'Shelfmarket'),
                    (14, 'Shelfmarket Done'),
                    (15, 'Delivered to Customer'),
                    (16, 'CM Utilization Done'),
                    (17, 'To be shipped awization'),
                    (18, 'Internal Shipment'),
                    (19, 'Return'),
                ]
            ),
        ),
        migrations.AlterField(
            model_name='qualityholdreleaserequest',
            name='status',
            field=models.IntegerField(
                choices=[
                    (0, 'Aborted'),
                    (1, 'Accepted to production'),
                    (2, 'Utilization'),
                    (3, 'Utilization Done'),
                    (4, 'Sent to production'),
                    (5, 'In production'),
                    (6, 'Sent to customer'),
                    (7, 'Finished product'),
                    (8, 'Quality Blocker'),
                    (9, 'Internal use'),
                    (10, 'To be shipped'),
                    (11, 'Quality Control'),
                    (12, 'Aborted done'),
                    (13, 'Shelfmarket'),
                    (14, 'Shelfmarket Done'),
                    (15, 'Delivered to Customer'),
                    (16, 'CM Utilization Done'),
                    (17, 'To be shipped awization'),
                    (18, 'Internal Shipment'),
                    (19, 'Return'),
                ],
                help_text='The status (QC/QB) that products were in when the request was created',
            ),
        ),
    ]
