from django.contrib import (
    admin,
    messages,
)
from django.http import HttpResponseRedirect
from django.urls import reverse
from django.utils.html import escape
from django.utils.safestring import mark_safe

from admin_customization.admin import ButtonActionsBaseAdmin
from product_feeds.models import (
    OlapicFeedItem,
    OlapicTask,
)
from product_feeds.services.olapic import (
    OlapicExportService,
    OlapicFeedItemDataFiller,
)


def item_image_preview(obj):
    if obj.image:
        html = (
            "<div style='width:300px; float:left'><img src='%s' width='300px' /></div>"
        ) % (escape(obj.image.url))
        return mark_safe(html)  # noqa: S308
    else:
        return '-'


class OlapicFeedItemAdmin(ButtonActionsBaseAdmin, admin.ModelAdmin):
    model = OlapicFeedItem
    list_display = (
        'id',
        'user_nickname',
        'posted_at',
        'instagram_link',
        item_image_preview,
        'ugc_type',
        'name',
        'color',
        'category',
        'description',
        'white_list',
    )
    readonly_fields = (
        'name',
        'color',
        'category',
        'product_url',
        'product_unique_id',
        'image_url',
        item_image_preview,
    )
    list_filter = (
        'ugc_type',
        'white_list',
        'color',
        'content_type',
        'category',
    )
    search_fields = ('user_nickname', 'object_id')
    empty_value_display = '-'
    actions = ('fill_olapic_feed_items_with_furniture_data',)
    button_actions = ('export_xml_feed',)
    fieldsets = (
        (
            'GENERAL DATA',
            {
                'fields': (
                    'user_nickname',
                    'posted_at',
                    'instagram_link',
                    'ugc_type',
                    'description',
                    'google_drive_link',
                    'white_list',
                    'object_id',
                    'content_type',
                    'image',
                    'image_url',
                    item_image_preview,
                )
            },
        ),
        (
            'FURNITURE DATA',
            {
                'fields': (
                    'name',
                    'color',
                    'category',
                    'product_url',
                    'product_unique_id',
                )
            },
        ),
    )

    @admin.action(description='Fill items with furniture data')
    def fill_olapic_feed_items_with_furniture_data(self, request, queryset):
        for obj in queryset:
            furniture = obj.furniture
            if furniture:
                OlapicFeedItemDataFiller(obj).fill_data()
                (
                    self.message_user(
                        request,
                        f'Olapic Feed Item {obj.id} is filled with  with furniture data.',  # noqa: E501
                    ),
                )
            else:
                messages.error(
                    request,
                    f'Furniture {obj.content_type.name} with id {obj.object_id} '
                    f'does not exist! Olapic Feed Item {obj.id} could not be filled '
                    f'with data!',
                )

    @admin.display(description='Export xml feed')
    def export_xml_feed(self, request):
        export_service = OlapicExportService()
        export_service.export(request.user)
        if export_service.warning_message:
            messages.warning(request, export_service.warning_message)
        if export_service.success:
            messages.info(
                request,
                'The feed passed Olapic schema validation and ordered to be put '
                'to sftp server.',
            )
        else:
            messages.error(request, export_service.error_message)
        return HttpResponseRedirect(
            reverse('admin:product_feeds_olapicfeeditem_changelist')
        )


class OlapicTaskAdmin(admin.ModelAdmin):
    list_display = (
        'id',
        'creator',
        'exported_at',
        'updated_at',
        'status',
    )
    readonly_fields = (
        'creator',
        'exported_at',
        'updated_at',
        'status',
        'xml_file',
    )

    def has_add_permission(self, request, obj=None):
        return False


admin.site.register(OlapicFeedItem, OlapicFeedItemAdmin)
admin.site.register(OlapicTask, OlapicTaskAdmin)
