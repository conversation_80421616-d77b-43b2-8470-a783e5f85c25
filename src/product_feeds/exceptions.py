class SkippableSerializationError(Exception):
    """
    Raised in SkippableListSerializer whenever problem with instance is skippable.
    """


class FeedCrawlerException(Exception):  # noqa: N818
    message = 'error'


class NoContentException(FeedCrawlerException):
    message = 'No content'


class BadResponseStatusException(FeedCrawlerException):
    message = 'Bad response status'


class FeedItemException(Exception):  # noqa: N818
    def __init__(self, message=None):
        super().__init__(message)
        if message:
            self.message = message


class NoPriceException(FeedItemException):
    message = "Couldn't find price in schema"


class WrongPriceException(FeedItemException):
    message = 'Wrong price'


class WrongCurrencyException(FeedItemException):
    message = 'Wrong currency'


class OlapicFeedDidNotPassSchemaValidationError(Exception):
    """Raised when Olapic feed does not pass schema validation."""

    message = 'Olapic feed did not pass schema validation.'
