import contextlib
import csv
import io

from datetime import datetime
from decimal import (
    ROUND_HALF_UP,
    Decimal,
)

from django.conf import settings
from django.contrib.contenttypes.fields import GenericForeignKey
from django.contrib.contenttypes.models import ContentType
from django.core.cache import cache
from django.core.exceptions import ObjectDoesNotExist
from django.core.files.base import ContentFile
from django.db import models
from django.db.models import Q
from django.utils import (
    timezone,
    translation,
)
from django.utils.translation import gettext

from PIL import Image

from custom.enums import ShelfType
from gallery.enums import FurnitureStatusEnum
from gallery.models import Watty
from product_feeds.choices import (
    FeedContentTypes,
    FeedImageBackgroundColors,
    FeedImageCameraSettings,
    FeedImageColorOverrides,
    FeedImageResolutions,
    FeedVariantStatus,
    FeedVariantValidationStatus,
    OlapicTaskStatuses,
    UGCTypes,
    all_colors_choices,
)
from promotions.utils import strikethrough_promo
from regions.constants import OTHER_REGION_NAME
from regions.models import (
    Country,
    Currency,
)

HEIGHT_LINE_CHECK = 0.6  # check pixels on 60% of image height
TOP_LINE_CHECK_MARGIN = 0.01  # it should have 1% margin from top
WIDTH_LIMIT_FROM_LEFT_SIDE = (
    0.05  # check if we have at least 5% of margins from left side
)
RGB_MAX_THRESHOLD = 25  # max diff on R,G,B allowed from background

# check for 20 pixels right and down from main background pixel (1,1)
# to understand if this is our render, or photo from session
RANGE_TO_CHECK_IF_OUR_RENDER = 20


def check_difference_in_pixels(pixel1, pixel2, threshold):
    return (
        max(
            abs(pixel1[0] - pixel2[0]),
            abs(pixel1[1] - pixel2[1]),
            abs(pixel1[2] - pixel2[2]),
        )
        > threshold
    )


def feed_data_methods_registry():
    registry = {}

    def datamethod_decorator(func):
        registry[func.__name__] = getattr(func, 'description', '')
        func.is_data_getter = True
        return func

    datamethod_decorator.all = registry
    return datamethod_decorator


data_method = feed_data_methods_registry()


class FeedCategory(models.Model):
    name = models.CharField(max_length=250)
    static_values = models.JSONField(null=True, blank=True, default=dict)

    class Meta:
        verbose_name = 'Feed item category'
        verbose_name_plural = 'Feed item categories'
        ordering = ('name',)

    def __str__(self):
        return self.name

    def copy_items_from(self, other_category, save_images):
        self.delete_duplicated_items(other_category)

        for feed_item in other_category.feeditem_set.all():
            new_feed_item = self.feeditem_set.create(
                furniture_in_category=feed_item.furniture_in_category
            )
            new_feed_item.save()
            if save_images:
                for image in feed_item.feeditemimage_set.all():
                    new_image = FeedItemImage()
                    new_image.image = image.image
                    new_image.config = image.config
                    new_image.item = new_feed_item
                    new_image.save()

    def delete_duplicated_items(self, other_cat):
        """To avoid deleting an object with identical furniture_id but with different
        content type we use this robust filtering.
        """
        jetty_ct = ContentType.objects.get(model='jetty')
        watty_ct = ContentType.objects.get(model='watty')

        self.feeditem_set.filter(
            Q(
                furniture_in_category__content_type=jetty_ct,
                furniture_in_category__furniture_id__in=other_cat.feeditem_set.filter(
                    furniture_in_category__content_type=jetty_ct,
                ).values_list('furniture_in_category__furniture_id'),
            )
            | Q(
                furniture_in_category__content_type=watty_ct,
                furniture_in_category__furniture_id__in=other_cat.feeditem_set.filter(
                    furniture_in_category__content_type=watty_ct,
                ).values_list('furniture_in_category__furniture_id'),
            )
        ).delete()


class FeedCategoryLocalizedName(models.Model):
    category = models.ForeignKey(
        FeedCategory,
        on_delete=models.CASCADE,
    )
    language = models.CharField(max_length=2)
    name = models.CharField(max_length=100)

    class Meta(object):
        unique_together = (('category', 'language'),)


class FeedCategoryCopy(models.Model):
    category = models.ForeignKey(
        FeedCategory,
        on_delete=models.CASCADE,
    )
    title = models.CharField(max_length=120)
    description = models.CharField(max_length=1000)
    color = models.IntegerField(choices=all_colors_choices(), null=True)
    language = models.CharField(max_length=2)  # from Country.get_all_language_codes()

    def __str__(self):
        return 'copy for {}: {}, {}'.format(
            self.category.name, self.color, self.language
        )

    class Meta(object):  # noqa: DJ012
        verbose_name = 'Copy description/fields for feed'
        verbose_name_plural = 'Copy descriptions'
        unique_together = (('category', 'color', 'language'),)


class Feed(models.Model):
    """
    column config should contain column names as keys and getter method names as values

    """

    validated = models.BooleanField(default=False)
    validation_scheduled_at = models.DateTimeField(null=True, editable=False)
    validated_at = models.DateTimeField(null=True, editable=False)
    slug = models.CharField(max_length=100, unique=True)
    name = models.CharField(max_length=50)
    language = models.CharField(max_length=2)  # from Country.get_all_language_codes()
    content_type = models.CharField(max_length=3, choices=FeedContentTypes.choices)
    categories = models.ManyToManyField(FeedCategory)
    column_config = models.JSONField(null=True, blank=True, default=list)
    static_values = models.JSONField(null=True, blank=True, default=dict)
    active = models.BooleanField(default=False)

    def __str__(self):
        return self.name

    class Meta(object):  # noqa: DJ012
        verbose_name = 'Feed definition'
        verbose_name_plural = 'Feeds'

    @property
    def dialect(self):
        dialects = {'csv': 'excel', 'tsv': 'excel-tab'}
        return dialects.get(str(self.content_type), 'excel')

    @property
    def content_type_display(self):
        return dict(FeedContentTypes.choices).get(str(self.content_type))

    @property
    def separator(self):
        separators = {
            'csv': ',',
            'tsv': '\t',
        }
        return separators.get(str(self.content_type), ',')

    @property
    def feed_items(self):
        return (
            FeedItem.objects.filter(category_id__in=self.categories.values('id'))
            .select_related(
                'furniture_in_category',
                'category',
            )
            .prefetch_related(
                'furniture_in_category__furniture',
                'furniture_in_category__furniture__owner',
                'furniture_in_category__furniture__owner__profile',
                'furniture_in_category__furniture__owner__profile__region',
                'furniture_in_category__furniture__owner__profile__region__currency',
                'furniture_in_category__furniture__owner__profile__region__currency__rates',
                'furniture_in_category__furniture__additional_images',
                'category__feed_set__feedimageslot_set',
                'feeditemimage_set',
                'category__feedcategorycopy_set',
            )
        )

    @property
    def feed_items_count(self):
        return self.feed_items.count()

    def gen_data_row(self, feed_item, raise_exception=True, **kwargs):
        # now it needs refactoring
        row = []
        kwargs.update({'feed': self})
        has_errors = False
        category_static_values = feed_item.category.static_values or {}
        for column in self.column_config:
            try:
                attr_name = column['attribute']
                feed_attr = getattr(feed_item, attr_name)
                if attr_name in data_method.all:  # allow only registered methods
                    data = feed_attr(**kwargs) if callable(feed_attr) else feed_attr
                else:
                    raise AttributeError  # try static if not registered or not existing
            except AttributeError:
                error = 'unable to find value for {}'.format(column['attribute'])
                data = category_static_values.get(
                    column['header'], self.static_values.get(column['header'], error)
                )
                if data == error:
                    has_errors = True
            except Exception as e:
                if 'show_errors' in kwargs:
                    has_errors = True
                    data = str(e)
                elif raise_exception:
                    raise e
                else:
                    return None
            if isinstance(data, tuple):
                row.extend(data)
            else:
                row.append(str(data))
        if 'show_errors' in kwargs and not has_errors:
            return None
        return row

    @property
    def image_headers(self):
        headers = []
        for image_slot in self.feedimageslot_set.order_by('slot_number'):
            headers.append(image_slot.header)  # noqa: PERF401
        return headers

    @property
    def headers(self):
        headers = list()  # noqa: C408
        for column in self.column_config:
            if column['header'] == 'images_placeholder':
                headers.extend(self.image_headers)
            else:
                headers.append(column['header'])
        return headers

    def data_rows(self, variant, limit=None, raise_exception=True, **kwargs):
        rows = []
        kwargs['variant'] = variant
        feed_items = self.feed_items
        if limit and self.feed_items_count > limit:
            feed_items = feed_items[:limit]
        for feed_item in feed_items:
            row = self.gen_data_row(feed_item, raise_exception, **kwargs)
            if row:
                rows.append(row)
        return rows

    def validate(self, variant):
        error_rows = self.data_rows(
            variant=variant,
            cached=False,
            show_errors=True,
            domain='http://placeholder',
            raise_exception=False,
        )
        if error_rows:
            self.validated = False
        else:
            self.validated = True
        self.save()
        return self.validated


class FeedVariant(models.Model):
    """Created to differentiate language and currency for the same Feeds."""

    feed = models.ForeignKey(Feed, on_delete=models.CASCADE, related_name='variants')
    currency = models.ForeignKey(Currency, on_delete=models.CASCADE)
    country = models.ForeignKey(
        Country,
        on_delete=models.CASCADE,
        limit_choices_to=~Q(region__name=OTHER_REGION_NAME),
    )
    file = models.FileField(
        upload_to='product_feeds/feed_variant/%Y/%m', null=True, editable=False
    )
    file_updated_at = models.DateTimeField(null=True, editable=False)
    status = models.IntegerField(
        choices=FeedVariantStatus.choices,
        default=FeedVariantStatus.NOT_GENERATED,
    )
    validation_status = models.IntegerField(
        choices=FeedVariantValidationStatus.choices,
        default=FeedVariantValidationStatus.NOT_VALIDATED,
    )
    validation_started_at = models.DateTimeField(null=True, blank=True)
    validation_errors = models.TextField(blank=True, null=True)  # noqa: DJ001
    last_processing_time = models.DurationField(blank=True, null=True)

    class Meta:
        unique_together = ('currency', 'country', 'feed')

    def __str__(self):
        return f'{self.feed} - {self.country.code} {self.currency.code}'

    def regenerate_feed_file(self, domain, raise_exception=True):
        start_time = datetime.now()

        try:
            self._regenerate_feed_file(domain, raise_exception)
        except Exception:
            self.status = FeedVariantStatus.ERROR
            self.save(update_fields=('status',))
        else:
            finish_time = datetime.now()
            self.file_updated_at = finish_time
            self.last_processing_time = finish_time - start_time
            self.status = FeedVariantStatus.DONE
            self.save(
                update_fields=('file_updated_at', 'status', 'last_processing_time')
            )

    def _regenerate_feed_file(self, domain, raise_exception=True):
        translation.activate(self.feed.language)
        data = self.feed.data_rows(
            variant=self,
            domain=domain,
            raise_exception=raise_exception,
        )

        with io.StringIO() as file_data:
            writer = csv.writer(file_data, dialect=self.feed.dialect)
            writer.writerow(self.feed.headers)
            for row in data:
                writer.writerow(row)
            file_data.seek(0)
            self.file.save(
                f'{self.feed.slug}_{self.country.code}_{self.currency.code}.csv',
                ContentFile(bytes(file_data.read(), 'utf-8')),
            )


class FeedImageConfig(models.Model):
    name = models.CharField(max_length=200)
    square_crop = models.BooleanField(default=False)
    resolution = models.CharField(max_length=20, choices=FeedImageResolutions.choices)
    is_static = models.BooleanField(
        default=False
    )  # True should tell not to generate FeedItemImages.image
    has_scene = models.BooleanField(default=False)
    open_compartments = models.BooleanField(
        default=False, help_text='Open doors/drawers'
    )
    camera_setting = models.CharField(  # noqa: DJ001
        max_length=20, choices=FeedImageCameraSettings.choices, null=True, blank=True
    )
    background_color = models.CharField(  # noqa: DJ001
        max_length=20, choices=FeedImageBackgroundColors.choices, null=True, blank=True
    )
    override_color = models.IntegerField(
        default=FeedImageColorOverrides.ORIGINAL,
        choices=FeedImageColorOverrides.choices,
    )
    has_zoom_enabled = models.BooleanField(
        default=False, help_text='enabled = larger shelves in screenshots'
    )
    has_items = models.BooleanField(
        default=False, help_text='items enabled on screenshots'
    )
    items_for_render_strategy = models.ForeignKey(
        'items_for_render.Strategy', on_delete=models.SET_NULL, null=True, blank=True
    )
    uses_external_renderer = models.BooleanField(
        default=False, help_text='(Experimental) Uses external ShelfView renderer'
    )
    external_renderer_options = models.TextField(  # noqa: DJ001
        null=True,
        blank=True,
        help_text='(Experimental) Additional setup for external renderer',
    )

    def __str__(self):
        return 'ID: {},{}'.format(self.id, self.name)


class FeedImageSlot(models.Model):
    feed = models.ForeignKey(
        Feed,
        on_delete=models.CASCADE,
    )
    image_config = models.ForeignKey(
        FeedImageConfig,
        on_delete=models.CASCADE,
    )
    header = models.CharField(max_length=200)
    slot_number = models.SmallIntegerField()

    def __str__(self):
        return '{}:{}'.format(self.feed.name, self.slot_number)

    class Meta(object):  # noqa: DJ012
        verbose_name = 'Feed image slots configuration'
        verbose_name_plural = 'Feed image slots configurations'


class FeedItem(models.Model):
    category = models.ForeignKey(
        FeedCategory,
        on_delete=models.CASCADE,
    )
    furniture_in_category = models.ForeignKey(
        'rating_tool.FurnitureInCategory',
        on_delete=models.CASCADE,
    )

    class Meta(object):
        # TODO: uncomment after removing confliting `FeedItem` instances
        # unique_together = (('category', 'furniture_in_category'),)
        verbose_name = 'Feed item'
        verbose_name_plural = 'Feed items'

    def __str__(self):
        return 'Category: {}, gallery id: {} {}'.format(
            self.category.name,
            self.furniture_in_category.content_type.model,
            self.furniture_in_category.furniture_id,
        )

    def get_feed(self, **kwargs):
        feed = kwargs.get('feed', None)
        if not feed:
            raise TypeError('Should have feed argument passed')
        return feed

    def set_furniture_status(self):
        if (
            self.furniture_in_category.furniture.furniture_status
            != FurnitureStatusEnum.SPECIAL
        ):
            self.furniture_in_category.furniture.furniture_status = (
                FurnitureStatusEnum.SPECIAL
            )
            self.furniture_in_category.furniture.save()

    @data_method
    def item_id(self, feed=None, **kwargs):
        """Sku or fallback jetty id"""
        if not feed:
            return self.jetty_id(**kwargs)
        else:
            return self.jetty_id(**kwargs)

    @data_method
    def id_cat_id(self, **kwargs):
        return '{}C{}'.format(self._furniture_id_for_feed, self.category_id)

    @data_method
    def title_with_id(self, **kwargs):
        return 'title_{}'.format(self._furniture_id_for_feed)

    @data_method
    def description_with_id(self, **kwargs):
        return 'description_{}'.format(self._furniture_id_for_feed)

    # FIXME: change name to something without `jetty` in it
    @data_method
    def jetty_id(self, **kwargs):
        return str(self._furniture_id_for_feed)

    def category_copy(self, **kwargs):
        feed_category_copy = ''
        feed = self.get_feed(**kwargs)
        if feed:
            with contextlib.suppress(ObjectDoesNotExist):
                feed_category_copy = self.category.feedcategorycopy_set.get(
                    language=feed.language,
                    color=self.furniture_in_category.color_for_feeds,
                )
        return feed_category_copy

    @data_method
    def localized_category_name(self, **kwargs):
        feed = self.get_feed(**kwargs)
        return self.category.feedcategorylocalizedname_set.get(
            language=feed.language
        ).name

    @data_method
    def title(self, **kwargs):
        copy = self.category_copy(**kwargs)
        return copy.title

    @data_method
    def description(self, **kwargs):
        copy = self.category_copy(**kwargs)
        return copy.description

    # FIXME: change name to something without `jetty` in it
    @data_method
    def jetty_price(self, **kwargs):
        if not hasattr(self, '_furniture_price'):
            furniture = self.furniture_in_category.furniture
            self._furniture_price = furniture.get_regionalized_price(
                self.region(**kwargs),
                self.currency(**kwargs),
            )
        return self._furniture_price

    def region(self, **kwargs):
        feed_variant = kwargs.get('variant')
        if not hasattr(self, '_region'):
            self._region = feed_variant.country.region
        return self._region

    def currency(self, **kwargs):
        feed_variant = kwargs.get('variant')
        if not hasattr(self, '_currency'):
            self._currency = feed_variant.currency
        return self._currency

    @data_method
    def price(self, **kwargs):
        return '{:.2f}'.format(self.jetty_price(**kwargs))

    @data_method
    def price_with_currency(self, **kwargs):
        return '{:.2f} {}'.format(self.jetty_price(**kwargs), self._currency.code)

    @data_method
    def shipping_price(self, **kwargs):
        return '{:.2f} {}'.format(0, self._currency.code)

    @data_method
    def price_regionalized(self, **kwargs):
        cached_region_data = self.region(**kwargs).cached_region_data
        return cached_region_data.get_format_price(self.jetty_price(**kwargs))

    @data_method
    def sale_price(self, **kwargs):
        voucher = cache.get('strikethrough_promo_voucher')
        if not voucher:
            promo = strikethrough_promo(self.region(**kwargs))
            if promo:
                voucher = promo.promo_code
                cache.set('strikethrough_promo_voucher', voucher, 30 * 60)

        if not voucher:
            return self.price_with_currency(**kwargs)

        return '{:.2f} {}'.format(
            voucher.calculate_price_for_furniture(
                self.furniture_in_category.furniture, self.jetty_price(**kwargs)
            ).quantize(
                Decimal('1'),
                rounding=ROUND_HALF_UP,
            ),
            self._currency.code,
        )

    @data_method
    def color(self, **kwargs):
        return self.furniture_in_category.furniture.color.translated_color.capitalize()

    @data_method
    def material(self, **kwargs):
        if self.furniture_in_category.furniture.shelf_type in (
            ShelfType.TYPE02.value,
            ShelfType.TYPE03.value,
        ):
            # copy for particle board so applies for Type03
            return gettext('comparison_products_material_type02')

        return self.furniture_in_category.furniture.get_material()

    @data_method
    def size(self, **kwargs):
        furniture = self.furniture_in_category.furniture
        return '{} cm x {} cm {} cm'.format(
            furniture.get_width(), furniture.get_height(), furniture.get_depth()
        )

    @data_method
    def length(self, **kwargs):
        return '{} cm'.format(self.furniture_in_category.furniture.get_width())

    @data_method
    def height(self, **kwargs):
        return '{} cm'.format(self.furniture_in_category.furniture.get_height())

    @data_method
    def depth(self, **kwargs):
        return '{} cm'.format(self.furniture_in_category.furniture.get_depth())

    @data_method
    def shipping_weight(self, **kwargs):
        if self.furniture_in_category.weight is not None:
            return str(self.furniture_in_category.weight)

        self.furniture_in_category.weight = (
            self.furniture_in_category.furniture.get_estimated_weight_gross()
        )
        if not self.furniture_in_category.weight:
            self.furniture_in_category.weight = (
                self.furniture_in_category.furniture.get_accurate_weight_gross()
            )
        self.furniture_in_category.save(update_fields=['weight'])
        return str(self.furniture_in_category.weight)

    @data_method
    def pattern(self, **kwargs):
        return str(self.furniture_in_category.furniture.get_pattern_name().capitalize())

    @data_method
    def shelf_type(self, **kwargs):
        return (
            ShelfType(self.furniture_in_category.furniture.shelf_type)
            .name.lower()
            .replace('_', ' ')
        )

    @data_method
    def features_description(self, **kwargs):
        features = ('doors', 'drawers')
        result = [
            feature.capitalize()
            for feature in features
            if getattr(self.furniture_in_category, feature)
        ]

        return ', '.join(result) if result else None

    def merge_image_sets(self, feed_image_set, category_image_set):
        image_set = []
        category_images_iterator = iter(category_image_set)
        category_image = next(category_images_iterator, None)
        for image in feed_image_set:
            while category_image and category_image.slot_number < image.slot_number:
                category_image = next(category_images_iterator, None)
            if category_image and category_image.slot_number == image.slot_number:
                image_set.append(category_image)
            else:
                image_set.append(image)
        return image_set

    @data_method
    def image_links(self, **kwargs):
        feed = self.get_feed(**kwargs)
        image_links = []
        category_image_set = self.category.feedcategoryimageslot_set.order_by(
            'slot_number'
        )
        feed_image_set = feed.feedimageslot_set.order_by('slot_number')
        image_set = self.merge_image_sets(feed_image_set, category_image_set)
        for image_slot in image_set:
            image = self.feeditemimage_set.get(config_id=image_slot.image_config_id)
            image_links.append(image.image.url)
        return tuple(image_links)

    @data_method
    def link(self, **kwargs):
        furniture = self.furniture_in_category.furniture
        domain = kwargs.get('domain', '')
        region = self.region(**kwargs)
        return '{}{}?forced_region={}&feed_category={}'.format(
            domain, furniture.get_absolute_url(), region.name, self.category_id
        )

    @data_method
    def availability_date(self, **kwargs):
        return timezone.now().isoformat()

    @data_method
    def android_url(self, **kwargs):
        return self.furniture_in_category.furniture.get_app_deeplink()

    @data_method
    def ios_url(self, **kwargs):
        return self.furniture_in_category.furniture.get_app_deeplink()

    @data_method
    def delivery_time(self, **kwargs):
        return self.furniture_in_category.furniture.get_delivery_time_days()

    @property
    def _furniture_id_for_feed(self) -> str:
        if isinstance(self.furniture_in_category.furniture, Watty):
            return f'w{self.furniture_in_category.furniture_id}'
        return f'{self.furniture_in_category.furniture_id}'


class OlapicFeedItem(models.Model):
    user_nickname = models.CharField(max_length=120)
    posted_at = models.DateField(null=True, blank=True)
    instagram_link = models.URLField(null=True, blank=True)  # noqa: DJ001
    ugc_type = models.IntegerField(choices=UGCTypes.choices, blank=True, null=True)
    description = models.CharField(max_length=500, null=True, blank=True)  # noqa: DJ001
    google_drive_link = models.URLField(null=True, blank=True)  # noqa: DJ001
    white_list = models.BooleanField(default=False)
    content_type = models.ForeignKey(
        ContentType,
        limit_choices_to=(
            models.Q(app_label='gallery', model='jetty')
            | models.Q(app_label='gallery', model='watty')
        ),
        on_delete=models.CASCADE,
    )
    object_id = models.PositiveIntegerField()
    furniture = GenericForeignKey('content_type', 'object_id')
    image = models.ImageField(blank=True, null=True)
    # Fields below are filled with data from furniture by admin action
    name = models.CharField(max_length=60, blank=True, null=True)  # noqa: DJ001
    color = models.CharField(max_length=64, blank=True, null=True)  # noqa: DJ001
    category = models.CharField(max_length=28, blank=True, null=True)  # noqa: DJ001

    @property
    def product_url(self):
        try:
            return f'{settings.SITE_URL}{self.furniture.get_item_url()}'
        except AttributeError:
            return ''

    @property
    def product_unique_id(self):
        if self.furniture and self.content_type.name == 'watty':
            return f'w{self.object_id}'
        elif self.furniture and self.content_type.name == 'jetty':
            return str(self.object_id)
        return ''

    @property
    def image_url(self):
        image_url = ''
        if self.image:
            try:
                image_url = self.image.url
            except ValueError:
                pass
        return image_url

    def __str__(self):  # noqa: DJ012
        return f'Olapic Feed Item {self.pk} for {self.content_type.name}'

    class Meta:  # noqa: DJ012
        verbose_name = 'Olapic Feed Item'


class OlapicTask(models.Model):
    creator = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    exported_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    status = models.IntegerField(
        choices=OlapicTaskStatuses.choices,
        default=OlapicTaskStatuses.WAITING,
    )
    xml_file = models.FileField(
        upload_to='product_feeds/olapic_feeds',
        blank=True,
        null=True,
    )

    def __str__(self):
        return f'Olapic Task {self.pk}'

    class Meta:  # noqa: DJ012
        verbose_name = 'Olapic Task'


class FeedItemImage(models.Model):
    item = models.ForeignKey(
        FeedItem,
        on_delete=models.CASCADE,
    )
    config = models.ForeignKey(
        FeedImageConfig,
        on_delete=models.CASCADE,
    )
    image = models.ImageField(upload_to='product_feeds/feed_item_image/%Y/%m')

    class Meta(object):
        verbose_name = 'Feed item image'
        verbose_name_plural = 'Feed items images'
        unique_together = (('item', 'config'),)

    def file_prefix(self):
        return '{}_{}'.format(self.config.camera_setting, self.config.background_color)

    def is_valid_image(self):
        try:
            with Image.open(self.image) as im:
                image_width, image_height = im.size
                background_pixel = im.getpixel((1, 1))

                # skip images that were not generated by us
                # our images have empty im.info
                if len(im.info) > 0:
                    return True

                # lets check left top corner
                for x in range(RANGE_TO_CHECK_IF_OUR_RENDER):
                    for y in range(RANGE_TO_CHECK_IF_OUR_RENDER):
                        if check_difference_in_pixels(
                            background_pixel, im.getpixel((x, y)), RGB_MAX_THRESHOLD
                        ):
                            return True

                for i in range(round(image_width * WIDTH_LIMIT_FROM_LEFT_SIDE)):
                    test_pixel = im.getpixel(
                        (i, round(image_height * HEIGHT_LINE_CHECK))
                    )
                    if check_difference_in_pixels(
                        background_pixel, test_pixel, RGB_MAX_THRESHOLD
                    ):
                        return False

                for i in range(round(image_width)):
                    test_pixel = im.getpixel(
                        (i, round(image_height * TOP_LINE_CHECK_MARGIN))
                    )
                    if check_difference_in_pixels(
                        background_pixel, test_pixel, RGB_MAX_THRESHOLD
                    ):
                        return False
                return True
        except FileNotFoundError:
            return False  # as missing image is also bad images


class FeedCategoryImageSlot(models.Model):
    category = models.ForeignKey(FeedCategory, on_delete=models.CASCADE)
    image_config = models.ForeignKey(FeedImageConfig, on_delete=models.CASCADE)
    header = models.CharField(max_length=200)
    slot_number = models.SmallIntegerField()

    def __str__(self):
        return '{}:{}'.format(self.category.name, self.slot_number)

    class Meta(object):  # noqa: DJ012
        verbose_name = 'Feed category image slots configuration'
        verbose_name_plural = 'Feed category image slots configurations'


class FeedRefreshSchedule(models.Model):
    refresh_at = models.DateTimeField()
    refreshed_at = models.DateTimeField(editable=False, null=True)
    feeds = models.ManyToManyField(Feed)
