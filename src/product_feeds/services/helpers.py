import abc
import inspect
import json
import logging
import os
import sys

from functools import lru_cache

from django.conf import settings
from django.core.cache import cache
from django.core.files import File
from django.db.models.query_utils import Q
from django.utils.translation import gettext as _
from rest_framework.utils.encoders import J<PERSON>NEncoder

from custom.models import Countries
from custom.utils.dimensions import Dimensions
from gallery.models import (
    FurnitureImage,
    Jetty,
)
from product_feeds.services.helper_dict import (
    descriptions,
    titles,
)
from regions.models import Region

logger = logging.getLogger('cstm')


class ProductFeedFurnitureHelperFactory(object):
    @lru_cache(typed=True)  # noqa: B019
    def _get_interesting_classes(self, product):
        return [
            c
            for c in [
                i[1]
                for i in inspect.getmembers(
                    sys.modules['product_feeds.services.helpers'],
                    lambda c: inspect.isclass(c)
                    and issubclass(c, ProductFeedFurnitureHelper),
                )[1:]
            ]
            if c.interested_in() == product.__class__
        ]

    def get_helper(self, product, country, **kwargs):
        helper_classes = self._get_interesting_classes(product)
        return (
            helper_classes[0](product, country, **kwargs)
            if len(helper_classes) > 0
            else None
        )


class ProductFeedFurnitureHelper(abc.ABC):
    def __init__(self, product, country, **kwargs):
        if not isinstance(product, self.interested_in()):
            raise TypeError('Interested in: %s' % self.interested_in.__name__)
        self._product = product
        self._country = country

    @staticmethod
    @abc.abstractmethod
    def interested_in():
        raise NotImplementedError

    @property
    def id(self):
        return self._product.id

    @property
    @lru_cache(typed=True)  # noqa: B019
    def currency(self):
        # if you want to allow diff currency remember to pass curr obj to get_price_for
        return self._country.currency

    @property
    @lru_cache(typed=True)  # noqa: B019
    def price(self):
        region = self._kwargs.get('region') or Region.objects.get(
            name=self._country.name
        )
        return self._product.get_regionalized_price(region)

    @property
    @lru_cache(typed=True)  # noqa: B019
    def size(self):
        return '{} cm x {} cm {} cm'.format(
            self._product.get_width(),
            self._product.get_height(),
            self._product.get_depth(),
        )

    @property
    @lru_cache(typed=True)  # noqa: B019
    def lenght(self):
        return '{} cm'.format(self._product.get_width())

    @property
    @lru_cache(typed=True)  # noqa: B019
    def height(self):
        return '{} cm'.format(self._product.get_height())

    @property
    @lru_cache(typed=True)  # noqa: B019
    def depth(self):
        return '{} cm'.format(self._product.get_depth())

    @property
    @lru_cache(typed=True)  # noqa: B019
    def material(self):
        return self._product.get_material()

    @property
    @lru_cache(typed=True)  # noqa: B019
    def color(self):
        return self._product.translated_material_name

    @property
    @lru_cache(typed=True)  # noqa: B019
    def shipping_price(self):
        return 0

    @property
    @lru_cache(typed=True)  # noqa: B019
    def shipping_weight(self):
        return self._product.get_estimated_weight_gross()

    @property
    def sale_price(self):
        region = (
            self._kwargs.get('region')
            or Region.objects.filter(name=self._country.name).first()
        )
        shelf_price = self._product.get_regionalized_price(region=region)
        return int(shelf_price)

    @property
    def sale_price_effective_date(self):
        return ''

    @property
    @lru_cache(typed=True)  # noqa: B019
    def image_link(self):
        return (
            'http://tylko.com{}'.format(self._product.preview.url)
            if self._product.preview
            else ''
        )

    @property
    @abc.abstractmethod
    def additional_image_link(self):
        raise NotImplementedError

    @property
    @lru_cache(typed=True)  # noqa: B019
    def link(self):
        returned_link = 'https://tylko.com{}'.format(self._product.get_absolute_url())
        if self._country.name == 'united_kingdom':
            returned_link += '?forced_region=united_kingdom'
        elif self._country.name == 'switzerland':
            returned_link += '?forced_region=switzerland'
        return returned_link

    @property
    @abc.abstractmethod
    def campaign_number(self):
        raise NotImplementedError

    @property
    @lru_cache(typed=True)  # noqa: B019
    def adwords_redirect(self):
        return (
            '{}?utm_source=uk.google&utm_medium=shopping&'
            'utm_campaign=uk.google.shopping.{}.{}'
        ).format(self.link, self.campaign_number, self._product.id)

    @property
    def app_url(self):
        return 'tylko://{}/{}'.format(
            self._product.__class__.__name__.lower(), self._product.id
        )

    @property
    def ios_app_store_id(self):
        return '991055398'

    @property
    def app_name(self):
        return 'Tylko'

    @property
    def android_package(self):
        return 'com.tylko.furniture5'

    @property
    @abc.abstractmethod
    def product_type(self):
        raise NotImplementedError

    @property
    @lru_cache(typed=True)  # noqa: B019
    def item_group_id(self):
        return self._product.get_pretty_id_name()

    @property
    @abc.abstractmethod
    def google_product_category(self):
        raise NotImplementedError

    @property
    @abc.abstractmethod
    def custom_label_0(self):
        raise NotImplementedError

    @property
    @lru_cache(typed=True)  # noqa: B019
    def custom_label_1(self):
        return self._product.get_pretty_id_name()

    @property
    @abc.abstractmethod
    def custom_label_2(self):
        raise NotImplementedError

    @property
    @abc.abstractmethod
    def custom_label_3(self):
        raise NotImplementedError

    @property
    @abc.abstractmethod
    def designer(self):
        raise NotImplementedError


class ProductFeedJettyHelper(ProductFeedFurnitureHelper):
    inf = 5000
    from collections import OrderedDict

    categories = OrderedDict(
        (
            (
                'common_feed_title_category_sideboard',
                {
                    'Width': [120, inf],
                    'Width Intervals': None,
                    'Height': [0, 130],
                    'Rows height': None,
                    'Rows count': [3, 4],
                    'Doors': True,
                    'Drawers': True,
                    'Open': True,
                },
            ),
            (
                'common_feed_title_category_bookshelf',
                {
                    'Width': [70, inf],
                    'Width Intervals': 10,
                    'Height': [140, inf],
                    'Rows height': None,
                    'Rows count': None,
                    'Doors': None,
                    'Drawers': None,
                    'Open': None,
                },
            ),
            (
                'common_feed_title_category_bookcase',
                {
                    'Width': [75, inf],
                    'Width Intervals': 10,
                    'Height': [140, inf],
                    'Rows height': None,
                    'Rows count': None,
                    'Doors': None,
                    'Drawers': None,
                    'Open': None,
                },
            ),
            (
                'common_feed_title_category_shelf',
                {
                    'Width': [100, inf],
                    'Width Intervals': None,
                    'Height': [19, 38],
                    'Rows height': None,
                    'Rows count': [1, 2],
                    'Doors': True,
                    'Drawers': False,
                    'Open': True,
                },
            ),
            (
                'common_feed_title_category_storage_system',
                {
                    'Width': [155, inf],
                    'Width Intervals': None,
                    'Height': None,
                    'Rows height': None,
                    'Rows count': None,
                    'Doors': True,
                    'Drawers': True,
                    'Open': True,
                },
            ),
            (
                'common_feed_title_category_tv_stand',
                {
                    'Width': [75, 145],
                    'Width Intervals': 10,
                    'Height': [40, 150],
                    'Rows height': None,
                    'Rows count': [1, 2],
                    'Doors': True,
                    'Drawers': True,
                    'Open': True,
                },
            ),
            (
                'common_feed_title_category_shoe_rack',
                {
                    'Width': [80, 150],
                    'Width Intervals': 10,
                    'Height': [30, 60],
                    'Rows height': None,
                    'Rows count': [1, 2],
                    'Doors': None,
                    'Drawers': None,
                    'Open': None,
                },
            ),
            (
                'common_feed_title_category_room_divider',
                {
                    'Width': [72, 153],
                    'Width Intervals': 10,
                    'Height': [140, inf],
                    'Rows height': None,
                    'Rows count': None,
                    'Doors': True,
                    'Drawers': True,
                    'Open': True,
                },
            ),
            (
                'common_feed_title_category_cd_shelf',
                {
                    'Width': [20, 120],
                    'Width Intervals': 10,
                    'Height': [70, inf],
                    'Rows height': 208,
                    'Rows count': None,
                    'Doors': None,
                    'Drawers': None,
                    'Open': True,
                },
            ),
            (
                'common_feed_title_category_dvd_shelf',
                {
                    'Width': [20, 125],
                    'Width Intervals': 10,
                    'Height': [70, inf],
                    'Rows height': 208,
                    'Rows count': None,
                    'Doors': None,
                    'Drawers': None,
                    'Open': True,
                },
            ),
            (
                'common_feed_title_category_vinyl_shelf',
                {
                    'Width': [20, 120],
                    'Width Intervals': 10,
                    'Height': [70, inf],
                    'Rows height': 398,
                    'Rows count': None,
                    'Doors': None,
                    'Drawers': None,
                    'Open': True,
                },
            ),
            (
                'common_feed_title_category_highboard',
                {
                    'Width': [120, inf],
                    'Width Intervals': None,
                    'Height': [130, 170],
                    'Rows height': None,
                    'Rows count': None,
                    'Doors': True,
                    'Drawers': True,
                    'Open': True,
                },
            ),
            (
                'common_feed_title_category_alcove_shelving',
                {
                    'Width': [74, inf],
                    'Width Intervals': 10,
                    'Height': [130, 170],
                    'Rows height': None,
                    'Rows count': None,
                    'Doors': None,
                    'Drawers': None,
                    'Open': None,
                },
            ),
            (
                'common_feed_title_category_shelving_unit',
                {
                    'Width': [0, inf],
                    'Width Intervals': None,
                    'Height': None,
                    'Rows height': None,
                    'Rows count': None,
                    'Doors': None,
                    'Drawers': None,
                    'Open': None,
                },
            ),
        )
    )
    static_categories = OrderedDict(
        [
            (
                'common_feed_title_category_sideboard',
                [6778, 3044, 185180, 3023, 3073, 6716, 6742, 6749],
            ),
            ('common_feed_title_category_bookshelf', [3065, 3071, 6801, 6802, 19080]),
            (
                'common_feed_title_category_bookcase',
                [
                    185177,
                ],
            ),
            ('common_feed_title_category_shelf', [3008, 3057, 6748]),
            (
                'common_feed_title_category_storage_system',
                [
                    100928,
                    6740,
                    3032,
                    3000,
                    3070,
                    6804,
                    6807,
                    3037,
                    185179,
                    6739,
                    6725,
                    100926,
                    3025,
                ],
            ),
            (
                'common_feed_title_category_tv_stand',
                [2998, 6758, 6754, 3009, 3059, 6747, 6746],
            ),
            ('common_feed_title_category_shoe_rack', [6744, 3012, 3062]),
            ('common_feed_title_category_room_divider', [6811, 3085]),
            ('common_feed_title_category_cd_shelf', [6721, 6720]),
            (
                'common_feed_title_category_dvd_shelf',
                [
                    3078,
                    3021,
                ],
            ),
            (
                'common_feed_title_category_vinyl_shelf',
                [19081, 6713, 6762, 3005, 3055, 3004, 3003, 67742],
            ),
            ('common_feed_title_category_highboard', [3028, 3027, 6794]),
            ('common_feed_title_category_alcove_shelving', []),
            ('common_feed_title_category_shelving_unit', [3087, 100927]),
        ]
    )

    def __init__(self, product, country, **kwargs):
        super(ProductFeedJettyHelper, self).__init__(product, country, **kwargs)

        self._kwargs = kwargs
        self.cat = self.determine_category()

    @property
    def title(self):
        de = {'White': 'Weiß', 'Black': 'Schwarz', 'Grey': 'Grau'}
        fr = {'White': 'blanc', 'Black': 'noir', 'Grey': 'gris'}
        try:
            color = self._product.translated_material_name.split('_')[1].capitalize()
            title = titles[self.cat][self._country.language_code.lower()]
            title = title.replace('*Color*', color.lower())
            title = title.replace('*color*', de[color])
            title = title.replace('*couleur*', fr[color])
        except (IndexError, KeyError):
            title = titles[self.cat][self._country.language_code.lower()]
        return title

    @property
    def description(self):
        return descriptions[self.cat][self._country.language_code.lower()]

    @property
    def category(self):
        return self.determine_category()

    @staticmethod
    def interested_in():
        return Jetty

    def get_static_category(self):
        for cat in self.static_categories:
            if self._product.pk in self.static_categories[cat]:
                return cat
        return ''

    def determine_category(self):
        if self.get_static_category():
            return self.get_static_category()
        cat = self.categories
        jetty = self._product
        for c in cat:
            if not cat[c]['Width Intervals'] and not (
                cat[c]['Width'][0] <= jetty.get_width() <= cat[c]['Width'][1]
            ):
                continue
            if cat[c]['Width Intervals']:
                val = jetty.get_width()
                _range = list(
                    range(
                        cat[c]['Width'][0],
                        cat[c]['Width'][1] + 1,
                        cat[c]['Width Intervals'],
                    )
                )
                if val not in _range:
                    continue

            if cat[c]['Height'] and not (
                cat[c]['Height'][0] <= jetty.get_height() <= cat[c]['Height'][1]
            ):
                continue
            if cat[c]['Rows height'] and cat[c]['Rows height'] not in jetty.get_rows():
                continue
            if cat[c]['Rows count'] and not (
                cat[c]['Rows count'][0]
                <= len(jetty.get_rows())
                <= cat[c]['Rows count'][1]
            ):
                continue
            if cat[c]['Doors'] in [False, True]:
                if jetty.doors and not cat[c]['Doors']:
                    continue
                elif not jetty.doors and cat[c]['Doors']:
                    continue
            if cat[c]['Drawers'] in [False, True]:
                if jetty.drawers and not cat[c]['Drawers']:
                    continue
                elif not jetty.drawers and cat[c]['Drawers']:
                    continue
            if cat[c]['Open'] in [False, True]:
                if jetty.doors and not cat[c]['Open']:
                    continue
                elif not jetty.doors and cat[c]['Open']:
                    continue
            return c
        return ' '

    def get_category_number(self):
        for num, cat in enumerate(self.static_categories):
            if self._product.pk in self.static_categories[cat]:
                return num + 1
        return 0

    def generate_image_name_preview(self):
        file_name = self._product.preview.file.name
        new_file_name = '{}_front_stock.png'.format(os.path.split(file_name)[0])
        os.rename(file_name, new_file_name)
        new_name = '{}/front_stock.png'.format(
            os.path.split(self._product.preview.name)[0],
        )
        f = open(new_file_name, 'r')
        self._product.preview.save(new_name, File(f))
        return True

    @property
    def additional_image_link(self):
        images = []
        if 'additional_image_id' in self._kwargs:
            additional_image_id = self._kwargs['additional_image_id']
            for suffix in (
                '',
                'B',
                'F',
            ):
                images.append(  # noqa: PERF401
                    'http://tylko.com{}productfeed_renders/{}{}.jpg'.format(
                        settings.MEDIA_URL, additional_image_id, suffix
                    )
                )
        else:
            additional_image_id = self.id
        furniture_images = self._product.additional_images.all()
        for furniture_image in furniture_images:
            images.append('http://tylko.com%s' % furniture_image.image.url)  # noqa: PERF401
        self.images = images
        return images[0]

    def __getattribute__(self, name):
        if (
            'additional_image_link' in name
            and name != 'additional_image_link'
            and name[-1].isdigit()
            and len(self.images) > int(name[-1])
        ):
            return self.images[int(name[-1])]
        return super(ProductFeedJettyHelper, self).__getattribute__(name)

    @property
    def product_type(self):
        return '465,500061,6372,447,4195,6358'

    @property
    def campaign_number(self):
        return '901-0001'

    @property
    def google_product_category(self):
        return '464'

    @property
    def custom_label_0(self):
        return self.category
        # return 'Shelves'

    @property
    def custom_label_1(self):
        return self._product.get_pattern_name()

    @property
    def custom_label_2(self):
        if self._product.doors:
            return 'Doors & drawers' if self._product.drawers else 'Doors'
        return 'Drawers' if self._product.drawers else 'None'

    @property
    def custom_label_3(self):
        return ''

    @property
    def custom_label_4(self):
        return ''

    @property
    def designer(self):
        return 'Tylko'


class ProductFeedJettyHelper2(ProductFeedJettyHelper):
    @property
    def additional_image_link(self):
        images = [
            'http://tylko.com{}productfeed_renders/{}{}.jpg'.format(
                settings.MEDIA_URL, self.id, suffix
            )
            for suffix in ('', 'B', 'F')
        ]

        self.images = images
        return images[0] if images else ''


class ProductFeedJettyHelperCriteoCopies(ProductFeedJettyHelper):
    @property
    @lru_cache(typed=True)  # noqa: B019
    def additional_image_link(self):
        return 'http://tylko.com%s' % self._product.preview.url

    @property
    def image_link(self):
        if 'additional_image_id' in self._kwargs:
            additional_image_id = self._kwargs['additional_image_id']
        else:
            additional_image_id = self.id
        furniture_images = FurnitureImage.objects.filter(
            furniture_object_id=additional_image_id
        )
        images = [
            'http://tylko.com{}'.format(furniture_image.image.url)
            for furniture_image in furniture_images
        ]

        self.images = images

        return images[0] if images else ''


class ProductFeedJettyHelperCriteo(ProductFeedJettyHelper):
    @property
    def description(self):
        return _('criteo_feed_description')

    @property
    def title(self):
        product_colour_split = self.color.split('_')
        product_colour = product_colour_split[0]
        if len(product_colour_split) > 1:
            product_colour = product_colour_split[1]

        product_furniture_category = self._product.furniture_category
        if product_furniture_category == 'bench':
            colour_key_base = 'criteo_feed_title_colour_bench'
        else:
            colour_key_base = 'criteo_feed_title_colour'
        colour_key = '{}_{}'.format(colour_key_base, product_colour)
        furniture_category_key = (
            'criteo_feed_title_category_%s' % product_furniture_category
        )
        colour = _(colour_key)
        furniture_category = _(furniture_category_key)
        return '{} {}'.format(colour, furniture_category)

    def as_dict(self):
        dimensions = self._product.get_dimensions()
        return {
            'id': self.id,
            'title': self.title,
            'description': self.description,
            'price': self.price,
            'size': self.size,
            'material': self._product.material,
            'color': self._product.color.translated_color,
            'shipping_price': self.shipping_price,
            'shipping_weight': self.shipping_weight,
            'sale_price': self.sale_price,
            'sale_price_effective_date': self.sale_price_effective_date,
            'image_link': self.image_link,
            'additional_image_link': self.additional_image_link,
            'link': self.link,
            'adwords_redirect': self.adwords_redirect,
            'product_type': self.product_type,
            'item_group_id': self.item_group_id,
            'google_product_category': self.google_product_category,
            'custom_label_0': self.custom_label_0,
            'custom_label_1': self.custom_label_1,
            'custom_label_2': self.custom_label_2,
            'custom_label_3': self.custom_label_3,
            'project_id': self._product.get_project_id(),
            'width': dimensions.get_dimension(Dimensions.DimensionType.WIDTH),
            'height': dimensions.get_dimension(Dimensions.DimensionType.HEIGHT),
            'depth': dimensions.get_dimension(Dimensions.DimensionType.DEPTH),
        }


def fill_criteo_cache():
    furniture = Jetty.objects.filter(Q(owner__username='<EMAIL>'))
    helpers = []
    helpers_dicts = []
    country = Countries.germany
    for product in furniture:
        helper = ProductFeedJettyHelperCriteo(product, country)
        helpers.append(helper)
        helpers_dicts.append(helper.as_dict())
    cache.set(
        'product_feed_criteo_records',
        json.dumps(helpers_dicts, cls=JSONEncoder),
        None,
    )
    return helpers_dicts
