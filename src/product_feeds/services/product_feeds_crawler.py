import csv
import json

from collections import defaultdict
from decimal import Decimal
from io import StringIO

from django.core.files import File
from rest_framework import status

import requests

from lxml import html

from product_feeds.exceptions import (
    BadResponseStatusException,
    FeedCrawlerException,
    FeedItemException,
    NoContentException,
    NoPriceException,
    WrongPriceException,
)


class FeedCrawler:
    """Crawler that validates csv from Feed.

    Iterates over each line of csv, creates and instance of FeedRow and calls
    `.validate()` method.
    If FeedRaw raises and error, adds an error to `self.errors` attribute.

    TODO: This needs some rework before being used again. Now we create a new user for
    every row which is suboptimal, to put things nicely. Google feeds are having 300k
    rows, so we can make 300k users and orders with one click right now.
    Don't use this until its fixed.
    """

    def __init__(self, file: File) -> None:
        self.file = file
        self.price_field = 'price'
        self.link_field = 'link'
        self.errors = defaultdict(list)

    def crawl(self) -> None:
        # django seems to ignore a `mode=` arg to open() and that's why we open file
        # in binary mode and later decode content to string.
        with self.file.open() as fp:
            sample = fp.read(16384).decode('utf-8')
            fp.seek(0)
            content = fp.read().decode('utf-8')

            dialect = csv.Sniffer().sniff(sample)
            reader = csv.DictReader(StringIO(content), dialect=dialect)
            for row in reader:
                self._validate_row(reader.line_num, row)

    def _validate_row(self, row_id: int, row: dict) -> None:
        try:
            row = FeedRow(
                row_id=row_id,
                link=row.get(self.link_field),
                price=row.get(self.price_field),
            )
            row.validate()
        except FeedItemException as e:
            self.errors[row_id].append(e.message)

        except FeedCrawlerException as e:
            self.errors[row_id].append(e.message)


class FeedRow:
    """Helper class to validate a single row from a Feed's csv file."""

    def __init__(self, row_id: int, link: str, price: int) -> None:
        self.row_id = row_id
        self.price = price
        self.link = link
        self.errors = []
        self.schema = self._get_schema()
        self.validators = [self.validate_price]

    def validate(self) -> None:
        for validator in self.validators:
            try:
                validator()
            except FeedCrawlerException as e:  # noqa: PERF203
                self.errors.append(e.message)

        if self.errors:
            raise FeedItemException(message=self.errors)

    def validate_price(self) -> None:
        if Decimal(self.price) != self._get_schema_price():
            raise WrongPriceException

    def _get_schema(self):
        content = self._get_content()
        root = html.fromstring(content)
        if schema := root.xpath('.//script[@type="application/ld+json"]'):
            return schema
        raise NoContentException

    def _get_schema_price(self) -> Decimal:
        if price := json.loads(self.schema[0].text).get('offers', {}).get('price'):
            return Decimal(str(price))
        raise NoPriceException

    def _get_content(self) -> str:
        raise NotImplementedError("Don't use this. Refactor first.")
        response = requests.get(self.link)
        if response.status_code != status.HTTP_200_OK:
            raise BadResponseStatusException
        return response.text
