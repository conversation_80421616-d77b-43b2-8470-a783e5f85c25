import contextlib
import csv

from socket import error as SocketError  # noqa: N812

from django.conf import settings
from django.core.files.temp import NamedTemporaryFile
from django.utils import timezone

import paramiko

from celery import shared_task
from celery.utils.log import get_task_logger
from facebook_business import FacebookAdsApi
from facebook_business.adobjects.productcatalog import ProductCatalog

from custom.utils.exports import _send_email
from gallery.enums import FurnitureStatusEnum
from gallery.models import Jetty
from product_feeds.choices import (
    FeedVariantStatus,
    FeedVariantValidationStatus,
    OlapicTaskStatuses,
)
from product_feeds.models import (
    Feed,
    FeedRefreshSchedule,
    FeedVariant,
    OlapicTask,
)
from product_feeds.services.facebook_catalogs_api import (
    add_products_to_catalog_in_batch,
)
from product_feeds.services.product_feeds_crawler import FeedCrawler
from regions.models import Region

task_logger = get_task_logger('celery_task')

FB_PRODUCT_BATCH_SIZE = 50


@shared_task
def validate_feed(feed_id):
    feed = Feed.objects.get(pk=feed_id)
    feed.validate(variant=feed.variants.first())
    feed.validated_at = timezone.now()
    feed.save(update_fields=['validated_at'])


@shared_task
def refresh_feed_variant(feed_variant_id, domain, raise_exception=True):
    feed_variant = FeedVariant.objects.get(pk=feed_variant_id)
    feed_variant.status = FeedVariantStatus.PROCESSING
    feed_variant.save(update_fields=('status',))

    feed_variant.regenerate_feed_file(domain=domain, raise_exception=raise_exception)


@shared_task
def check_errors_in_feed(feed_id, email, domain):
    try:
        feed = Feed.objects.get(pk=feed_id)
    except Feed.DoesNotExist:
        raise Exception('Missing feed in error check task, check {}'.format(feed_id))
    with NamedTemporaryFile(mode='w+', suffix='.csv') as output:
        writer = csv.writer(output)
        writer.writerow(feed.headers)
        rows = feed.data_rows(
            variant=feed.variants.first(),
            domain=domain,
            cached=False,
            show_errors=True,
            raise_exception=False,
        )
        for row in rows:
            writer.writerow(row)
        output.flush()
        output.seek(0)
        _send_email(
            (email,),
            output.name,
            'Feed errors for {}'.format(feed.slug),
            'Errors attached, if empty === no errors',
        )


@shared_task
def refresh_scheduled_feeds():
    scheduled_refreshes = FeedRefreshSchedule.objects.filter(
        refresh_at__lte=timezone.now(), refreshed_at=None
    )
    for refresh_item in scheduled_refreshes:
        for feed in refresh_item.feeds.all():
            for variant in feed.variants.all():
                refresh_feed_variant.delay(variant.id, domain=settings.SITE_URL)
        refresh_item.refreshed_at = timezone.now()
        refresh_item.save(update_fields=['refreshed_at'])


@shared_task
def refresh_active_feeds():
    feeds = Feed.objects.filter(active=True)
    for feed in feeds:
        for variant in feed.variants.all():
            refresh_feed_variant.delay(variant.id, domain=settings.SITE_URL)


@shared_task
def send_jetties_to_facebook_feed(jetty_ids, catalog_id, region_name):
    api = FacebookAdsApi.init(
        app_id=settings.FB_APP_ID,
        app_secret=settings.FB_APP_SECRET,
        access_token=settings.FB_APP_SYSTEM_TOKEN,
        api_version=settings.FB_API_VERSION,
    )
    catalog = ProductCatalog(catalog_id, api=api)
    # TODO: remove hardcoded region after mvp
    jetties = Jetty.objects.filter(
        id__in=jetty_ids,
        owner__profile__region__name=region_name,
    ).exclude(furniture_status=FurnitureStatusEnum.ORDERED)
    qs_length = len(jetties)
    region = Region.objects.get(name=region_name)
    for index in range(0, qs_length, FB_PRODUCT_BATCH_SIZE):
        batch = api.new_batch()
        add_products_to_catalog_in_batch(
            jetties[index : min(index + FB_PRODUCT_BATCH_SIZE, qs_length)],
            catalog,
            batch,
            region,
        )


@shared_task
def put_olapic_feed_to_remote_server(olapic_task_id: int) -> None:
    olapic_task = OlapicTask.objects.get(id=olapic_task_id)
    with contextlib.ExitStack() as stack:
        ssh = stack.enter_context(paramiko.SSHClient())
        # prevents 'not found in known_hosts' error
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        try:
            ssh.connect(
                hostname=settings.OLAPIC_ADDRESS,
                username=settings.OLAPIC_USERNAME,
                password=settings.OLAPIC_PASSWORD,
            )
        except (paramiko.SSHException, SocketError) as error_message:
            olapic_task.status = OlapicTaskStatuses.ERROR
            olapic_task.save()
            task_logger.error(
                'Connecting to Olapic sftp failed. Error message: %s',
                error_message,
            )
            return
        sftp_client = stack.enter_context(ssh.open_sftp())
        try:
            sftp_client.putfo(olapic_task.xml_file, '/feeds/olapic_feeds.xml')
            olapic_task.status = OlapicTaskStatuses.DONE
        except Exception as error_message:
            olapic_task.status = OlapicTaskStatuses.ERROR
            task_logger.error(
                'Putting Olapic xml feed to Olapic sftp failed. Error message: %s',
                error_message,
            )
        finally:
            olapic_task.save()


@shared_task
def validate_feed_with_crawler(variant_id: int) -> None:
    variant = FeedVariant.objects.get(id=variant_id)
    variant.validation_status = FeedVariantValidationStatus.VALIDATING
    variant.validation_started_at = timezone.now()
    variant.save(update_fields=['validation_status', 'validation_started_at'])

    crawler = FeedCrawler(file=variant.file)
    crawler.crawl()

    update_fields = ['validation_status']
    if not crawler.errors:
        variant.validation_status = FeedVariantValidationStatus.VALIDATED
    else:
        error_msg = [
            f'Row: {row_id}. Errors: {errors}'
            for row_id, errors in crawler.errors.items()
        ]
        variant.validation_status = FeedVariantValidationStatus.ERROR
        variant.validation_errors = '\n'.join(error_msg)
        update_fields.append('validation_errors')

    variant.save(update_fields=update_fields)
