from unittest.mock import patch

from django.core.files.base import ContentFile

import pytest

from product_feeds.exceptions import BadResponseStatusException
from product_feeds.services.product_feeds_crawler import FeedCrawler


class TestFeedCrawler:
    @pytest.fixture
    def csv_file_good_price(self):
        return b'title;link;price\nasdf;example.com/crawler_test;123'

    @pytest.fixture
    def csv_file_bad_price(self):
        return b'title;link;price\nasdf;example.com/crawler_test;456'

    @pytest.fixture
    def site_content(self):
        return '''
        <html>
        <script data-n-head="ssr" data-hid="nuxt-jsonld--61bcf2db" type="application/ld+json">
        {"@context":"https://schema.org",
        "offers":{"price":123}}
        </script>
        </html>
        '''  # noqa: E501

    @pytest.fixture
    def site_content_no_price(self):
        return '''
        <html>
        <script data-n-head="ssr" data-hid="nuxt-jsonld--61bcf2db" type="application/ld+json">
        {"@context":"https://schema.org",
        "offers":{}}
        </script>
        </html>
        '''  # noqa: E501

    @patch('product_feeds.services.product_feeds_crawler.FeedRow._get_content')
    def test_all_good(self, mocked_get_content, csv_file_good_price, site_content):
        mocked_get_content.return_value = site_content
        file = ContentFile(csv_file_good_price)
        crawler = FeedCrawler(file)
        crawler.crawl()
        assert not crawler.errors

    @patch('product_feeds.services.product_feeds_crawler.FeedRow._get_content')
    def test_404(self, mocked_get_content, csv_file_good_price, site_content):
        mocked_get_content.side_effect = BadResponseStatusException
        file = ContentFile(csv_file_good_price)
        crawler = FeedCrawler(file)
        crawler.crawl()
        assert crawler.errors
        assert crawler.errors[2] == ['Bad response status']

    @patch('product_feeds.services.product_feeds_crawler.FeedRow._get_content')
    def test_bad_price(self, mocked_get_content, csv_file_bad_price, site_content):
        mocked_get_content.return_value = site_content
        file = ContentFile(csv_file_bad_price)
        crawler = FeedCrawler(file)
        crawler.crawl()
        assert crawler.errors
        assert crawler.errors[2] == ['Wrong price']

    @patch('product_feeds.services.product_feeds_crawler.FeedRow._get_content')
    def test_no_schema(self, mocked_get_content, csv_file_good_price):
        mocked_get_content.return_value = '<html><div>hello world</div></html>'
        file = ContentFile(csv_file_good_price)
        crawler = FeedCrawler(file)
        crawler.crawl()
        assert crawler.errors
        assert crawler.errors[2] == ['No content']

    @patch('product_feeds.services.product_feeds_crawler.FeedRow._get_content')
    def test_no_price(
        self,
        mocked_get_content,
        csv_file_good_price,
        site_content_no_price,
    ):
        mocked_get_content.return_value = site_content_no_price
        file = ContentFile(csv_file_good_price)
        crawler = FeedCrawler(file)
        crawler.crawl()
        assert crawler.errors
        assert crawler.errors[2] == ["Couldn't find price in schema"]
