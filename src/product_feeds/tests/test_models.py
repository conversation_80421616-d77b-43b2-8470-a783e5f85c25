from datetime import (
    datetime,
    timedelta,
)
from unittest.mock import patch

from django.conf import settings
from django.core.files import File

import pytest

from freezegun import freeze_time

from product_feeds.choices import FeedVariantStatus


@pytest.mark.django_db
class TestOlapicFeedItem:
    def test_product_url(self, olapic_feed_item):
        expected_url = f'{settings.SITE_URL}{olapic_feed_item.furniture.get_item_url()}'

        assert olapic_feed_item.product_url == expected_url

    def test_product_unique_id_with_jetty(self, olapic_feed_item_factory):
        feed_item = olapic_feed_item_factory(is_jetty=True)

        assert feed_item.product_unique_id == str(feed_item.object_id)

    def test_product_unique_id_with_watty(self, olapic_feed_item_factory):
        feed_item = olapic_feed_item_factory(is_watty=True)

        assert feed_item.product_unique_id == f'w{feed_item.object_id}'

    def test_image_url_when_no_image(self, olapic_feed_item_factory):
        feed_item = olapic_feed_item_factory(image=None)

        assert feed_item.image_url == ''

    def test_image_url_with_image(self, mocker, olapic_feed_item_factory):
        image_file = mocker.MagicMock(spec=File)
        image_file.name = 'test.png'
        image_file.url = '/uploaded/test.png'
        feed_item = olapic_feed_item_factory(image=image_file)

        assert feed_item.image_url.startswith('/uploaded/test')
        assert feed_item.image_url.endswith('.png')


@pytest.mark.django_db
class TestFeedVariant:
    @patch('product_feeds.models.FeedVariant._regenerate_feed_file')
    def test_regenerate_feed_file_sets_status_error_if_exception_happens(
        self,
        mock_regenerate_feed_file_private,
        feed_variant,
    ):
        mock_regenerate_feed_file_private.side_effect = Exception

        feed_variant.regenerate_feed_file(domain=settings.SITE_URL)

        feed_variant.refresh_from_db()
        assert feed_variant.status == FeedVariantStatus.ERROR

    def test_regenerate_feed_file_sets_status_done_if_regeneration_succeeded(
        self,
        feed_variant,
    ):
        feed_variant.regenerate_feed_file(domain=settings.SITE_URL)

        feed_variant.refresh_from_db()
        assert feed_variant.status == FeedVariantStatus.DONE

    @patch('product_feeds.models.FeedVariant._regenerate_feed_file')
    def test_regenerate_feed_file_sets_last_processing_time_after_processing(
        self,
        _,  # noqa: PT019
        feed_variant,
    ):
        process_time = 10
        with freeze_time(datetime.now(), auto_tick_seconds=process_time):
            feed_variant.regenerate_feed_file(domain=settings.SITE_URL)

        feed_variant.refresh_from_db()
        assert feed_variant.status == FeedVariantStatus.DONE
        assert feed_variant.last_processing_time == timedelta(seconds=process_time)
