import xml.etree.ElementTree as ET  # noqa: S405

from xml.dom.minidom import parseString  # noqa: S408

from django.core.files import File
from django.utils import translation

import pytest

from custom.enums import (
    LanguageEnum,
    ShelfType,
    Type01Color,
    Type03Color,
    Type13Color,
    VeneerType01Color,
)
from gallery.enums import FurnitureCategory
from product_feeds.services.olapic import (
    OlapicExportService,
    OlapicFeedItemDataFiller,
    OlapicFeedXmlFileCreator,
)


@pytest.mark.django_db
class TestOlapicExportService:
    def test_create_olapic_task(self, user):
        xml_string = '<xml>test</xml>'
        task = OlapicExportService().create_olapic_task(user, xml_string)

        assert task.xml_file.name.endswith('olapic_feeds.xml')
        assert task.xml_file.read().decode('utf-8') == xml_string


@pytest.mark.django_db
class TestOlapicFeedXmlFileCreator:
    def test_create_xml_olapic_feed(self, olapic_feed_item_factory):
        olapic_feed_item_factory.create_batch(5)
        xml_bytes = OlapicFeedXmlFileCreator()._create_xml_bytes()
        xml_string = xml_bytes.decode('utf-8')

        document = parseString(xml_string)  # noqa: S318
        root = ET.fromstring(xml_string)  # noqa: S314

        assert document.version == '1.0'
        assert document.encoding == 'UTF-8'
        assert len(document.getElementsByTagName('Product')) == 5

        assert root.tag == 'Feed'
        assert root[0].tag == 'Products'
        assert root[0][0].tag == 'Product'
        assert root[0][0][0].tag == 'Name'
        assert root[0][0][1].tag == 'ProductUniqueID'
        assert root[0][0][2].tag == 'ProductUrl'
        assert root[0][0][3].tag == 'ImageUrl'
        assert root[0][0][4].tag == 'Description'
        assert root[0][0][5].tag == 'Color'


@pytest.mark.django_db
class TestOlapicFeedItemDataFiller:
    @pytest.fixture
    def mocked_image(self, mocker):
        image_file = mocker.MagicMock(spec=File)
        image_file.name = 'test.png'
        return image_file

    def test_fill_olapic_feed_item_with_furniture_data(
        self,
        mocked_image,
        jetty,
        olapic_feed_item_factory,
    ):
        jetty.preview = mocked_image
        feed_item = olapic_feed_item_factory(furniture=jetty)

        assert not feed_item.name
        assert not feed_item.color
        assert not feed_item.category
        assert not feed_item.image

        OlapicFeedItemDataFiller(feed_item).fill_data()

        assert feed_item.name
        assert feed_item.color
        assert feed_item.category
        assert feed_item.image

    def test_fill_olapic_feed_item_with_furniture_data_when_empty_preview(
        self,
        mocked_image,
        jetty,
        olapic_feed_item_factory,
    ):
        jetty.preview = None
        feed_item = olapic_feed_item_factory(furniture=jetty)

        assert not feed_item.name
        assert not feed_item.color
        assert not feed_item.category
        assert not feed_item.image

        OlapicFeedItemDataFiller(feed_item).fill_data()

        assert feed_item.name
        assert feed_item.color
        assert feed_item.category
        assert not feed_item.image

    def test_get_category(self, watty, olapic_feed_item_factory):
        feed_item = olapic_feed_item_factory(furniture=watty)

        with translation.override(LanguageEnum.EN):
            category = OlapicFeedItemDataFiller(feed_item)._get_category()

        assert category == 'Wardrobe'

    @pytest.mark.parametrize(
        ('shelf_type', 'category', 'material', 'name'),
        [
            (
                ShelfType.TYPE01.value,
                FurnitureCategory.BOOKCASE.value,
                Type01Color.WHITE.value,
                'Tylko Original Bookcase (in White)',
            ),
            (
                ShelfType.VENEER_TYPE01.value,
                FurnitureCategory.CHEST.value,
                VeneerType01Color.OAK.value,
                'Tylko Original Chest (in Oak Veneer)',
            ),
        ],
    )
    def test_getting_jetty_name(
        self,
        shelf_type,
        category,
        material,
        name,
        jetty_factory,
        olapic_feed_item_factory,
    ):
        jetty = jetty_factory(
            shelf_category=category,
            shelf_type=shelf_type,
            material=material,
        )
        feed_item = olapic_feed_item_factory(furniture=jetty)

        name = OlapicFeedItemDataFiller(feed_item)._get_name()

        assert name == name

    @pytest.mark.parametrize(
        ('shelf_type', 'material', 'name'),
        [
            (
                ShelfType.TYPE03.value,
                Type03Color.BEIGE_PINK.value,
                'Tone Wardrobe in Cashmere Beige + Antique Pink',
            ),
            (ShelfType.TYPE13.value, Type13Color.WHITE.value, 'Edge Wardrobe in White'),
        ],
    )
    def test_getting_watty_name(
        self,
        shelf_type,
        material,
        name,
        watty_factory,
        olapic_feed_item_factory,
    ):
        watty = watty_factory(shelf_type=shelf_type, material=material)
        feed_item = olapic_feed_item_factory(furniture=watty)

        name = OlapicFeedItemDataFiller(feed_item)._get_name()

        assert name == name

    def test_get_color(
        self,
        olapic_feed_item_factory,
        jetty,
    ):
        feed_item = olapic_feed_item_factory(furniture=jetty)
        expected_color = (
            ShelfType(jetty.shelf_type).colors(jetty.material).translated_color
        )

        color = OlapicFeedItemDataFiller(feed_item)._get_color()

        assert color == expected_color
