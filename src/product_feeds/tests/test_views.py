from django.urls import reverse
from rest_framework import status

import pytest

from product_feeds.views import (
    get_feed_items_for_categories_qs,
    get_feed_items_qs,
)


@pytest.mark.django_db
def test_feed_preview_returns_404_for_missing_slug(client):
    url = reverse('marketing_feed_preview', kwargs={'slug': 'nonexistent'})
    response = client.get(url)
    assert response.status_code == status.HTTP_404_NOT_FOUND


@pytest.mark.django_db
class TestGetFeedItems:
    def test_get_feed_items_overrides_config_if_category_specifies_one(
        self,
        feed_category,
        feed_image_config_factory,
        feed_item_factory,
        feed_factory,
        feed_image_slot_factory,
        feed_category_image_slot_factory,
    ):
        category_image_config = feed_image_config_factory()
        feed_item_image_config = feed_image_config_factory()
        feed_category_image_slot_factory(
            image_config=category_image_config,
            category=feed_category,
        )
        feed = feed_factory(categories=[feed_category])
        feed_item_factory(category=feed_category)
        feed_image_slot_factory(
            image_config=feed_item_image_config,
            feed=feed,
        )

        results = get_feed_items_for_categories_qs()
        assert len(results) == 1
        assert results[0].config_id == category_image_config.id

    def test_get_feed_items_annotates_config_correctly(
        self,
        feed_category,
        feed_image_config,
        feed_item_factory,
        feed_factory,
        feed_image_slot_factory,
    ):
        feed = feed_factory(categories=[feed_category])
        feed_item_factory(category=feed_category)
        feed_image_slot_factory(
            image_config=feed_image_config,
            feed=feed,
        )
        feed_item_factory(category=feed_category)

        assert get_feed_items_qs()[0].config_id == feed_image_config.id
