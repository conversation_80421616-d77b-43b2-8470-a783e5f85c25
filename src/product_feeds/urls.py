from django.urls import (
    include,
    path,
)
from rest_framework import routers

from product_feeds.views import (
    FeedErrorsView,
    FeedItemImageViewSet,
    FeedPreviewView,
    FileBasedMarketingFeedView,
)

router = routers.SimpleRouter()
router.register('api/v1/product_feeds/feed_item_image', FeedItemImageViewSet)

urlpatterns = [
    path(
        'product_feed/feed-preview/slug/<slug:slug>/',
        FeedPreviewView.as_view(),
        name='marketing_feed_preview',
    ),
    path(
        'product_feed/feed-preview/slug/<slug:slug>/<slug:country>/<slug:currency>/',
        FeedPreviewView.as_view(),
        name='marketing_feed_preview',
    ),
    path(
        'product_feed/feed-preview/slug/errors/<slug:slug>/',
        FeedErrorsView.as_view(),
        name='marketing_feed_errors',
    ),
    path(
        'product_feed/<slug:slug>/',
        FileBasedMarketingFeedView.as_view(),
        name='marketing_feed',
    ),
    path(
        'product_feed/<slug:slug>/<slug:country>/<slug:currency>/',
        FileBasedMarketingFeedView.as_view(),
        name='marketing_feed',
    ),
    path('', include(router.urls)),
]
