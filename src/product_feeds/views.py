import base64
import csv
import logging

from django import forms
from django.contrib import messages
from django.contrib.auth.models import User
from django.core.files.base import ContentFile
from django.db.models import (
    Exists,
    F,
    OuterRef,
)
from django.http import (
    Http404,
    HttpResponse,
)
from django.template.loader import render_to_string
from django.utils import translation
from django.views.generic.base import TemplateView
from django.views.generic.edit import FormView
from rest_framework import mixins
from rest_framework.generics import (
    RetrieveAPIView,
    get_object_or_404,
)
from rest_framework.settings import api_settings
from rest_framework.viewsets import GenericViewSet

from custom.models import Countries
from gallery.models import Jetty
from gallery.permissions import WriteOnlyToNotPresetAndNotReadonly
from product_feeds.models import (
    Feed,
    FeedCategory,
    FeedCategoryCopy,
    FeedCategoryLocalizedName,
    FeedItem,
    FeedItemImage,
    FeedVariant,
)
from product_feeds.serializers import FeedItemImageSerializer
from product_feeds.services.helpers import (
    ProductFeedFurnitureHelperFactory,
    ProductFeedJettyHelper2,
    ProductFeedJettyHelperCriteoCopies,
)
from product_feeds.tasks import check_errors_in_feed
from regions.models import (
    Country,
    Currency,
)

logger = logging.getLogger('cstm')


def get_domain(request):
    return '{}://{}'.format(request.scheme, request.get_host())


def get_feed_items_qs():
    items_qs = FeedItem.objects.annotate(
        config_id=F('category__feed__feedimageslot__image_config')
    ).exclude(config_id=None)
    image_created = FeedItemImage.objects.filter(
        config_id=OuterRef('config_id'), item_id=OuterRef('pk')
    )
    return items_qs.annotate(image_created=Exists(image_created)).exclude(
        image_created=True
    )


def get_feed_items_for_categories_qs():
    items_qs = FeedItem.objects.annotate(
        config_id=F('category__feedcategoryimageslot__image_config')
    ).exclude(config_id=None)
    image_created = FeedItemImage.objects.filter(
        config_id=OuterRef('config_id'), item_id=OuterRef('pk')
    )
    return items_qs.annotate(image_created=Exists(image_created)).exclude(
        image_created=True
    )


class FileBasedMarketingFeedView(RetrieveAPIView):
    authentication_classes = ()
    permission_classes = ()

    def get_object(self):
        feed = get_object_or_404(Feed, slug=self.kwargs.get('slug'))
        country_param = self.kwargs.get('country')
        currency_param = self.kwargs.get('currency')
        if not country_param or not currency_param:
            variant = FeedVariant.objects.filter(feed=feed).first()
            if not variant:
                raise Http404
            return variant
        country = get_object_or_404(
            Country,
            code=country_param.upper(),
        )
        currency = get_object_or_404(
            Currency,
            code=currency_param.upper(),
        )
        return get_object_or_404(
            FeedVariant,
            country=country,
            currency=currency,
            feed=feed,
        )

    def get(self, request, *args, **kwargs):
        instance = self.get_object()
        response = HttpResponse(
            instance.file,
            content_type='application/csv',
        )
        response['Content-Disposition'] = 'attachment; filename="{0}"'.format(
            instance.file.name,
        )
        return response


class FeedPreviewView(FileBasedMarketingFeedView, TemplateView):
    template_name = 'admin/product_feeds/feed_preview.html'

    def get(self, request, *args, **kwargs):
        variant = self.get_object()
        feed = variant.feed
        context = {
            'headers': feed.headers,
            'rows': feed.data_rows(
                variant=feed.variants.first(),
                domain=get_domain(request),
                limit=int(request.GET.get('limit', 100)),
                raise_exception=True,
            ),
        }
        return self.render_to_response(context=context)


class FeedErrorsView(TemplateView):
    template_name = 'admin/product_feeds/feed_preview.html'

    def get(self, request, *args, **kwargs):
        feed = get_object_or_404(Feed, slug=kwargs.get('slug', ''))
        check_errors_in_feed.delay(
            feed.id,
            request.user.username,
            get_domain(request),
        )
        context = {'email': request.user.username}
        return self.render_to_response(context=context)


class FeedsAddToCategoryView(TemplateView):
    template_name = 'admin/product_feeds/add_to_category_tool.html'

    def get_context_data(self, **kwargs):
        return {
            'category': FeedCategory.objects.get(pk=kwargs['category_id']),
            **super().get_context_data(**kwargs),
        }


class FeedsListFeedView(TemplateView):
    template_name = 'admin/product_feeds/view_category_tool.html'


class FeedItemImageViewSet(mixins.CreateModelMixin, GenericViewSet):
    permission_classes = list(api_settings.DEFAULT_PERMISSION_CLASSES) + [  # noqa: RUF005
        WriteOnlyToNotPresetAndNotReadonly,
    ]
    queryset = FeedItemImage.objects.all()
    serializer_class = FeedItemImageSerializer

    def perform_create(self, serializer):
        if self.request.data.get('magic_preview', None) is not None:
            try:
                feed_image = FeedItemImage.objects.get(
                    config_id=serializer.data['config_id'],
                    item_id=serializer.data['item_id'],
                )
            except FeedItemImage.DoesNotExist:
                feed_image = FeedItemImage(**serializer.data)
            file_name = '{}.png'.format(feed_image.file_prefix())
            feed_image.image = ContentFile(
                base64.b64decode(self.request.data['magic_preview']),
                file_name,
            )
            feed_image.save()


class CsvImportForm(forms.Form):
    csv_file = forms.FileField()


COLOR_TRANSLATION = {
    'EN': ((0, 'White'), (1, 'Black'), (3, 'Grey'), (4, 'Purple'), (5, 'Wooden'))
}


class ImportCategoriesView(FormView):
    template_name = 'admin/product_feeds/import_feeds.html'
    form_class = CsvImportForm
    success_url = '/admin/product_feeds/feedcategory/'

    def form_valid(self, form):
        context = self.get_context_data(form=form)
        csv_file = self.request.FILES['csv_file']
        reader = csv.reader(csv_file)
        skipped_lines = ''
        added = 0
        for id, row in enumerate(reader):
            if id == 0:
                continue
            old_category = FeedCategory.objects.filter(name=row[0].strip()).last()
            if old_category is None:
                skipped_lines += '%s,' % id
            else:
                added += 1
                new_category = FeedCategory()
                new_category.name = row[2]
                new_category.save()

                # localized names
                new_localized = FeedCategoryLocalizedName()
                new_localized.category = new_category
                new_localized.language = row[1].lower()
                new_localized.name = row[3]
                new_localized.save()

                # copy
                for color, color_translation in COLOR_TRANSLATION[row[1]]:
                    new_copy_entry = FeedCategoryCopy()
                    new_copy_entry.category = new_category
                    new_copy_entry.title = row[4].replace('[COLOR]', color_translation)
                    new_copy_entry.description = row[5].replace(
                        '[COLOR]', color_translation
                    )
                    new_copy_entry.color = color
                    new_copy_entry.language = row[1].lower()
                    new_copy_entry.save()

                # items
                new_category.copy_items_from(old_category, False)

        messages.add_message(
            self.request,
            messages.INFO,
            'It worked, added {} categories and skipped lines {}'.format(
                added, skipped_lines
            ),
        )
        return self.render_to_response(context)


class ProductFeedView(TemplateView):
    content_type = 'text/tab-separated-values'

    def get_template_names(self):
        pf_type = self.kwargs.get('pf_type', None)
        if pf_type is not None:
            if pf_type == 'google_tsv':
                return 'product_feed_google_new.tsv'
            else:
                return 'product_feed_all_new.tsv'

    def get(self, request, country_code=Countries.germany.code, pf_type='all_tsv'):
        country = Countries.get_country_by_code(country_code)
        if country is None:
            country = Countries.germany
        translation.activate(country.language_code)

        furniture = list(
            Jetty.objects.filter(owner__username='<EMAIL>', deleted=False)
        )

        helpers = []
        factory = ProductFeedFurnitureHelperFactory()
        if country.language_code == Countries.germany.language_code:
            for product in furniture:
                helper = factory.get_helper(product, country)
                helpers.append(helper)
            for product in Jetty.objects.filter(
                owner__username='<EMAIL>'
            ):
                helper = factory.get_helper(product, country)
                helpers.append(helper)
        else:
            for product in furniture:
                helper = factory.get_helper(product, country)
                helpers.append(helper)

        helpers.extend(
            ProductFeedJettyHelper2(Jetty.objects.get(id=element), country)
            for element in [
                19077,
                19079,
                67741,
                19080,
                19081,
                67742,
                19082,
                19083,
                19084,
                19086,
            ]
        )

        helpers.extend(
            ProductFeedJettyHelperCriteoCopies(product, country)
            for product in Jetty.objects.filter(
                deleted=False,
                owner=User.objects.get(username='<EMAIL>'),
            )
        )

        if country.language_code == Countries.united_kingdom.language_code:
            for product in Jetty.objects.filter(
                deleted=False,
                owner=User.objects.get(username='<EMAIL>'),
            ).exclude(id=73019):
                helper = factory.get_helper(
                    product,
                    country,
                    additional_image_id=19082,
                )
                if helper:
                    helpers.append(helper)
                else:
                    logger.warning('No helper found for product: {}'.format(product))
            helpers.append(
                factory.get_helper(
                    Jetty.objects.get(id=73019), country, perfect_fit=True
                )
            )

        context = {
            'lang_code': country.language_code,
            'helpers': helpers,
        }

        if 'preview' in request.GET:
            self.content_type = None

        if 'csv' in request.GET:
            template = render_to_string(self.get_template_names(), context)
            csv = template.replace('\t', ';')
            filename = 'feed.csv'
            response = HttpResponse(csv, content_type='text/plain')
            response['Content-Disposition'] = 'attachment; filename={0}'.format(
                filename
            )
            return response

        return self.render_to_response(context)
