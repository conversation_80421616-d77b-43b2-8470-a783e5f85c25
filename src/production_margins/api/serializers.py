from rest_framework import serializers

from production_margins.models import (
    CustomPricingFactor,
    ElementsOrder,
    GalaStockLevelMaterial,
    MaterialInfoAttachment,
    PricingFactorItem,
)


class PricingFactorItemSerializer(serializers.ModelSerializer):
    class Meta:
        model = PricingFactorItem
        fields = [
            'codename',
            'category',
            'material_type',
            'weight',
            'thickness',
            'length',
            'measurement_unit',
            'price',
            'loss_factor',
            'description',
            'conversion',
            'archived_at',
            'status',
        ]


class CustomPricingFactorSerializer(serializers.ModelSerializer):
    class Meta:
        model = CustomPricingFactor
        fields = [
            'codename',
            'manufactor',
            'price',
            'loss_factor',
            'manufacturer_code',
            'date_from',
            'date_to',
            'note',
        ]


class ElementsOrderSerializer(serializers.ModelSerializer):
    class Meta:
        model = ElementsOrder
        fields = ('id', 'generated_at', 'batches', 'deleted', 'new_report')


class MaterialInfoAttachmentSerializer(serializers.ModelSerializer):
    class Meta:
        model = MaterialInfoAttachment
        fields = '__all__'


class GalaStockLevelMaterialSerializer(serializers.ModelSerializer):
    class Meta:
        model = GalaStockLevelMaterial
        fields = [
            'index',
            'invoice',
            'warehouse',
            'quantity',
            'entry_date',
            'defect',
            'defect_description',
            'supplier',
        ]

    def get_unique_together_validators(self):
        """Overriding method to disable unique together checks"""
        return []
