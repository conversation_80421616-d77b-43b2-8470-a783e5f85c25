from django.urls import path

from production_margins.api.views import (
    CustomPricingFactorAPIView,
    ElementsOrderFileAPIView,
    ElementsOrderListAPIView,
    GalaStockLevelMaterialAPIView,
    ManufacturerCodeAPIView,
    MaterialInfoAttachmentAPIView,
    PricingFactorItemAPIView,
)

urlpatterns = [
    path(
        'material_info/pfi',
        PricingFactorItemAPIView.as_view({'get': 'list'}),
        name='pricing-factor-item-api-list',
    ),
    path(
        'material_info/mc',
        ManufacturerCodeAPIView.as_view({'get': 'list'}),
        name='manufacturer-code-api-list',
    ),
    path(
        'material_info/cpf',
        CustomPricingFactorAPIView.as_view({'get': 'list'}),
        name='custom-pricing-factor-api-list',
    ),
    path(
        'elements_orders',
        ElementsOrderListAPIView.as_view(),
        name='elements-order-list',
    ),
    path(
        'elements_orders_json/<int:pk>/',
        ElementsOrderFileAPIView.as_view(),
        name='elements-order-json',
    ),
    path(
        'material_info/attachments',
        MaterialInfoAttachmentAPIView.as_view({'get': 'list', 'post': 'create'}),
        name='material-info-attachments-api',
    ),
    path(
        'material_info/attachment/<int:pk>',
        MaterialInfoAttachmentAPIView.as_view(
            {
                'get': 'retrieve',
                'post': 'update',
                'put': 'update',
                'patch': 'partial_update',
                'delete': 'destroy',
            }
        ),
        name='material-info-attachment-api',
    ),
    path(
        'gala_stock_level_materials',
        GalaStockLevelMaterialAPIView.as_view(),
        name='gala-stock-level-materials',
    ),
]
