from django.db.models import Q
from django.db.models.functions import Now
from rest_framework import status
from rest_framework.authentication import TokenAuthentication
from rest_framework.exceptions import ValidationError
from rest_framework.generics import (
    GenericAPIView,
    ListAPIView,
    get_object_or_404,
)
from rest_framework.pagination import PageNumberPagination
from rest_framework.response import Response
from rest_framework.settings import api_settings
from rest_framework.views import APIView
from rest_framework.viewsets import ModelViewSet

from producers.api.pagination import ProducerPagePagination
from producers.api.permissions import (
    ProducerPanelPermission,
    ProducerPanelWarehousePermission,
)
from producers.models import Manufactor
from production_margins.api.serializers import (
    CustomPricingFactorSerializer,
    ElementsOrderSerializer,
    GalaStockLevelMaterialSerializer,
    MaterialInfoAttachmentSerializer,
    PricingFactorItemSerializer,
)
from production_margins.data_management.serializers.manufacturer_code import (
    ManufacturerCodeSerializer,
)
from production_margins.models import (
    CustomPricingFactor,
    ElementsOrder,
    GalaStockLevelMaterial,
    ManufacturerCode,
    MaterialInfoAttachment,
    PricingFactorItem,
)
from production_margins.utils import elements_order_file_as_json


class APIViewPagination(PageNumberPagination):
    page_size = 1000
    page_size_query_param = 'page_size'
    max_page_size = 10000


class PricingFactorItemAPIView(ModelViewSet):
    authentication_classes = (TokenAuthentication,)
    queryset = PricingFactorItem.objects.all()
    serializer_class = PricingFactorItemSerializer
    pagination_class = APIViewPagination


class ManufacturerCodeAPIView(ModelViewSet):
    authentication_classes = (TokenAuthentication,)
    queryset = ManufacturerCode.objects.all()
    serializer_class = ManufacturerCodeSerializer
    pagination_class = APIViewPagination


class CustomPricingFactorAPIView(ModelViewSet):
    authentication_classes = (TokenAuthentication,)
    queryset = CustomPricingFactor.objects.filter(
        Q(date_to__gte=Now()) | Q(date_to__isnull=True),
    )
    serializer_class = CustomPricingFactorSerializer
    pagination_class = APIViewPagination


class ElementsOrderListAPIView(ListAPIView):
    permission_classes = [ProducerPanelPermission | ProducerPanelWarehousePermission]
    pagination_class = ProducerPagePagination
    authentication_classes = [
        *api_settings.DEFAULT_AUTHENTICATION_CLASSES,
        TokenAuthentication,
    ]
    serializer_class = ElementsOrderSerializer

    def get_queryset(self):
        manufactor = Manufactor.objects.filter(
            Q(owner=self.request.user) | Q(accounts__user=self.request.user)
        ).first()
        return (
            ElementsOrder.objects.filter(batches__manufactor=manufactor)
            .order_by('-id')
            .distinct()
        )


class ElementsOrderFileAPIView(GenericAPIView):
    permission_classes = [ProducerPanelPermission]
    authentication_classes = [
        *api_settings.DEFAULT_AUTHENTICATION_CLASSES,
        TokenAuthentication,
    ]

    def get(self, request, pk, *args, **kwargs):
        instance = get_object_or_404(self.get_queryset(), pk=pk)
        return Response(
            data=elements_order_file_as_json(instance.order_file),
            content_type='application/json',
        )

    def get_queryset(self):
        manufactor = Manufactor.objects.filter(
            Q(owner=self.request.user) | Q(accounts__user=self.request.user)
        ).first()
        return ElementsOrder.objects.filter(batches__manufactor=manufactor).distinct()


class MaterialInfoAttachmentAPIView(ModelViewSet):
    authentication_classes = (TokenAuthentication,)
    queryset = MaterialInfoAttachment.objects.all()
    serializer_class = MaterialInfoAttachmentSerializer
    pagination_class = APIViewPagination


class GalaStockLevelMaterialAPIView(APIView):
    permission_classes = (ProducerPanelPermission,)
    authentication_classes = (TokenAuthentication,)

    def post(self, request, *args, **kwargs):
        if 'data' not in request.data:
            raise ValidationError(
                'Wrong format. Expected form: {"data": [{}, {}, ...]}'
            )
        serializer = GalaStockLevelMaterialSerializer(
            data=request.data['data'], many=True
        )
        serializer.is_valid(raise_exception=True)
        results = {'created': 0, 'updated': 0}
        for item in serializer.validated_data:
            index = item.pop('index')
            invoice = item.pop('invoice')
            _, created = GalaStockLevelMaterial.objects.update_or_create(
                index=index, invoice=invoice, defaults=item
            )
            results['created' if created else 'updated'] += 1

        return Response(status=status.HTTP_201_CREATED, data=results)
