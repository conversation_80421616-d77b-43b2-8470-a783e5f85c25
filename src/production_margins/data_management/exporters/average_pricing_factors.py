from collections import defaultdict
from typing import (
    Iterable,
    Union,
)

from django.db.models import Prefetch

from production_margins.data_management.exporters.base_exporter import (
    BaseSerializedItemsExporter,
)
from production_margins.data_management.serializers.average_pricing_factors import (
    AVERAGE_PRICING_FACTORS_EXPORT_COLUMNS,
    AveragePricingFactorExportSerializer,
)
from production_margins.models import (
    CustomPricingFactor,
    ElementManagedInfo,
    PricingFactorItem,
)


class AveragePricingFactorsExporter(BaseSerializedItemsExporter):
    dict_export_fields = AVERAGE_PRICING_FACTORS_EXPORT_COLUMNS[1:]
    dict_export_key = AVERAGE_PRICING_FACTORS_EXPORT_COLUMNS[0]
    serializer = AveragePricingFactorExportSerializer
    export_item_prefetch_related = ()

    @property
    def export_item_prefetch_related(self) -> Iterable[Union[str, Prefetch]]:  # noqa: F811
        for_date = self.context.get('for_date')
        prefetch_args = []
        if for_date:
            prefetch_args.append(
                Prefetch(
                    'custompricingfactor_set',
                    queryset=CustomPricingFactor.objects.valid_for_date(
                        for_date,
                    ),
                    to_attr='custom_pricing_factor_valid_for_date',
                ),
            )
        prefetch_args.extend(
            [
                Prefetch(
                    'custompricingfactor_set',
                    queryset=CustomPricingFactor.objects.valid_for_today(),
                    to_attr='custom_pricing_factor_valid_for_today',
                ),
                Prefetch(
                    'elementmanagedinfo_set',
                    queryset=ElementManagedInfo.objects.valid_for_today(),
                    to_attr='elementmanagedinfo_valid_for_today',
                ),
            ]
        )
        return prefetch_args

    @property
    def default_export_queryset(self):
        return PricingFactorItem.objects.all()

    def to_dict(self) -> dict:
        """This is a much more sensible format used by CSTM Pricing Factors model."""
        exported_data = defaultdict(dict)
        for export_item in self.export_items_serialized:
            export_key = export_item[self.dict_export_key]
            for field_name in self.dict_export_fields:
                exported_data[field_name][export_key] = export_item[field_name]
        return dict(exported_data)
