import csv
import datetime
import io
import json

from typing import (
    ClassVar,
    Iterable,
    Iterator,
    List,
    Type,
    Union,
)

from django.core.serializers.json import DjangoJSONEncoder
from django.db.models import Prefetch
from rest_framework import serializers


class BaseSerializedItemsExporter:
    dict_export_key = ClassVar[str]
    dict_export_fields = ClassVar[Iterable[str]]
    default_export_queryset = ClassVar[Iterable]
    serializer = ClassVar[Type[serializers.ModelSerializer]]
    export_item_prefetch_related = ClassVar[Iterable[Union[str, Prefetch]]]

    def __init__(
        self,
        queryset: Iterable = None,  # noqa: RUF013
        additional_export_fields: Iterable[str] = None,  # noqa: RUF013
        for_date: datetime.datetime = None,  # noqa: RUF013
    ):
        self.export_queryset = queryset or self.default_export_queryset
        if additional_export_fields:
            self.dict_export_fields.extend(additional_export_fields)
        self.context = {}
        if for_date:
            self.context['for_date'] = for_date

    def to_dict(self):
        return [export_item for export_item in self.export_items_serialized]  # noqa: C416

    def to_json(self) -> str:
        return json.dumps(
            self.to_dict(),
            cls=DjangoJSONEncoder,
            indent=4,
        )

    def to_csv(self) -> str:
        output = io.StringIO()
        writer = csv.DictWriter(
            output,
            fieldnames=self.csv_export_columns,
            restval='',
        )
        writer.writeheader()
        for export_item in self.export_items_serialized:
            writer.writerow(
                {
                    column_name: export_item[column_name]
                    for column_name in self.csv_export_columns
                }
            )
        return output.getvalue()

    @property
    def export_items_serialized(self) -> Iterator[dict]:
        items = self.export_queryset.prefetch_related(
            *self.export_item_prefetch_related,
        )
        for item in items:
            yield self.serializer(item, context=self.context).data

    @property
    def csv_export_columns(self) -> List[str]:
        return [self.dict_export_key, *self.dict_export_fields]
