from typing import Iterator

from production_margins.choices import MaterialInfoAttachmentType
from production_margins.data_management.exporters import BaseSerializedItemsExporter
from production_margins.data_management.serializers.manufacturer_code import (
    MANUFACTURER_CODE_EXPORT_COLUMNS,
    ManufacturerCodeSerializer,
)
from production_margins.models import ManufacturerCode


class ManufacturerCodeExporter(BaseSerializedItemsExporter):
    dict_export_fields = MANUFACTURER_CODE_EXPORT_COLUMNS[1:]
    dict_export_key = MANUFACTURER_CODE_EXPORT_COLUMNS[0]
    serializer = ManufacturerCodeSerializer
    export_item_prefetch_related = ()

    @property
    def default_export_queryset(self):
        return ManufacturerCode.objects.all()


class ManufacturerCodeAttachmentsExporter(BaseSerializedItemsExporter):
    dict_export_key = 'code'

    @property
    def dict_export_fields(self) -> list[str]:
        return [option[-1] for option in MaterialInfoAttachmentType.choices]

    @property
    def export_items_serialized(self) -> Iterator[dict]:
        # queryset prefetched in the caller
        for item in self.export_queryset:
            att_items = getattr(item, 'prefetched_att', [])
            row = {'code': item.code}
            row.update(
                {
                    option[-1]: any(
                        att.is_valid_today
                        for att in att_items
                        if att.document_type == option[0]
                    )
                    for option in MaterialInfoAttachmentType.choices
                }
            )

            yield row
