from datetime import datetime

from django.db.models import (
    Prefetch,
    Q,
)
from rest_framework import serializers
from rest_framework.validators import UniqueValidator

from cached_property import cached_property_with_ttl

from producers.models import Manufactor
from production_margins.models import (
    CustomPricingFactor,
    PricingFactorItem,
)

PRICING_FACTOR_ITEM_EXPORT_COLUMNS: list[str] = [
    'id',
    'codename',
    'category',
    'price',
    'loss_factor',
    'weight',
    'thickness',
    'length',
    'measurement_unit',
    'description',
    'conversion',
    'status',
]

PRICING_FACTOR_ITEM_CPF_EXPORT_COLUMNS: list[str] = [
    *PRICING_FACTOR_ITEM_EXPORT_COLUMNS,
]

PRICING_FACTOR_ITEM_PF_EXPORT_COLUMNS: list[str] = [
    'codename',
    'C1_category',
    'C2_type',
    'C3_version',
    'C4_description',
    'price_per_unit',
    'loss_factor',
    'weight_per_unit',
    'thickness',
    'length',
    'measurement_unit',
    'polish_desc',
    'conversion',
    'unit',
]


class PricingFactorItemCPFColumnsExportSerializer(serializers.ModelSerializer):
    """Serializer with additional data from active Custom Pricing Factors."""

    category = serializers.CharField(source='c1_category')

    measurement_unit = serializers.CharField(
        source='get_measurement_unit_display',
    )

    def to_representation(self, instance):
        data = super().to_representation(instance)
        manufacturers = Manufactor.objects.prefetch_related(
            Prefetch(
                'custompricingfactor_set',
                CustomPricingFactor.objects.filter(
                    self._today_date_query,
                    pricing_factor_item=instance,
                ).order_by('-id'),
                to_attr='active_custom_pricing_factors',
            )
        )
        for manufacturer in manufacturers:
            if manufacturer.active_custom_pricing_factors:
                cpf = manufacturer.active_custom_pricing_factors[0]
                data[f'cpf_price_{manufacturer.name}'] = cpf.price
                data[f'cpf_loss_factor_{manufacturer.name}'] = cpf.loss_factor
            else:
                data[f'cpf_price_{manufacturer.name}'] = ''
                data[f'cpf_loss_factor_{manufacturer.name}'] = ''
        return data

    @staticmethod
    def fields_generated_per_manufacturer():
        for manufacturer in Manufactor.objects.all():
            yield f'cpf_price_{manufacturer.name}'
            yield f'cpf_loss_factor_{manufacturer.name}'

    @cached_property_with_ttl(3600)
    def _today_date_query(self):
        today = datetime.now()
        return Q(
            Q(date_to__gte=today) | Q(date_to__isnull=True),
            date_from__lte=today,
        )

    class Meta:
        model = PricingFactorItem
        fields = PRICING_FACTOR_ITEM_CPF_EXPORT_COLUMNS


class PricingFactorItemPFDataExportSerializer(serializers.ModelSerializer):
    """Serializer with columns corresponding to Pricing Factors format."""

    C1_category = serializers.CharField(source='c1_category')
    C2_type = serializers.CharField(source='c2_type')
    C3_version = serializers.CharField(source='c3_version')
    C4_description = serializers.CharField(source='c4_description')
    price_per_unit = serializers.FloatField(source='price')
    weight_per_unit = serializers.FloatField(source='weight')
    loss_factor = serializers.SerializerMethodField()
    polish_desc = serializers.CharField(source='description')
    unit = serializers.CharField(source='get_measurement_unit_display')

    class Meta:
        model = PricingFactorItem
        fields = PRICING_FACTOR_ITEM_PF_EXPORT_COLUMNS

    def get_loss_factor(self, obj: PricingFactorItem) -> float:
        if not obj.loss_factor:
            return float(1)
        return float((obj.loss_factor + 100) / 100)


class PricingFactorItemExportSerializer(serializers.ModelSerializer):
    """Basic serializer for exporting PFI data between environments."""

    class Meta:
        model = PricingFactorItem
        fields = PRICING_FACTOR_ITEM_EXPORT_COLUMNS
        extra_kwargs = {
            'codename': {
                'validators': [
                    # NOTE: we want unique validator, but not the regex validator
                    UniqueValidator(queryset=PricingFactorItem.objects.all()),
                ],
            },
        }
