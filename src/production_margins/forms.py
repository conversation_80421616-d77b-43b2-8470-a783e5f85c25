import re

from django import forms
from django.contrib.admin.widgets import AdminDateWidget
from django.core.exceptions import ValidationError
from django.utils.safestring import mark_safe

from crispy_forms.helper import FormHelper
from crispy_forms.layout import Submit

from custom.enums import (
    PhysicalProductVersion,
    ShelfType,
)
from producers.choices import BatchType
from producers.models import Product
from production_margins.choices import (
    CodenamesRecalculationChoice,
    PricingFactorItemCategory,
    ReportDataType,
)
from production_margins.models import (
    ElementsOrder,
    ManufacturerCode,
)


class ElementsOrderForm(forms.ModelForm):
    class Meta:
        model = ElementsOrder
        fields = (
            'order_file',
            'batches',
            'total_cost',
            'service_cost',
            'deleted',
            'new_report',
        )

    def clean_batches(self):
        if 'batches' in self.changed_data:
            initial_batches_id = [batch.id for batch in self.initial['batches']]
            new_batches = self.cleaned_data['batches'].exclude(
                id__in=initial_batches_id,
            )

            batches_with_elements_order = [
                batch.id for batch in new_batches if batch.elementsorder_set.exists()
            ]

            if batches_with_elements_order:
                raise forms.ValidationError(
                    f'Batches with ids {batches_with_elements_order}, '
                    f'are already generated'
                )

            batch_types = self.cleaned_data['batches'].values_list(
                'batch_type',
                flat=True,
            )
            batch_types_set = set(batch_types)
            if BatchType.COMPLAINTS in batch_types_set and len(batch_types_set) > 1:
                raise forms.ValidationError('You cant mix new orders with complaints')

        return self.cleaned_data['batches']


class CodenamesReportForm(forms.Form):
    report_type = forms.ChoiceField(choices=ReportDataType.choices)
    reporting_date = forms.DateField(
        widget=forms.TextInput(attrs={'type': 'date'}),
        help_text='Select the month for the report. Click on any day in that month.',
        required=False,
    )
    pricing_factors = forms.ChoiceField(choices=CodenamesRecalculationChoice.choices)
    product_ids = forms.CharField(widget=forms.Textarea, required=False)
    is_aggregated = forms.BooleanField(
        label='Aggregated codenames?',
        widget=forms.CheckboxInput,
        help_text=(
            'Report will be grouped by: codename, item_type, color, category, '
            + 'country and configurator type'
        ),
        required=False,
    )
    type01_ppv = forms.ChoiceField(
        required=False,
        choices=[(None, 'Original')] + PhysicalProductVersion.choices(),  # noqa: RUF005
    )
    type01v_ppv = forms.ChoiceField(
        required=False,
        choices=[(None, 'Original')] + PhysicalProductVersion.choices(),  # noqa: RUF005
    )
    type02_ppv = forms.ChoiceField(
        required=False,
        choices=[(None, 'Original')] + PhysicalProductVersion.choices(),  # noqa: RUF005
    )
    type03_ppv = forms.ChoiceField(
        required=False,
        choices=[(None, 'Original')] + PhysicalProductVersion.choices(),  # noqa: RUF005
    )
    type13_ppv = forms.ChoiceField(
        required=False,
        choices=[(None, 'Original')] + PhysicalProductVersion.choices(),  # noqa: RUF005
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_class = 'form-horizontal'
        self.helper.label_class = 'col-lg-2'
        self.helper.field_class = 'col-lg-8'
        self.helper.add_input(Submit('submit', 'Submit', css_class='btn-primary'))

    def clean(self):
        cleaned_data = super().clean()
        is_monthly_report = (
            int(cleaned_data.get('report_type')) == ReportDataType.MONTHLY
        )
        if is_monthly_report and not cleaned_data.get('reporting_date'):
            raise forms.ValidationError(
                'Reporting month must be filled for monthly report.',
            )
        if not is_monthly_report and not cleaned_data.get('product_ids'):
            raise forms.ValidationError('Product ids cannot be empty for ids report.')
        cleaned_data['ppv_overrides'] = self._clean_ppv(cleaned_data)
        return cleaned_data

    def _clean_ppv(self, cleaned_data):
        data = {
            ShelfType.TYPE01.production_code: cleaned_data.get('type01_ppv'),
            ShelfType.VENEER_TYPE01.production_code: cleaned_data.get('type01v_ppv'),
            ShelfType.TYPE02.production_code: cleaned_data.get('type02_ppv'),
            ShelfType.TYPE03.production_code: cleaned_data.get('type03_ppv'),
            ShelfType.TYPE13.production_code: cleaned_data.get('type13_ppv'),
        }
        return {shelf_type: int(ppv) for shelf_type, ppv in data.items() if ppv}

    def clean_product_ids(self):
        if not self.cleaned_data.get('product_ids'):
            return []
        product_ids = self.cleaned_data['product_ids'].strip(',')
        cleaned_product_ids = []
        for product_id in product_ids.split(','):
            try:
                cleaned_product_ids.append(int(product_id))
            except ValueError:  # noqa: PERF203
                raise forms.ValidationError(f'{product_id} is not a valid integer')
        return cleaned_product_ids


class ChooseDateForm(forms.Form):
    _selected_action = forms.CharField(widget=forms.MultipleHiddenInput)
    date_from = forms.DateField(
        label='date_from',
        required=False,
        widget=AdminDateWidget,
        help_text=mark_safe(
            '</br>Fill in to change <b>date from</b> in selected items',
        ),
    )
    date_to = forms.DateField(
        label='date_to',
        required=False,
        widget=AdminDateWidget,
        help_text=mark_safe(
            '</br>Fill in to change <b>date to</b> in selected items',
        ),
    )


class NoteForm(forms.Form):
    note = forms.CharField(widget=forms.Textarea(attrs={'rows': '5'}), required=False)


class TaxReportForm(forms.Form):
    start_date = forms.DateField(
        widget=forms.TextInput(attrs={'type': 'date'}),
        help_text='Select a start date.',
        required=True,
    )
    end_date = forms.DateField(
        widget=forms.TextInput(attrs={'type': 'date'}),
        help_text='Select an end date.',
        required=True,
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_class = 'form-horizontal'
        self.helper.label_class = 'col-lg-2'
        self.helper.field_class = 'col-lg-8'
        self.helper.add_input(Submit('submit', 'Submit', css_class='btn-primary'))

    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data['start_date']
        end_date = cleaned_data['end_date']
        if start_date > end_date:
            raise forms.ValidationError(
                'The end date must be greater than or equal to the start date.',
            )
        return cleaned_data


class ManufacturerCodeForm(forms.ModelForm):
    class Meta:
        model = ManufacturerCode
        fields = '__all__'  # noqa: DJ007

    def clean_code(self):
        return self.cleaned_data['code'].upper()


class SearchByCodenameForm(forms.Form):
    pricing_factor_item_codenames = forms.CharField(
        widget=forms.Textarea(attrs={'rows': 50, 'cols': 150}),
        required=True,
    )

    def clean_pricing_factor_item_codenames(self):
        codenames = self.cleaned_data['pricing_factor_item_codenames'].splitlines()
        return [codename.rstrip() for codename in codenames]


class ProductsListForElementsOrderToInvoiceForm(forms.Form):
    products_lists = forms.CharField(widget=forms.Textarea)
    invoice_number = forms.CharField()

    def parse_products_lists(self):
        pattern = r'\b\d{3,}\b'
        products_ids = re.findall(pattern, self.cleaned_data['products_lists'])
        return [int(product_id) for product_id in products_ids]

    def clean_products_lists(self):
        product_ids = self.parse_products_lists()
        queryset = Product.objects.filter(id__in=product_ids).select_related('batch')
        if queryset.filter(batch__isnull=True).exists():
            products_without_batch = queryset.filter(batch__isnull=True).values_list(
                'id', flat=True
            )
            raise ValidationError(
                f'Products: {list(products_without_batch)} do not have a batch assigned'
            )
        return product_ids


class PricingFactorItemForm(forms.ModelForm):
    def clean(self):
        cleaned_data = super().clean()
        is_packaging = cleaned_data['category'] == PricingFactorItemCategory.PACKAGING
        if is_packaging and not cleaned_data['material_type']:
            raise ValidationError('Please specify material type for ecotax.')
