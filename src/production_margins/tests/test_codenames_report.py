import pandas as pd
import pytest

from producers.models import Product
from production_margins import codenames_report


@pytest.fixture
def product_with_minimal_working_cached_serialization(
    manufactor,
    product_factory,
    product_details_jetty_factory,
):
    product = product_factory(manufactor=manufactor)
    serialization = {
        'materials': {
            'material_data': {
                'fitting_screw_black_modular-pin-tip': {
                    'usage': 6,
                    'unit': 'szt',
                    'cost': 2.34,
                },
            },
            'semiproducts_data': {
                'semiproduct_cut-out_orange_felt': {
                    'usage': 2,
                    'unit': 'szt',
                    'cost': 2.0,
                },
            },
            'services_data': {
                'service_connector_orange_paint-modular-pin-tip': {
                    'usage': 6,
                    'unit': 'szt',
                    'cost': 0.24,
                },
            },
            'packaging_data': {
                'packaging_cardboard_white_startbox': {
                    'usage': 1,
                    'unit': 'szt',
                    'cost': 0.99,
                },
            },
        },
        'item': {
            'weight': 10,
            'packs': [
                {'weight': 6},
                {'weight': 4},
            ],
        },
        'id_manufactor': manufactor.id,
        'id_pricing_factors': 123,
    }
    product_details_jetty_factory(
        cached_serialization=serialization,
        product=product,
    )
    return product


def test_generate_codenames_report_calls_proper_functions(db, mocker, product):
    mocked_extract = mocker.patch(
        'production_margins.codenames_report._extract_report_info_from_products',
        return_value=(1, 2),
    )
    mocked_fill = mocker.patch(
        'production_margins.codenames_report._report_data_with_calculated_columns',
    )
    codenames_report.generate_codenames_report(
        Product.objects.filter(id__in=[product.id]),
        is_original_serialization=False,
    )
    assert mocked_extract.call_count == 1
    assert mocked_fill.call_count == 1


@pytest.mark.parametrize('no_of_products', [2, 8])
def test_extract_report_info_from_products_calls_subextract_for_each_product(
    db,
    mocker,
    product_factory,
    no_of_products,
):
    mocked_subextract = mocker.patch(
        'production_margins.codenames_report._extract_report_info_from_single_product',
    )
    mocked_get_data_from_pricing_factors = mocker.patch(
        'production_margins.codenames_report._get_pricing_factors_for_date',
    )
    product_factory.create_batch(no_of_products)
    codenames_report._extract_report_info_from_products(
        Product.objects.all(),
        is_original_serialization=True,
    )
    assert mocked_subextract.call_count == no_of_products
    # Number of calls for PF should be equal to number of unique
    # months from `created_at` date for product in queryset.
    # Here we create batch with the same `created_at` date, so we should
    # ask for PF only once.
    assert mocked_get_data_from_pricing_factors.call_count == 1


def test_extract_report_info_from_products_ignores_erroneus_products(
    db,
    mocker,
    product,
):
    mocker.patch(
        'production_margins.codenames_report._extract_report_info_from_single_product',
        side_effect=KeyError,
    )
    mocker.patch(
        'production_margins.codenames_report._get_pricing_factors_for_date',
    )
    report_data, errors = codenames_report._extract_report_info_from_products(
        Product.objects.filter(id=product.id),
        is_original_serialization=True,
    )
    assert report_data.empty
    assert errors == [product.id]


def test_extract_report_info_from_single_product_returns_a_dataframe_with_row_for_each_codename(  # noqa: E501
    db,
    product_with_minimal_working_cached_serialization,
):
    usage_info = codenames_report._extract_report_info_from_single_product(
        product_with_minimal_working_cached_serialization,
        is_original_serialization=True,
    )
    assert len(usage_info) == 4
    assert all(
        usage_info['codename']
        == pd.Series(
            [
                'fitting_screw_black_modular-pin-tip',
                'semiproduct_cut-out_orange_felt',
                'service_connector_orange_paint-modular-pin-tip',
                'packaging_cardboard_white_startbox',
            ]
        )
    )


def test_get_product_base_info_returns_product_and_order_data(
    db,
    product_factory,
    order_factory,
):
    order = order_factory(paid_at='2021-11-01')
    product = product_factory(order=order)
    base_info = codenames_report._get_product_base_info(product)
    assert base_info['product_id'] == product.id
    assert base_info['paid_at'] == order.paid_at


def test_get_serialization_base_info_extracts_data_from_serialization():
    serialization_data = {
        'serialized_at': '2021-11-02',
        'id_pricing_factors': 125,
        'item': {
            'weight': 5,
            'packs': [
                {'weight': 5},
            ],
        },
    }
    serialization_info = codenames_report._get_serialization_base_info(
        serialization_data
    )
    assert serialization_info['serialized_at'] == serialization_data['serialized_at']
    assert (
        serialization_info['pricing_factors_id']
        == serialization_data['id_pricing_factors']
    )


def test_get_furniture_parameters_extracts_data_from_order_item(
    db,
    order_factory,
    order_item_factory,
    product_with_minimal_working_cached_serialization,
):
    order_item = order_item_factory(is_jetty=True)
    order = order_factory(items=[order_item])
    product_with_minimal_working_cached_serialization.order = order
    furniture_parameters = codenames_report._get_furniture_parameters(
        product_with_minimal_working_cached_serialization
    )
    assert furniture_parameters['country'] == order.country.lower()
    assert furniture_parameters['item_type'] == order_item.order_item.shelf_type


def test_report_data_with_calculated_columns_fills_base_data(db):
    codenames = [
        'fitting_screw_black_modular-pin-tip',
        'semiproduct_cut-out_orange_felt',
        'service_connector_orange_paint-modular-pin-tip',
        'packaging_cardboard_white_startbox',
    ]
    base_data = pd.DataFrame(
        {
            'codename': codenames,
            'summary_quantity': [1, 2, 5, 125],
            'price_per_unit': [125, 125, 125, 125],
            'management_cost': [0.05, 0.05, 0.05, 0.05],
            'loss_factor': [1.01, 1.03, 1.1, 2],
            'weight_per_unit': [0.5, 0.5, 0.5, 0.5],
        }
    )
    full_data = codenames_report._report_data_with_calculated_columns(base_data)
    assert all(
        full_data.summary_total_cost
        == pd.Series([132.5625, 270.3750, 721.8750, 32812.5000])
    )


def test_generate_aggregated_report_calls_proper_function(db, mocker, product):
    mocked_extract = mocker.patch(
        'production_margins.codenames_report._extract_report_info_from_products',
        return_value=(1, 2),
    )
    mocked_aggregated = mocker.patch(
        'production_margins.codenames_report._report_data_with_aggregated_codenames',
    )
    mocker.patch(
        'production_margins.codenames_report._report_data_with_calculated_columns',
    )

    codenames_report.generate_codenames_report(
        products=Product.objects.filter(id__in=[product.id]),
        is_original_serialization=False,
        is_aggregated=True,
    )

    assert mocked_extract.call_count == 1
    assert mocked_aggregated.call_count == 1


dataframe_template = pd.DataFrame(
    {
        # group by these columns
        'codename': 8 * ['fitting_screw_black_modular-pin-tip'],
        'item_type': [0, 0, 1, 1, 0, 0, 1, 1],
        'color': [0, 0, 0, 0, 3, 3, 7, 7],
        'category': 4 * ['sideboard'] + 4 * ['tvstand'],
        'country': 8 * ['poland'],
        'configurator_type': 8 * ['ROW'],
        # average these columns
        'weight_net': 8 * [1.5],
        'weight_gross': 8 * [2],
        'width': [2000, 2020, 2040, 2060, 1500, 1600, 1700, 1800],
        'height': [930, 1230, 1530, 1830, 330, 530, 730, 1130],
        'depth': [300, 420, 300, 420, 300, 420, 300, 420],
        # sum this columns
        'summary_price': 8 * [1],
        'summary_losses': 8 * [2],
        'summary_material_consumption_with_losses': 8 * [3],
        'summary_cost_with_losses': 8 * [4],
        'summary_cost_of_management': 8 * [5],
        'summary_total_cost': 8 * [6],
    }
)


def test_report_data_aggregated_correctly_group_codenames(mocker):
    mocked_calculate_summary = mocker.patch(
        'production_margins.codenames_report._report_data_with_calculated_columns',
        return_value=pd.DataFrame(dataframe_template),
    )
    aggregated_data = codenames_report._report_data_with_aggregated_codenames(
        usages_dataframe=mocked_calculate_summary()
    )

    # data shall be grouped into 4 groups
    assert aggregated_data.shape[0] == 4


def test_report_data_aggregated_has_correctly_sum_values(mocker):
    mocked_calculate_summary = mocker.patch(
        'production_margins.codenames_report._report_data_with_calculated_columns',
        return_value=dataframe_template,
    )
    aggregated_data = codenames_report._report_data_with_aggregated_codenames(
        usages_dataframe=mocked_calculate_summary()
    )

    assert all(aggregated_data.weight_net == pd.Series(4 * [1.5]))
    assert all(aggregated_data.weight_gross == pd.Series(4 * [2]))
    assert all(aggregated_data.width == pd.Series([2010, 1550, 2050, 1750]))
    assert all(aggregated_data.height == pd.Series([1080, 430, 1680, 930]))
    assert all(aggregated_data.depth == pd.Series(4 * [360]))


def test_report_data_aggregated_has_correctly_calculated_values(mocker):
    mocked_calculate_summary = mocker.patch(
        'production_margins.codenames_report._report_data_with_calculated_columns',
        return_value=dataframe_template,
    )
    aggregated_data = codenames_report._report_data_with_aggregated_codenames(
        usages_dataframe=mocked_calculate_summary()
    )

    assert all(aggregated_data.summary_price == pd.Series(4 * [2]))
    assert all(aggregated_data.summary_losses == pd.Series(4 * [4]))
    assert all(
        aggregated_data.summary_material_consumption_with_losses == pd.Series(4 * [6])
    )
    assert all(aggregated_data.summary_cost_with_losses == pd.Series(4 * [8]))
    assert all(aggregated_data.summary_cost_of_management == pd.Series(4 * [10]))
    assert all(aggregated_data.summary_total_cost == pd.Series(4 * [12]))
