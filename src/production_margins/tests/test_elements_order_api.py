from io import BytesIO

from django.core.files.base import ContentFile
from django.urls import reverse
from rest_framework import status

import openpyxl
import pytest

from producers.tests.conftest import setup_manufacturer_with_product_factory


def empty_order_xlsx() -> bytes:
    workbook = openpyxl.Workbook()
    sheet = workbook.active
    sheet.title = 'Zamowienie 123'
    workbook.create_sheet('ZESTAWIENIE')
    stream = BytesIO()
    workbook.save(stream)
    stream.seek(0)
    return stream.read()


@pytest.fixture
def api_token_and_elements_order(product_batch_factory, elements_order_factory):
    product, token = setup_manufacturer_with_product_factory('GALA')
    batch = product_batch_factory(manufactor=product.manufactor)
    batch.batch_items.set([product])
    elements_order = elements_order_factory()
    elements_order.order_file.save('test.xlsx', ContentFile(empty_order_xlsx()))
    elements_order.batches.set([batch])
    return token, elements_order


@pytest.mark.django_db
def test_elements_order_api_list(api_client, api_token_and_elements_order):
    token, elements_order = api_token_and_elements_order
    elements_order.new_report = elements_order
    elements_order.save()
    url = reverse('elements-order-list')
    response = api_client.get(url, HTTP_AUTHORIZATION=f'Token {token}')
    assert response.status_code == status.HTTP_200_OK
    assert response.status_code == 200


@pytest.mark.django_db
def test_getting_elements_order_file_as_json(api_client, api_token_and_elements_order):
    token, elements_order = api_token_and_elements_order
    url = reverse('elements-order-json', kwargs={'pk': elements_order.pk})
    response = api_client.get(url, HTTP_AUTHORIZATION=f'Token {token}')
    assert response.status_code == status.HTTP_200_OK
    assert response.status_code == 200
    assert response['content-type'] == 'application/json'
