from datetime import (
    date,
    timedelta,
)
from decimal import Decimal

from django.core.exceptions import ValidationError

import pytest

from freezegun import freeze_time

from producers.enums import CustomPricingFactorChangeRequestStatusEnum
from production_margins.enums import PricingFactorItemStatus
from production_margins.models import CustomPricingFactor


@pytest.mark.django_db
def test_custom_pricing_factor_validate_dates(custom_pricing_factor_factory):
    with pytest.raises(ValidationError):
        custom_pricing_factor_factory(
            date_from=date(2022, 5, 30),
            date_to=date(2022, 5, 29),
        )


@pytest.mark.django_db
@pytest.mark.parametrize(
    ('date_from', 'date_to'),
    (  # noqa: PT007
        (date(2022, 1, 1), date(2023, 1, 1)),
        (date(2022, 4, 1), date(2022, 5, 1)),
        (date(2022, 5, 10), date(2022, 5, 20)),
        (date(2022, 5, 31), date(2022, 6, 30)),
    ),
)
def test_custom_pricing_factor_validate_dates_overlap(
    pricing_factor_item,
    custom_pricing_factor_factory,
    manufactor,
    date_from,
    date_to,
):
    custom_pricing_factor_factory(
        pricing_factor_item=pricing_factor_item,
        manufactor=manufactor,
        date_from=date(2022, 5, 1),
        date_to=date(2022, 5, 31),
    )

    with pytest.raises(ValidationError):
        custom_pricing_factor_factory(
            pricing_factor_item=pricing_factor_item,
            manufactor=manufactor,
            date_from=date_from,
            date_to=date_to,
        )


FREEZE_TIME_2023_08_30 = date(2023, 8, 30)


@pytest.mark.django_db
@freeze_time(FREEZE_TIME_2023_08_30)
@pytest.mark.parametrize(
    ('status', 'expected_archived_at'),
    (  # noqa: PT007
        (PricingFactorItemStatus.ARCHIVED.value, FREEZE_TIME_2023_08_30),
        (PricingFactorItemStatus.ACTIVE.value, None),
        ('', None),
    ),
)
def test_pricing_factor_item_set_archived_at_when_status_change(
    pricing_factor_item, status, expected_archived_at
):
    pricing_factor_item.status = status
    pricing_factor_item.save()
    pricing_factor_item.refresh_from_db()
    assert pricing_factor_item.status == status
    assert pricing_factor_item.archived_at == expected_archived_at


@pytest.mark.django_db
class TestCustomPricingFactorChangeRequestAcceptance:
    @pytest.fixture
    def base_date(self):
        return date.today()

    @pytest.fixture
    def current_custom_pricing_factor(self, custom_pricing_factor_factory, base_date):
        return custom_pricing_factor_factory(
            date_from=base_date - timedelta(days=10), date_to=None
        )

    def test_request_acceptance_based_on_status(
        self,
        custom_pricing_factor_change_request_factory,
        current_custom_pricing_factor,
        base_date,
    ):
        for status in CustomPricingFactorChangeRequestStatusEnum:
            request = custom_pricing_factor_change_request_factory(
                custom_pricing_factor=current_custom_pricing_factor,
                status=status.value,
                date_from=base_date,
            )
            expected_result = status == CustomPricingFactorChangeRequestStatusEnum.NEW
            assert request.can_be_accepted() == expected_result

    def test_new_request_no_other_active_requests(
        self,
        custom_pricing_factor_change_request_factory,
        current_custom_pricing_factor,
        base_date,
    ):
        new_request = custom_pricing_factor_change_request_factory(
            custom_pricing_factor=current_custom_pricing_factor,
            status=CustomPricingFactorChangeRequestStatusEnum.NEW.value,
            date_from=base_date,
        )
        assert new_request.can_be_accepted()

    def test_selected_request_is_newest_when_more_active_requests(
        self,
        custom_pricing_factor_change_request_factory,
        current_custom_pricing_factor,
        base_date,
    ):
        earlier_date = base_date - timedelta(days=5)
        later_date = base_date - timedelta(days=4)
        first_request = custom_pricing_factor_change_request_factory(
            custom_pricing_factor=current_custom_pricing_factor,
            status=CustomPricingFactorChangeRequestStatusEnum.NEW.value,
            date_from=earlier_date,
        )
        second_request = custom_pricing_factor_change_request_factory(
            custom_pricing_factor=current_custom_pricing_factor,
            status=CustomPricingFactorChangeRequestStatusEnum.NEW.value,
            date_from=later_date,
        )
        assert first_request.can_be_accepted()
        assert not second_request.can_be_accepted()

    @pytest.mark.parametrize(
        'days_difference, expected_result',  # noqa: PT006
        [
            (-11, False),
            (-10, False),
            (-9, True),
            (-1, True),
            (0, True),
            (1, True),
            (2, False),
        ],
    )
    def test_overlapping_dates_with_declared_date_to(
        self,
        custom_pricing_factor_change_request_factory,
        custom_pricing_factor_factory,
        base_date,
        days_difference,
        expected_result,
    ):
        custom_pricing_factor = custom_pricing_factor_factory(
            date_from=base_date - timedelta(days=10), date_to=base_date
        )

        change_request = custom_pricing_factor_change_request_factory(
            custom_pricing_factor=custom_pricing_factor,
            status=CustomPricingFactorChangeRequestStatusEnum.NEW.value,
            date_from=base_date + timedelta(days=days_difference),
        )

        assert change_request.can_be_accepted() == expected_result

    @pytest.mark.parametrize(
        'initial_code, new_code, expected_code',  # noqa: PT006
        [
            ('InitialCode', 'InitialCode', 'InitialCode'),
            ('InitialCode', 'NewCode', 'NewCode'),
        ],
    )
    def test_update_with_valid_data_for_available_codes(
        self,
        pricing_factor_item,
        custom_pricing_factor_change_request_factory,
        custom_pricing_factor_factory,
        initial_code,
        new_code,
        expected_code,
        manufacturer_code_factory,
    ):
        original_manufacturer_code = manufacturer_code_factory(code=initial_code)
        if initial_code != new_code:
            # create new available code to change
            available_manufacturer_code = manufacturer_code_factory(code=new_code)
            pricing_factor_item.manufacturercode_set.add(available_manufacturer_code)
        pricing_factor_item.manufacturercode_set.add(original_manufacturer_code)

        original_pricing_factor = custom_pricing_factor_factory(
            pricing_factor_item=pricing_factor_item,
            price=Decimal('100.00'),
            manufacturer_code=original_manufacturer_code,
            date_from=date.today() - timedelta(days=10),
            date_to=None,
        )

        change_request = custom_pricing_factor_change_request_factory(
            custom_pricing_factor=original_pricing_factor,
            status=CustomPricingFactorChangeRequestStatusEnum.NEW.value,
            price=Decimal('150.00'),
            manufacturer_code=new_code,
            date_from=date.today() + timedelta(days=1),
        )

        change_request.update_and_create_custom_pricing_factor()

        original_pricing_factor.refresh_from_db()
        new_custom_pricing_factor = CustomPricingFactor.objects.last()

        assert original_pricing_factor.date_to == change_request.date_from - timedelta(
            days=1
        )
        assert new_custom_pricing_factor.date_from == change_request.date_from
        assert new_custom_pricing_factor.date_to is None
        assert new_custom_pricing_factor.price == change_request.price
        assert new_custom_pricing_factor.manufacturer_code.code == expected_code
