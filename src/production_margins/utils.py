from django.shortcuts import redirect
from django.urls import reverse

import pandas as pd

from openpyxl.reader.excel import load_workbook
from openpyxl.utils import get_column_letter

from producers.utils import BaseArchiveCodenameRemoval


def build_codename_based_dict(custom_pricing_factors):
    result = {}
    for codename_element in custom_pricing_factors:
        result[codename_element['codename']] = codename_element
    return result


def search_custom_pricing_factor_by_ids(modeladmin, request, queryset, form):
    custom_pricing_factor_codenames = form.cleaned_data['pricing_factor_item_codenames']
    custom_pricing_factors = modeladmin.model.objects.filter(
        pricing_factor_item__codename__in=custom_pricing_factor_codenames
    )

    custom_pricing_factor_ids = ','.join(
        str(cpf_id) for cpf_id in custom_pricing_factors.values_list('id', flat=True)
    )

    url = reverse('admin:production_margins_custompricingfactor_changelist')
    return redirect(f'{url}?id__in={custom_pricing_factor_ids}')


def auto_adjust_columns_on_worksheet(sheet):
    for idx, col in enumerate(sheet.columns, 1):
        max_length = 0
        column_name = get_column_letter(idx)
        for cell in col:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except Exception:  # noqa: PERF203, S110
                # Necessary to avoid error on empty cells
                pass
        adjusted_width = (max_length + 2) * 1.2
        sheet.column_dimensions[column_name].width = adjusted_width


class PricingFactorArchiveRemoval(BaseArchiveCodenameRemoval):
    def remove_from_data_frame(self, df):
        archived_codenames = self.get_archived_codenames()
        archived_codenames_df = df.loc[df.index.intersection(archived_codenames), :]
        for index, column in archived_codenames_df.iterrows():
            if self.is_zero_usage(column):
                df = df.drop(index)
        return df


def elements_order_file_as_json(elements_order_file):
    xls = pd.read_excel(elements_order_file, sheet_name=['ZESTAWIENIE'])
    df = xls['ZESTAWIENIE']
    df.replace({pd.np.nan: None}, inplace=True)
    return {
        'totals': get_totals_from_details_sheet(elements_order_file),
        'details': df.to_dict(orient='records'),
    }


def get_totals_from_details_sheet(elements_order_file):
    wb = load_workbook(elements_order_file)
    sheet = wb.active
    results = {
        'Koszt - ogółem': 0,
        'Koszt - moduły': 0,
        'Koszt - pokrowce': 0,
    }
    for column in range(1, 20):
        for row in range(1, 20):
            cell = sheet.cell(row=row, column=column)
            if cell.value in results:
                results[cell.value] = sheet.cell(
                    row=row, column=column + 1
                ).value.split()[0]
    return results
