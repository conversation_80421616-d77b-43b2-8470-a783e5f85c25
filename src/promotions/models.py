import base64
import io
import logging

from datetime import datetime
from typing import Optional

from django import forms
from django.contrib.contenttypes.fields import GenericForeignKey
from django.contrib.contenttypes.models import ContentType
from django.core.files.uploadedfile import InMemoryUploadedFile
from django.db import models
from django.db.models import (
    Q,
    QuerySet,
)
from django.template import (
    Context,
    Template,
)
from django.utils import timezone

from PIL import Image

from custom.enums import LanguageEnum
from custom.models.validators import DateRangeValidator
from custom.utils.in_memory_cache import expiring_lru_cache
from promotions.choices import (
    ChevronThemeEnum,
    ConfiguratorAlertChoices,
    SaleTypeChoices,
)
from regions.cached_region import CachedRegionData
from regions.constants import DEFAULT_CURRENCY
from regions.models import Region
from regions.types import RegionLikeObject
from vouchers.models import (
    Voucher,
    VoucherGroup,
)

logger = logging.getLogger('cstm')

PROMOTION_IMAGE_SIZE_LIMIT = 1024 * 100


def validate_size(image):
    if image.file.size > PROMOTION_IMAGE_SIZE_LIMIT:
        raise forms.ValidationError(
            f'Image too big - max. {PROMOTION_IMAGE_SIZE_LIMIT / 1024} kb.',
        )


class Promotion(models.Model):
    active = models.BooleanField(default=False)
    promo_group = models.ManyToManyField(VoucherGroup, related_name='promotions')
    promo_code = models.ForeignKey(
        Voucher,
        null=True,
        blank=True,
        on_delete=models.CASCADE,
    )
    start_date = models.DateTimeField()
    end_date = models.DateTimeField(
        null=True,
        blank=True,
        help_text=(
            'Determines when the promotion ends. '
            'Note: Countdown is calculated based on this date.'
        ),
    )
    created_at = models.DateTimeField(auto_now_add=True)
    strikethrough_pricing = models.BooleanField(default=False)

    class Meta:
        verbose_name = 'Promotion'
        verbose_name_plural = 'Promotions'

    def __str__(self):
        promo_code = getattr(self.promo_code, 'code', '')
        return f'Promotion: {promo_code}'

    @staticmethod
    @expiring_lru_cache(ttl=60 * 60)
    def get_global_promotions_in_range(
        region: RegionLikeObject,
        start_date: datetime,
        end_date: datetime,
    ) -> QuerySet['Promotion']:
        return (
            Promotion.objects.filter(
                Q(promo_code__amount_starts__lte=1)  # only promos without entry point
                & Q(configs__enabled_regions__id=region.id)
                & Q(start_date__lte=end_date, end_date__gte=start_date)
                & Q(strikethrough_pricing=True)
            )
            .prefetch_related(
                'promo_code',
                'promo_code__discounts',
                'promo_code__region_entries',
            )
            .distinct()
        )

    def clean(self):
        super().clean()
        DateRangeValidator()(self)

    def save(self, *args, **kwargs):  # noqa: DJ012
        # circular import
        from pricing_v3.tasks import populate_pricing_history_registry
        from promotions.utils import clear_promo_cache

        self.full_clean()
        super().save(*args, **kwargs)

        populate_pricing_history_registry.delay()
        clear_promo_cache()


class PromotionPicture(models.Model):
    title = models.CharField(max_length=512, blank=False, null=False, unique=True)
    image = models.ImageField(
        'Original image, check proper resolution',
        upload_to='promotions/promotion_picture/%Y/%m',
        validators=[validate_size],
    )
    image_webp = models.ImageField(
        'Webp version, generate by action',
        upload_to='promotions/promotion_picture/%Y/%m',
        blank=True,
        null=True,
        validators=[validate_size],
    )
    image_base64_thumbnail = models.JSONField(default=dict, null=True, blank=True)

    content_type = models.ForeignKey(
        ContentType,
        limit_choices_to=(
            models.Q(app_label='gallery', model='jetty')
            | models.Q(app_label='gallery', model='watty')
        ),
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name='Furniture type',
    )
    object_id = models.PositiveIntegerField(
        null=True,
        blank=True,
        verbose_name='Furniture ID',
    )
    furniture = GenericForeignKey()

    class Meta:
        verbose_name = 'Promotion picture'

    def __str__(self):
        return self.title

    def create_webp_version(self):
        if self.image:
            im = Image.open(self.image)
            im_webp = io.BytesIO()

            im.save(im_webp, format='WEBP')
            thumbnail_base64_webp = io.BytesIO()
            final_width = 48
            final_height = int(
                (float(im.size[1]) * float((final_width / float(im.size[0]))))
            )
            im.thumbnail((final_width, final_height), Image.ANTIALIAS)
            im.save(thumbnail_base64_webp, format='WEBP')
            im_webp.seek(0)
            thumbnail_base64_webp.seek(0)

            self.image_webp = InMemoryUploadedFile(
                im_webp,
                None,
                '{}.webp'.format(self.title),
                'image/webp',
                im_webp.getbuffer().nbytes,
                None,
            )
            self.image_base64_thumbnail = '{}{}'.format(
                'data:image/webp;base64,',
                base64.b64encode(thumbnail_base64_webp.getvalue()).decode('utf-8'),
            )
            self.save()

    @property
    def related_furniture_url(self) -> Optional[str]:
        is_preset = self.furniture and self.furniture.preset
        return self.furniture.get_absolute_url() if is_preset else None


class PromotionConfig(models.Model):
    promotion = models.ForeignKey(
        Promotion,
        related_name='configs',
        on_delete=models.CASCADE,
    )
    created_at = models.DateTimeField(auto_now_add=True)
    enabled_regions = models.ManyToManyField(Region)
    sale_type = models.IntegerField(
        choices=SaleTypeChoices.choices,
        default=SaleTypeChoices.SALE,
        help_text='Choose a sale type to have appropriate sale labels on PLP.',
    )

    # Pictures
    mobile_picture = models.ForeignKey(
        PromotionPicture,
        blank=True,
        null=True,
        related_name='mobile_picture',
        on_delete=models.CASCADE,
    )
    desktop_picture = models.ForeignKey(
        PromotionPicture,
        blank=True,
        null=True,
        related_name='desktop_picture',
        on_delete=models.CASCADE,
    )
    grid_picture = models.ForeignKey(
        PromotionPicture,
        blank=True,
        null=True,
        related_name='grid_picture',
        on_delete=models.CASCADE,
    )

    # Themes
    theme = models.CharField(max_length=32, blank=True)
    theme_light = models.BooleanField(default=False)

    # Homepage Promo
    hp_promo = models.BooleanField(
        default=False,
        help_text='select this if you want to have promo section enabled on hp',
    )
    hp_promo_video = models.BooleanField(
        default=False,
        help_text='select this if you want to have video/vistia version of video on hp',
    )

    # Ribbon
    ribbon_enabled = models.BooleanField(default=False)
    ribbon_text_color = models.CharField(
        max_length=7,
        blank=True,
        help_text='Expected HEX color format e.g. #FFFFFF.',
    )
    ribbon_background_color = models.CharField(
        max_length=7,
        blank=True,
        help_text='Expected HEX color format e.g. #FFFFFF.',
    )

    # Cart Ribbon
    cart_ribbon_enabled = models.BooleanField(default=False)

    # Configurator
    configurator_alert_enabled = models.BooleanField(default=False)
    configurator_new_alert_enabled = models.IntegerField(
        default=ConfiguratorAlertChoices.DISABLED,
        choices=ConfiguratorAlertChoices.choices,
    )

    # Grid/catalogue
    grid_show_promo_value = models.BooleanField(default=False)
    grid_show_category_promotion = models.BooleanField(default=False)

    # Additional fields
    extra_data = models.JSONField(
        default=dict,
        blank=True,
        null=True,
        help_text='Additional data for frontend usage to avoid Sanity updates.',
    )

    class Meta:
        verbose_name = 'Promotion config'
        verbose_name_plural = 'Promotion configs'

    def __str__(self):
        promo_code = getattr(self.promotion.promo_code, 'code', '')
        return f'PromotionConfig: <{promo_code}>'

    def get_copy(
        self,
        language: LanguageEnum,
        region: CachedRegionData,
    ) -> dict[str, str]:
        promotion_config_copy = self.copies.filter(language=language).last()
        if not promotion_config_copy:
            return {}

        region_voucher = (
            self.promotion.promo_code.region_entries.filter(region=region).last()
            or self.promotion.promo_code
        )
        context = Context(
            {
                'amount_starts': int(region_voucher.amount_starts),
                'amount_limit': int(region_voucher.amount_limit),
                'value': int(region_voucher.value),
                'currency_symbol': (
                    region.currency_symbol if region else DEFAULT_CURRENCY['symbol']
                ),
                'currency_code': (
                    region.currency_code if region else DEFAULT_CURRENCY['code']
                ),
                'promotion_end_date': self.promotion.end_date.strftime('%d.%m'),
                'promotion_code': self.promotion.promo_code.code,
                'promotion_value': (
                    self.promotion.promo_code.get_regionalized_value_display(None)
                ),
            }
        )
        return promotion_config_copy.get_copy_data(context=context)


class PromotionConfigCopy(models.Model):
    config = models.ForeignKey(
        PromotionConfig,
        on_delete=models.CASCADE,
        related_name='copies',
    )

    language = models.CharField(
        max_length=2,
        choices=LanguageEnum.choices,
        default=LanguageEnum.EN,
    )
    cart_ribbon_copy_header_1 = models.TextField(null=True, blank=True)  # noqa: DJ001
    cart_ribbon_copy_header_mobile_1 = models.TextField(null=True, blank=True)  # noqa: DJ001

    configurator_copy_promo_alert_1 = models.TextField(null=True, blank=True)  # noqa: DJ001
    configurator_copy_promo_alert_mobile_1 = models.TextField(null=True, blank=True)  # noqa: DJ001

    grid_copy_slot_1_header_1 = models.TextField(null=True, blank=True)  # noqa: DJ001

    class Meta:
        verbose_name = 'Promotion Config Copy'
        verbose_name_plural = 'Promotion configs copy'

    def __str__(self):
        promo_code = getattr(self.config.promotion.promo_code, 'code', '')
        return f'PromotionConfigCopy: {promo_code} - {self.language}'

    def get_copy_data(self, context: Context) -> dict[str, str]:
        copy = {
            'cart_ribbon_copy_header_1': self.cart_ribbon_copy_header_1,
            'cart_ribbon_copy_header_mobile_1': self.cart_ribbon_copy_header_mobile_1,
            'configurator_copy_promo_alert_1': self.configurator_copy_promo_alert_1,
            'configurator_copy_promo_alert_mobile_1': (
                self.configurator_copy_promo_alert_mobile_1
            ),
            'grid_copy_slot_1_header_1': self.grid_copy_slot_1_header_1,
        }

        for key, value in copy.items():
            copy[key] = Template(value).render(context)

        copy['ribbon_copy_lines'] = self.get_ribbon_lines(context)
        return copy

    def get_ribbon_lines(self, context: Context) -> list[dict[str, str]]:
        return [
            {
                'copy_header': Template(line.copy_header).render(context),
                'copy_header_mobile': Template(
                    line.copy_header_mobile or line.copy_header
                ).render(context),
                'copy_link': line.copy_link,
                'show_timer': line.show_timer,
            }
            for line in self.ribbon_lines.all()
        ]


class RibbonLine(models.Model):
    config_copy = models.ForeignKey(
        PromotionConfigCopy,
        on_delete=models.CASCADE,
        related_name='ribbon_lines',
    )
    copy_header = models.TextField()
    copy_header_mobile = models.TextField(null=True, blank=True)  # noqa: DJ001
    copy_link = models.TextField(null=True, blank=True)  # noqa: DJ001
    show_timer = models.BooleanField(default=False)

    class Meta:
        verbose_name_plural = 'Ribbon lines'

    def __str__(self):
        promo_code = getattr(self.config_copy.config.promotion.promo_code, 'code', '')
        return f'RibbonLine: {promo_code} - {self.config_copy.language}'


class CountdownConfig(models.Model):
    promotion_config = models.OneToOneField(
        PromotionConfig,
        on_delete=models.CASCADE,
        related_name='countdown',
    )
    enabled = models.BooleanField(default=True)
    show_on_pdp = models.BooleanField(default=True, help_text='Show countdown on PDP')
    start_date = models.DateTimeField(help_text='Start date of the countdown')
    end_date = models.DateTimeField(null=True, blank=True)
    chevron_theme = models.IntegerField(
        choices=ChevronThemeEnum.choices,
        default=ChevronThemeEnum.LIGHT,
    )
    text_color = models.CharField(max_length=32)

    def __str__(self):
        promo_code = getattr(self.promotion_config.promotion.promo_code, 'code', '')
        return f'CountdownConfig: <{promo_code}> - start_date: {self.start_date}'

    def is_active(self) -> bool:
        end_date = self.end_date or self.promotion_config.promotion.end_date
        return self.enabled and self.start_date <= timezone.now() <= end_date
