import difflib
import logging
import pathlib
import random
import re

from collections import (
    OrderedDict,
    defaultdict,
)
from copy import copy
from dataclasses import dataclass
from datetime import datetime
from typing import (
    List,
    Set,
    Union,
)

from django.contrib.contenttypes.fields import GenericForeignKey
from django.contrib.contenttypes.models import ContentType
from django.core.cache import cache
from django.db import models

from custom.enums import Furniture
from gallery.models import (
    Jetty,
    Watty,
)
from rating_tool.enums import CategoryNumbers
from regions.models import Region

logger = logging.getLogger('cstm')

CAT_ALL = 'cat_all'  # Name for no category


@dataclass
class BoardItem:
    id: int
    material: str
    model_name: str
    additional_dict: dict

    @property
    def is_usp(self):
        return self.material == -1


class BoardCategory(models.Model):
    name = models.CharField(max_length=40)
    date_added = models.DateTimeField(auto_now_add=True)
    description = models.CharField(max_length=1000)
    for_category_grid = models.BooleanField(default=False)
    for_board_grid = models.BooleanField(default=False)
    for_mini_grid = models.BooleanField(default=False)
    enabled_for_webview = models.BooleanField(default=True)

    enabled_for_test = models.CharField(max_length=200, null=True, blank=True)  # noqa: DJ001
    enabled_regions = models.ManyToManyField(Region)  # empty == all
    boards_data = models.JSONField(null=True, blank=True)
    parent = models.ForeignKey('self', null=True, blank=True, on_delete=models.CASCADE)

    def get_board_raw_data(self):
        """Used by BI Team.

        Returns:
            list: a list of dictionaries representing items from self.boards_data
            dictionary, updated with data from database.
            Usps are omitted.
            Missing data has shelf_type = -1 and no grid_all_colors key.
            If self.boards_data is missing, empty list is returned.
        """
        if not self.boards_data:
            logger.warning('Missing board data for category %i', self.id)
            return []

        raw_data = []
        furniture_ids = defaultdict(set)
        for board_name, board in self.boards_data.items():
            for index, item in enumerate(board):
                item = BoardItem(*item)
                # We omit usps
                if int(item.material) == -1:
                    continue
                item_data = {
                    'id': item.id,
                    'model_name': item.model_name,
                    'material': item.material,
                    'board': board_name,
                    'category_id': self.id,
                    'position': index,
                    'category': item.additional_dict.get('category', ''),
                    'shelf_type': -1,
                }
                furniture_ids[item.model_name].add(item.id)
                raw_data.append(item_data)

        return self._update_with_data_from_objects(raw_data, furniture_ids)

    def _update_with_data_from_objects(self, raw_data, furniture_ids):
        additional_data = self._get_data_from_objects(furniture_ids)
        updated_data = []
        for item in raw_data:
            model_name = item['model_name']
            item_id = item['id']

            try:
                data = additional_data[model_name][item_id]
            except KeyError:
                logger.warning('%s: %i missing in db', model_name, item_id)
                updated_data.append(item)
            else:
                updated_data.append({**item, **data})

        return updated_data

    @staticmethod
    def _get_data_from_objects(furniture_ids):
        additional_data = {}
        for model_name, ids in furniture_ids.items():
            model = Furniture(model_name).model
            queryset = model.objects.filter(id__in=ids).values(
                'id',
                'shelf_type',
                'grid_all_colors',
            )
            additional_data[model_name] = {item['id']: item for item in queryset}

        return additional_data

    @staticmethod
    def get_default_for_mini_grid(request):
        from rating_tool import serializers

        user_region = (
            request.cached_region
            if request and hasattr(request, 'cached_region')
            else None
        )
        if not user_region and (
            request
            and request.user
            and request.user.is_authenticated
            and request.user.profile
        ):
            user_region = request.user.profile.region
        elif not user_region:
            user_region = Region.get_other()

        active_categories = list(
            BoardCategory.objects.filter(
                for_mini_grid=True,  # here we take the first category
                # which has minigrid - differentiation after regions
            )
            .prefetch_related('enabled_regions')
            .values(
                'id',
                'for_board_grid',
                'name',
                'enabled_for_test',
                'enabled_regions',
            )
        )
        last_category = None
        if active_categories:
            active_region_categories = copy(active_categories)
            categories_with_regions = OrderedDict()
            for category in active_region_categories:
                if category['id'] not in categories_with_regions:
                    categories_with_regions[category['id']] = category
                    categories_with_regions[category['id']]['regions'] = [
                        category['enabled_regions']
                    ]
                else:
                    categories_with_regions[category['id']]['regions'].append(
                        category['enabled_regions']
                    )

            if categories_with_regions:
                categories_with_regions = sorted(
                    categories_with_regions.items(),
                    key=lambda item: len(item[1]['regions']),
                )
                for c_id, category in categories_with_regions:
                    if user_region.pk in category['regions']:
                        last_category = c_id
                        break
                if not last_category:
                    logger.exception(
                        'BoardCategory bad configuration using regional fallback'
                    )
                    last_category = c_id

        if active_categories and not last_category:
            logger.exception('BoardCategory bad configuration using fallback')
            last_category = active_categories[-1]['id']

        key = '_get_cache_able_default_for_boards_{}'.format(last_category)
        serialized_category = cache.get(key)
        if serialized_category:
            serialized_category = serializers.BoardCategoryBoardSerializer(
                data=serialized_category
            )

        if serialized_category and serialized_category.is_valid():
            category = BoardCategory(**serialized_category.validated_data)
        else:
            category = (
                BoardCategory.objects.filter(id=last_category)
                .defer('boards_data')
                .last()
            )
            serialized_category = dict(
                serializers.BoardCategoryBoardSerializer(category).data
            )
            cache.set(key, serialized_category, 60 * 60)
        return category

    @staticmethod
    def get_default_for_boards(request):
        request_is_not_none = request is not None
        cookies = dict(getattr(request, 'COOKIES', {}))
        cookies = {
            key: (val == 'ok') for key, val in cookies.items() if val in ['ok', 'nok']
        }
        session = dict(getattr(request, 'session', {}))
        session = {key: val for key, val in session.items() if val in [True, False]}
        cookies_session = {**cookies, **session}
        cookies_session = sorted(cookies_session.items(), key=lambda item: item[0])
        tests = []
        if request:
            tests = [test.codename for test in request.cached_actual_tests]
        cookies_session = [(key, val) for key, val in cookies_session if key in tests]
        request_cached_region = (
            request.cached_region
            if request_is_not_none and hasattr(request, 'cached_region')
            else None
        )

        if request_cached_region:
            user_region = request_cached_region
        else:
            if (
                request_is_not_none
                and request.user
                and request.user.is_authenticated
                and request.user.profile
            ):
                user_region = request.user.profile.region
            else:
                user_region = Region.get_other()

        return BoardCategory._get_cache_able_default_for_boards(
            cookies_session=cookies_session,
            user_region=user_region,
            is_for_webview=getattr(request, 'webview_app', False),
        )

    @classmethod
    def _get_cache_able_default_for_boards(
        cls, cookies_session, user_region, is_for_webview=False
    ):
        from rating_tool import serializers

        active_categories = list(
            BoardCategory.objects.filter(for_board_grid=True)
            .prefetch_related('enabled_regions')
            .values(
                'id',
                'for_board_grid',
                'name',
                'enabled_for_test',
                'enabled_regions',
                'enabled_for_webview',
                'parent_id',
            )
        )
        categories_enable_for_test = filter(
            lambda x: x['enabled_for_test'], active_categories
        )
        cookies_session = dict(cookies_session)
        test_categories = [
            category
            for category in categories_enable_for_test
            if cookies_session.get(category['enabled_for_test'], None)
        ]

        if test_categories:
            active_categories = test_categories

        # ok, more than one to choose, lets first try to take ones with regions
        last_category = None
        if active_categories:
            active_region_categories = copy(active_categories)
            # No matching categories
            if not test_categories:
                active_region_categories = filter(
                    lambda x: not x['enabled_for_test'], active_categories
                )

            categories_with_regions = OrderedDict()
            for category in active_region_categories:
                if category['id'] not in categories_with_regions:
                    categories_with_regions[category['id']] = category
                    categories_with_regions[category['id']]['regions'] = [
                        category['enabled_regions']
                    ]
                else:
                    categories_with_regions[category['id']]['regions'].append(
                        category['enabled_regions']
                    )

            if categories_with_regions:
                categories_with_regions = sorted(
                    categories_with_regions.items(),
                    key=lambda item: len(item[1]['regions']),
                )
                # find best matching
                for c_id, category in categories_with_regions:  # noqa: B007
                    if user_region.pk in category['regions']:
                        last_category = category
                        break
                if not last_category:
                    logger.exception(
                        'BoardCategory bad configuration using regional fallback'
                    )
                    last_category = category

        if not cookies_session:
            # fallback for pdp
            last_category = active_categories[-1]
        if active_categories and not last_category:
            logger.exception('BoardCategory bad configuration using fallback')
            last_category = active_categories[-1]

        if is_for_webview:
            last_category = (
                cls._find_parent_enabled_for_webview(last_category) or last_category
            )

        key = '_get_cache_able_default_for_boards_{}'.format(last_category['id'])
        serialized_category = cache.get(key)
        if serialized_category:
            serialized_category = serializers.BoardCategoryBoardSerializer(
                data=serialized_category
            )

        if serialized_category and serialized_category.is_valid():
            category = BoardCategory(**serialized_category.validated_data)
        else:
            category = (
                BoardCategory.objects.filter(id=last_category['id'])
                .defer('boards_data')
                .last()
            )
            serialized_category = dict(
                serializers.BoardCategoryBoardSerializer(category).data
            )
            cache.set(key, serialized_category, 60 * 60)
        return category

    @classmethod
    def _find_parent_enabled_for_webview(cls, category_data: dict):
        while category_data and not category_data['enabled_for_webview']:
            category_data = (
                BoardCategory.objects.values(
                    'id',
                    'enabled_for_webview',
                    'parent_id',
                )
                .filter(id=category_data['parent_id'])
                .first()
            )
        return category_data

    @staticmethod
    def create_new_category(cat_base='new', category_name='', parent=None):
        new_category_name = category_name or ''
        while (
            not new_category_name
            or new_category_name in BoardCategory.objects.values_list('name', flat=True)
        ):
            new_category_name = '{}_{}'.format(
                category_name or cat_base,
                random.randint(10000000, 100000001),  # noqa: S311
            )
        category_obj = BoardCategory()
        category_obj.name = new_category_name
        if parent:
            category_obj.parent = parent
        category_obj.save()

        return category_obj

    @staticmethod
    def create_or_update_nested_category(
        category_name_or_obj, parent_name_or_obj=None, boards_to_duplicate=None
    ):
        new_flag = False
        # -- Obiekt Parent
        if isinstance(parent_name_or_obj, BoardCategory):
            parent = parent_name_or_obj
        else:
            parent = BoardCategory.objects.filter(name=parent_name_or_obj).first()
        # -- Obiekt kategorii
        if isinstance(category_name_or_obj, BoardCategory):
            category = category_name_or_obj
        else:
            category = BoardCategory.objects.filter(name=category_name_or_obj).first()
            if not category:
                new_flag = True
                category = BoardCategory.create_new_category(
                    category_name=category_name_or_obj, parent=parent
                )
        # -- Obiekty boards
        boards_to_duplicate = boards_to_duplicate or []
        for board in boards_to_duplicate:
            # -- Usun istniejące tablice dla takiego filtru
            category.boards.filter(filter_descr=board.filter_descr).exclude(
                id=board.id
            ).delete()
            # -- Powiel board
            board.id = None
            board.category = category
            board.save()

        return category, new_flag

    def get_mini_grid_board_data(self, filter_descr):
        board_obj = self.get_board_from_obj_or_parents(
            self,
            self.parent,
            filter_descr,
        )
        return board_obj or None

    def get_board_data_for_querydict(self, **kwargs):
        if kwargs.get('override_filter_name', None) is not None:
            filter_descr = kwargs.get('override_filter_name', None)
        else:
            filter_descr = Board.get_filter_descr_from_filter_params(**kwargs)
        if self.boards_data and filter_descr in list(self.boards_data.keys()):
            return self.boards_data[filter_descr]
        board_obj = self.get_board_from_obj_or_parents(self, self.parent, filter_descr)
        if board_obj:
            return board_obj.order or list()  # noqa: C408

        if self.boards_data:
            nearest_boards = difflib.get_close_matches(
                filter_descr, list(self.boards_data.keys()), n=1
            )
            if len(nearest_boards) > 0:
                return self.boards_data[nearest_boards[0]]

        raise AttributeError('Brakujące dane boards w obiekcie kategorii!')

    @staticmethod
    def get_board_from_obj_or_parents(cat, parent_cat, board_filter_descr):
        board = cat.boards.filter(filter_descr=board_filter_descr).first()
        if not board and parent_cat:
            board = parent_cat.get_board_from_obj_or_parents(
                parent_cat, parent_cat.parent, board_filter_descr
            )
        return board

    def get_boards_from_current_and_parent_categories(self):
        current_parent = self
        boards = []
        parents_list = []
        parent_distance = 0  # how far we are from parent

        while current_parent:
            boards += list(
                current_parent.boards.exclude(order=[])
                .annotate(
                    parent_distance=models.Value(parent_distance, models.IntegerField())
                )
                .all()
            )
            parents_list.append(current_parent)
            # check if there is no inheritance loop
            if current_parent.parent and current_parent.parent not in parents_list:
                current_parent = current_parent.parent
                parent_distance += 1
            else:
                break

        return boards

    def generate_boards_data_dict(self):
        boards = self.get_boards_from_current_and_parent_categories()
        data = {}
        boards = sorted(boards, key=lambda k: k.parent_distance, reverse=True)
        for board in boards:
            data[board.filter_descr] = board.order or []
        self.boards_data = data
        self.save(update_fields=['boards_data'])

    def _get_board_items(self) -> List[BoardItem]:
        items = []
        for board in self.boards_data.values():
            for item in board:
                board_item = BoardItem(*item)
                if board_item.material != -1:
                    items.append(board_item)
        return items

    def get_furniture_ids(self, furniture_name: str) -> Set[int]:
        return {
            board_item.id
            for board_item in self._get_board_items()
            if board_item.model_name == furniture_name
        }

    def lookup_board_in_parent_tree(self, board_name):
        """
        Find a board with a given name in BoardCategory inheritance tree.
        Later this board will be copied into new BoardCategory.
        """
        board = None
        parent = self
        parents_list = []
        while not board and parent:
            board = (
                parent.boards.filter(
                    filter_descr=board_name,
                )
                .distinct()
                .first()
            )
            parents_list.append(parent)
            # check if there is no inheritance loop
            if parent.parent and parent.parent not in parents_list:
                parent = parent.parent
        return board

    def save(self, *args, **kwargs):  # noqa: DJ012
        now = datetime.now().strftime('%Y-%m-%d %H:%M')
        if self.pk is not None:
            previous = BoardCategory.objects.get(pk=self.pk)
            if previous and self.for_category_grid != previous.for_category_grid:
                self.description += '| {}: {} -> {} |'.format(
                    now, 'for_category_grid', self.for_category_grid
                )
            if previous and self.for_board_grid != previous.for_board_grid:
                self.description += '| {}: {} -> {} |'.format(
                    now, 'for_board_grid', self.for_board_grid
                )
            if previous and self.enabled_for_test != previous.enabled_for_test:
                self.description += '| {}: {} -> {} |'.format(
                    now, 'enabled_for_test', self.enabled_for_test
                )
        super(BoardCategory, self).save(*args, **kwargs)

    def __str__(self):  # noqa: DJ012
        return f'id: {self.id}, {self.name}'

    class Meta:  # noqa: DJ012
        verbose_name_plural = 'Board Categories'
        ordering = ('id',)


class Board(models.Model):
    filter_descr = models.CharField(max_length=100, null=True, blank=True)  # noqa: DJ001
    category = models.ForeignKey(
        BoardCategory,
        null=True,
        blank=True,
        related_name='boards',
        on_delete=models.CASCADE,
    )
    order = models.JSONField(null=True, blank=True)
    date_added = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return 'Board {}, cat {}'.format(self.id, self.category.name)

    @property
    def type(self):
        valid_types = ['type_all', 'type_2', 'type_mix']
        return next((x for x in valid_types if x in self.filter_descr), 'type_1')

    @staticmethod
    def get_filter_descr_from_filter_params(**kwargs):
        category = (
            kwargs.get('shelfCategories')
            if kwargs.get('shelfCategories')
            and kwargs.get('shelfCategories') != ['undefined']
            else [CAT_ALL]
        )
        proper_features = []
        for f in kwargs.get('features', []):
            if f == 'none':
                proper_features.append('f_open')
            elif f == 'bothdoorsandrawers':
                proper_features.append('f_doors')
                proper_features.append('f_drawers')
            else:
                proper_features.append('f_' + str(f))
        shelf_types = ['type_all'] if category[0] != 'cat_7' else []
        for f in kwargs.get('shelfTypes', []):
            if f == '3':
                shelf_types = ['type_4']
            elif f == '2':
                shelf_types = ['type_3']
            elif f == '1':
                shelf_types = ['type_2']
            elif f == '0':
                shelf_types = []
            elif f == 'mix':
                shelf_types = ['type_mix']
        row_heights = ['r_' + str(r) for r in kwargs.get('rowHeights', [])]
        filter_descr = '__'.join(
            sorted(category + proper_features + row_heights + shelf_types)
        )
        return filter_descr

    def get_furniture_ids(self, furniture_type: str) -> List[int]:
        furniture_ids = []
        for item in self.order:
            item = BoardItem(*item)
            if not item.is_usp and item.model_name == furniture_type:
                furniture_ids.append(item.id)
        return furniture_ids

    def get_shelf_category(self) -> str:
        """Retrieves the shelf category from the 'cat' number in filter_descr"""
        filter_descr = self.filter_descr
        cat_match = re.match(r'cat_\d+', filter_descr)
        if not cat_match:
            return ''
        try:
            shelf_category = CategoryNumbers[cat_match.group().upper()].value
        except KeyError:
            shelf_category = ''
        return shelf_category


class FurnitureInCategory(models.Model):
    category = models.ForeignKey(
        BoardCategory,
        null=True,
        blank=True,
        on_delete=models.CASCADE,
    )
    weight = models.FloatField()
    content_type = models.ForeignKey(
        ContentType,
        limit_choices_to=(
            models.Q(app_label='gallery', model='jetty')
            | models.Q(app_label='gallery', model='watty')
        ),
        on_delete=models.CASCADE,
    )
    furniture_id = models.PositiveIntegerField()
    furniture: Union[Jetty, Watty] = GenericForeignKey('content_type', 'furniture_id')

    @property
    def svg(self):
        current_dir = pathlib.Path(__file__).parent.absolute()

        with open(f'{current_dir}/media/jetty_rated.svg', 'r') as fh:
            return fh.read()

    @property
    def furniture_created_at(self):
        return self.furniture.created_at

    @classmethod
    def create_new(cls, **kwargs):
        obj = cls()
        for name, val in list(kwargs.items()):
            setattr(obj, name, val)
        obj.set_feature_fields()
        obj.save()
        return obj

    def set_feature_fields(self):
        self.weight = 0

    @property
    def doors(self) -> bool:
        return bool(self.furniture.doors)

    @property
    def drawers(self) -> bool:
        return bool(self.furniture.drawers)

    @property
    def color_for_feeds(self) -> int:
        """
        FeedCategoryCopy takes colors from product_feeds.choices.all_colors_choices.
        Here we transform shelf type and material to proper color unique for type.
        Ex:
        T1 - White is 0
        T1 - Black is 1
        T2 - White is 100
        T3 - White is 300
        T13 - White is 400
        """
        return (self.furniture.shelf_type * 100) + self.furniture.material

    def __repr__(self):  # noqa: DJ012
        return (
            f'<{self.__class__.__name__}: '
            f'{self.content_type.model} {self.furniture_id}>'
        )

    def __str__(self):
        return str(self.furniture_id)

    class Meta:  # noqa: DJ012
        verbose_name_plural = 'Furniture in Categories'
