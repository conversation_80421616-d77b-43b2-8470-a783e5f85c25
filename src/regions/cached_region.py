from decimal import Decimal
from typing import NamedTuple

from rest_framework.request import Request

import babel.numbers

from babel.core import Locale
from money.currency import Currency
from money.money import Money

from regions.utils import get_region_code_from_url


class CachedRegionData(NamedTuple):
    id: int
    name: str
    currency_id: int
    currency_name: str
    currency_code: str
    currency_symbol: str
    country_id: int
    country_name: str
    country_code: str
    country_locale: str

    def get_format_price(self, price: int | Decimal) -> str:
        currency_code = getattr(Currency, self.currency_code)
        money_object = Money(str(price), currency=currency_code)

        country_locale = self.country_locale or 'de_DE'

        currency_pattern = (
            Locale.parse(country_locale)
            .currency_formats['standard']
            .pattern.replace('\xa0', '')
        )

        if Decimal(price) % 1 == 0:
            pattern = currency_pattern.replace(',##0.00', '')
        else:
            pattern = currency_pattern

        return babel.numbers.format_currency(
            money_object.amount,
            money_object.currency.value,
            locale=country_locale,
            format=pattern,
            currency_digits=False,
        )


def get_region_data_from_request(request: Request) -> CachedRegionData:
    return (
        CachedRegionData(**request.session['cached_region'])
        if request.user.is_anonymous or not request.user.profile.region
        else request.user.profile.cached_region_data
    )


def get_region_from_referer(request: Request) -> CachedRegionData:
    if not request.user.is_anonymous:
        return request.user.profile.cached_region_data
    try:
        region_code = get_region_code_from_url(request.META.get('HTTP_REFERER', ''))
    except (IndexError, AttributeError):
        # wrong url format:
        return CachedRegionData(**request.session['cached_region'])
    from regions.models import Country

    # this should be optimised to avoid db hit
    region = Country.objects.get(code=region_code.upper()).region
    if region_code != request.session.get('cached_region', {}).get('country_code', ''):
        request.session['cached_region'] = region.get_data_as_dict()
    return region.cached_region_data
