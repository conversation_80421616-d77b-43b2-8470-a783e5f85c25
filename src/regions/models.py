import logging
import typing
import unicodedata

from django.conf import settings
from django.contrib.postgres.fields import DateRangeField
from django.core.cache import cache
from django.db import models
from django.utils import (
    timezone,
    translation,
)
from django.utils.functional import (
    cached_property,
    lazystr,
)
from django.utils.translation import gettext_lazy as _

from custom.enums import LanguageEnum
from custom.utils.decorators import (
    cache_function,
    cache_model_method,
)
from custom.utils.in_memory_cache import expiring_lru_cache
from custom.utils.mixins import ModelStrSummaryMixin
from regions.cached_region import CachedRegionData
from regions.constants import (
    DEFAULT_CURRENCY,
    OTHER_REGION_NAME,
    REGION_CACHE_PERIOD,
)
from regions.mixins import (
    RateCopyOnSaveMixin,
    RatedInTimeMixin,
)
from regions.utils import get_all_languages_as_choices

logger = logging.getLogger('cstm')


class CountryRegionVat(models.Model):
    country = models.ForeignKey('Country', on_delete=models.CASCADE)
    vat = models.DecimalField(max_digits=6, decimal_places=4)
    valid_date_range = DateRangeField()

    def __str__(self):
        return '{} {}'.format(self.country.name, self.valid_date_range)

    class Meta:  # noqa: DJ012
        unique_together = (
            (
                'country',
                'valid_date_range',
            ),
        )


class Country(ModelStrSummaryMixin, models.Model):
    class Meta(object):
        verbose_name_plural = 'Countries'

    name = models.CharField(db_index=True, max_length=64, unique=True)  # noqa: DJ012
    code = models.CharField(db_index=True, max_length=4)
    vat = models.DecimalField(max_digits=6, decimal_places=4)
    language_code = models.CharField(max_length=4)
    locale = models.CharField(max_length=8)
    # todo - why don't we want to make OneToOneField ?
    region = models.ForeignKey(
        'regions.Region',
        related_name='countries',
        on_delete=models.CASCADE,
    )
    location = models.CharField(max_length=16)
    region_vat = models.BooleanField(default=False)

    def __str__(self):
        return self.name

    def get_country_region_vat_for_sell_at_date(self, sell_at):
        country_region_vat = self.countryregionvat_set.filter(
            valid_date_range__contains=sell_at
        ).last()
        if country_region_vat:
            return country_region_vat.vat
        return self.vat

    @staticmethod
    def get_all_language_codes():
        language_codes = (
            Country.objects.order_by('language_code')
            .distinct('language_code')
            .values_list('language_code', flat=True)
        )
        return [(lc, lc.upper()) for lc in language_codes]

    @staticmethod
    def language_code_choices():
        choices = Country.get_all_language_codes()
        return [('', '--')] + choices  # noqa: RUF005

    @staticmethod
    @cache_function(cache_period=86400)
    def get_countries_with_translation_keys(
        language_code: str,
    ) -> list[tuple[str, str, str]]:
        """Returns a language-wise sorted list of countries."""
        countries = Country.objects.all().prefetch_related(
            'region',
            'region__currency',
        )
        countries_details = {
            country.get_translated_name(language_code): (
                country.name,
                country.get_translation_key(),
                country.region.get_currency().code,
            )
            for country in countries
        }
        sorted_countries = sorted(
            countries_details,
            key=(lambda details: unicodedata.normalize('NFD', details).lower()),
        )
        return [countries_details[c] for c in sorted_countries]

    @property
    def translations(self) -> dict[str, lazystr]:
        return {
            'AT': _('common_austria'),
            'BE': _('common_belgium'),
            'BG': _('common_bulgaria'),
            'HR': _('common_croatia'),
            'CZ': _('common_czech'),
            'DK': _('common_denmark'),
            'EE': _('common_estonia'),
            'FI': _('common_finland'),
            'FR': _('common_france'),
            'DE': _('common_germany'),
            'GR': _('common_greece'),
            'HU': _('common_hungary'),
            'IE': _('common_ireland'),
            'IT': _('common_italy'),
            'LV': _('common_latvia'),
            'LT': _('common_lithuania'),
            'LU': _('common_luxembourg'),
            'NL': _('common_netherlands'),
            'NO': _('common_norway'),
            'PL': _('common_poland'),
            'PT': _('common_portugal'),
            'RO': _('common_romania'),
            'SK': _('common_slovakia'),
            'SI': _('common_slovenia'),
            'ES': _('common_spain'),
            'SE': _('common_sweden'),
            'CH': _('common_switzerland'),
            'UK': _('common_united_kingdom'),
        }

    def get_translated_name(self, language_code: LanguageEnum) -> str:
        with translation.override(language_code):
            return str(self.translations.get(self.code.upper(), self.name))

    def get_translation_key(self) -> str:
        trans = self.translations.get(self.code.upper(), _(''))
        return trans._proxy____args[0]


class Currency(RatedInTimeMixin, ModelStrSummaryMixin, models.Model):
    name = models.CharField(db_index=True, max_length=32, unique=True)
    code = models.CharField(max_length=4, unique=True)
    symbol = models.CharField(max_length=4, unique=True)

    class Meta(object):
        verbose_name_plural = 'Currencies'

    def __str__(self):
        return self.name


class CurrencyRate(RateCopyOnSaveMixin, ModelStrSummaryMixin, models.Model):
    currency = models.ForeignKey(
        Currency,
        related_name='rates',
        on_delete=models.CASCADE,
    )
    rate = models.DecimalField(max_digits=8, decimal_places=2)
    time = models.DateTimeField(db_index=True, default=timezone.now)

    class Meta(object):
        ordering = ['currency', '-time']


class RegionManager(models.Manager):
    def get_queryset(self) -> models.QuerySet:
        return super().get_queryset().select_related('currency')

    @expiring_lru_cache(ttl=settings.REGIONS_CACHE_TTL_SECONDS)
    def get_regions_cached(self) -> models.QuerySet:
        return self.get_queryset().exclude(name=OTHER_REGION_NAME)


class Region(RatedInTimeMixin, ModelStrSummaryMixin, models.Model):
    name = models.CharField(db_index=True, max_length=32, unique=True)
    currency = models.ForeignKey(Currency, on_delete=models.PROTECT)
    default_for_language = models.CharField(  # noqa: DJ001
        choices=get_all_languages_as_choices(),
        default=None,
        max_length=4,
        blank=True,
        null=True,
        unique=False,
    )
    is_eu = models.BooleanField(default=False)
    objects = RegionManager()
    flag = models.CharField(max_length=64, null=True)  # noqa: DJ001, DJ012

    def __str__(self):
        return self.name

    def get_country(self, without_cache=False):
        from custom.middleware import get_current_request

        request = get_current_request()
        if (
            without_cache is False
            and getattr(request, 'cached_country', None) is not None
        ):
            return getattr(request, 'cached_country', None)
        else:
            return self.country

    @cached_property
    @cache_model_method(cache_period=REGION_CACHE_PERIOD)
    def country(self) -> Country | None:
        return self.countries.first()

    def get_currency(self):
        return self.currency

    @staticmethod
    def get_other() -> 'Region':
        region = cache.get('region_other')
        if region is None:
            region = Region.objects.get(name=OTHER_REGION_NAME)
            cache.set('region_other', region, REGION_CACHE_PERIOD)

            currency = cache.get('currency_default')
            if currency is None:
                currency = Currency.objects.get(code=DEFAULT_CURRENCY['code'])
                cache.set('currency_default', currency, REGION_CACHE_PERIOD)

        return region

    @staticmethod
    def get_region_for_language(language_code):
        region = Region.objects.filter(
            default_for_language__iexact=language_code
        ).first()
        if region is None:
            region = Region.get_other()
        return region

    @property
    def has_assembly_service_available(self):
        return self.name in settings.ASSEMBLY_REGION_KEYS

    def get_data_as_dict(self) -> dict[str, typing.Any]:
        country = self.get_country(without_cache=True)
        return {
            'id': self.id,
            'name': self.name,
            'currency_id': self.currency.id,
            'currency_name': self.currency.name,
            'currency_code': self.currency.code,
            'currency_symbol': self.currency.symbol,
            'country_id': country.id if country else '',
            'country_name': country.name if country else '',
            'country_code': country.code if country else '',
            'country_locale': country.locale if country else '',
        }

    @property
    def cached_region_data(self) -> CachedRegionData:
        return CachedRegionData(**self.get_data_as_dict())


class RegionRate(RateCopyOnSaveMixin, ModelStrSummaryMixin, models.Model):
    class Meta(object):
        ordering = ['region', '-time']

    rate = models.DecimalField(max_digits=8, decimal_places=2)  # noqa: DJ012
    region = models.ForeignKey(
        Region,
        related_name='rates',
        on_delete=models.CASCADE,
    )
    time = models.DateTimeField(db_index=True, default=timezone.now)

    @classmethod
    @expiring_lru_cache(ttl=settings.REGIONS_CACHE_TTL_SECONDS)
    def get_current_rate_for_region(cls, region_id: int) -> 'RegionRate':
        return cls.objects.filter(region_id=region_id).first()


class GeoRegion(models.Model):
    region_name = models.CharField(max_length=100, db_index=True)
    population = models.IntegerField()
    country = models.ForeignKey(Country, on_delete=models.CASCADE)
    exported_to_big_query = models.DateTimeField(null=True, blank=True, default=None)

    def __str__(self):
        return self.region_name

    class Meta(object):  # noqa: DJ012
        unique_together = (('region_name', 'country'),)


class GeoCity(models.Model):
    city_name = models.CharField(max_length=100, db_index=True)
    population = models.IntegerField()
    region = models.ForeignKey(GeoRegion, on_delete=models.CASCADE)
    exported_to_big_query = models.DateTimeField(null=True, blank=True, default=None)

    def __str__(self):
        return self.city_name

    class Meta(object):  # noqa: DJ012
        unique_together = (('city_name', 'region'),)
