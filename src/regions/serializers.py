from decimal import Decimal
from typing import Optional

from django.utils.translation import gettext as _
from rest_framework import serializers
from rest_framework.fields import ReadOnlyField

from abtests.models import ABTest
from mailing.constants import NEWSLETTER_VOUCHER_VALUES
from pricing_v3.models import SamplePriceSettings
from regions.cached_region import CachedRegionData
from regions.constants import REGION_LANGAUGE_MAP
from regions.models import (
    Currency,
    CurrencyRate,
    Region,
    RegionRate,
)
from regions.services.limitations import LimitationService


class ChangeRegionSerializer(serializers.Serializer):
    region_name = serializers.CharField()


class CurrencyRateSerializer(serializers.ModelSerializer):
    class Meta(object):
        fields = ['id', 'rate']
        model = CurrencyRate


class CurrencySerializer(serializers.ModelSerializer):
    rate = CurrencyRateSerializer(source='current_rate')

    class Meta(object):
        fields = ['id', 'name', 'code', 'symbol', 'rate']
        model = Currency


class RegionRateSerializer(serializers.ModelSerializer):
    class Meta(object):
        fields = ['id', 'rate']
        model = RegionRate


class RegionSerializer(serializers.ModelSerializer):
    currency_id = ReadOnlyField(source='currency.id')
    currency = CurrencySerializer(read_only=True)
    rate = RegionRateSerializer(source='current_rate')

    class Meta(object):
        model = Region
        fields = ['id', 'name', 'currency', 'currency_id', 'rate']


class RegionCheckoutSerializer(serializers.ModelSerializer):
    trans = serializers.SerializerMethodField()
    dec = serializers.CharField(source='flag')
    currency = serializers.CharField(source='currency.code')

    class Meta:
        fields = ['name', 'currency', 'dec', 'trans']
        model = Region

    def get_trans(self, instance):
        return _('common_' + instance.name)


class RegionGlobalSerializer(serializers.Serializer):
    region_code = serializers.CharField(source='country_code')
    region_name = serializers.CharField(source='name')
    currency_code = serializers.CharField()
    country_locale = serializers.CharField()
    newsletter_voucher_value = serializers.SerializerMethodField()

    # Product/Service limitations
    assembly_available = serializers.SerializerMethodField()
    corduroy_available = serializers.SerializerMethodField()
    doorstep_sotty_delivery = serializers.SerializerMethodField()
    old_sofa_collection_available = serializers.SerializerMethodField()
    s01_available = serializers.SerializerMethodField()
    t03_available = serializers.SerializerMethodField()
    white_gloves_delivery_available = serializers.SerializerMethodField()

    # Samples
    storage_sample_price = serializers.SerializerMethodField()
    storage_sample_promo_price = serializers.SerializerMethodField()
    is_storage_sample_promo_active = serializers.SerializerMethodField()
    sofa_sample_price = serializers.SerializerMethodField()
    sofa_sample_promo_price = serializers.SerializerMethodField()
    is_sofa_sample_promo_active = serializers.SerializerMethodField()

    available_languages = serializers.SerializerMethodField()
    feature_flags = serializers.SerializerMethodField()

    class Meta:
        fields = (
            'region_code',
            'region_name',
            'currency_code',
            'country_locale',
            't03_available',
            's01_available',
            'corduroy_available',
            'assembly_available',
            'old_sofa_collection_available',
            'white_gloves_delivery_available',
            'doorstep_sotty_delivery',
            'newsletter_voucher_value',
            'storage_sample_price',
            'storage_sample_promo_price',
            'is_storage_sample_promo_active',
            'sofa_sample_price',
            'sofa_sample_promo_price',
            'is_sofa_sample_promo_active',
            'available_languages',
            'feature_flags',
        )

    @property
    def rco(self):
        return self.context['rco']

    @property
    def limitation_service(self) -> LimitationService:
        return LimitationService(region=self.instance)

    @staticmethod
    def get_newsletter_voucher_value(obj: CachedRegionData) -> Optional[str]:
        voucher_data = NEWSLETTER_VOUCHER_VALUES.get(obj.currency_code)
        return obj.get_format_price(voucher_data.value)

    def get_assembly_available(self, _: CachedRegionData) -> bool:
        return self.limitation_service.is_assembly_available

    def get_corduroy_available(self, _: CachedRegionData) -> bool:
        return self.limitation_service.is_corduroy_available

    def get_s01_available(self, _: CachedRegionData) -> bool:
        return self.limitation_service.is_s01_available

    def get_t03_available(self, _: CachedRegionData) -> bool:
        return self.limitation_service.is_t03_available

    def get_old_sofa_collection_available(self, _: CachedRegionData) -> bool:
        return self.limitation_service.is_old_sofa_collection_available

    def get_white_gloves_delivery_available(self, _: CachedRegionData) -> bool:
        return self.limitation_service.is_white_gloves_delivery_available

    def get_doorstep_sotty_delivery(self, _: CachedRegionData) -> bool:
        return self.limitation_service.is_doorstep_sotty_delivery

    def get_storage_sample_price(self, _: CachedRegionData) -> Decimal:
        return self.rco.calculate_regionalized(
            base_value=SamplePriceSettings.get_storage_sample_base_price()
        )

    def get_storage_sample_promo_price(self, _: CachedRegionData) -> Decimal:
        return self.rco.calculate_regionalized(
            base_value=SamplePriceSettings.get_storage_sample_sale_price()
        )

    @staticmethod
    def get_is_storage_sample_promo_active(_: CachedRegionData) -> bool:
        return SamplePriceSettings.is_storage_sample_promo_active()

    def get_sofa_sample_price(self, _: CachedRegionData) -> Decimal:
        return self.rco.calculate_regionalized(
            base_value=SamplePriceSettings.get_sofa_sample_base_price()
        )

    def get_sofa_sample_promo_price(self, _: CachedRegionData) -> Decimal:
        return self.rco.calculate_regionalized(
            base_value=SamplePriceSettings.get_sofa_sample_sale_price()
        )

    @staticmethod
    def get_is_sofa_sample_promo_active(_: CachedRegionData) -> bool:
        return SamplePriceSettings.is_sofa_sample_promo_active()

    def get_available_languages(self, obj: CachedRegionData) -> tuple[str]:
        return REGION_LANGAUGE_MAP[obj.name]

    def get_feature_flags(self, obj: CachedRegionData) -> list[str]:
        active_ab_tests = ABTest.objects.get_active_tests_cached_list(region=obj)
        return [
            active_ab_test.codename
            for active_ab_test in active_ab_tests
            if active_ab_test.feature_flag
        ]
