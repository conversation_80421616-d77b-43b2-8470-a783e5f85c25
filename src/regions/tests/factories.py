import datetime
import random

from django.utils import timezone

import factory
import freezegun

from factory import fuzzy
from money.currency import Currency as ThirdPartyCurrency

from regions.constants import DEFAULT_CURRENCY
from regions.models import CurrencyRate


class CurrencyFactory(factory.django.DjangoModelFactory):
    symbol = factory.SelfAttribute('.code')

    class Meta:
        model = 'regions.Currency'
        django_get_or_create = ('code',)

    class Params:
        euro = factory.Trait(name='Euro', code='EUR', symbol='€')
        pound = factory.Trait(name='Pound Sterling', code='GBP', symbol='£')
        zloty = factory.Trait(name='Zloty', code='PLN', symbol='zł')

    @factory.lazy_attribute
    def code(self):
        return random.choice(list(ThirdPartyCurrency)).value  # noqa: S311

    @factory.lazy_attribute
    def name(self):
        if self.code in DEFAULT_CURRENCY['code']:
            return DEFAULT_CURRENCY['name']

        return f'Currency {self.code}'

    @factory.post_generation
    def rates(self, create, extracted, **kwargs):
        if not create:
            return

        if extracted:
            for rate in extracted:
                self.rates.add(rate)
        else:  # if no rates provided, add one random
            with freezegun.freeze_time(timezone.now() - datetime.timedelta(days=2000)):
                self.rates.add(CurrencyRate.objects.create(currency=self, rate=2.34))


class CurrencyRateFactory(factory.django.DjangoModelFactory):
    currency = factory.SubFactory('regions.tests.factories.CurrencyFactory')
    rate = fuzzy.FuzzyDecimal(low=0.01, high=5.59)
    time = fuzzy.FuzzyDateTime(
        start_dt=datetime.datetime(
            2020,
            7,
            30,
            10,
            48,
            tzinfo=datetime.UTC,
        ),
    )

    class Meta:
        model = 'regions.CurrencyRate'


class RegionFactory(factory.django.DjangoModelFactory):
    name = factory.Sequence(lambda counter: f'Region {counter}')
    currency = factory.SubFactory('regions.tests.factories.CurrencyFactory')
    default_for_language = None
    is_eu = True
    flag = None

    class Meta:
        model = 'regions.Region'
        django_get_or_create = ('name',)

    class Params:
        germany = factory.Trait(
            name='germany',
            default_for_language='DE',
            currency=factory.SubFactory(CurrencyFactory, euro=True),
            is_eu=True,
        )
        united_kingdom = factory.Trait(
            name='united_kingdom',
            default_for_language='EN',
            currency=factory.SubFactory(CurrencyFactory, pound=True),
            is_eu=False,
        )
        france = factory.Trait(
            name='france',
            default_for_language='FR',
            currency=factory.SubFactory(CurrencyFactory, euro=True),
            is_eu=True,
        )
        spain = factory.Trait(
            name='spain',
            default_for_language='ES',
            currency=factory.SubFactory(CurrencyFactory, euro=True),
            is_eu=True,
        )
        netherlands = factory.Trait(
            name='netherlands',
            default_for_language='NL',
            currency=factory.SubFactory(CurrencyFactory, euro=True),
            is_eu=True,
        )
        poland = factory.Trait(
            name='poland',
            default_for_language='PL',
            currency=factory.SubFactory(CurrencyFactory, zloty=True),
            is_eu=True,
        )
        other = factory.Trait(
            name='_other',
            default_for_language=None,
            currency=factory.SubFactory(CurrencyFactory, euro=True),
            is_eu=False,
        )


class CountryFactory(factory.django.DjangoModelFactory):
    name = factory.Sequence(lambda n: f'Country {n}')
    region = factory.SubFactory('regions.tests.factories.RegionFactory')
    vat = fuzzy.FuzzyDecimal(low=0.15, high=0.23)

    class Meta:
        model = 'regions.Country'
        django_get_or_create = ('name',)

    class Params:
        germany = factory.Trait(
            name='Germany',
            code='DE',
            locale='de_DE',
            region=factory.SubFactory(RegionFactory, germany=True),
        )
        united_kingdom = factory.Trait(
            name='united_kingdom',
            code='GB',
            locale='en_GB',
            region=factory.SubFactory(RegionFactory, united_kingdom=True),
        )
        france = factory.Trait(
            name='france',
            code='FR',
            locale='fr_FR',
            region=factory.SubFactory(RegionFactory, france=True),
        )
        spain = factory.Trait(
            name='spain',
            code='ES',
            locale='es_ES',
            region=factory.SubFactory(RegionFactory, spain=True),
        )
        netherlands = factory.Trait(
            name='netherlands',
            code='NL',
            locale='nl_NL',
            region=factory.SubFactory(RegionFactory, netherlands=True),
        )


class RegionRateFactory(factory.django.DjangoModelFactory):
    region = factory.SubFactory('regions.tests.factories.RegionFactory')
    rate = fuzzy.FuzzyDecimal(low=0.01, high=5.59)
    time = fuzzy.FuzzyDateTime(
        start_dt=datetime.datetime(
            2020,
            7,
            30,
            10,
            48,
            tzinfo=datetime.UTC,
        ),
    )

    class Meta:
        model = 'regions.RegionRate'


class CountryRegionVatFactory(factory.django.DjangoModelFactory):
    country = factory.SubFactory('regions.tests.factories.CountryFactory')
    vat = factory.fuzzy.FuzzyDecimal(low=0.1, high=0.2)
    valid_date_range = factory.fuzzy.FuzzyDate(
        start_date=datetime.date(2021, 1, 1),
        end_date=datetime.date(2030, 1, 1),
    )

    class Meta:
        model = 'regions.CountryRegionVat'
