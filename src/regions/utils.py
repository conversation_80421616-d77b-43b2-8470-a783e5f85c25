from typing import (
    TYPE_CHECKING,
    Optional,
)

from django.conf import settings
from django.urls import reverse

from custom.models import Countries
from custom.utils.url import remove_url_params

if TYPE_CHECKING:
    from regions.models import Region


def get_all_languages_as_choices():
    return sorted(
        [
            (lc, lc.upper())
            for lc in {c.language_code for c in Countries.get_all().values()}
        ]
    )


def is_t03_available(user, region=None):
    """Return if T03 is available for the given region"""
    if user.is_authenticated:
        region = user.profile.get_region()
    elif region is None:
        return False
    return region.name in settings.T03_REGION_KEYS


def reverse_with_region(url_pattern: str, region_code: str, *args, **kwargs) -> str:
    """Reverse the URL pattern with the region code
    ex. tylko.com/en-uk/furniture
    """
    url = reverse(url_pattern, *args, **kwargs).split('/')
    url[1] = f'{url[1]}-{region_code}'
    return '/'.join(url)


def get_region_code_from_url(url: str) -> str:
    clean_url = remove_url_params(url)
    if clean_url.startswith('/'):
        region_code = clean_url.split('/')[1].split('-')[1]
    else:
        region_code = clean_url.split('/')[3].split('-')[1]
    if len(region_code) != 2:
        # if the url is outside our webpage and for some reason it fits the pattern
        raise AttributeError(f'Region code {region_code} is not valid')

    return region_code


def get_region_from_url(url: str) -> Optional['Region']:
    try:
        region_code = get_region_code_from_url(url)
    except (IndexError, AttributeError):
        # wrong url format:
        return

    from regions.models import Country

    try:
        return Country.objects.get(code=region_code.upper()).region
    except Country.DoesNotExist:
        return
