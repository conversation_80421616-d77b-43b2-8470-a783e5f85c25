from django.conf import settings
from django.utils import translation
from rest_framework import status
from rest_framework.generics import (
    ListAPIView,
    RetrieveAPIView,
)
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework.views import APIView

from carts.services.cart_service import CartService
from custom.enums import LanguageEnum
from ecommerce_api.mixins import EcommerceAPIMixin
from events.domain_events.marketing_events import RegionUpdateEvent
from regions.cached_region import CachedRegionData
from regions.change_region import change_region
from regions.mixins import RegionCalculationsObject
from regions.models import Region
from regions.serializers import (
    ChangeRegionSerializer,
    RegionGlobalSerializer,
    RegionSerializer,
)


class ChangeRegionView(APIView):
    permission_classes = ()

    def _process_anonymous_user(self, region):
        language = LanguageEnum(region.default_for_language)
        country = region.get_country()
        language_code = country.language_code if country else None

        data = {
            'status': 'ok',
            'region': region.name,
            'language': language,
        }
        self.request.session['cached_region'] = region.get_data_as_dict()
        self.request.session['cached_language'] = LanguageEnum(language_code)

        return Response(data)

    def _process_authenticated_user(self, user_profile, region):
        user_profile = self.request.user.profile
        change_region(user_profile, region)

        # TODO: replace with `translation.override()`
        translation.activate(user_profile.language)
        currency_code = region.get_currency().code
        country = region.get_country(without_cache=True)
        RegionUpdateEvent(
            user=self.request.user,
            country=getattr(country, 'code', 'DE'),
            language=user_profile.language,
            currency_code=currency_code,
        )
        response = self._prepare_response(
            user_profile=user_profile,
            region=region,
            locale=getattr(country, 'locale', 'DE_de'),
        )

        return response

    def _prepare_response(
        self,
        user_profile,
        region,
        locale,
    ):
        cart = CartService.get_or_create_cart(user_profile.user)
        response = Response(
            {
                'status': 'ok',
                'language': user_profile.language,
                'message': '',
                'total_price': cart.get_total_price(),
                'total_price_as_number': cart.get_total_price_number(),
                'total_price_as_number_before_discount': (
                    cart.get_total_price_number_before_discount()
                ),
                'discount_price': cart.display_regionalized(cart.region_promo_amount),
                'total_price_before_discount': cart.display_regionalized(
                    cart.get_total_price_number_before_discount()
                ),
                'payment_methods': [],  # TODO delete when removing old adyen
                'currency': region.currency.code,
                'locale': locale,
                'region_code': region.country.code if region.country else '',
            },
            status=status.HTTP_200_OK,
        )
        response.set_cookie(settings.LANGUAGE_COOKIE_NAME, user_profile.language)
        response.set_cookie('rg', user_profile.region_id)
        return response

    def post(self, request):
        serializer = ChangeRegionSerializer(data=request.data)

        if not serializer.is_valid():
            return Response(
                {
                    'status': 'error',
                    'message': 'Invalid data',
                    'details': serializer.errors,
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            region = Region.objects.get(name__iexact=request.data['region_name'])
        except Region.DoesNotExist:
            return Response(
                {
                    'status': 'error',
                    'message': 'Given region does not exist.',
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            user_profile = self.request.user.profile
        except AttributeError:
            return self._process_anonymous_user(region)

        return self._process_authenticated_user(user_profile, region)


class RegionListAPIView(ListAPIView):
    queryset = Region.objects.all()
    serializer_class = RegionSerializer
    permission_classes = (AllowAny,)


class RegionGlobalAPIView(EcommerceAPIMixin, RetrieveAPIView):
    queryset = Region.objects.select_related('currency')
    permission_classes = [AllowAny]
    serializer_class = RegionGlobalSerializer

    def _update_profile_language_if_needed(self, region: Region) -> None:
        cached_region = self.request.session.get('cached_region')
        if not cached_region or cached_region.get('name') == region.name:
            return
        if self.request.user.is_anonymous:
            self.request.session['cached_region'] = region.get_data_as_dict()
        else:
            change_region(self.request.user.profile, region)

    def get_object(self) -> CachedRegionData:
        if region_code := self.kwargs.get('region_code'):
            try:
                region = self.queryset.get(countries__code=region_code.upper())
                self._update_profile_language_if_needed(region)
                return region.cached_region_data
            except Region.DoesNotExist:
                return self.region
        return self.region

    def get_serializer_context(self):
        return {
            'rco': RegionCalculationsObject(self.region),
            **super().get_serializer_context(),
        }
