import io

from pathlib import Path

from django.contrib.contenttypes.fields import GenericForeignKey
from django.contrib.contenttypes.models import ContentType
from django.core.files.base import ContentFile
from django.db import models

from PIL import Image

from catalogue.enums import PLPImageKind
from feeds.image_configs import ImageConfigOption
from gallery.enums import FurnitureImageType
from render_tasks.choices import (
    RenderTaskStatuses,
    WebglRenderTaskType,
)


class BaseRenderTask(models.Model):
    status = models.IntegerField(
        choices=RenderTaskStatuses.choices,
        default=RenderTaskStatuses.TO_DO,
    )
    created_at = models.DateTimeField(auto_now_add=True)
    started_at = models.DateTimeField(blank=True, null=True)
    resolved_at = models.DateTimeField(blank=True, null=True)
    consumer = models.CharField(max_length=128, blank=True, default='')

    class Meta:
        abstract = True


class WebglRenderTask(BaseRenderTask):
    content_type = models.ForeignKey(
        ContentType,
        limit_choices_to=(
            models.Q(app_label='gallery', model='jetty')
            | models.Q(app_label='gallery', model='watty')
            | models.Q(app_label='gallery', model='sotty')
        ),
        on_delete=models.CASCADE,
    )
    object_id = models.PositiveIntegerField()
    furniture = GenericForeignKey('content_type', 'object_id')
    task_type = models.CharField(choices=WebglRenderTaskType.choices, max_length=20)
    image_configuration = models.PositiveIntegerField(
        choices=ImageConfigOption.webgl_config_choices(),
        null=True,
        blank=True,
    )
    feed_item = models.ForeignKey(
        'feeds.FeedItem',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )


class UnrealRenderTask(BaseRenderTask):
    furniture_json = models.JSONField()
    image = models.ImageField(upload_to='unreal_render_tasks', null=True, blank=True)
    image_config = models.JSONField(null=True, blank=True)

    furniture_content_type = models.ForeignKey(
        ContentType,
        limit_choices_to=(
            models.Q(app_label='gallery', model='jetty')
            | models.Q(app_label='gallery', model='watty')
            | models.Q(app_label='gallery', model='sotty')
        ),
        on_delete=models.CASCADE,
        related_name='unreal_render_tasks_furniture',
    )
    furniture_id = models.PositiveIntegerField(db_index=True)
    furniture = GenericForeignKey('furniture_content_type', 'furniture_id')

    initiator_content_type = models.ForeignKey(
        ContentType,
        limit_choices_to=(
            models.Q(app_label='catalogue', model='catalogueentry')
            | models.Q(app_label='feeds', model='feeditem')
            | models.Q(app_label='gallery', model='furnitureimage')
        ),
        on_delete=models.CASCADE,
        related_name='unreal_render_tasks_initiator',
    )
    initiator_id = models.PositiveIntegerField()
    initiator = GenericForeignKey('initiator_content_type', 'initiator_id')

    additional_data = models.JSONField(default=dict)

    def __str__(self):
        return (
            f'Unreal Render Task: furniture: {self.furniture_id} - '
            f'{self.get_status_display()}'
        )

    def save_as_preview(self, force=False):
        # save it as webp, because its lighter
        if not self.furniture or (self.furniture.preview and not force):
            return
        image_name = Path(self.image.name)
        new_image_name = str(image_name.with_suffix('.webp'))
        with Image.open(self.image) as img:
            img = img.convert('RGB')  # convert to RGB before saving as webp
            output = io.BytesIO()
            img.save(output, format='WEBP')
            output.seek(0)
            content_file = ContentFile(output.read())
            self.furniture.preview.save(new_image_name, content_file)
        self.furniture.save(update_fields=['preview'])

    def save_on_initiator(self):
        if self.initiator_content_type.model == 'catalogueentry':
            self.save_on_catalogue_entry()
        elif self.initiator_content_type.model == 'feeditem':
            from feeds.services.images.unreal import save_unreal_image

            save_unreal_image(self)
        else:
            self.initiator.image = self.image
            self.initiator.save(update_fields=['image'])
        self.save_as_preview(
            force=getattr(self.initiator, 'type', None)
            == FurnitureImageType.UNREAL_STUDIO
        )

    def save_on_catalogue_entry(self):
        """Save and convert to webp"""
        image_kind = PLPImageKind.get_by_value(
            self.image_config.get(
                'plp_image_kind',
                PLPImageKind.LIFESTYLE.value,
            )
        )
        image_name = Path(self.image.name)
        new_image_name = image_name.with_suffix('.webp')

        with Image.open(self.image) as img:
            img = img.convert('RGB')  # convert to RGB before saving as webp
            content_file = self._create_content_file(img)
            if image_kind == PLPImageKind.PRODUCT:
                self.initiator.product_unreal_image_webp.save(
                    new_image_name, content_file
                )
                img.thumbnail((100, 100))
                thumbnail_file = self._create_content_file(img)
                thumbnail_name = new_image_name.with_stem(
                    f'{new_image_name.stem}_thumbnail'
                )
                self.initiator.product_unreal_thumbnail_image_webp.save(
                    thumbnail_name, thumbnail_file
                )
            elif image_kind == PLPImageKind.LIFESTYLE:
                self.initiator.lifestyle_unreal_image_webp.save(
                    new_image_name, content_file
                )
            else:
                raise ValueError(f'Unknown PLPImageKind: {image_kind}')

        self.initiator.save(update_attributes=False)

    @staticmethod
    def _create_content_file(img: Image) -> ContentFile:
        with io.BytesIO() as output:
            img.save(output, format='WEBP')
            output.seek(0)
            return ContentFile(output.read())
