import logging

from datetime import timedelta

from django.utils import timezone

from celery import shared_task

from custom.enums import (
    Furniture,
    ShelfType,
)
from render_tasks.choices import RenderTaskStatuses
from render_tasks.models import WebglRenderTask
from render_tasks.utils import (
    generate_configurator_preview_image,
    generate_gallery_unreal_images,
    generate_unreal_studio_for_preview,
)

logger = logging.getLogger('cstm')


@shared_task
def revert_task_to_todo():
    hour_ago = timezone.now() - timedelta(hours=1)
    WebglRenderTask.objects.filter(
        status=RenderTaskStatuses.IN_PROGRESS, started_at__lt=hour_ago
    ).update(status=RenderTaskStatuses.TO_DO)


@shared_task
def order_unreal_preview(furniture_type: Furniture, furniture_id: int) -> None:
    furniture_type = Furniture(furniture_type)
    if furniture_type == Furniture.sample_box:
        raise ValueError('Are you nuts? No unreal images for samples.')

    try:
        item = furniture_type.model.objects.get(id=furniture_id)
    except furniture_type.model.DoesNotExist:
        logger.debug(
            f"Couldn't find {furniture_type} with ID {furniture_id} while "
            'creating unreal preview'
        )
        return

    if not item.is_generating_unreal_image_possible:
        return

    generate_unreal_studio_for_preview(item=item)

    if item.shelf_type in [
        ShelfType.TYPE13,
        ShelfType.VENEER_TYPE13,
        ShelfType.TYPE23,
        ShelfType.TYPE24,
        ShelfType.TYPE25,
    ]:
        generate_configurator_preview_image(item=item)

    elif furniture_type == Furniture.sotty:
        generate_gallery_unreal_images(item=item)
