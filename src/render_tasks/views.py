import datetime
import json

from django.core.paginator import Paginator
from django.db.models import (
    Case,
    Value,
    When,
)
from django.utils import timezone
from rest_framework import status
from rest_framework.decorators import action
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework.viewsets import GenericViewSet

from render_tasks.choices import RenderTaskStatuses
from render_tasks.models import (
    UnrealRenderTask,
    WebglRenderTask,
)
from render_tasks.serializers import (
    UnrealRenderTaskSerializer,
    WebglRenderTaskSerializer,
)


class RenderTaskViewSet(GenericViewSet):
    @action(methods=['post'], detail=False)
    def get_task(self, request):
        instance = self._get_next_task_in_queue()

        if not instance:
            return Response(status=status.HTTP_204_NO_CONTENT)

        serializer = self.get_serializer(instance=instance)
        instance.status = RenderTaskStatuses.IN_PROGRESS
        instance.started_at = datetime.datetime.now()
        instance.consumer = request.META['HTTP_USER_AGENT'][:128]
        instance.save()
        return Response(serializer.data, status=status.HTTP_200_OK)

    def _get_next_task_in_queue(self):
        queryset = (
            self.get_queryset()
            .filter(status=RenderTaskStatuses.TO_DO)
            .order_by(
                'created_at',
            )
        )
        return queryset.first()


class WebglRenderTaskViewSet(RenderTaskViewSet):
    serializer_class = WebglRenderTaskSerializer
    queryset = WebglRenderTask.objects.all()
    permission_classes = (AllowAny,)

    @action(methods=['post'], detail=False)
    def get_tasks(self, request):
        page = self._get_next_tasks_in_queue()
        if not page:
            return Response(status=status.HTTP_204_NO_CONTENT)

        serializer = self.get_serializer(instance=page, many=True)
        queryset = self.get_queryset().filter(id__in=[task.id for task in page])
        queryset.update(
            status=RenderTaskStatuses.IN_PROGRESS,
            started_at=datetime.datetime.now(),
            consumer=request.META['HTTP_USER_AGENT'][:128],
        )
        return Response(serializer.data, status=status.HTTP_200_OK)

    @action(methods=['put'], detail=True)
    def finish_task(self, request, **kwargs):
        instance = self.get_object()
        if request.data.get('status') == 'completed':
            instance.status = RenderTaskStatuses.DONE
        else:
            instance.status = RenderTaskStatuses.FAILED
        instance.save()

        return Response({'message': 'ok'}, status=status.HTTP_200_OK)

    @action(methods=['put'], detail=False)
    def finish_tasks(self, request, **kwargs):
        instances = self.queryset.filter(id__in=request.data.getlist('ids'))
        if request.data.get('status') == 'completed':
            instances.update(
                status=RenderTaskStatuses.DONE,
                resolved_at=timezone.now(),
            )
        else:
            instances.update(status=RenderTaskStatuses.FAILED)

        return Response({'message': 'ok'}, status=status.HTTP_200_OK)

    def _get_next_tasks_in_queue(self):
        queryset = (
            self.get_queryset()
            .filter(status=RenderTaskStatuses.TO_DO)
            .order_by(
                'created_at',
            )
        )
        return Paginator(queryset, 100).get_page(1)


class UnrealRenderTaskViewSet(RenderTaskViewSet):
    serializer_class = UnrealRenderTaskSerializer
    queryset = UnrealRenderTask.objects.all()
    permission_classes = (AllowAny,)  # TODO change to TokenAuthentication

    @action(methods=['put'], detail=True)
    def finish_task(self, request, **kwargs):
        instance = self.get_object()
        hostname = request.data.get('hostname', 'No hostname specified')

        if not instance.furniture:
            return self._handle_failed_task(
                instance,
                f'{instance.furniture_content_type.model} '
                f'with id {instance.furniture_id} does not exist',
                hostname,
            )

        image_file = request.FILES.get('image_file')
        if not image_file:
            return self._handle_failed_task(
                instance,
                'No image file provided',
                hostname,
            )

        update_fields = ['image', 'resolved_at', 'status']
        instance.resolved_at = timezone.now()
        instance.image.save(f'unreal_{instance.id}.jpeg', image_file)
        instance.status = RenderTaskStatuses.DONE

        if scene := request.data.get('scene'):
            instance.additional_data = {'scene': json.loads(scene)}
            update_fields.append('additional_data')

        instance.save(update_fields=update_fields)
        instance.save_on_initiator()
        return Response({'message': 'ok'}, status=status.HTTP_200_OK)

    def _handle_failed_task(self, instance, failure_reason, hostname):
        instance.status = RenderTaskStatuses.FAILED
        instance.additional_data = {
            'failure_reason': failure_reason,
            'hostname': hostname,
        }
        instance.resolved_at = timezone.now()
        instance.save(update_fields=['status', 'resolved_at', 'additional_data'])

        return Response({'message': 'Failed task saved'}, status=status.HTTP_200_OK)

    def _get_next_task_in_queue(self):
        ordering_condition = Case(
            When(initiator_content_type__model='furnitureimage', then=Value(0)),
            When(initiator_content_type__model='feeditem', then=Value(1)),
            When(initiator_content_type__model='catalogueentry', then=Value(2)),
        )
        return (
            self.get_queryset()
            .filter(status=RenderTaskStatuses.TO_DO)
            .order_by(ordering_condition, 'created_at')
            .first()
        )
