import logging

from django.contrib import admin
from django.contrib.admin.actions import delete_selected
from django.db.models import Count
from django.utils.html import format_html
from django.utils.safestring import mark_safe

from botocore.exceptions import ClientError
from past.utils import old_div

from custom.enums import LanguageEnum
from feeds.utils import truncate_sentence
from reviews.enums import ReviewAttribute
from reviews.models import (
    Review,
    ReviewAnswer,
    ReviewPhotos,
    ReviewScore,
    ReviewTag,
    ReviewTranslation,
)
from reviews.tasks import assign_tags

logger = logging.getLogger('cstm')


class NullListFilter(admin.SimpleListFilter):
    def lookups(self, request, model_admin):
        return (
            (
                '1',
                'No order connected',
            ),
            (
                '0',
                'Order connected',
            ),
        )

    def queryset(self, request, queryset):
        if self.value() in ('0', '1'):
            kwargs = {'{0}__isnull'.format(self.parameter_name): self.value() == '1'}
            return queryset.filter(**kwargs)
        return queryset


class OrderNullListFilter(NullListFilter):
    title = 'order'
    parameter_name = 'order'


class ReviewWithImagesFilter(admin.SimpleListFilter):
    title = 'images'
    parameter_name = 'images'

    def lookups(self, request, model_admin):
        return (
            (
                'has_images',
                'With images',
            ),
            (
                'no_images',
                'Without images',
            ),
        )

    def queryset(self, request, queryset):
        if self.value() == 'has_images':
            return queryset.annotate(number_of_images=Count('photos')).exclude(
                number_of_images=0
            )
        elif self.value() == 'no_images':
            return queryset.annotate(number_of_images=Count('photos')).filter(
                number_of_images=0
            )


class DuplicatesListFilter(admin.SimpleListFilter):
    title = 'duplicates'

    parameter_name = 'duplicates'

    def lookups(self, request, model_admin):
        return (
            ('email', 'Email duplicates'),
            ('description', 'Description duplicates'),
        )

    def queryset(self, request, queryset):
        if not self.value():
            return queryset

        if self.value() == 'email':
            return self._get_email_duplicates(queryset, self.value(), True)

        elif self.value() == 'description':
            return self._get_description_duplicates(queryset, self.value(), True)

    @staticmethod
    def _get_email_duplicates(queryset, value, *args):
        duplicates = (
            queryset.values('email')
            .annotate(field_count=Count('email'))
            .filter(field_count__gt=1)
            .values_list('email', flat=True)
        )
        if value:
            return queryset.filter(email__in=duplicates).order_by('email')

        return queryset.exclude(email__in=duplicates).order_by('email')

    @staticmethod
    def _get_description_duplicates(queryset, value, *args):
        duplicates = (
            ReviewTranslation.objects.filter(review__in=queryset)
            .values('description')
            .annotate(field_count=Count('description'))
            .filter(field_count__gt=1)
            .values_list('description', flat=True)
        )
        if value:
            return queryset.filter(translations__description__in=duplicates).order_by(
                'translations__description'
            )
        return queryset.exclude(translations__description__in=duplicates).order_by(
            'translations__description'
        )


class ReviewAttributeFilter(admin.SimpleListFilter):
    title = 'Tags'
    parameter_name = 'tags'
    empty_value = 'no_tags'

    def lookups(self, request, model_admin):
        return ReviewAttribute.choices + [(self.empty_value, 'No tags')]

    def queryset(self, request, queryset):
        if self.value() in ReviewAttribute.values:
            return queryset.filter(tags__name__in=[self.value()])
        elif self.value() == self.empty_value:
            return queryset.filter(tags__isnull=True)
        else:
            return queryset


class LanguageListFilter(admin.SimpleListFilter):
    title = 'original language'
    parameter_name = 'translations__language'

    def lookups(self, request, model_admin):
        return LanguageEnum.choices

    def queryset(self, request, queryset):
        if not self.value():
            return queryset

        return queryset.filter(
            translations__language=self.value(),
            translations__is_original_language=True,
        )


class ReviewPhotosInline(admin.StackedInline):
    model = ReviewPhotos
    readonly_fields = [
        'image_preview',
    ]

    @admin.display(description='Image preview')
    def image_preview(self, obj: ReviewPhotos) -> str:
        if obj.image:
            return format_html(
                f'<img src="{obj.image.url}" style="max-width: 150px;"/>',
            )
        return '-'


class ReviewAnswerInline(admin.StackedInline):
    model = ReviewAnswer
    extra = 0


class ReviewTranslationInline(admin.StackedInline):
    model = ReviewTranslation
    extra = 0


class ReviewAdmin(admin.ModelAdmin):
    actions = ('update_tags',)
    change_list_template = 'review_tool_button.html'
    list_display = (
        'id',
        'created_at',
        'get_title',
        'get_truncated_description',
        'get_uploaded_photo',
        'score',
        'name',
        'email',
        'enabled',
        'featured',
        'recommended_for_pdp',
        'featured_on_spaces',
        'shelf_type',
        'furniture_type',
        'get_available_languages',
        'get_template_type_description',
        'image_number',
        'get_connected_order',
        'get_links',
        'get_tags',
        'get_previews',
    )
    list_filter = (
        'score',
        'enabled',
        'recommended_for_pdp',
        'featured',
        'featured_on_spaces',
        'shelf_type',
        'furniture_type',
        ReviewAttributeFilter,
        LanguageListFilter,
        DuplicatesListFilter,
        ReviewWithImagesFilter,
        OrderNullListFilter,
        'category',
        'space',
        'tags_positive',
        'tags_negative',
    )
    search_fields = ('email__iexact', 'name__iexact')
    list_editable = ('enabled', 'featured', 'recommended_for_pdp')
    raw_id_fields = ('order', 'owner')
    list_per_page = 20
    inlines = (
        ReviewTranslationInline,
        ReviewPhotosInline,
        ReviewAnswerInline,
    )

    class Media(object):
        css = {
            'all': ('css/reviews_admin.css',),
        }

    def get_queryset(self, request):
        return Review.latest_objects.all()

    @mark_safe  # noqa: S308
    def image_number(self, obj):
        try:
            resp = [
                [x.image_type, old_div(x.image.size, (1000 * 1000)) if x.image else '-']
                for x in obj.photos.all()
            ]
        except (OSError, ClientError):
            resp = [['error', 'error']]
        resp_html = ''
        for x in resp:
            resp_html += '{} - {} mb <br/>'.format(x[0], x[1])
        return resp_html

    image_number.short_description = 'Imgs'

    @mark_safe  # noqa: S308
    def get_connected_order(self, obj):
        if obj.order is None:
            return '-'
        else:
            return "<a href='/admin/orders/order/{}'>Order {}</a>".format(
                obj.order_id,
                obj.order_id,
            )

    get_connected_order.short_description = 'Order'

    @mark_safe  # noqa: S308
    def get_links(self, obj):
        if obj.order:
            return (
                '<a href="/admin/review_tool/#/review/{}"> Review tool </a> <br/><br/> '
                '<a href="/cs/user_overview/{}/"> User overview </a>'
            ).format(
                obj.id,
                obj.order.owner_id,
            )
        else:
            return (
                '<a href="/admin/review_tool/#/review/{}"> Review tool </a><br/><br/>'
                'No order connected'
            ).format(
                obj.id,
            )

    get_links.short_description = 'Actions'

    @admin.display(description='Template type')
    def get_template_type_description(self, obj):
        return obj.get_template_type_display()

    @admin.display(description='Available languages')
    def get_available_languages(self, obj):
        return [translation.language for translation in obj.translations.all()]

    @admin.display(description='Title')
    def get_title(self, obj):
        return obj.title

    def delete_selected(self, request, queryset):
        """Overwrites delete_selected which is using SQL directly and not invoking
        the delete method on each instance.
        """
        furniture_types = set(queryset.values_list('furniture_type', flat=True))
        delete_selected_result = delete_selected(self, request, queryset)
        for furniture_type in furniture_types:
            ReviewScore.update_score(furniture_type)
        return delete_selected_result

    @admin.display(description='Photo')
    def get_uploaded_photo(self, obj):
        img = obj.photos.first()
        if not img:
            return ''
        return format_html(
            f'<img src="{img.image.url}" '
            'style="max-width: 150px; max-height: 150px;" />'
        )

    @admin.display(description='Content')
    def get_truncated_description(self, obj):
        description = obj.description
        if not description:
            return ''

        val = truncate_sentence(description, 300)
        if len(val) != len(description):
            return f'{val} [...]'
        return val

    @admin.display(description='Tags')
    def get_tags(self, obj) -> str:
        return ', '.join(
            tag_name.title()
            for tag_name in obj.tags.order_by('name').values_list('name', flat=True)
        )

    def update_tags(self, request, queryset):
        for review in queryset:
            assign_tags.delay(review.id)


class ReviewScoreAdmin(admin.ModelAdmin):
    list_display = (
        'id',
        'furniture_type',
        'avg_score',
        'number_of_reviews',
        'created_at',
    )


class ReviewTagAdmin(admin.ModelAdmin):
    raw_id_fields = ('reviews', 'reviews_negative')


class ReviewPhotosAdmin(admin.ModelAdmin):
    list_display = ('id', 'review', 'image', 'image_type')
    raw_id_fields = ('review',)


class ReviewAnswerAdmin(admin.ModelAdmin):
    raw_id_fields = ('review',)


admin.site.register(Review, ReviewAdmin)
admin.site.register(ReviewScore, ReviewScoreAdmin)
admin.site.register(ReviewPhotos, ReviewPhotosAdmin)
admin.site.register(ReviewTag, ReviewTagAdmin)
admin.site.register(ReviewAnswer, ReviewAnswerAdmin)
