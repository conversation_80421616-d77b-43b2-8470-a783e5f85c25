import enum

from django.db import models

from custom.enums import ChoicesMixin
from gallery.enums import FurnitureCategory


class ReviewAttribute(models.TextChoices):
    ASSEMBLY = 'assembly'
    COMFORT = 'comfort'
    CUSTOMER_SUPPORT = 'customer_support'
    DELIVERY = 'delivery'
    FABRIC = 'fabric'
    STYLE = 'style'
    QUALITY = 'quality'


# Deprecated. left it, because it's used in migrations
class ReviewCategoryEnum(ChoicesMixin, enum.IntEnum):
    CATEGORY_NONE = 0
    CATEGORY_SHOERACK = 1
    CATEGORY_TVSHELF = 2
    CATEGORY_SIDEBOARD = 3
    CATEGORY_BOOKCASE = 4
    CATEGORY_WALLSTORAGE = 5
    CATEGORY_CHEST_OF_DRAWERS = 6
    CATEGORY_VINYL_STORAGE = 7
    CATEGORY_WARDROBE = 8
    CATEGORY_BEDSIDE_TABLE = 9
    CATEGORY_DESK = 10
    CATEGORY_SOFA = 11
    CATEGORY_DRESSING_TABLE = 12

    @classmethod
    def choices(cls):
        return [(member.value, member.name.replace('_', ' ')) for member in cls]

    @classmethod
    def values(cls):
        return [member.value for member in cls]


# Deprecated. left it, because it's used in migrations
REVIEW_CATEGORY_TO_FURNITURE_CATEGORY = {
    ReviewCategoryEnum.CATEGORY_SHOERACK: FurnitureCategory.SHOERACK,
    ReviewCategoryEnum.CATEGORY_BOOKCASE: FurnitureCategory.BOOKCASE,
    ReviewCategoryEnum.CATEGORY_TVSHELF: FurnitureCategory.TV_STAND,
    ReviewCategoryEnum.CATEGORY_WALLSTORAGE: FurnitureCategory.WALL_STORAGE,
    ReviewCategoryEnum.CATEGORY_SIDEBOARD: FurnitureCategory.SIDEBOARD,
    ReviewCategoryEnum.CATEGORY_VINYL_STORAGE: FurnitureCategory.VINYL_STORAGE,
    ReviewCategoryEnum.CATEGORY_CHEST_OF_DRAWERS: FurnitureCategory.CHEST,
    ReviewCategoryEnum.CATEGORY_WARDROBE: FurnitureCategory.WARDROBE,
    ReviewCategoryEnum.CATEGORY_BEDSIDE_TABLE: FurnitureCategory.BEDSIDE_TABLE,
    ReviewCategoryEnum.CATEGORY_DESK: FurnitureCategory.DESK,
    ReviewCategoryEnum.CATEGORY_DRESSING_TABLE: FurnitureCategory.DRESSING_TABLE,
    # TODO: Sofa reviews for new categories
    ReviewCategoryEnum.CATEGORY_SOFA: FurnitureCategory.TWO_SEATER,
}
