from django.db.models import Count

from django_filters import rest_framework as django_filters
from taggit.models import Tag

from reviews.models import (
    Review,
    ReviewTranslation,
)


class ReviewFilter(django_filters.FilterSet):
    has_images = django_filters.BooleanFilter(
        lookup_expr='isnull',
        field_name='photos',
        exclude=True,
    )
    tags = django_filters.ModelMultipleChoiceFilter(
        field_name='tags',
        lookup_expr='in',
        to_field_name='name',
        queryset=Tag.objects.all(),
    )
    language = django_filters.ModelChoiceFilter(
        field_name='translations',
        to_field_name='language',
        queryset=ReviewTranslation.objects.all(),
    )

    class Meta:
        model = Review
        fields = [
            'language',
            'space',
            'category',
        ]


class ReviewExtendedFilter(ReviewFilter):
    year = django_filters.NumberFilter(field_name='created_at', lookup_expr='year')
    month = django_filters.NumberFilter(field_name='created_at', lookup_expr='month')
    has_email_duplicates = django_filters.BooleanFilter(
        field_name='email',
        method='get_email_duplicates',
    )
    has_description_duplicates = django_filters.BooleanFilter(
        field_name='description',
        method='get_description_duplicates',
    )

    @staticmethod
    def get_description_duplicates(queryset, field_name, value):
        duplicates = (
            ReviewTranslation.objects.filter(review__in=queryset)
            .values(field_name)
            .annotate(field_count=Count(field_name))
            .filter(field_count__gt=1)
            .values_list(field_name, flat=True)
        )
        if value:
            return queryset.filter(
                **{f'translations__{field_name}__in': duplicates}
            ).order_by(f'translations__{field_name}')
        return queryset.exclude(
            **{f'translations__{field_name}__in': duplicates}
        ).order_by(f'translations__{field_name}')

    @staticmethod
    def get_email_duplicates(queryset, field_name, value):
        duplicates = (
            queryset.values(field_name)
            .annotate(field_count=Count(field_name))
            .filter(field_count__gt=1)
            .values_list(field_name, flat=True)
        )
        if value:
            return queryset.filter(**{f'{field_name}__in': duplicates}).order_by(
                field_name
            )
        return queryset.exclude(**{f'{field_name}__in': duplicates}).order_by(
            field_name
        )

    class Meta(ReviewFilter.Meta):
        fields = ReviewFilter.Meta.fields + [  # noqa: RUF005
            'score',
            'enabled',
            'featured',
            'shelf_type',
            'furniture_type',
            'order',
            'review_sentiment',
            'created_at',
            'photos',
            'email',
        ]
