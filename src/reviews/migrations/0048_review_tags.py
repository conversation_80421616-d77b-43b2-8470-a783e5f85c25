# Generated by Django 4.2.23 on 2025-08-04 12:14

from django.db import migrations

import taggit.managers


class Migration(migrations.Migration):

    dependencies = [
        (
            'taggit',
            '0006_rename_taggeditem_content_type_object_id_taggit_tagg_content_8fc721_idx',
        ),
        ('reviews', '0047_remove_review_categories'),
    ]

    operations = [
        migrations.AddField(
            model_name='review',
            name='tags',
            field=taggit.managers.TaggableManager(
                help_text='A comma-separated list of tags.',
                through='taggit.TaggedItem',
                to='taggit.Tag',
                verbose_name='Tags',
            ),
        ),
    ]
