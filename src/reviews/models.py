import logging

from collections import defaultdict
from decimal import (
    ROUND_HALF_UP,
    Decimal,
)
from typing import Union

from django.conf import settings
from django.db import (
    models,
    transaction,
)
from django.db.models import (
    <PERSON><PERSON><PERSON><PERSON>,
    Count,
    OuterRef,
    Subquery,
)
from django.db.models.aggregates import (
    Avg,
    Sum,
)
from django.utils import timezone
from django.utils.html import escape
from django.utils.safestring import mark_safe

from sorl.thumbnail.fields import ImageField
from taggit.managers import TaggableManager

from custom.enums import (
    LanguageEnum,
    ShelfType,
)
from custom.utils.in_memory_cache import expiring_lru_cache
from gallery.enums import (
    ConfiguratorTypeEnum,
    FurnitureCategory,
)
from reviews.choices import (
    ReviewAnswerOwnerChoices,
    ReviewSentimentChoices,
    Spaces,
    TemplateChoices,
)
from reviews.images import resize_review_image

logger = logging.getLogger('cstm')

TAG_LIST = [
    'perfect fit',
    'price',
    'price performance ratio',
    'app',
    'design',
    'quality durability',
    'experience',
    'assembly',
    'assembly service',
    'service cs',
    'delivery',
    'production',
    'packaging',
]


class ReviewWithImagesAndTranslationsManager(models.Manager):
    """Annotate queryset with prefetched photos and translations data."""

    def get_queryset(self):
        original_review_translation = ReviewTranslation.objects.filter(
            review=OuterRef('pk'),
            is_original_language=True,
        ).values('title', 'description', 'language')[:1]
        return (
            super()
            .get_queryset()
            .prefetch_related('photos', 'translations')
            .annotate(
                num_items=Count('photos'),
                title=Subquery(
                    original_review_translation.values('title'),
                    output_field=CharField(),
                ),
                description=Subquery(
                    original_review_translation.values('description'),
                    output_field=CharField(),
                ),
                original_language=Subquery(
                    original_review_translation.values('language'),
                    output_field=CharField(),
                ),
            )
            .order_by('-created_at', '-num_items')
        )


class ReviewQuerySet(models.QuerySet):
    def average_score(self) -> float | None:
        avg_score = self.aggregate(avg_score=Avg('score'))['avg_score']
        if isinstance(avg_score, Decimal):
            return avg_score.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
        return avg_score


class ReviewManager(models.Manager):
    def get_queryset(self) -> ReviewQuerySet:
        return ReviewQuerySet(self.model, using=self._db)

    @expiring_lru_cache(ttl=settings.REVIEW_CACHE_TTL_SECONDS)
    def get_categories(self) -> set[FurnitureCategory]:
        """Categories, that have at least one enabled review."""
        return {
            FurnitureCategory(category)
            for category in self.filter(enabled=True)
            .values_list('category', flat=True)
            .distinct()
        }


class Review(models.Model):
    name = models.CharField(max_length=256)
    email = models.CharField(max_length=512)
    country = models.CharField(max_length=512)
    design_type = models.IntegerField(default=0)
    furniture_type = models.CharField(max_length=120, db_index=True)
    score = models.IntegerField(default=0)
    verified_owner = models.BooleanField(default=False)
    questions = models.JSONField(default=dict, blank=True, null=True)
    enabled = models.BooleanField(default=False, db_index=True)
    featured = models.BooleanField(default=False)
    featured_on_spaces = models.BooleanField(default=False)
    recommended_for_pdp = models.BooleanField(default=False)
    category = models.CharField(  # noqa: DJ001
        max_length=32,
        choices=FurnitureCategory.choices,
        null=True,
    )
    space = models.IntegerField(choices=Spaces.choices, null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    special_quality_until = models.DateTimeField(blank=True, null=True)
    template_type = models.CharField(
        max_length=50, choices=TemplateChoices.choices, default=TemplateChoices.TEXT
    )
    shelf_type = models.IntegerField(
        choices=ShelfType.choices(),
        blank=True,
        null=True,
    )
    review_sentiment = models.IntegerField(
        choices=ReviewSentimentChoices.choices,
        default=ReviewSentimentChoices.NOT_RATED,
    )
    owner = models.ForeignKey(
        'user_profile.UserProfile',
        null=True,
        blank=True,
        on_delete=models.CASCADE,
    )
    order = models.ForeignKey(
        'orders.order',
        null=True,
        blank=True,
        on_delete=models.CASCADE,
    )
    configurator_type = models.PositiveSmallIntegerField(
        choices=ConfiguratorTypeEnum.choices(),
        help_text=(
            'MIXED - Wardrobe, '
            'COLUMN - column configurator (i.e. for S+), '
            'ROW - everything else,',
        ),
        null=True,
        blank=True,
    )
    color = models.IntegerField(default=0, null=True)
    tags = TaggableManager()

    objects = ReviewManager()
    latest_objects = ReviewWithImagesAndTranslationsManager()

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._original_enabled = self.enabled

    def __str__(self):
        return 'Review {} for {} from {} for {} stars - {}'.format(
            self.id,
            self.furniture_type,
            self.name,
            self.score,
            self.created_at,
        )

    def save(self, *args, **kwargs):
        from reviews.tasks import assign_tags

        exists = bool(self.pk)
        update_score = self.enabled or (
            exists and self._original_enabled != self.enabled
        )
        super().save(*args, **kwargs)
        if update_score:
            ReviewScore.update_score(self.furniture_type)
        if not exists:
            transaction.on_commit(lambda: assign_tags.delay(review_id=self.id))

    def delete(self, using=None, keep_parents=False):
        furniture_type = self.furniture_type
        super().delete(using=using, keep_parents=keep_parents)
        ReviewScore.update_score(furniture_type)

    def get_previews_raw(self):
        if self.order is None:
            return []
        try:
            return [
                x.order_item.preview.url
                for x in self.order.items.all()
                if x.order_item.preview
            ]
        except IOError:
            return []

    def get_previews(self):
        sum_pictures = ''
        if self.order is None:
            return '-'
        for index, item in enumerate(self.order.items.all()):
            sum_pictures += "<img src='{}' style='max-width: 300px'/>".format(
                escape(item.order_item.preview.url) if item.order_item.preview else '',
            )
            if (index + 1) % 3 == 0:
                sum_pictures += '<br/>'
        return mark_safe(  # noqa: S308
            "<div style='width:450px; float:left'>" + sum_pictures + '</div>'
        )

    def get_proper_photo(self):
        """
        Group review related photos by image type and return first photo with
        desired image type. Image types are ordered by priority.
        """

        # right now vertical template is not used anymore,
        # to be decided after we unify all review components and review tool
        image_types_by_priority = ('pic-1-1', 'pic-4-3', 'original')

        image_types_map = defaultdict(list)
        for photo in (photos := self.photos.all()):
            image_types_map[photo.image_type].append(photo)

        for proper_image_type in image_types_by_priority:
            if proper_images := image_types_map[proper_image_type]:
                return proper_images[0]

        return photos[0] if photos else None


class ReviewTranslation(models.Model):
    review = models.ForeignKey(
        'Review',
        on_delete=models.CASCADE,
        related_name='translations',
    )
    language = models.CharField(
        max_length=2,
        choices=LanguageEnum.choices,
        default=LanguageEnum.EN,
    )
    is_original_language = models.BooleanField(default=False)
    title = models.CharField(max_length=1024)
    description = models.TextField()

    def save(self, *args, **kwargs):
        """Make sure that only one translation is marked as original language."""
        if self.is_original_language:
            original_language_translations = ReviewTranslation.objects.filter(
                review=self.review,
                is_original_language=True,
            )
            if original_language_translations.exists():
                if original_language_translations.first() != self:
                    original_language_translations.update(is_original_language=False)

        super().save(*args, **kwargs)

    def __str__(self):  # noqa: DJ012
        return 'ReviewTranslation for <Review: {}> in <{}>'.format(
            self.review.id,
            self.get_language_display(),
        )


class ReviewAnswer(models.Model):
    OWNER_1 = 0
    OWNER_2 = 1
    OWNER_3 = 2
    OWNER_4 = 3
    OWNER_5 = 4
    OWNER_6 = 5
    OWNER_CHOICES = (
        (OWNER_1, 'Kris'),
        (OWNER_2, 'Karolina'),
        (OWNER_3, 'Gosia'),
        (OWNER_4, 'Kasia'),
        (OWNER_5, 'Mikołaj'),
        (OWNER_6, 'Ben'),
    )

    description = models.TextField(blank=True, null=True)  # noqa: DJ001
    owner_id = models.IntegerField(
        choices=ReviewAnswerOwnerChoices.choices,
        default=ReviewAnswerOwnerChoices.OWNER_1,
    )
    created_at = models.DateTimeField(default=timezone.now)
    visible = models.BooleanField(default=False)
    review = models.OneToOneField(
        Review,
        blank=True,
        null=True,
        related_name='answer',
        unique=True,
        on_delete=models.CASCADE,
    )

    class Meta:
        verbose_name = 'Review answer'

    def __str__(self):
        if self.review:
            return 'Answer for review {}'.format(self.review.id)
        else:
            return 'Answer without review, how?'

    def update(self, description, owner_id, created_at, visible):
        self.description = description
        self.owner_id = owner_id
        self.created_at = created_at
        self.visible = visible
        self.save()


class ReviewPhotos(models.Model):
    review = models.ForeignKey(
        Review,
        related_name='photos',
        on_delete=models.CASCADE,
    )
    image = ImageField(upload_to='reviews/review_photos/%Y/%m')
    image_type = models.CharField(max_length=30, default='original')

    image_small = ImageField(
        upload_to='reviews/review_photos/%Y/%m',
        blank=True,
        null=True,
    )
    image_small_webp = ImageField(
        upload_to='reviews/review_photos/%Y/%m',
        blank=True,
        null=True,
    )
    image_medium = ImageField(
        upload_to='reviews/review_photos/%Y/%m',
        blank=True,
        null=True,
    )
    image_medium_webp = ImageField(
        upload_to='reviews/review_photos/%Y/%m',
        blank=True,
        null=True,
    )

    class Meta:
        verbose_name = 'Review photo'
        verbose_name_plural = 'Review photos'

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._image_old = self.image

    def __str__(self):
        return 'Photo {} for review {} {}'.format(
            self.id,
            self.review.id,
            self.review.email,
        )

    def save(self, *args, **kwargs):
        if not self.pk or (self._image_old != self.image):
            self.generate_all_image_versions()
        super().save(*args, **kwargs)

    def generate_all_image_versions(self):
        if self.image:
            medium_images = resize_review_image(self.image, 648)
            small_images = resize_review_image(self.image, 324)
            self.image_small, self.image_small_webp = small_images
            self.image_medium, self.image_medium_webp = medium_images


class ReviewScore(models.Model):
    furniture_type = models.CharField(max_length=120, db_index=True, unique=True)
    avg_score = models.FloatField(blank=True, null=True)
    number_of_reviews = models.IntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return 'Score adjustment for  {} , stars: {}, number: {}'.format(
            self.furniture_type,
            self.avg_score,
            self.number_of_reviews,
        )

    @staticmethod
    def update_score(furniture_type):
        reviews = Review.objects.filter(enabled=True, furniture_type=furniture_type)
        new_score = reviews.aggregate(Avg('score'))['score__avg']
        try:
            rs = ReviewScore.objects.get(furniture_type=furniture_type)
            rs.avg_score = new_score
            rs.number_of_reviews = reviews.count()
            rs.save(update_fields=['avg_score', 'number_of_reviews'])
        except ReviewScore.DoesNotExist:
            logger.error(
                'No ReviewScore for furniture_type=%s exists!',
                furniture_type,
            )

    @classmethod
    def get_general_review_score(
        cls,
    ) -> tuple[Union[float, int, None], Union[float, int, None]]:
        """
        Average review score and amount based on Jetty and Watty reviews.

        Note:
        ----
            Do not display review score and amount for separate furniture types.
            In order to unify review scores throughout the app always display
            average score based on Jetty and Watty reviews together.
        """
        review_score = cls.objects.filter(
            furniture_type__in=('jetty', 'watty')
        ).aggregate(Avg('avg_score'), Sum('number_of_reviews'))
        avg_score = review_score['avg_score__avg']
        number_of_reviews = review_score['number_of_reviews__sum']

        return (
            round(avg_score, 1) if avg_score else None,
            round(number_of_reviews, 1) if number_of_reviews else None,
        )


# todo: deprecated, last review tagged is from october 2021
class ReviewTag(models.Model):
    name = models.CharField(max_length=40)
    reviews = models.ManyToManyField(Review, related_name='tags_positive', blank=True)
    reviews_negative = models.ManyToManyField(
        Review, related_name='tags_negative', blank=True
    )

    class Meta:
        verbose_name = 'Review tag'
        verbose_name_plural = 'Review tags'

    def __str__(self):
        return 'Tag: {}'.format(self.name)
