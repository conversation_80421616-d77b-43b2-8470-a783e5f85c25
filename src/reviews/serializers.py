import base64
import datetime

from typing import Literal

from django.core.files.base import ContentFile
from django.db import IntegrityError
from django.utils import translation
from django.utils.functional import cached_property
from rest_framework import serializers

from custom.enums import (
    LanguageEnum,
    ShelfType,
)
from custom.utils.python2_specific import SanitizedModelSerializer
from gallery.enums import (
    Fabric,
    FurnitureCategory,
)
from reviews.models import (
    Review,
    ReviewAnswer,
    ReviewPhotos,
    ReviewScore,
    ReviewTag,
    ReviewTranslation,
)


class ToolReviewTagSerilizer(serializers.ModelSerializer):
    class Meta:
        model = ReviewTag
        fields = ('name',)


class ToolReviewPhotosSerializer(serializers.ModelSerializer):
    image_base64 = serializers.CharField(
        write_only=True, allow_blank=True, required=False
    )
    image = serializers.ImageField(required=False, read_only=True)

    class Meta:
        model = ReviewPhotos
        fields = ('id', 'image', 'image_type', 'image_base64')


class ToolReviewAnswerSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(required=False)

    class Meta:
        model = ReviewAnswer
        fields = (
            'id',
            'description',
            'owner_id',
            'created_at',
            'visible',
        )


class ToolReviewListSerializer(SanitizedModelSerializer):
    photos = ToolReviewPhotosSerializer(many=True)
    previews = serializers.SerializerMethodField()
    language = serializers.CharField(source='original_language', required=False)
    description = serializers.CharField(required=False)

    class Meta:
        model = Review
        fields = (
            'id',
            'created_at',
            'email',
            'enabled',
            'featured',
            'featured_on_spaces',
            'space',
            'shelf_type',
            'name',
            'language',
            'template_type',
            'description',
            'order',
            'furniture_type',
            'category',
            'score',
            'previews',
            'photos',
        )

    def get_previews(self, obj):
        return obj.get_previews_raw()


class ToolReviewSerializer(ToolReviewListSerializer):
    country = serializers.CharField()
    tags_negative = ToolReviewTagSerilizer(many=True, required=False)
    tags_positive = ToolReviewTagSerilizer(many=True, required=False)
    answer = ToolReviewAnswerSerializer(many=False, required=False)
    carrier = serializers.SerializerMethodField()
    order_notes = serializers.SerializerMethodField()
    assembly = serializers.SerializerMethodField()
    title = serializers.CharField()
    description = serializers.CharField()
    language = serializers.CharField(source='original_language')

    class Meta:
        model = Review
        fields = ToolReviewListSerializer.Meta.fields + (  # noqa: RUF005
            'name',
            'title',
            'description',
            'language',
            'country',
            'category',
            'furniture_type',
            'score',
            'verified_owner',
            'tags_positive',
            'tags_negative',
            'enabled',
            'answer',
            'color',
            'carrier',
            'order_notes',
            'assembly',
        )

    def get_carrier(self, obj):
        order = obj.order
        if not order:
            return None
        if order.logistic_info:
            return order.logistic_info[-1].carrier
        return None

    def get_order_notes(self, obj):
        order = obj.order
        if not order:
            return None
        return order.order_notes

    def get_assembly(self, obj):
        order = obj.order
        if not order:
            return None
        return order.assembly


class ToolReviewUpdateSerializer(ToolReviewSerializer):
    title = serializers.CharField(write_only=True, required=False)
    description = serializers.CharField(write_only=True, required=False)
    language = serializers.CharField(write_only=True, required=False)

    class Meta:
        model = Review
        fields = ToolReviewSerializer.Meta.fields

    def update(self, instance, validated_data):
        self._update_review_translation(instance, validated_data)

        tags_positive = validated_data.pop('tags_positive', [])
        tags_negative = validated_data.pop('tags_negative', [])
        photos = validated_data.pop('photos', [])
        if 'answer' in validated_data:
            answer = validated_data.pop('answer')
        else:
            answer = None

        if answer is not None:
            if 'id' in answer:
                instance.answer.update(
                    answer['description'],
                    answer['owner_id'],
                    answer['created_at'],
                    answer['visible'],
                )
            else:
                item = ToolReviewAnswerSerializer(data=answer)
                if item.is_valid():
                    try:
                        item.save(review=instance)
                    except IntegrityError:
                        pass
        else:
            # lets remove answer if it was not send
            try:
                instance.answer.delete()
            except ReviewAnswer.DoesNotExist:
                pass

        instance.save()

        instance.tags_positive.clear()
        for tag in tags_positive:
            tag_id = tag.get('name', None)
            if tag_id:
                instance.tags_positive.add(ReviewTag.objects.get(name=tag_id))

        instance.tags_negative.clear()
        for tag in tags_negative:
            tag_id = tag.get('name', None)
            if tag_id:
                instance.tags_negative.add(ReviewTag.objects.get(name=tag_id))

        for photo in photos:
            image_base64 = photo.get('image_base64', '')

            if image_base64 and photo['image_type'] != 'original':
                instance.photos.exclude(image_type='original').filter(
                    image_type=photo.get('image_type')
                ).delete()

                # lets remove old images with the same image_type
                new_photo = ReviewPhotos()
                new_photo.review = instance
                new_photo.image_type = photo['image_type']
                new_photo.image = ContentFile(
                    base64.b64decode(photo['image_base64']),
                    'review_{}_{}.png'.format(
                        instance.id,
                        new_photo.image_type,
                    ),
                )
                new_photo.save()
        return super().update(instance, validated_data)

    @staticmethod
    def _update_review_translation(instance: Review, validated_data: dict) -> None:
        title = validated_data.pop('title', None)
        description = validated_data.pop('description', None)
        language = validated_data.pop('language', None)

        if not any((title, description, language)):
            return

        review_translation = instance.translations.filter(
            is_original_language=True
        ).last()
        data = {
            'title': title,
            'description': description,
            'language': language,
        }
        for key, value in data.items():
            update_fields = []
            if value:
                update_fields.append(key)
                setattr(review_translation, key, value)

            review_translation.save(update_fields=update_fields)


class ReviewPhotosSerializer(serializers.ModelSerializer):
    class Meta:
        model = ReviewPhotos
        fields = (
            'id',
            'image',
        )


class ReviewExportSerializer(SanitizedModelSerializer):
    country = serializers.CharField()
    name = serializers.CharField()
    title = serializers.CharField()
    photos_count = serializers.ReadOnlyField(source='photos.count')
    description = serializers.CharField()
    uploaded_at = serializers.SerializerMethodField()
    tags_positive = serializers.SerializerMethodField()
    tags_negative = serializers.SerializerMethodField()

    class Meta:
        model = Review
        fields = (
            'id',
            'name',
            'email',
            'country',
            'design_type',
            'furniture_type',
            'score',
            'title',
            'description',
            'created_at',
            'photos_count',
            'template_type',
            'shelf_type',
            'uploaded_at',
            'special_quality_until',
            'category',
            'enabled',
            'featured',
            'order_id',
            'tags_positive',
            'tags_negative',
        )

    def get_uploaded_at(self, obj):
        return datetime.datetime.now().isoformat()

    def get_tags_positive(self, obj):
        return ';'.join([x.name for x in obj.tags_positive.all()])

    def get_tags_negative(self, obj):
        return ';'.join([x.name for x in obj.tags_negative.all()])


class SingleReviewSerializer(serializers.ModelSerializer):
    """Serializer for single review with photos and translations.

    Note: This serializer uses annotated fields for "title", "description",
     "translated_title", "translated_description" and "original_language".

     Make sure to use `latest_objects` manager for queryset.
    """

    title = serializers.CharField()
    description = serializers.CharField()
    translated_title = serializers.CharField(required=False)
    translated_description = serializers.CharField(required=False)
    original_language = serializers.CharField()
    proper_photo = serializers.SerializerMethodField()
    photo_small = serializers.SerializerMethodField()
    photo_small_webp = serializers.SerializerMethodField()
    photo_medium = serializers.SerializerMethodField()
    photo_medium_webp = serializers.SerializerMethodField()
    category = serializers.SerializerMethodField()
    product_line = serializers.SerializerMethodField()
    color = serializers.SerializerMethodField()
    material = serializers.SerializerMethodField()

    class Meta:
        model = Review
        fields = (
            'name',
            'country',
            'score',
            'title',
            'translated_title',
            'description',
            'translated_description',
            'original_language',
            'category',
            'product_line',
            'created_at',
            'proper_photo',
            'photo_small',
            'photo_small_webp',
            'photo_medium',
            'photo_medium_webp',
            'color',
            'material',
        )

    @staticmethod
    def get_proper_photo(obj):
        proper_photo = obj.get_proper_photo()
        if proper_photo and proper_photo.image:
            return proper_photo.image.url
        return None

    @staticmethod
    def get_photo_small(obj):
        proper_photo = obj.get_proper_photo()
        if proper_photo and proper_photo.image_small:
            return proper_photo.image_small.url
        return None

    @staticmethod
    def get_photo_small_webp(obj):
        proper_photo = obj.get_proper_photo()
        if proper_photo and proper_photo.image_small_webp:
            return proper_photo.image_small_webp.url
        return None

    @staticmethod
    def get_photo_medium(obj):
        proper_photo = obj.get_proper_photo()
        if proper_photo and proper_photo.image_medium:
            return proper_photo.image_medium.url
        return None

    @staticmethod
    def get_photo_medium_webp(obj):
        proper_photo = obj.get_proper_photo()
        if proper_photo and proper_photo.image_medium_webp:
            return proper_photo.image_medium_webp.url

    def get_category(self, obj: Review) -> str:
        language = self.context.get('language', LanguageEnum.EN)
        with translation.override(language):
            return FurnitureCategory(obj.category).translated_name

    @staticmethod
    def get_product_line(obj: Review) -> str:
        if obj.shelf_type is None:
            return ''

        return ShelfType(obj.shelf_type).product_line

    def get_color(self, obj: Review) -> str:
        if obj.shelf_type is None or obj.color is None:
            return ''
        language = self.context.get('language', LanguageEnum.EN)
        with translation.override(language):
            return ShelfType(obj.shelf_type).colors(obj.color).translated_simple_color

    def get_material(self, obj: Review) -> Fabric | Literal['']:
        if obj.shelf_type not in ShelfType.get_sotty_shelf_types() or obj.color is None:
            return ''
        return ShelfType(obj.shelf_type).colors(obj.color).fabric


class GeneralReviewSerializer(serializers.ModelSerializer):
    reviews = SingleReviewSerializer(many=True, source='*')
    reviews_average_score = serializers.SerializerMethodField()
    reviews_count = serializers.SerializerMethodField()

    class Meta:
        model = Review
        fields = ('reviews', 'reviews_average_score', 'reviews_count')

    def get_reviews_average_score(self, _):
        reviews_score, _ = self._review_scores
        return reviews_score

    def get_reviews_count(self, _):
        _, reviews_count = self._review_scores
        return reviews_count

    @cached_property
    def _review_scores(self) -> tuple[int, int]:
        return ReviewScore.get_general_review_score()


class CreateReviewSerializer(serializers.ModelSerializer):
    title = serializers.CharField(write_only=True)
    description = serializers.CharField(write_only=True)
    score = serializers.IntegerField(min_value=1, max_value=5)
    category = serializers.ChoiceField(choices=FurnitureCategory.choices)
    photos = ReviewPhotosSerializer(required=False, many=True)

    class Meta:
        model = Review
        fields = (
            'pk',
            'name',
            'email',
            'country',
            'category',
            'score',
            'title',
            'description',
            'photos',
        )

    def create(self, validated_data: dict) -> Review:
        images_data = validated_data.pop('photos', [])
        if profile := self.context.get('user_profile', None):
            validated_data.update({'owner': profile})

        title = validated_data.pop('title', None)
        description = validated_data.pop('description', None)
        language = self.context.get('language', LanguageEnum.EN)

        review = Review.objects.create(**validated_data)

        ReviewTranslation.objects.create(
            review=review,
            title=title,
            description=description,
            language=language,
            is_original_language=True,
        )

        for image in images_data:
            ReviewPhotos.objects.create(review=review, **image)
        return review
