import logging

from pydantic import BaseModel

from custom.gemini import GeminiClient
from reviews.enums import ReviewAttribute
from reviews.models import Review

logger = logging.getLogger('cstm')

SYSTEM_PROMPT = (
    'You are a review‑tagging assistant. Given a product review, return *only* '
    'zero or more enums chosen **strictly** from the following list: '
    f"{', '.join(ReviewAttribute.values)}. "
    'Do not include any other keys, text, or comments.'
)


class ReviewTagsDto(BaseModel):
    tags: list[ReviewAttribute]


class ReviewTagger:
    def __init__(self, review: Review, client: GeminiClient | None = None) -> None:
        self.client = client or GeminiClient()
        original_review = review.translations.filter(is_original_language=True).first()
        self.title = original_review.title
        self.description = original_review.description

    def _call_llm(self) -> ReviewTagsDto:
        review_text = f'Title: {self.title}\nDescription: {self.description}'
        response_config = {
            'response_mime_type': 'application/json',
            # doesn't work when passing standard BaseModel
            'response_schema': ReviewTagsDto.model_json_schema(),
        }
        response = self.client.generate_content(
            contents=review_text, prompt=SYSTEM_PROMPT, **response_config
        )
        return ReviewTagsDto.model_validate(response.parsed)

    def predict(self) -> list[ReviewAttribute]:
        try:
            dto = self._call_llm()
            return dto.tags
        except Exception as exception:
            logger.error(f'ReviewTagger found error: {exception}')
            return []
