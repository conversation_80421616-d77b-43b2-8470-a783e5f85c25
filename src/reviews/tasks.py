import json
import logging
import os

from datetime import datetime

from celery import shared_task
from celery.utils.log import get_task_logger
from google.cloud.exceptions import GoogleCloudError

from custom.utils.slack import log_bigquery_error_to_slack
from reviews.models import (
    TAG_LIST,
    Review,
)
from reviews.serializers import ReviewExportSerializer
from reviews.services import ReviewSlackService
from reviews.services.tagger import ReviewTagger
from reviews.utils import match_review_with_order

logger = logging.getLogger('cstm')
task_logger = get_task_logger('celery_task')

try:
    from google.cloud import bigquery
except ImportError:
    logger.exception('Error while importing bigquery from google.cloud')


@shared_task
def export_reviews_to_big_query():
    response_list = []

    queryset = Review.latest_objects.all()
    for review in queryset:
        tags = list(  # noqa: C411
            [x.lower() for x in review.tags_positive.values_list('name', flat=True)]
        )
        json_entry = ReviewExportSerializer(review).data
        # add tags here
        for tag in TAG_LIST:
            json_entry[tag.replace(' ', '_')] = tag in tags
        response_list.append(json_entry)
    result = [json.dumps(record) for record in response_list]
    client = bigquery.Client()
    dataset_ref = client.dataset('reviews_eu')
    table_ref = dataset_ref.table('all_reviews_list_with_tags')
    job_config = bigquery.LoadJobConfig()
    job_config.source_format = bigquery.SourceFormat.NEWLINE_DELIMITED_JSON
    job_config.write_disposition = bigquery.WriteDisposition.WRITE_TRUNCATE

    file_name = (
        datetime.today()
        .isoformat()
        .replace(':', '_')
        .replace('.', '_')
        .replace('-', '_')
    )
    dir = 'biquery'
    path = '{}/{}'.format(os.path.expanduser('~'), dir)
    try:
        os.makedirs(path)
    except OSError:
        if not os.path.isdir(path):
            raise
    file_with_path = '{}/reviews{}.json'.format(path, file_name)
    with open(file_with_path, 'w') as outfile:
        outfile.write('\n'.join(result))

    with open(file_with_path, 'rb') as source_file:
        job = client.load_table_from_file(
            source_file, table_ref, location='EU', job_config=job_config
        )

    try:
        job.result()
    except GoogleCloudError as err:
        log_bigquery_error_to_slack(err, 'reviews_eu.all_reviews_list_with_tags')
        raise


@shared_task
def match_review_with_order_task(review_id):
    try:
        review = Review.objects.get(id=review_id)
    except Review.DoesNotExist:
        task_logger.error('Review with id %i does not exist.', review_id)
    else:
        if not review.order and review.email:
            match_review_with_order(review)


@shared_task
def send_bad_review_to_slack(review_id):
    try:
        review = Review.latest_objects.get(id=review_id)
    except Review.DoesNotExist:
        return
    ReviewSlackService(review).send_slack_notification()


@shared_task
def assign_tags(review_id: int) -> None:
    review = Review.objects.get(id=review_id)
    tagger = ReviewTagger(review=review)
    review.tags.set(tagger.predict())
