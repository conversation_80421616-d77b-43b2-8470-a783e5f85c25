from datetime import datetime

from django.urls import reverse

import pytest


@pytest.mark.django_db
class TestReviewFilter:
    url = reverse('admin:review_admin_api')

    def test_year_filter(self, api_client, admin_user, review_factory):
        review_factory.create_batch(5, created_at=datetime(2020, 10, 9))
        review_factory.create_batch(5, created_at=datetime(2021, 10, 7))
        api_client.force_login(user=admin_user)
        query = '?year=2021'

        response = api_client.get(self.url + query, format='json')

        assert len(response.data['results']) == 5

    def test_month_filter(self, api_client, admin_user, review_factory):
        review_factory.create_batch(5, created_at=datetime(2020, 10, 9))
        review_factory.create_batch(5, created_at=datetime(2020, 5, 7))
        review_factory.create_batch(5, created_at=datetime(2021, 10, 1))
        api_client.force_login(user=admin_user)
        query = '?month=10'

        response = api_client.get(self.url + query, format='json')

        assert len(response.data['results']) == 10

    @pytest.mark.parametrize(
        'query, result',  # noqa: PT006
        [
            ('?has_images=true', 1),
            ('?has_images=false', 5),
        ],
    )
    def test_has_images_filter(
        self,
        api_client,
        admin_user,
        review_factory,
        review_photos_factory,
        query,
        result,
    ):
        review = review_factory()
        review_photos_factory(review=review)
        review_factory.create_batch(5)
        api_client.force_login(user=admin_user)

        response = api_client.get(self.url + query, format='json')

        assert len(response.data['results']) == result

    @pytest.mark.parametrize(
        'query, result',  # noqa: PT006
        [
            ('?has_email_duplicates=true', 5),
            ('?has_email_duplicates=false', 10),
        ],
    )
    def test_has_email_duplicates_filter(
        self,
        api_client,
        admin_user,
        review_factory,
        query,
        result,
    ):
        review_factory.create_batch(5, email='<EMAIL>')
        review_factory.create_batch(10)
        api_client.force_login(user=admin_user)

        response = api_client.get(self.url + query, format='json')

        assert len(response.data['results']) == result

    @pytest.mark.parametrize(
        'query, result',  # noqa: PT006
        [
            ('?has_description_duplicates=true', 3),
            ('?has_description_duplicates=false', 10),
        ],
    )
    def test_has_description_duplicates_filter(
        self,
        api_client,
        admin_user,
        review_factory,
        query,
        result,
    ):
        review_factory.create_batch(3, translations__description='Nice shelf')
        review_factory.create_batch(10, translations=[])
        api_client.force_login(user=admin_user)

        response = api_client.get(self.url + query, format='json')

        assert len(response.data['results']) == result
