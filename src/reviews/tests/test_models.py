from decimal import Decimal
from unittest.mock import patch

import pytest

from custom.enums import Furniture
from reviews.models import (
    Review,
    ReviewScore,
)


@pytest.mark.django_db
class TestReview:
    @pytest.mark.django_db(transaction=True)
    @patch('reviews.tasks.assign_tags')
    def test_save_call_assign_tags(self, mock, review_factory):
        review = review_factory()

        mock.delay.assert_called_once_with(review_id=review.id)

    def test_review_score_is_updated_when_review_is_enabled(self, review_factory):
        ReviewScore.objects.create(furniture_type='jetty')
        reviews = review_factory.create_batch(9, score=5, furniture_type='jetty')
        for review in reviews:
            review.enabled = False
            review.save()

        assert ReviewScore.objects.get(furniture_type='jetty').number_of_reviews == 0

        for review in reviews[:3]:
            review.enabled = True
            review.score = 3
            review.save()

        assert ReviewScore.objects.get(furniture_type='jetty').number_of_reviews == 3
        assert ReviewScore.objects.get(furniture_type='jetty').avg_score == 3

    def test_review_score_is_updated_when_review_is_disabled(self, review_factory):
        ReviewScore.objects.create(furniture_type='jetty')
        reviews = review_factory.create_batch(
            9, score=5, furniture_type='jetty', enabled=True
        )

        assert ReviewScore.objects.get(furniture_type='jetty').number_of_reviews == 9

        for review in reviews[:2]:
            review.enabled = False
            review.save()

        assert ReviewScore.objects.get(furniture_type='jetty').number_of_reviews == 7

    def test_review_score_is_updated_when_review_is_deleted(self, review_factory):
        ReviewScore.objects.create(furniture_type='jetty')
        reviews = review_factory.create_batch(9, score=5, furniture_type='jetty')
        for review in reviews:
            review.enabled = True
            review.save()

        assert ReviewScore.objects.get(furniture_type='jetty').number_of_reviews == 9

        reviews[0].delete()

        assert ReviewScore.objects.get(furniture_type='jetty').number_of_reviews == 8


@pytest.mark.django_db
class TestReviewScore:
    def test_get_general_review_score_returns_avg_for_jetty_and_watty(
        self,
        review_score_factory,
    ):
        for furniture_type in ('jetty', 'watty'):
            review_score_factory(
                furniture_type=furniture_type,
                avg_score=5,
                number_of_reviews=100,
            )
        reviews_average_score, reviews_count = ReviewScore.get_general_review_score()

        assert reviews_average_score == 5
        assert reviews_count == 200

    def test_get_general_review_score_returns_none_if_no_review_scores(self):
        reviews_average_score, reviews_count = ReviewScore.get_general_review_score()

        assert reviews_average_score is None
        assert reviews_count is None


@pytest.mark.django_db
class TestReviewQuerySetAverageScore:
    def test_no_reviews(self):
        queryset = Review.objects.all()

        result = queryset.average_score()

        assert result is None

    def test_average_score(self, review_factory):
        review_factory(score=5, furniture_type=Furniture.jetty.value)
        review_factory(score=3, furniture_type=Furniture.watty.value)
        review_factory(score=4, furniture_type=Furniture.jetty.value)
        queryset = Review.objects.filter(furniture_type=Furniture.jetty.value)

        result = queryset.average_score()

        assert result == Decimal('4.50')
