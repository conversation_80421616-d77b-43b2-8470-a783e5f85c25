from unittest.mock import (
    MagicMock,
    patch,
)

import pytest

from reviews.enums import ReviewAttribute
from reviews.services.tagger import ReviewTagger


class DummyGeminiClient:
    def generate_content(self, contents: str, prompt: str, **config_kwargs):
        return MagicMock(
            parsed={'tags': [ReviewAttribute.ASSEMBLY, ReviewAttribute.COMFORT]}
        )


@pytest.mark.django_db
class TestReviewTagger:
    def test_predict(self, review_factory):
        review = review_factory()
        tagger = ReviewTagger(review, client=DummyGeminiClient())

        tags = tagger.predict()

        assert tags == [ReviewAttribute.ASSEMBLY, ReviewAttribute.COMFORT]

    @patch('reviews.tasks.assign_tags', side_effect=Exception)
    @patch('reviews.services.tagger.ReviewTagger._call_llm', side_effect=Exception)
    def test_predict_handles_exception(self, _, __, review_factory):
        review = review_factory()
        tagger = ReviewTagger(review, client=DummyGeminiClient())

        tags = tagger.predict()

        assert tags == []
