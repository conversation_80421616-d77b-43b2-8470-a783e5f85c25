import pytest

from orders.enums import OrderStatus
from reviews.tasks import match_review_with_order_task


@pytest.mark.django_db
class TestGlobalReviewScoreApiView:
    def test_match_review_with_order_task(
        self,
        user,
        review_factory,
        order_factory,
        order_item_factory,
    ):
        order = order_factory.create(owner=user, status=OrderStatus.DELIVERED)
        order_item = order_item_factory.create(order=order, content_type__model='jetty')

        review = review_factory(
            order=None,
            email=user.email,
            category=order_item.order_item.furniture_category,
        )

        match_review_with_order_task(review.id)
        review.refresh_from_db()
        assert review.order is not None

    def test_match_review_without_order(
        self,
        user,
        review_factory,
    ):
        review = review_factory.create(
            order=None,
            email=user.email,
        )

        match_review_with_order_task(review.id)
        review.refresh_from_db()
        assert review.order is None
