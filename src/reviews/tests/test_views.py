from unittest.mock import patch

from django.test import override_settings
from django.urls import reverse
from rest_framework import status

import pytest

from custom.enums import (
    ShelfType,
    Sofa01Color,
)
from gallery.constants import SOFA_FURNITURE_CATEGORY
from gallery.enums import (
    Fabric,
    FurnitureCategory,
)
from reviews.models import ReviewScore


@pytest.mark.django_db
class TestGlobalReviewScoreApiView:
    def test_view(self, review_factory, api_client, user):
        review_factory(category=FurnitureCategory.BOOKCASE, enabled=True)
        review_factory(category=FurnitureCategory.TWO_SEATER, enabled=True)
        avg_score, reviews_count = ReviewScore.get_general_review_score()

        url = reverse('global-review-score')
        api_client.force_authenticate(user=user)
        response = api_client.get(url)

        assert response.status_code == status.HTTP_200_OK
        response_json = response.json()
        assert response_json['avg_score'] == avg_score
        assert response_json['reviews_count'] == reviews_count
        assert FurnitureCategory.BOOKCASE in response_json['categories']
        assert FurnitureCategory.TWO_SEATER not in response_json['categories']
        assert SOFA_FURNITURE_CATEGORY in response_json['categories']


@pytest.mark.django_db
class TestReviewViewSet:
    @staticmethod
    def send_api_request(api_client):
        url = reverse('review-list')
        return api_client.get(url)

    def test_review_list(self, api_client, user, review_factory):
        review_factory.create_batch(5, enabled=True)
        expected_fields = {
            'name',
            'country',
            'score',
            'title',
            'description',
            'originalLanguage',
            'category',
            'productLine',
            'createdAt',
            'properPhoto',
            'photoSmall',
            'photoSmallWebp',
            'photoMedium',
            'photoMediumWebp',
            'color',
            'material',
        }

        response = self.send_api_request(api_client)

        assert response.status_code == status.HTTP_200_OK
        response_json = response.json()
        assert response_json['count'] == 5
        assert response_json['results'][0].keys() == expected_fields

    def test_review_list_with_one_picture(
        self, api_client, user, review_factory, review_photos_factory
    ):
        review = review_factory.create(enabled=True)
        image = review_photos_factory.create(review=review)

        response = self.send_api_request(api_client)

        assert response.status_code == status.HTTP_200_OK
        assert response.data['results'][0]['proper_photo'] == image.image.url

    def test_review_list_without_picture(self, api_client, user, review_factory):
        review_factory.create(enabled=True)

        response = self.send_api_request(api_client)

        assert response.status_code == status.HTTP_200_OK
        assert response.data['results'][0]['proper_photo'] is None

    @pytest.mark.parametrize(
        ('color', 'expected_fabric'),
        [
            (Sofa01Color.REWOOL2_BROWN, Fabric.WOOL),
            (Sofa01Color.CORDUROY_ECRU, Fabric.CORDUROY),
        ],
    )
    def test_review_list_color_material_fields(
        self, api_client, user, review_factory, color, expected_fabric
    ):
        review_factory(shelf_type=ShelfType.SOFA_TYPE01, color=color, enabled=True)

        response = self.send_api_request(api_client)

        assert response.status_code == status.HTTP_200_OK
        response_json = response.json()
        assert response_json['results'][0]['properPhoto'] is None
        assert response_json['results'][0]['color'] == color.translated_simple_color
        assert response_json['results'][0]['material'] == expected_fabric

    def test_filtering_on_has_images(
        self,
        api_client,
        review_factory,
        review_photos_factory,
    ):
        review_1 = review_factory.create(enabled=True, name='Lisa')
        review_2 = review_factory.create(enabled=True, name='John')
        review_photos_factory.create(review=review_1)

        url = reverse('review-list')
        response = api_client.get(f'{url}?has_images=true')
        assert response.status_code == status.HTTP_200_OK
        assert response.data['count'] == 1
        assert review_1.name in [
            review.get('name') for review in response.data['results']
        ]
        assert review_2.name not in [
            review.get('name') for review in response.data['results']
        ]

    @patch('reviews.tasks.match_review_with_order_task')
    def test_create_review(self, _, api_client, user):  # noqa: PT019
        url = reverse('review-list')
        api_client.force_authenticate(user=user)
        review_to_create = {
            'name': 'John Doe',
            'email': '<EMAIL>',
            'country': 'US',
            'category': FurnitureCategory.SHOERACK,
            'title': 'This is a review',
            'score': 5,
            'description': 'This is a description',
        }
        response = api_client.post(url, data=review_to_create)

        assert response.status_code == status.HTTP_201_CREATED
        assert response.data['name'] == review_to_create['name']
        assert response.data['email'] == review_to_create['email']
        assert response.data['score'] == review_to_create['score']

    @patch('reviews.tasks.match_review_with_order_task')
    def test_create_review_by_anonymous_user(self, _, api_client):  # noqa: PT019
        url = reverse('review-list')
        review_to_create = {
            'name': 'John Doe',
            'email': '<EMAIL>',
            'country': 'US',
            'category': FurnitureCategory.SHOERACK,
            'title': 'This is a review',
            'score': 5,
            'description': 'This is a description',
        }
        response = api_client.post(url, data=review_to_create)

        assert response.status_code == status.HTTP_201_CREATED
        assert response.data['name'] == review_to_create['name']
        assert response.data['email'] == review_to_create['email']
        assert response.data['score'] == review_to_create['score']

    @patch('requests.post')
    @patch('reviews.tasks.match_review_with_order_task')
    @patch('reviews.services.ReviewSlackService.get_slack_notification_body')
    @override_settings(IS_PRODUCTION=True)
    def test_slack_service_after_create_review_lower_than_five(
        self,
        mocked_get_slack_notification_body,
        _,  # noqa: PT019
        mocked_requests_post,
        api_client,
    ):
        url = reverse('review-list')
        review_to_create = {
            'name': 'John Doe',
            'email': '<EMAIL>',
            'country': 'US',
            'category': FurnitureCategory.SHOERACK,
            'title': 'This is a review',
            'score': 3,
            'description': 'This is a description',
        }
        response = api_client.post(url, data=review_to_create)

        assert response.status_code == status.HTTP_201_CREATED
        assert response.data['name'] == review_to_create['name']
        assert response.data['email'] == review_to_create['email']
        assert response.data['score'] == review_to_create['score']
        assert mocked_requests_post.call_count == 1
        assert mocked_get_slack_notification_body.call_count == 1

    def test_review_score(self, api_client, review_factory):
        review_factory(score=5, enabled=True, category=FurnitureCategory.BOOKCASE)
        review_factory(score=4, enabled=True, category=FurnitureCategory.BOOKCASE)
        # wrong category
        review_factory(score=3, enabled=True, category=FurnitureCategory.CHEST)
        # disabled
        review_factory(score=2, enabled=False)
        url = reverse('review-review-score') + f'?category={FurnitureCategory.BOOKCASE}'

        response = api_client.get(url)

        assert response.status_code == status.HTTP_200_OK
        response_json = response.json()
        assert response_json == {
            'averageScore': 4.5,
            'count': 2,
        }

    @override_settings(
        CACHES={
            'default': {
                'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
                'LOCATION': 'test-cache',
            }
        }
    )
    def test_review_score_cached(
        self, api_client, review_factory, django_assert_max_num_queries
    ):
        review_factory(score=5, enabled=True)
        url = reverse('review-review-score')
        noncached_response = api_client.get(url)

        # Second call should use cache and not hit the database
        # Only session and abtest queries, not review queries
        with django_assert_max_num_queries(2):
            response = api_client.get(url)

        assert response.status_code == status.HTTP_200_OK
        assert noncached_response.json() == response.json()
