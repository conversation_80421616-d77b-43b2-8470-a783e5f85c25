from django.urls import path
from rest_framework import routers

from reviews.views import (
    GlobalReviewScoreApiView,
    ReviewViewSet,
)

router = routers.SimpleRouter()
router.register('v2/reviews', ReviewViewSet, basename='review')


urlpatterns = [  # noqa: RUF005
    path(
        'v1/global-review-score/',
        GlobalReviewScoreApiView.as_view(),
        name='global-review-score',
    ),
] + router.urls
