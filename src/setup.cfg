[tool:pytest]
DJANGO_SETTINGS_MODULE = cstm_be.settings.test
python_files = tests.py tests_*.py test_*.py
norecursedirs = _dependencies *static media shared .git .cache tmp* settings data
addopts = -q
    # raise errors when unregistered marker used
    --strict-markers
    -n 4
    --cov-report=xml
    --cov-report=term
    --cov-config=setup.cfg
    --create-db
    --nomigrations
    --ds=cstm_be.settings.test
    --disable-socket
markers =
    production_system: marks tests that requests production system
    adyen: marks tests that requests Adyen
    nbp(mock_requests=True): marks tests that requests NBP API and mock all these requests if ``mock_requests`` truthy
    google: marks tests that requests Google APIs
    facebook: marks tests that requests Facebook APIs
    google_chrome_headless: marks tests that use Google Chrome Headless

[coverage:run]
branch = True
source = .
omit =
    */tests/*,
    */migrations/*,
    *config*,
    manage.py,
    *conftest.py,
    */cstm_be/*,
    */django_mailer/*,
    */django_user_agents/*
    */management/commands/*
data_file = .tests_reports/.coverage

[coverage:report]
skip_covered = True
show_missing = True
sort = Cover
exclude_lines =
    pragma: no cover
    # Don't complain about missing debug-only code:
    if settings\.DEBUG
    # Don't complain about mypy only related code:
    if typing\.TYPE_CHECKING
    if TYPE_CHECKING
    # Don't complain if tests don't hit defensive assertion code:
    raise NotImplementedError
    except ImportError
    # Don't complain if non-runnable code isn't run:
    if __name__ == .__main__.:

[coverage:xml]
output = .tests_reports/coverage.xml

[coverage:json]
output = .tests_reports/coverage.json

[coverage:html]
directory = .tests_reports/htmlcov/
title = Tylko Coverage Report

