from django.contrib.sites.models import Site
from rest_framework import mixins
from rest_framework.permissions import AllowAny
from rest_framework.viewsets import GenericViewSet

from allauth.socialaccount.models import SocialApp
from djangorestframework_camel_case.parser import Camel<PERSON>aseJ<PERSON>NParser
from djangorestframework_camel_case.render import Came<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

from socials.api.serializer import SocialAppSerializer


class SocialAppViewSet(mixins.ListModelMixin, GenericViewSet):
    queryset = SocialApp.objects.all()
    serializer_class = SocialAppSerializer
    renderer_classes = (CamelCaseJSONRenderer,)
    parser_classes = (CamelCaseJSONParser,)
    authentication_classes = ()
    permission_classes = (AllowAny,)

    def get_queryset(self):
        qs = SocialApp.objects.filter(sites__in=[Site.objects.get_current()])
        return qs
