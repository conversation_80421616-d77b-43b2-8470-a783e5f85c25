from unittest.mock import (
    Mock,
    PropertyMock,
)

from django.contrib.auth import get_user_model

import pytest

from allauth.account.adapter import get_adapter
from allauth.socialaccount.models import SocialLogin

from socials.social_login_adapter import SocialAccountAdapter

User = get_user_model()


@pytest.mark.django_db
class TestAccountAdapter:
    def test_save_user_does_not_duplicate_profile(self, user_factory):
        adapter = get_adapter()
        user = user_factory(
            username='<EMAIL>',
            email='<EMAIL>',
            password='test1234',  # noqa: S106
        )
        form_mock = Mock()
        form_data = {
            'first_name': user.first_name,
            'last_name': user.last_name,
            'email': user.email,
            'username': user.username,
        }
        type(form_mock).cleaned_data = PropertyMock(return_value=form_data)

        assert hasattr(user, 'profile')
        old_profile_id = user.profile.id
        saved_user = adapter.save_user(request=None, user=user, form=form_mock)
        assert saved_user.profile.id == old_profile_id
        assert saved_user == user


@pytest.mark.django_db
class TestSocialAccountAdapter:
    def test_pre_social_login_connects_existing_user(self, user_factory):
        adapter = SocialAccountAdapter()
        email = '<EMAIL>'
        existing_user = user_factory(
            username=email,
            email=email,
        )
        request = Mock()
        request.user.is_authenticated = False
        social_account = Mock()
        social_account.extra_data = {'email': email}
        sociallogin = Mock()
        sociallogin.email_addresses = []
        sociallogin.is_existing = False
        sociallogin.account = social_account
        sociallogin.user = Mock()

        adapter.pre_social_login(request, sociallogin)

        sociallogin.connect.assert_called_once_with(request, existing_user)

    def test_pre_social_login_does_nothing_for_authenticated_user(self):
        adapter = SocialAccountAdapter()
        request = Mock()
        request.user.is_authenticated = True
        sociallogin = Mock()

        adapter.pre_social_login(request, sociallogin)

        sociallogin.connect.assert_not_called()

    def test_pre_social_login_does_nothing_for_nonexistent_user(self):
        adapter = SocialAccountAdapter()
        request = Mock()
        request.user.is_authenticated = False
        social_account = Mock()
        social_account.extra_data = {'email': '<EMAIL>'}
        sociallogin = Mock()
        sociallogin.account = social_account

        adapter.pre_social_login(request, sociallogin)

        sociallogin.connect.assert_not_called()

    def test_pre_social_login_with_existing_connection(self):
        adapter = SocialAccountAdapter()
        request = Mock()
        sociallogin = Mock()
        sociallogin.is_existing = True

        adapter.pre_social_login(request, sociallogin)

        sociallogin.connect.assert_not_called()

    def test_pre_social_login_falls_back_to_extra_data_email(self, user_factory):
        adapter = SocialAccountAdapter()
        email = '<EMAIL>'
        existing_user = user_factory(
            username=email,
            email=email,
        )

        request = Mock()
        request.user.is_authenticated = False

        unverified_email = Mock()
        unverified_email.verified = False
        unverified_email.email = '<EMAIL>'

        sociallogin = Mock()
        sociallogin.is_existing = False
        sociallogin.email_addresses = [unverified_email]
        sociallogin.account = Mock()
        sociallogin.account.extra_data = {'email': email}

        adapter.pre_social_login(request, sociallogin)
        sociallogin.connect.assert_called_once_with(request, existing_user)

    def test_is_auto_signup_allowed_user_with_email_collision(self, user_factory):
        adapter = SocialAccountAdapter()
        request = Mock()
        email = '<EMAIL>'
        user_factory(
            username='x',
            email=email,
        )
        new_user = User(
            username=email,
            email=email,
        )
        sociallogin = SocialLogin(user=new_user)
        assert adapter.is_auto_signup_allowed(request, sociallogin) is True
