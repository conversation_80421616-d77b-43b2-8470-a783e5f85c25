from django.contrib.sites.models import Site
from django.urls import reverse
from rest_framework import status

import pytest

from socials.tests.factories import SocialAppFactory


@pytest.mark.django_db
class TestSocialAppViewSet:
    url = reverse('socials-list')

    def test_list_valid_apps(self, api_client):
        site = Site.objects.get_current()
        social_app = SocialAppFactory(
            name='Test App',
            provider='google',
            client_id='test_client_id',
            secret='test_secret',  # noqa: S106
        )
        social_app.sites.set([site])
        response = api_client.get(self.url)

        assert response.status_code == status.HTTP_200_OK
        app_data = response.data[0]
        assert app_data['id'] == social_app.id
        assert app_data['name'] == 'Test App'
        assert app_data['provider'] == 'google'

        expected_fields = {'id', 'name', 'provider'}
        actual_fields = set(app_data.keys())
        assert actual_fields == expected_fields

    def test_list_empty_when_no_apps_exist(self, api_client):
        response = api_client.get(self.url)

        assert response.status_code == status.HTTP_200_OK
        assert len(response.data) == 0
