from django.utils import translation
from django.utils.translation import gettext_lazy as _
from rest_framework import serializers
from rest_framework.serializers import ValidationError

from user_consents.choices import (
    CookieCategories,
    CookieDurationUnits,
)
from user_consents.models import (
    <PERSON><PERSON>,
    <PERSON>ieConsent,
)


class CookieSerializer(serializers.ModelSerializer):
    category = serializers.CharField(source='get_category_display')
    translation = serializers.SerializerMethodField()
    duration = serializers.SerializerMethodField()

    class Meta:
        model = Cookie
        fields = [
            'name',
            'description',
            'category',
            'translation',
            'domain_name',
            'duration_seconds',
            'duration',
            'provider',
        ]

    def get_translation(self, obj: <PERSON>ie) -> str:
        with translation.override(self.context['language']):
            return _(obj.translation_key)

    def get_duration(self, obj: <PERSON>ie) -> str:
        with translation.override(self.context['language']):
            translated_duration_unit = CookieDurationUnits(
                obj.duration_unit
            ).get_translation(obj.duration_number)
            if obj.duration_unit == CookieDurationUnits.SESSION:
                return translated_duration_unit

            return f'{obj.duration_number} {translated_duration_unit}'


class CookieConsentSerializer(serializers.ModelSerializer):
    consents = serializers.JSONField()

    class Meta:
        model = CookieConsent
        fields = ['visitor_guid', 'consents']

    def validate_consents(self, consents):
        expected_keys = {label.lower() for label in CookieCategories.labels}
        received_keys = set(consents.keys())
        if received_keys != expected_keys:
            raise ValidationError(
                f'Expected categories are: {sorted(expected_keys)}, '
                f'but these categories are given: {sorted(received_keys)}'
            )
        if not all(isinstance(v, bool) for v in consents.values()):
            raise ValidationError(
                'Values of consents are incorrect, should be a boolean.'
            )
        if consents['necessary'] is False:
            raise ValidationError('Necessary cookies must be set to true')
        return consents
