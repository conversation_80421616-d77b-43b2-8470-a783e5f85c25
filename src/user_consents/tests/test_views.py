from django.urls import reverse
from django.utils.translation import gettext_lazy as _
from rest_framework import status

import pytest

from user_consents.models import CookieConsent
from user_consents.views import CookieConsentCreateView


@pytest.mark.django_db
class TestCookieListView:
    url = reverse('cookies-list')

    def test_cookies_list_with_data(self, api_client, cookie):
        data = {
            'name': cookie.name,
            'description': cookie.description,
            'translation': _(cookie.translation_key),
            'category': cookie.category.label,
            'domain_name': cookie.domain_name,
            'duration_seconds': cookie.duration_seconds,
            'duration': '1 day',
            'provider': cookie.provider,
        }

        response = api_client.get(self.url)

        assert response.status_code == status.HTTP_200_OK
        assert response.data['updated_at'] == cookie.updated_at
        assert data in response.json()['cookies']

    def test_cookies_list_returns_only_active_cookies(
        self,
        api_client,
        cookie_factory,
    ):
        cookie = cookie_factory(name='active_cookie')
        inactive_cookie = cookie_factory(active=False, name='inactive_cookie')

        response = api_client.get(self.url)
        assert response.status_code == status.HTTP_200_OK

        response_cookies = [cookie['name'] for cookie in response.json()['cookies']]
        assert cookie.name in response_cookies
        assert inactive_cookie.name not in response_cookies


@pytest.mark.django_db
class TestCookieConsentCreateView:
    url = reverse('cookie-consents-create')

    def test_post_cookie_consents_creates_with_no_guid_given(self, api_client):
        headers = {
            'HTTP_USER_AGENT': 'firefox-22',
            'HTTP_X_FORWARDED_FOR': '*************',
        }
        data = {
            'consents': {
                'necessary': True,
                'performance': True,
                'functional': True,
                'advertising': True,
            }
        }

        response = api_client.post(self.url, data, format='json', **headers)
        consent = CookieConsent.objects.last()

        assert response.status_code == status.HTTP_201_CREATED
        assert response.data['consents'] == {
            'necessary': True,
            'performance': True,
            'functional': True,
            'advertising': True,
        }
        assert consent.visitor_ip == '*************'
        assert consent.user_agent == 'firefox-22'

    def test_post_cookie_consents_creates_with_given_guid(
        self,
        api_client,
        sample_endpoint_data,
    ):
        headers = {
            'HTTP_USER_AGENT': 'firefox-22',
            'HTTP_X_FORWARDED_FOR': '*************',
        }
        data = sample_endpoint_data

        response = api_client.post(self.url, data, format='json', **headers)

        assert response.status_code == status.HTTP_201_CREATED
        assert response.data['visitor_guid'] == str(data['visitor_guid'])
        assert response.data['consents'] == {
            'necessary': True,
            'performance': True,
            'functional': True,
            'advertising': True,
        }

    def test_post_cookie_consents_creates_with_no_user_agent(
        self,
        api_client,
        sample_endpoint_data,
    ):
        headers = {
            'HTTP_USER_AGENT': '',
            'HTTP_X_FORWARDED_FOR': '*************',
        }
        data = sample_endpoint_data

        response = api_client.post(self.url, data, format='json', **headers)

        assert response.status_code == status.HTTP_201_CREATED
        assert response.data['consents'] == {
            'necessary': True,
            'performance': True,
            'functional': True,
            'advertising': True,
        }

    def test_post_cookie_consents_creates_when_no_http_x_forwarded_for(
        self,
        api_client,
        sample_endpoint_data,
    ):
        headers = {
            'HTTP_USER_AGENT': 'firefox-22',
            'HTTP_X_FORWARDED_FOR': '',
            'REMOTE_ADDR': '127.0.0.1',
        }
        data = sample_endpoint_data

        response = api_client.post(self.url, data, format='json', **headers)
        consent = CookieConsent.objects.last()

        assert response.status_code == status.HTTP_201_CREATED
        assert response.data['consents'] == {
            'necessary': True,
            'performance': True,
            'functional': True,
            'advertising': True,
        }
        assert consent.visitor_ip == '127.0.0.1'

    @pytest.mark.parametrize(
        'remote_addr, http_x_forwarded_for, expected_ip',  # noqa: PT006
        [
            ('', '', ''),
            ('', '*************', '*************'),
            ('127.0.0.1', '', '127.0.0.1'),
            ('127.0.0.1', '*************', '*************'),
            ('127.0.0.1', '*************,*************', '*************'),
        ],
    )
    def test_get_visitor_ip(
        self,
        remote_addr,
        http_x_forwarded_for,
        expected_ip,
        api_rf,
        sample_endpoint_data,
    ):
        headers = {
            'HTTP_USER_AGENT': 'firefox-22',
            'REMOTE_ADDR': remote_addr,
            'HTTP_X_FORWARDED_FOR': http_x_forwarded_for,
        }
        data = sample_endpoint_data

        request = api_rf.post(self.url, data, format='json', **headers)

        assert CookieConsentCreateView.get_visitor_ip(request) == expected_ip

    def test_save_advertising_consent_to_session(
        self,
        api_client,
        sample_endpoint_data,
    ):
        headers = {
            'HTTP_USER_AGENT': 'firefox-22',
            'HTTP_X_FORWARDED_FOR': '*************',
        }
        data = sample_endpoint_data
        response = api_client.post(self.url, data, format='json', **headers)

        assert response.status_code == status.HTTP_201_CREATED
        assert api_client.session.get('advertising_consents') is True
