import logging

from django.contrib import admin
from django.contrib.auth.admin import (
    GroupAdmin,
    UserAdmin,
)
from django.contrib.auth.models import (
    Group,
    User,
)
from django.contrib.contenttypes.models import ContentType
from django.urls import path
from django.utils.html import (
    escape,
    format_html,
)
from django.utils.safestring import mark_safe
from rest_framework.authtoken.admin import TokenAdmin
from rest_framework.authtoken.models import TokenProxy

from user_profile.admin.forms import GroupAdminWithUsersForm
from user_profile.admin.views import BrazeEnricherAdminFormView
from user_profile.models import (
    UserLibraries,
    UserProfile,
)
from user_profile.tasks import anonymize_users

logger = logging.getLogger('cstm')


class UserProfileInline(admin.TabularInline):
    model = UserProfile
    raw_id_fields = ('registration_guest',)


class UserCustomAdmin(UserAdmin):
    show_full_result_count = False

    inlines = [UserProfileInline]
    list_display = UserAdmin.list_display + (  # noqa: RUF005
        'is_superuser',
        'profile_type',
    )
    list_filter = ('profile__user_type', 'is_superuser', 'is_staff')
    search_fields = ('username', 'email', 'id')

    actions = ['anonymize_users']

    @admin.action(description='Anonimyze users')
    def anonymize_users(self, request, queryset):
        return anonymize_users.delay(list(queryset.values_list('id', flat=True)))

    def profile_type(self, obj):
        return str(obj.profile.get_user_type_display())

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('profile')


class UserCustomProfileAdmin(admin.ModelAdmin):
    show_full_result_count = False

    change_form_template = 'admin/userlibraries_change_form.html'
    change_list_template = 'admin/userprofile/change_list.html'
    list_display = (
        'id',
        'first_name',
        'last_name',
        'invoice_first_name',
        'invoice_last_name',
        'user_type',
        'is_business_type',
        'user',
        'registration_source',
    )
    list_filter = (
        'user_type',
        'is_business_type',
        'registration_source',
    )
    search_fields = (
        'id',
        'first_name',
        'last_name',
        'invoice_company_name',
        'user__id',
    )
    raw_id_fields = ('user', 'registration_guest')

    def get_urls(self):
        return [  # noqa: RUF005
            path(
                'braze_enricher/',
                self.admin_site.admin_view(BrazeEnricherAdminFormView.as_view()),
                name='braze_csv_enricher',
            ),
        ] + super().get_urls()


class UserLibrariesAdmin(admin.ModelAdmin):
    show_full_result_count = False

    list_display = (
        'get_user_id_with_link',
        'get_user_username',
        'get_contact_info_summary',
        'activities',
        'region',
        'registration_source',
        'registration_referrer',
        'registration_referrer_uri',
    )
    list_filter = ('user_type', 'has_mobile_app')
    list_display_links = ('get_user_username',)
    search_fields = ['user__username', 'email', 'invoice_email', 'user__id']
    raw_id_fields = ('user', 'registration_guest')
    list_select_related = ('region', 'user')

    def activities(self, obj):
        return format_html(
            'First contact: {} <br/>Register: {} <br/>Last Login: {}',
            obj.registration_first_contact,
            obj.user.date_joined.strftime('%Y-%m-%d %H:%M'),
            obj.user.last_login.strftime('%Y-%m-%d %H:%M')
            if obj.user.last_login is not None
            else 'never',
        )

    @mark_safe  # noqa: S308
    def get_user_username(self, obj):
        return '<a href="/admin/user_profile/userprofile/{}/">%{}a>'.format(
            obj.id,
            escape(obj.user.username),
        )

    @mark_safe  # noqa: S308
    def get_user_id_with_link(self, obj):
        return '<a href="/admin/user_profile/userprofile/{}/">{}</a>'.format(
            obj.id,
            obj.user.id,
        )

    get_user_id_with_link.short_description = 'User id'
    get_user_id_with_link.admin_order_field = 'user_id'

    @mark_safe  # noqa: S308
    def get_contact_info_summary(self, obj):
        return '{} {} <br/> {} {}'.format(
            escape(obj.first_name if obj.first_name is not None else ''),
            escape(obj.last_name if obj.last_name is not None else ''),
            escape(obj.email if obj.email is not None else ''),
            escape(obj.company_name if obj.company_name is not None else ''),
        )


class CustomTokenAdmin(TokenAdmin):
    raw_id_fields = ('user',)


class GroupAdminWithUsers(GroupAdmin):
    form = GroupAdminWithUsersForm
    filter_horizontal = ['permissions']


admin.site.register(User, UserCustomAdmin)
admin.site.register(Group, GroupAdminWithUsers)
admin.site.register(UserProfile, UserCustomProfileAdmin)
admin.site.register(ContentType)
admin.site.register(UserLibraries, UserLibrariesAdmin)
admin.site.unregister(TokenProxy)
admin.site.register(TokenProxy, CustomTokenAdmin)
