from django.db import models


class AddressDataAbstractModel(models.Model):
    first_name = models.Char<PERSON><PERSON>('first name', max_length=256, default='', blank=True)
    last_name = models.Char<PERSON>ield('last name', max_length=256, null=True, blank=True)  # noqa: DJ001

    email = models.Char<PERSON>ield('email', max_length=256, null=True, blank=True)  # noqa: DJ001
    phone = models.CharField('phone number', max_length=30, null=True, blank=True)  # noqa: DJ001
    phone_prefix = models.Char<PERSON>ield('phone prefix', max_length=4, null=True, blank=True)  # noqa: DJ001

    company_name = models.Char<PERSON>ield(  # noqa: DJ001
        'company name',
        max_length=256,
        null=True,
        blank=True,
    )
    street_address_1 = models.CharField(  # noqa: DJ001
        'street address 1',
        max_length=256,
        null=True,
        blank=True,
    )
    street_address_2 = models.CharField(  # noqa: DJ001
        'street address 2',
        max_length=256,
        null=True,
        blank=True,
    )
    city = models.Char<PERSON>ield('city', max_length=256, null=True, blank=True)  # noqa: DJ001
    postal_code = models.Char<PERSON>ield('postal code', max_length=20, null=True, blank=True)  # noqa: DJ001
    country = models.CharField('country', max_length=50, null=True, blank=True)  # noqa: DJ001
    country_area = models.CharField(  # noqa: DJ001
        'country administrative area',
        max_length=128,
        null=True,
        blank=True,
    )
    vat = models.CharField('tax id (vat)', max_length=256, null=True, blank=True)  # noqa: DJ001

    invoice_last_name = models.CharField(  # noqa: DJ001
        'invoice last name',
        max_length=256,
        null=True,
        blank=True,
    )
    invoice_first_name = models.CharField(  # noqa: DJ001
        'invoice first name',
        max_length=256,
        null=True,
        blank=True,
    )
    invoice_email = models.CharField(  # noqa: DJ001
        'invoice email',
        max_length=256,
        null=True,
        blank=True,
    )
    invoice_company_name = models.CharField(  # noqa: DJ001
        'invoice company name',
        max_length=256,
        null=True,
        blank=True,
    )
    invoice_street_address_1 = models.CharField(  # noqa: DJ001
        'invoice street address 1',
        max_length=256,
        null=True,
        blank=True,
    )
    invoice_street_address_2 = models.CharField(  # noqa: DJ001
        'invoice street address 2',
        max_length=256,
        null=True,
        blank=True,
    )
    invoice_city = models.CharField(  # noqa: DJ001
        'invoice city',
        max_length=256,
        null=True,
        blank=True,
    )
    invoice_postal_code = models.CharField(  # noqa: DJ001
        'invoice postal code',
        max_length=20,
        null=True,
        blank=True,
    )
    invoice_country = models.CharField(  # noqa: DJ001
        'invoice country',
        max_length=50,
        null=True,
        blank=True,
    )
    invoice_country_area = models.CharField(  # noqa: DJ001
        'invoice country administrative area',
        max_length=128,
        null=True,
        blank=True,
    )
    invoice_vat = models.CharField(  # noqa: DJ001
        'invoice tax id (vat)',
        max_length=256,
        null=True,
        blank=True,
    )

    ADDRESS_FIELDS = (
        'email',
        'phone',
        'phone_prefix',
        'first_name',
        'last_name',
        'company_name',
        'street_address_1',
        'street_address_2',
        'city',
        'postal_code',
        'country',
        'country_area',
        'vat',
    )

    INVOICE_ADDRESS_FIELDS = (
        'invoice_email',
        'invoice_first_name',
        'invoice_last_name',
        'invoice_company_name',
        'invoice_street_address_1',
        'invoice_street_address_2',
        'invoice_city',
        'invoice_postal_code',
        'invoice_country',
        'invoice_country_area',
        'invoice_vat',
    )

    class Meta:
        abstract = True

    def address_from_invoice_fields(self):
        return {
            'email': self.invoice_email,
            'phone': '',
            'phone_prefix': '',
            'first_name': self.invoice_first_name,
            'last_name': self.invoice_last_name,
            'company_name': self.invoice_company_name,
            'street_address_1': self.invoice_street_address_1,
            'street_address_2': self.invoice_street_address_2,
            'city': self.invoice_city,
            'postal_code': self.invoice_postal_code,
            'country': self.invoice_country,
            'country_area': self.invoice_country_area,
            'vat': self.invoice_vat,
        }

    def address_from_order_fields(self):
        return {
            'email': self.email,
            'phone': self.phone,
            'phone_prefix': self.phone_prefix,
            'first_name': self.first_name,
            'last_name': self.last_name,
            'company_name': self.company_name,
            'street_address_1': self.street_address_1,
            'street_address_2': self.street_address_2,
            'city': self.city,
            'postal_code': self.postal_code,
            'country': self.country,
            'country_area': self.country_area,
            'vat': self.vat,
        }

    @property
    def different_billing_address(self) -> bool:
        different_name = (
            self.invoice_first_name and self.first_name != self.invoice_first_name
        )
        different_last_name = (
            self.invoice_last_name and self.last_name != self.invoice_last_name
        )
        different_city = (
            self.invoice_city and self.city and self.invoice_city != self.city
        )
        different_street_address_1 = (
            self.street_address_1
            and self.invoice_street_address_1
            and self.street_address_1 != self.invoice_street_address_1
        )
        different_street_address_2 = (
            self.street_address_2
            and self.invoice_street_address_2
            and self.street_address_2 != self.invoice_street_address_2
        )
        different_postal_code = (
            self.postal_code
            and self.invoice_postal_code
            and self.postal_code != self.invoice_postal_code
        )
        different_country = (
            self.country
            and self.invoice_country
            and self.country != self.invoice_country
        )
        return any(
            (
                different_name,
                different_last_name,
                different_city,
                different_street_address_1,
                different_street_address_2,
                different_postal_code,
                different_country,
            )
        )
