from functools import cached_property
from typing import (
    TYPE_CHECKING,
    Optional,
)

from django.conf import settings
from django.contrib.auth import get_user_model
from django.urls import reverse
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from rest_framework import serializers
from rest_framework.request import Request
from rest_framework.serializers import ValidationError

from carts.models import Cart
from carts.services.cart_service import CartService
from checkout.serializers import CheckoutAddressSerializer
from checkout.templatetags.checkout_tags import get_typeform_url
from custom.api_clients.braze import BrazeClient
from custom.context_processors.settings_cp import (
    get_user_email_hash,
    get_user_email_hash_no_hmac,
    get_user_hash,
)
from custom.enums import LanguageEnum
from events.utils import hash_normalized_string
from invoice.models import Invoice
from mailing.models import MailingCustomer
from orders.models import (
    Order,
    OrderItem,
)
from regions.models import Region
from regions.utils import reverse_with_region
from user_profile.choices import (
    SubscriptionSources,
    UserType,
)
from user_profile.constants import (
    FACEBOOK_COOKIE_NAME,
    FACEBOOK_UTM,
    INVESTORS_GROUP_NAME,
)
from user_profile.models import (
    PasswordResetToken,
    UserProfile,
)
from user_profile.services.subscription_logic import is_subscription_override_forbidden

if TYPE_CHECKING:
    from abtests.models import ABTest

User = get_user_model()


class ProfileSerializer(serializers.ModelSerializer):
    user_type = serializers.ReadOnlyField(source='get_user_type_display')
    username = serializers.ReadOnlyField(source='user.username')
    region_name = serializers.ReadOnlyField(source='region.name')
    currency_code = serializers.ReadOnlyField(source='region.currency.code')
    currency_symbol = serializers.ReadOnlyField(source='region.currency.symbol')
    invoice_address_used = serializers.BooleanField(
        source='different_billing_address',
        read_only=True,
    )

    class Meta(object):
        model = UserProfile
        read_only_fields = ('updated_at',)
        fields = (
            'username',
            'user_type',
            'updated_at',
            'account_name',
            'email',
            'first_name',
            'last_name',
            'gender',
            'company_name',
            'street_address_1',
            'street_address_2',
            'city',
            'postal_code',
            'country',
            'country_area',
            'phone',
            'vat',
            'notes',
            'company_name',
            'invoice_company_name',
            'invoice_first_name',
            'invoice_last_name',
            'invoice_email',
            'invoice_address_used',
            'invoice_company_name',
            'invoice_street_address_1',
            'invoice_street_address_2',
            'invoice_city',
            'invoice_postal_code',
            'invoice_country',
            'invoice_country_area',
            'invoice_vat',
            'invoice_address_used',
            'newsletter_agreed',
            'language',
            'region_name',
            'currency_code',
            'currency_symbol',
        )


class CheckoutInitialFormSerializer(serializers.ModelSerializer):
    sms = serializers.BooleanField(source='sms_agreed')
    newsletter = serializers.BooleanField(source='newsletter_agreed')
    locale = serializers.CharField(source='get_locale', read_only=True)
    invoice_address_used = serializers.BooleanField(
        source='different_billing_address',
        read_only=True,
    )
    currency_code = serializers.SerializerMethodField()
    criteo_email = serializers.SerializerMethodField()
    criteo_account = serializers.SerializerMethodField()
    floor_number = serializers.SerializerMethodField()
    no_elevator = serializers.SerializerMethodField()
    notes = serializers.SerializerMethodField()
    order_id = serializers.SerializerMethodField()
    postcheckout_survey_url = serializers.SerializerMethodField()
    is_profile_complete = serializers.SerializerMethodField()

    class Meta:
        model = UserProfile
        fields = (
            'email',
            'first_name',
            'last_name',
            'street_address_1',
            'street_address_2',
            'city',
            'phone_prefix',
            'phone',
            'postal_code',
            'country',
            'floor_number',
            'no_elevator',
            'notes',
            'vat',
            'invoice_company_name',
            'invoice_first_name',
            'invoice_last_name',
            'invoice_street_address_1',
            'invoice_address_used',
            'invoice_postal_code',
            'invoice_city',
            'invoice_country',
            'company_name',
            'sms',
            'newsletter',
            'currency_code',
            'locale',
            'criteo_email',
            'criteo_account',
            'order_id',
            'postcheckout_survey_url',
            'is_profile_complete',
        )

    @staticmethod
    def get_currency_code(obj: UserProfile) -> str:
        return obj.get_region().currency.code

    @staticmethod
    def get_criteo_email(obj: UserProfile) -> Optional[str]:
        if email := obj.user.email:
            return hash_normalized_string(email)

    @staticmethod
    def get_criteo_account(obj: UserProfile) -> int:
        language_accounts = {
            LanguageEnum.DE: 35393,
            LanguageEnum.FR: 48007,
        }
        return language_accounts.get(obj.language, 42503)

    def get_floor_number(self, _) -> Optional[int]:
        if order := self.context.get('order'):
            return order.floor_number

    def get_no_elevator(self, _) -> Optional[bool]:
        if order := self.context.get('order'):
            return order.no_elevator

    def get_notes(self, _) -> str:
        if order := self.context.get('order'):
            return order.notes

        return ''

    def get_order_id(self, _) -> Optional[int]:
        if order := self.context.get('order'):
            return order.id

        return None

    def get_postcheckout_survey_url(self, obj: UserProfile) -> str:
        if order := self.context.get('order'):
            return get_typeform_url(
                language_code=obj.language, order=order, escape_js=False
            )

        return ''

    @staticmethod
    def get_is_profile_complete(obj) -> bool:
        return CheckoutAddressSerializer.validate_for(obj)


class JSONWebTokenGuestSerializer(serializers.Serializer):
    device_identifier = serializers.CharField()


class EmailSerializer(serializers.Serializer):
    email = serializers.EmailField()

    def to_internal_value(self, data):
        data = super().to_internal_value(data)
        if 'email' in data:
            data['email'] = data['email'].lower()
        return data


class MinimalUserSerializer(serializers.Serializer):
    user_id = serializers.IntegerField()
    email = serializers.EmailField()

    def validate(self, data):
        user_id = data.get('user_id')
        try:
            user = User.objects.get(id=user_id)
        except User.DoesNotExist:
            raise ValidationError(
                {'Wrong user_id': f'The user with id {user_id} does not exist!'}
            )

        email = data.get('email', '').lower()
        user_email = getattr(user, 'email', None) or ''
        profile_email = getattr(user.profile, 'email', None) or ''
        if user_email.lower() != email and profile_email.lower() != email:
            raise ValidationError(
                {
                    'Wrong email': f'The email {email} does not belong to the user with'
                    f' id {user.id}'
                }
            )
        return data


class UserProfileB2BBigQuery(serializers.ModelSerializer):
    customer_id = serializers.ReadOnlyField(source='id')
    email_address = serializers.ReadOnlyField(source='email')
    is_b2b = serializers.BooleanField(source='is_business_type')
    bq_joined_time = serializers.DateTimeField(default=timezone.now)

    class Meta:
        fields = ['customer_id', 'email_address', 'is_b2b', 'bq_joined_time']
        model = UserProfile


class UserProfileAccountSerializer(serializers.ModelSerializer):
    email = serializers.EmailField(source='user.email', read_only=True)
    is_investor = serializers.SerializerMethodField()

    class Meta:
        fields = [
            'email',
            'first_name',
            'last_name',
            'street_address_1',
            'street_address_2',
            'city',
            'postal_code',
            'country',
            'phone',
            'company_name',
            'vat',
            'invoice_first_name',
            'invoice_last_name',
            'invoice_company_name',
            'invoice_street_address_1',
            'invoice_street_address_2',
            'invoice_city',
            'invoice_postal_code',
            'invoice_country',
            'invoice_vat',
            'language',
            'is_investor',
        ]
        model = UserProfile

    def get_is_investor(self, obj: UserProfile) -> bool:
        return obj.user.groups.filter(name=INVESTORS_GROUP_NAME).exists()


class OrderItemAccountSerializer(serializers.ModelSerializer):
    title = serializers.CharField(source='order_item.get_title')
    preview = serializers.CharField(source='order_item.preview.url')
    dimensions = serializers.SerializerMethodField()
    manual = serializers.SerializerMethodField()

    class Meta:
        model = OrderItem
        fields = ['title', 'preview', 'dimensions', 'manual']

    def get_dimensions(self, obj):
        return obj.order_item.get_item_description().get('dimensions')

    def get_manual(self, obj):
        product = obj.product_set.first()
        if not product:
            return None
        instruction = product.details.instruction
        return instruction.url if instruction else None


class InvoiceAccountSerializer(serializers.ModelSerializer):
    pdf = serializers.SerializerMethodField(source='pdf.url')

    class Meta:
        model = Invoice
        fields = ['pretty_id', 'pdf']

    def get_pdf(self, obj) -> str:
        return obj.pdf.url if obj.pdf else ''


class OrderAccountSerializer(serializers.ModelSerializer):
    visual_status = serializers.CharField(source='get_order_visual_status')
    order_status = serializers.CharField(source='get_status_display')
    check_status_link = serializers.SerializerMethodField()
    payment_link = serializers.SerializerMethodField()
    invoices = InvoiceAccountSerializer(default=None, many=True, source='get_invoices')
    items = OrderItemAccountSerializer(default=None, many=True, source='material_items')
    cart_id = serializers.IntegerField(source='cart.id')

    class Meta:
        model = Order
        fields = [
            'id',
            'created_at',
            'visual_status',
            'order_status',
            'check_status_link',
            'payment_link',
            'invoices',
            'items',
            'cart_id',
        ]

    def get_check_status_link(self, obj):
        return (
            f'{reverse("front-contact")}?topic=order_status&order_id={obj.id}&'
            f'postal_code={obj.postal_code}'
        )

    def get_payment_link(self, obj):
        if obj.is_payable():
            return reverse_with_region(
                'front-payment-method',
                obj.region.get_country(without_cache=True).code.lower(),
                args=[obj.id],
            )


class InsiderUserSerializer(serializers.ModelSerializer):
    braze_email_hash = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = ('email', 'braze_email_hash')

    @staticmethod
    def get_braze_email_hash(obj):
        return hash_normalized_string(obj.email) if obj.email else None

    def update(self, instance: User, validated_data: dict):
        instance.profile.email = validated_data['email']
        instance.profile.save(update_fields=['email'])
        return super().update(instance, validated_data)


class UserCreateSerializer(serializers.Serializer):
    email = serializers.EmailField(required=True)
    password = serializers.CharField(write_only=True)
    region = serializers.SlugRelatedField(
        queryset=Region.objects.all(),
        slug_field='name',
        required=True,
        write_only=True,
    )

    class Meta:
        fields = (
            'email',
            'password',
            'region',
        )

    def validate_email(self, value: str) -> str:
        if User.objects.filter(username=value).exists():
            raise serializers.ValidationError(f'User with {value} email already exists')
        return value

    def _update_profile(self, profile: UserProfile, region: Region, email: str) -> None:
        profile.email = email
        profile.region = region
        profile.country = region.country.name
        profile.language = LanguageEnum(region.default_for_language)
        profile.save(update_fields=['email', 'region', 'country', 'language'])

    def create(self, validated_data: dict) -> User:
        email = validated_data['email']
        user = User(username=email, email=email)
        user.set_password(validated_data['password'])
        user.save()
        self._update_profile(user.profile, validated_data['region'], email)
        return user


class UserFlatSerializer(serializers.ModelSerializer):
    """Flatten user and user profile serializer"""

    profile = ProfileSerializer()

    class Meta:
        model = User
        fields = (
            'id',
            'username',
            'profile',
        )

    def to_representation(self, obj):
        """Move fields from profile to user representation."""
        representation = super().to_representation(obj)
        profile_representation = representation.pop('profile', []) or []
        for key in profile_representation:
            representation[key] = profile_representation[key]

        return representation

    def to_internal_value(self, data):
        """Move fields related to profile to their own profile dictionary."""
        profile_internal = {
            key: data.pop(key) for key in ProfileSerializer.Meta.fields if key in data
        }
        return {
            'profile': profile_internal,
            **super().to_internal_value(data),
        }

    def update(self, instance, validated_data):
        profile_data = validated_data.pop('profile')
        super().update(instance, validated_data)

        profile = instance.profile
        for attr, value in profile_data.items():
            setattr(profile, attr, value)
        profile.save()

        return instance


class ResetPasswordSerializer(serializers.Serializer):
    token = serializers.CharField()
    password_1 = serializers.CharField()
    password_2 = serializers.CharField()

    def validate(self, data):
        if data['password_1'] != data['password_2']:
            raise serializers.ValidationError('Passwords do not match')
        return data

    @cached_property
    def _user(self) -> User:
        token = self.validated_data['token']
        try:
            return PasswordResetToken.objects.filter(token=token).last().user
        except AttributeError as e:
            raise serializers.ValidationError('Invalid token') from e

    def save(self, **kwargs) -> User:
        self._user.set_password(self.validated_data['password_1'])
        self._user.save()
        return self._user


class UserCartItemSerializer(serializers.ModelSerializer):
    class Meta:
        model = OrderItem
        fields = '__all__'


class UserCartSerializer(serializers.ModelSerializer):
    cart_items = UserCartItemSerializer(source='items', many=True)
    has_lighting = serializers.SerializerMethodField()
    library_items = serializers.SerializerMethodField()
    signed_in = serializers.SerializerMethodField()

    class Meta:
        model = Order
        fields = (
            'cart_items',
            'has_lighting',
            'library_items',
            'signed_in',
        )

    def get_has_lighting(self, obj):
        return any(getattr(item, 'lighting', False) for item in obj.items.all())

    # TODO look at get_library_item_number
    def get_library_items(self, obj):
        user = self.context['user']
        return user.profile.get_library_item_number() if user.is_authenticated else 0

    # TODO do we need that?
    def get_signed_in(self, obj):
        user = self.context['user']
        return 1 if user.is_authenticated else -1


class NewsletterSerializer(serializers.Serializer):
    email = serializers.EmailField(required=False, allow_null=True)
    phone_prefix = serializers.CharField(required=False, allow_null=True)
    phone = serializers.CharField(required=False, allow_null=True)
    source = serializers.ChoiceField(
        choices=SubscriptionSources.choices,
        default=SubscriptionSources.CONTENT,
    )

    def validate(self, validated_data):
        email = validated_data.get('email')
        if not email:
            return validated_data

        subscription_status = BrazeClient.get_email_subscription_group_status(
            settings.BRAZE_EMAIL_SUBSCRIPTION_GROUPS['newsletter'],
            email,
        )
        override_subscription_forbidden = is_subscription_override_forbidden(
            status=subscription_status, source=validated_data.get('source')
        )

        if validated_data['source'] == SubscriptionSources.NEWSLETTER_FOR_CUSTOMERS:
            is_returning_customer = MailingCustomer.objects.filter(email=email).exists()
            if not is_returning_customer:
                raise ValidationError(_('lp_promo_error'))

            elif override_subscription_forbidden:
                raise ValidationError(_('lp_promo_error_2'))

        elif override_subscription_forbidden:
            raise ValidationError(_('emily_footer_newsletter_alert_1'))

        return validated_data


class BaseUserGlobalSerializer(serializers.Serializer):
    global_session_id = serializers.SerializerMethodField()

    ab_ids = serializers.SerializerMethodField()
    ab_tests_list = serializers.SerializerMethodField()
    feature_flags_ids = serializers.SerializerMethodField()
    feature_flags_list = serializers.SerializerMethodField()
    comes_from_facebook = serializers.SerializerMethodField()

    class Meta:
        fields = [
            'global_session_id',
            'user_id',
            'user_type',
            'user_hash_id',
            'user_hash_email',
            'user_hash_email_no_hmac',
            'user_language',
            'is_signed_in',
            'cart_id',
            'order_id',
            'has_t03',
            'cart_items_count',
            'library_items_count',
            'ab_ids',
            'ab_tests_list',
            'feature_flags_ids',
            'feature_flags_list',
            'comes_from_facebook',
        ]

    @property
    def request(self) -> Request:
        return self.context['request']

    @property
    def cart(self) -> Optional[Cart]:
        return self.context['cart']

    @property
    def order(self) -> Optional['Order']:
        return self.context['order']

    @property
    def ab_tests(self) -> dict['ABTest', bool]:
        return self.context['ab_tests']

    @property
    def feature_flags(self) -> list['ABTest']:
        return self.context['feature_flags']

    def get_global_session_id(self, _) -> str:
        return self.request.COOKIES.get(
            'global_session_id',
            self.request.session.session_key,
        )

    def get_ab_ids(self, _) -> str:
        ab_ids = [f'{test.id}:{int(active)}' for test, active in self.ab_tests.items()]
        return ','.join(ab_ids)

    def get_ab_tests_list(self, _) -> list[list[str | bool]]:
        return [
            [f'{test.codename}|{test.rate_split}', active]
            for test, active in self.ab_tests.items()
        ]

    def get_feature_flags_ids(self, _) -> str:
        feature_flags_ids = [str(test.id) for test in self.feature_flags]
        return ','.join(feature_flags_ids)

    def get_feature_flags_list(self, _) -> list[str]:
        return [test.codename for test in self.feature_flags]

    def get_comes_from_facebook(self, _) -> bool:
        # can be deleted after meta_traffic_cta_order_reversed AB test is over
        fb_cookie = self.context.get('request').COOKIES.get(FACEBOOK_COOKIE_NAME)
        if fb_cookie == 'True':
            return True
        return FACEBOOK_UTM in self.context.get('request').META.get('HTTP_REFERER', '')


class AuthenticatedUserGlobalSerializer(
    BaseUserGlobalSerializer,
    serializers.ModelSerializer,
):
    user_id = serializers.IntegerField(source='id')
    user_type = serializers.IntegerField(source='profile.user_type')
    user_hash_id = serializers.SerializerMethodField()
    user_hash_email = serializers.SerializerMethodField()
    user_hash_email_no_hmac = serializers.SerializerMethodField()
    user_language = serializers.SerializerMethodField()
    is_signed_in = serializers.SerializerMethodField()

    cart_id = serializers.SerializerMethodField()
    order_id = serializers.SerializerMethodField()
    has_assembly = serializers.SerializerMethodField()
    has_corduroy = serializers.SerializerMethodField()
    has_s01 = serializers.SerializerMethodField()
    has_t03 = serializers.SerializerMethodField()
    cart_items_count = serializers.SerializerMethodField()
    library_items_count = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = BaseUserGlobalSerializer.Meta.fields + [  # noqa: RUF005
            'has_assembly',
            'has_corduroy',
            'has_s01',
        ]

    def get_user_hash_id(self, _) -> str:
        return get_user_hash(self.request)

    def get_user_hash_email(self, _) -> Optional[str]:
        return get_user_email_hash(self.request)

    def get_user_hash_email_no_hmac(self, _) -> Optional[str]:
        return get_user_email_hash_no_hmac(self.request)

    @staticmethod
    def get_user_language(obj) -> str:
        return obj.profile.language

    @staticmethod
    def get_is_signed_in(obj: User) -> bool:
        return obj.profile.user_type in UserType.get_signed_in_types()

    def get_cart_id(self, _) -> int:
        return self.cart.id if self.cart else None

    def get_order_id(self, _) -> int:
        return self.order.id if self.order else None

    def get_has_assembly(self, _) -> bool:
        return self.cart and self.cart.assembly

    def get_has_corduroy(self, _) -> bool:
        if not self.cart:
            return False

        return CartService(cart=self.cart).has_corduroy

    def get_has_s01(self, _) -> bool:
        if not self.cart:
            return False

        return CartService(cart=self.cart).has_s01

    def get_has_t03(self, _) -> bool:
        if not self.cart:
            return False

        return CartService(cart=self.cart).has_t03

    def get_cart_items_count(self, _) -> int:
        if not self.cart:
            return 0
        return CartService(cart=self.cart).get_cart_size()

    @staticmethod
    def get_library_items_count(obj: User) -> int:
        return obj.profile.get_library_item_number()

    def get_comes_from_facebook(self, obj: User) -> bool:
        # for users that came from FB before we started setting the spying cookie
        if result := super().get_comes_from_facebook(obj):
            return result
        registration_referrer_uri = (
            getattr(obj.profile, 'registration_referrer_uri', '') or ''
        )
        return 'utm_source=facebook' in registration_referrer_uri


class AnonymousUserGlobalSerializer(BaseUserGlobalSerializer):
    user_id = serializers.IntegerField(allow_null=True)
    user_type = serializers.CharField(default='')
    user_hash_id = serializers.CharField(default='')
    user_hash_email = serializers.CharField(default='')
    user_language = serializers.SerializerMethodField()
    is_signed_in = serializers.BooleanField(default=False)

    cart_id = serializers.CharField(default='')
    order_id = serializers.CharField(default='')
    has_t03 = serializers.BooleanField(default=0)
    cart_items_count = serializers.IntegerField(default=0)
    library_items_count = serializers.IntegerField(default=0)

    def get_user_language(self, _) -> str:
        return LanguageEnum(self.request.session['cached_language'])
