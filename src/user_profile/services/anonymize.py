from django.contrib.auth import get_user_model
from django.utils import timezone

from events.domain_events.marketing_events import MarketingExcludeUpdateEvent

User = get_user_model()

ANONYMIZE_VALUE = 'anonymize'

USER_FIELDS_TO_ANONYMIZE: list[str] = [
    'first_name',
    'last_name',
    'email',
]

USER_PROFILE_FIELDS_TO_ANONYMIZE: list[str] = [
    'first_name',
    'last_name',
    'email',
    'phone',
    'company_name',
    'street_address_1',
    'street_address_2',
    'city',
    'postal_code',
    'country',
    'country_area',
    'vat',
    'notes',
    'invoice_company_name',
    'invoice_first_name',
    'invoice_last_name',
    'invoice_email',
    'invoice_street_address_1',
    'invoice_street_address_2',
    'invoice_city',
    'invoice_postal_code',
    'invoice_country',
]


def anonymize_user(user: User) -> None:
    for field_name in USER_FIELDS_TO_ANONYMIZE:
        setattr(user, field_name, ANONYMIZE_VALUE)

    user.username = f'anon_{user.id}'
    user.is_active = False

    for field_name in USER_PROFILE_FIELDS_TO_ANONYMIZE:
        setattr(user.profile, field_name, ANONYMIZE_VALUE)

    user.save(update_fields=USER_FIELDS_TO_ANONYMIZE + ['username', 'is_active'])  # noqa: RUF005
    user.profile.save(update_fields=USER_PROFILE_FIELDS_TO_ANONYMIZE)
    user.carts.all().delete()

    MarketingExcludeUpdateEvent(
        user=user,
        complaint_active=False,
        last_finished_complaint_date=timezone.now(),
    )
