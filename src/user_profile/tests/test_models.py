from datetime import timedelta

from django.utils import timezone

import pytest

from freezegun import freeze_time

from user_profile.models import LoginAccessToken


@pytest.mark.django_db
class TestLoginAccessToken:
    def test_create_for_super_user(self, user_factory):
        admin_user = user_factory(is_superuser=True)
        token = LoginAccessToken.get_or_create_for_user(admin_user)
        assert token == ''

    def test_create_for_staff_user(self, user_factory):
        staff_user = user_factory(username='superuser', is_staff=True)
        token = LoginAccessToken.get_or_create_for_user(staff_user)
        assert token == ''

    def test_create_for_user_with_existing_valid_token(
        self,
        user,
        login_access_token_factory,
    ):
        with freeze_time(timezone.now() - timedelta(weeks=6)):
            login_access_token = login_access_token_factory(user=user)

        token = LoginAccessToken.get_or_create_for_user(user)
        assert token == login_access_token.token

    def test_create_for_user_with_existing_invalid_token(
        self,
        user,
        login_access_token_factory,
    ):
        with freeze_time(timezone.now() - timedelta(weeks=12)):
            login_access_token = login_access_token_factory(user=user)

        token = LoginAccessToken.get_or_create_for_user(user)
        assert token != login_access_token.token
        assert LoginAccessToken.objects.filter(user=user).count() == 2

    @pytest.mark.parametrize(('created_weeks_ago', 'valid'), ((10, False), (6, True)))  # noqa: PT007
    def test_token_valid(
        self,
        created_weeks_ago,
        valid,
        user,
        login_access_token_factory,
    ):
        with freeze_time(timezone.now() - timedelta(weeks=created_weeks_ago)):
            login_access_token = login_access_token_factory(user=user)

        assert login_access_token.is_token_valid() is valid

    @pytest.mark.parametrize(
        ('created_weeks_ago', 'should_create_new'),
        ((5, False), (3, True)),  # noqa: PT007
    )
    def test_should_create_new_token(
        self,
        created_weeks_ago,
        should_create_new,
        user,
        login_access_token_factory,
    ):
        with freeze_time(timezone.now() - timedelta(weeks=created_weeks_ago)):
            login_access_token = login_access_token_factory(user=user)

        assert login_access_token.should_create_new_token() is should_create_new
