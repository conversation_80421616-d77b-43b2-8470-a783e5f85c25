from django.urls import path
from django.views.decorators.csrf import csrf_exempt
from rest_framework import routers

from user_profile.views import (
    ForgottenPasswordInit,
    InsiderUserAPIView,
    LoginAccessTokenCreateView,
    MailingUserWishlistAPIView,
    NewsletterAddView,
    ResetPasswordAPIView,
    RetoolUserAPIViewSet,
    UserAccountProfileView,
    UserOrderListView,
)
from user_profile.views.views import (
    EmailCheckAPIView,
    UserGlobalAPIView,
)

urlpatterns = [
    path(
        'v1/forgotten_password/',
        ForgottenPasswordInit.as_view(),
        name='rest_reset_password_init',
    ),
    path(
        'v1/login-access-token/',
        LoginAccessTokenCreateView.as_view(),
        name='login-access-token',
    ),
    path(
        'v2/account/profile/',
        UserAccountProfileView.as_view(),
        name='account-profile',
    ),
    path('v2/account/orders/', UserOrderListView.as_view(), name='account-orders'),
    path(
        'v1/user/<int:pk>/mailing-wishlist/',
        MailingUserWishlistAPIView.as_view(),
        name='mailing-user-wishlist',
    ),
    path(
        'v2/users/check-email/', EmailCheckAPIView.as_view(), name='users-check-email'
    ),
    path(
        'v1/auth/user/<pk>/',
        InsiderUserAPIView.as_view(),
        name='insider-user-view',
    ),
    path(
        'v1/reset_password/',
        ResetPasswordAPIView.as_view(),
        name='reset-password',
    ),
    path(
        'v1/newsletter/',
        csrf_exempt(NewsletterAddView.as_view()),
        name='rest_add_newsletter',
    ),
    path('v1/user-global/', UserGlobalAPIView.as_view(), name='user-global'),
]

router = routers.SimpleRouter()
router.register('v2/users', RetoolUserAPIViewSet)
urlpatterns += router.urls
