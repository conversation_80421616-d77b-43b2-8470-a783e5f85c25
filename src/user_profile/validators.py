from django.core.exceptions import ValidationError
from django.core.validators import EmailValidator


class BusinessEmailValidator:
    BUSINESS_EMAIL_PREFIXES = [
        'hello',
        'contact',
        'kontakt',
        'info',
        'office',
        'hello',
        'hallo',
        'shopping',
        'tylko',
        'tylco',
        'mail',
        'bestellung',
        'welcome',
        'purchase',
        'hi',
        'order',
        'accounts',
        'post',
    ]

    BUSINESS_DOMAINS = ['design', 'studio', 'architect', 'architekt']

    def __call__(self, email):
        if email is None:
            return False

        email = email.lower()
        is_valid = validate_email(email)
        if not is_valid:
            return False

        user_part, domain_part = email.rsplit('@', 1)
        is_business_prefix = self.is_business_prefix_or_domain(
            user_part, self.BUSINESS_EMAIL_PREFIXES
        )
        if is_business_prefix:
            return True

        is_business_domain = self.is_business_prefix_or_domain(
            domain_part, self.BUSINESS_DOMAINS
        )
        return is_business_domain

    def is_business_prefix_or_domain(self, email_part, patterns):
        return any([prefix in email_part for prefix in patterns])  # noqa: C419


def validate_email(email):
    try:
        EmailValidator()(email)
        return True
    except ValidationError:
        return False
