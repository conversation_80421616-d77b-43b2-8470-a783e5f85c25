from django.contrib.auth.models import User
from rest_framework import status
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework.views import APIView

from events.choices import BrazeSubscriptionStates
from events.services.subscription_event_handler import (
    EmailSubscriptionData,
    EmailSubscriptionEventHandler,
    SmsSubscriptionData,
    SmsSubscriptionEventHandler,
)
from events.utils import hash_normalized_string
from user_profile.choices import SubscriptionSources
from user_profile.decorators import create_and_login_user
from user_profile.serializers import NewsletterSerializer


class NewsletterAddView(APIView):
    serializer_class = NewsletterSerializer
    permission_classes = [AllowAny]

    @create_and_login_user()
    def post(self, request, *args, **kwargs):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        email = serializer.validated_data.get('email')
        source = SubscriptionSources(serializer.validated_data['source'])

        self.process_subscription(
            request.user,
            source,
            email=email,
            phone_prefix=serializer.validated_data.get('phone_prefix'),
            phone_number=serializer.validated_data.get('phone'),
        )

        return Response(status=status.HTTP_200_OK)

    @staticmethod
    def process_subscription(
        user: User,
        source: SubscriptionSources,
        email: str = None,  # noqa: RUF013
        phone_prefix: str = None,  # noqa: RUF013
        phone_number: str = None,  # noqa: RUF013
    ) -> None:
        if user.is_anonymous:
            return

        external_id = None
        create_new_profile = True

        if email:
            create_new_profile = False
            external_id = hash_normalized_string(email)
            subscription_handler = EmailSubscriptionEventHandler(
                user=user,
                external_id=external_id,
                check_subscription=False,
                subscription_data=EmailSubscriptionData.create_for_newsletter(
                    email=email, source=source
                ),
            )
            subscription_handler.handle()

        if phone_prefix and phone_number:
            subscription_data = SmsSubscriptionData(
                source=source,
                phone_prefix=phone_prefix,
                phone_number=phone_number,
                group_state=BrazeSubscriptionStates.SUBSCRIBED,
            )
            if not external_id:
                user_email = user.email or user.profile.email
                if user_email:
                    external_id = hash_normalized_string(user_email)
                    create_new_profile = False
                else:
                    external_id = hash_normalized_string(subscription_data.full_phone)

            subscription_handler = SmsSubscriptionEventHandler(
                user=user,
                external_id=external_id,
                subscription_data=subscription_data,
                check_subscription=False,
                create_new_profile=create_new_profile,
            )
            subscription_handler.handle()
