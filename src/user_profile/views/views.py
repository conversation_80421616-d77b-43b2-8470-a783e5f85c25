import hashlib
import logging

from django.contrib.auth.models import (
    AnonymousUser,
    User,
)
from django.db.models import (
    Q,
    QuerySet,
    prefetch_related_objects,
)
from django.utils import translation
from django.utils.encoding import force_bytes
from rest_framework import status
from rest_framework.authentication import (
    SessionAuthentication,
    TokenAuthentication,
)
from rest_framework.authtoken.models import Token
from rest_framework.generics import (
    CreateAPIView,
    GenericAPIView,
    ListAPIView,
    RetrieveAPIView,
    RetrieveUpdateAPIView,
)
from rest_framework.pagination import PageNumberPagination
from rest_framework.permissions import (
    AllowAny,
    IsAdminUser,
    IsAuthenticated,
)
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.viewsets import ModelViewSet

from drf_spectacular.utils import extend_schema

from abtests.utils import get_sorted_active_ab_tests_data
from carts.choices import CartStatusChoices
from carts.services.cart_service import CartService
from custom.enums import LanguageEnum
from custom.permissions import BrazePermission
from customer_service.models import CSUserProfile
from ecommerce_api.mixins import EcommerceAPIMixin
from events.domain_events.marketing_events import (
    ChangePasswordRequestEvent,
    LanguageUpdateEvent,
    UserContactDataUpdateEvent,
)
from gallery.serializers import MailingSavedFurnitureSerializer
from mailing.templates import ForgottenPasswordMail
from orders.enums import OrderStatus
from user_profile.models import (
    AccessToken,
    LoginAccessToken,
    PasswordResetToken,
)
from user_profile.serializers import (
    AnonymousUserGlobalSerializer,
    AuthenticatedUserGlobalSerializer,
    EmailSerializer,
    InsiderUserSerializer,
    MinimalUserSerializer,
    OrderAccountSerializer,
    ResetPasswordSerializer,
    UserCreateSerializer,
    UserFlatSerializer,
    UserProfileAccountSerializer,
)

logger = logging.getLogger('cstm')


def hash_device_id(device_id):
    return hashlib.sha1(force_bytes(device_id)).hexdigest()[:30]  # noqa: S324


class ForgottenPasswordInit(GenericAPIView):
    """Endpoint for requesting password reset.

    Creates a token for resetting the password and sends an email message to the given
    email with the link containing the created token enabling password reset.

    To avoid account enumeration attack we'll respond with 200 even if the email
    doesn't exist in the database.
    """

    permission_classes = ()
    serializer_class = EmailSerializer

    @extend_schema(responses={status.HTTP_200_OK: {}})
    def post(self, request, html_data=None):
        if html_data is not None:
            request.data = html_data
        serializer = self.get_serializer(data=request.data)
        if not serializer.is_valid():
            logger.error(
                'Reset password email was not sent, errors: %s', serializer.errors
            )
            return Response({}, status=status.HTTP_400_BAD_REQUEST)

        email = serializer.data['email']
        try:
            user = User.objects.get(username__iexact=email)
        except User.DoesNotExist:
            return Response({}, status=status.HTTP_200_OK)
        token = PasswordResetToken(user=user)
        token.token = AccessToken.create_token()
        token.save()
        ChangePasswordRequestEvent(
            user=user,
            email=email,
            password_reset_token=token.token,
        )
        mail = ForgottenPasswordMail(email, {'token': token.token})
        mail.send()
        return Response({}, status=status.HTTP_200_OK, content_type='application/json')


class ResetPasswordAPIView(APIView):
    serializer_class = ResetPasswordSerializer
    permission_classes = ()

    def _cleanup(self, user: User) -> None:
        LoginAccessToken.objects.filter(user=user).delete()
        # FIXME uncomment after we fix sessions
        # task_delete_user_sessions.delay(user.id)

    def post(self, request, **kwargs) -> Response:
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        user = serializer.save()
        self._cleanup(user)
        return Response({}, status=status.HTTP_200_OK)


class LoginAccessTokenCreateView(CreateAPIView):
    authentication_classes = (TokenAuthentication,)
    permission_classes = (BrazePermission,)
    serializer_class = MinimalUserSerializer

    def create(self, request, *args, **kwargs):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        user_id = serializer.data.get('user_id')
        user = User.objects.get(pk=user_id)
        login_access_token = LoginAccessToken.get_or_create_for_user(user)
        result = {'LoginAccessToken': login_access_token}
        return Response(result, status=status.HTTP_201_CREATED)


class UserAccountProfileView(RetrieveUpdateAPIView):
    """Endpoint with user data for user account

    Used for getting user's data and updating it except email.
    """

    serializer_class = UserProfileAccountSerializer
    permission_classes = (AllowAny,)

    def get_object(self):
        return self.request.user.profile

    def get(self, request, *args, **kwargs):
        if request.user.is_anonymous:
            return Response(status=status.HTTP_401_UNAUTHORIZED)
        return super().get(request, *args, **kwargs)

    def update(self, *args, **kwargs):
        result = super().update(*args, **kwargs)

        UserContactDataUpdateEvent(
            user=self.request.user,
            first_name=self.request.data.get('first_name', ''),
            last_name=self.request.data.get('last_name', ''),
            phone_number=self.request.data.get('phone', ''),
            phone_prefix=self.request.data.get('phone_prefix', ''),
            postal_code=self.request.data.get('postal_code', ''),
        )

        # After updating profile data, we need to copy it to all orders
        # in cart and draft status
        if orders := self.request.user.order_set.filter(
            status__in=[OrderStatus.CART, OrderStatus.DRAFT]
        ):
            for order in orders:
                order.copy_address_from_profile()
        return result

    def partial_update(self, request, *args, **kwargs):
        language_code = request.data.get('language')
        if request.user.is_anonymous:
            request.session['cached_language'] = language_code
            return Response(status=status.HTTP_200_OK)

        response = super().partial_update(request, *args, **kwargs)
        if language_code:
            self._handle_language_update(language_code)

        return response

    def _handle_language_update(self, language_code: str) -> None:
        LanguageUpdateEvent(user=self.request.user, language=language_code)


class UserOrderListView(ListAPIView):
    serializer_class = OrderAccountSerializer
    permission_classes = (IsAuthenticated,)

    def get_queryset(self):
        return self.request.user.order_set.prefetch_related(
            'items',
            'items__order_item',
        ).exclude(
            Q(status__in=[OrderStatus.CANCELLED, OrderStatus.CART])
            | Q(cart__status=CartStatusChoices.ACTIVE)
        )


class MailingUserWishlistAPIView(RetrieveAPIView):
    """View for retrieving serialization of saved furniture owned by specific user."""

    authentication_classes = (TokenAuthentication,)
    permission_classes = (BrazePermission,)
    serializer_class = MailingSavedFurnitureSerializer
    queryset = User.objects.select_related('profile', 'profile__region')

    def dispatch(self, request, *args, **kwargs):
        self.object = self.get_object()
        return super().dispatch(request, *args, **kwargs)

    def get_serializer_context(self):
        return {
            'region': self.object.profile.region,
            **super().get_serializer_context(),
        }

    def retrieve(self, request, *args, **kwargs):
        related_furniture = self.object.profile.get_library_items(
            with_prefetched_images=True
        )
        language = LanguageEnum(self.object.profile.language)
        with translation.override(language):
            serializer = self.get_serializer(related_furniture, many=True)
            return Response(serializer.data)


class EmailCheckAPIView(GenericAPIView):
    permission_classes = (AllowAny,)
    serializer_class = EmailSerializer

    def post(self, request, **kwargs) -> Response:
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        email = serializer.data['email']
        is_user_registered = User.objects.filter(username=email).exists()
        return Response({'is_user_registered': is_user_registered})


class InsiderUserAPIView(RetrieveUpdateAPIView):
    """
    Update User email fields if they have none.
    """

    authentication_classes = [
        TokenAuthentication,
        SessionAuthentication,
    ]
    serializer_class = InsiderUserSerializer

    def get_queryset(self):
        if isinstance(self.request.auth, Token):
            return User.objects.all()

        return User.objects.filter(id=self.request.user.id)

    def update(self, request, *args, **kwargs):
        instance = self.get_object()
        if instance.email or instance.profile.email:
            return Response(
                'This user already has an email address.',
                status.HTTP_400_BAD_REQUEST,
            )
        return super().update(request, *args, **kwargs)


class UserPagination(PageNumberPagination):
    page_size = 100


class RetoolUserAPIViewSet(ModelViewSet):
    authentication_classes = [TokenAuthentication, SessionAuthentication]
    permission_classes = [IsAdminUser]
    queryset = User.objects.all()
    pagination_class = UserPagination

    serializer_classes_map = {
        'create': UserCreateSerializer,
    }

    def get_serializer_class(self):
        return self.serializer_classes_map.get(self.action, UserFlatSerializer)

    def get_queryset(self):
        if self.request.GET.get('use_cs_models', False):
            return self._use_cs_models()
        return super().get_queryset()

    def _use_cs_models(self) -> QuerySet[User]:
        # Users and Orders tables are too big to query them on production, so we use CS
        # models, that are well indexed
        query = self.request.GET.get('search', '')
        profile_user_ids = CSUserProfile.objects.filter(
            Q(email__icontains=query) | Q(user_email__icontains=query)
        ).values_list('user_id', flat=True)
        return User.objects.filter(id__in=profile_user_ids)


class UserGlobalAPIView(EcommerceAPIMixin, RetrieveAPIView):
    permission_classes = [AllowAny]

    def get_object(self) -> User | AnonymousUser:
        return self.request.user

    def get_serializer_class(self):
        if self.request.user.is_anonymous:
            return AnonymousUserGlobalSerializer

        return AuthenticatedUserGlobalSerializer

    def get_serializer_context(self):
        context = super().get_serializer_context()

        ab_tests, feature_flags = get_sorted_active_ab_tests_data(
            self.request,
            self.region,
        )
        context.update({'ab_tests': ab_tests, 'feature_flags': feature_flags})

        if self.request.user.is_anonymous:
            return context

        order = None
        if cart := CartService.get_cart(self.request.user):
            order = cart.order
            prefetch_related_objects([cart], 'items', 'items__cart_item')
        context.update({'order': order, 'cart': cart})
        return context
