# Generated by Django 4.2.23 on 2025-07-29 16:33

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        ('vouchers', '0065_voucher_is_stackable'),
    ]

    operations = [
        migrations.AlterField(
            model_name='itemdiscount',
            name='material',
            field=models.PositiveSmallIntegerField(
                blank=True,
                choices=[
                    (
                        0,
                        'T01 White / T02 White / T03 White / T01 Veneer Ash / T13 White / T13 Veneer Light / Tone Expressions Off White / Sofa Rewool2 Brown',
                    ),
                    (
                        1,
                        'T01 Black / T02 Terracotta / T03 Beige / T01 Veneer Oak / T13 Veneer Dark / Tone Expressions Oyster Beige / Sofa Rewool2 Olive Green',
                    ),
                    (
                        3,
                        'T01 Grey / T02 Sand / T03 Beige Pink / T13 Gray / Tone Expressions Inky Black / Sofa Rewool2 Butter Yellow',
                    ),
                    (7, 'T01 Yellow / T02 Sky Blue / Sofa Corduroy Ecru'),
                    (
                        8,
                        'T01 <PERSON> Pink / T02 Burgundy / T13 Clay Brown / Sofa Corduroy Rock',
                    ),
                    (
                        9,
                        'T01 Blue / T02 Cotton / T13 Olive Green / Sofa Corduroy Dark Brown',
                    ),
                    (11, 'T01 Moss Green / T13 Black / Sofa Corduroy Tobacco'),
                    (
                        2,
                        'T02 Midnight Blue / T03 Graphite / T01 Veneer Dark Oak / Tone Expressions Pistachio Green / Sofa Rewool2 Light Gray',
                    ),
                    (
                        6,
                        'T02 Matte Black / T03 Graphite Pink / T13 Gray Plywood / Sofa Rewool2 Baby Blue',
                    ),
                    (10, 'T02 Gray / T13 Beige / Sofa Corduroy Steel'),
                    (15, 'T02 Reisingers Pink / T03 White Stone Gray'),
                    (16, 'T02 Sage Green / T03 White Sage Green'),
                    (17, 'T02 Stone Gray / T03 White Misty Blue'),
                    (
                        4,
                        'T03 White Pink / Tone Expressions Powder Pink / Sofa Rewool2 Shadow Pink',
                    ),
                    (12, 'T03 Graphite Stone Gray / Sofa Corduroy Pink'),
                    (13, 'T03 Graphite Sage Green / Sofa Corduroy Camouflage'),
                    (14, 'T03 Graphite Misty Blue / Sofa Corduroy Blue Klein'),
                    (18, 'T03 Cashmere Stone Gray'),
                    (19, 'T03 Cashmere Sage Green'),
                    (20, 'T03 Cashmere Misty Blue'),
                    (5, 'T13 White Plywood / Sofa Rewool2 Green'),
                ],
                null=True,
            ),
        ),
    ]
