import logging
import uuid

from django.conf import settings
from django.contrib.auth import login
from django.utils import timezone
from django.utils.deprecation import MiddlewareMixin

from carts.services.cart_service import CartService
from cstm_be.settings.base import WAITING_LIST_COOKIE_AND_SESSION_NAME
from waiting_list.choices import WaitingListStatus
from waiting_list.models import WaitingListEntry

logger = logging.getLogger('cstm')


class WaitingListTokenMiddleware(MiddlewareMixin):
    def process_request(self, request):
        if 'wltoken' not in request.GET:
            return

        try:
            # todo: add validation for proper uuid
            token = uuid.UUID(request.GET['wltoken'])
            waiting_list_entry = WaitingListEntry.objects.get(token=token)
        except WaitingListEntry.DoesNotExist:
            request.session['waiting_list_expired'] = True
            return

        # todo: check for expire date, and if not yet used
        if waiting_list_entry.is_token_valid():
            user = waiting_list_entry.owner
            if user.is_staff or user.is_superuser:
                logger.error(
                    'WaitingListToken %s used to log in user_id=%s as staff',
                    waiting_list_entry.token,
                    user.id,
                )
                return
            # todo: to be checked, maybe can be removed and used one from settings?
            user.backend = 'django.contrib.auth.backends.ModelBackend'
            login(request, user)

            if waiting_list_entry.first_entry_at is None:
                waiting_list_entry.first_entry_at = timezone.now()
                waiting_list_entry.save(update_fields=['first_entry_at'])
                waiting_list_entry.update_status(
                    status=WaitingListStatus.TOKEN_USED_FOR_ENTRY,
                    creator=user,
                )

            cart = CartService.get_or_create_cart(user)
            order = CartService(cart).sync_with_order(request=request)
            waiting_list_entry.orders.add(order)
            waiting_list_entry.save()
            timestamp_expires_at = int(waiting_list_entry.token_expire_at.timestamp())

            request.session[WAITING_LIST_COOKIE_AND_SESSION_NAME] = timestamp_expires_at
        else:
            request.session['waiting_list_expired'] = True

    def process_response(self, request, response):
        if (
            not request.path.startswith('/api')
            and not request.path.startswith('/admin')
            and hasattr(request, 'session')
            and WAITING_LIST_COOKIE_AND_SESSION_NAME in request.session
        ):
            response.set_cookie(
                WAITING_LIST_COOKIE_AND_SESSION_NAME,
                request.session[WAITING_LIST_COOKIE_AND_SESSION_NAME],
                domain=settings.SESSION_COOKIE_DOMAIN,
                secure=settings.SESSION_COOKIE_SECURE or None,
                expires=request.session[WAITING_LIST_COOKIE_AND_SESSION_NAME],
            )
            if hasattr(request, 'session'):
                request.session.pop(WAITING_LIST_COOKIE_AND_SESSION_NAME, None)
        return response
