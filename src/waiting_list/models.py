import uuid

from django.conf import settings
from django.db import (
    models,
    transaction,
)

from custom.enums import Furniture
from custom.utils.dimensions import Dimensions
from waiting_list.choices import (
    DesiredTimes,
    WaitingListStatus,
)


class WaitingListSetup(models.Model):
    """Model managing sales limiting mechanism.

    This model should be used as append only i.e.
    only the latest object from this model applies to global mechanism.
    """

    enabled = models.BooleanField(default=False)
    creator = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        editable=False,
        on_delete=models.CASCADE,
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    @classmethod
    def is_sales_enabled(cls):
        """Return if Watty sales is enabled."""

        try:
            current_setup = cls.objects.latest()
        except WaitingListSetup.DoesNotExist:
            #  if no setup is provided, assume sale is on
            return True

        return current_setup.enabled

    def __str__(self):  # noqa: DJ012
        #  add active for latest one?
        return 'Wardrobes sales setup {}'.format(
            'OPEN' if self.enabled else 'LIMITED',
        )

    class Meta:  # noqa: DJ012
        get_latest_by = 'created_at'
        verbose_name_plural = 'Limiting T03 Sale logs'
        verbose_name = 'Limiting T03 Sale log'


class WaitingListEntry(models.Model):
    """Relation to Watty model saved on waiting list."""

    email_address = models.EmailField(db_index=True)
    desired_time = models.PositiveSmallIntegerField(
        choices=DesiredTimes.choices, default=DesiredTimes.EMPTY.value
    )
    token = models.UUIDField(default=uuid.uuid4, editable=False, unique=True)
    mailing_flow_status = models.PositiveSmallIntegerField(null=True, blank=True)
    status = models.PositiveSmallIntegerField(
        choices=WaitingListStatus.choices, default=WaitingListStatus.NEW.value
    )
    watty = models.ForeignKey('gallery.Watty', on_delete=models.CASCADE)
    first_entry_at = models.DateTimeField(null=True, blank=True)
    token_expire_at = models.DateTimeField(null=True, blank=True)
    token_sent_at = models.DateTimeField(null=True, blank=True)

    owner = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        null=True,
        editable=True,
        on_delete=models.CASCADE,
    )
    orders = models.ManyToManyField(
        'orders.Order',
        blank=True,
        related_name='waiting_list_entries',
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    @staticmethod
    def get_furniture_type():
        return Furniture.watty.value

    @transaction.atomic
    def update_status(self, status, creator):
        """Create new WaitingListEntryStatus and update self.status."""

        if not isinstance(status, WaitingListStatus):
            raise TypeError('Status has to be one of WaitingListEntry.Statuses')

        self.status = status
        self.save()

        entry_status = WaitingListEntryStatus(
            entry=self,
            status=status,
            creator=creator,
        )
        entry_status.save()
        return entry_status

    def is_token_valid(self):
        if self.status == WaitingListStatus.TOKEN_USED_FOR_PURCHASE:
            return False
        return True

    def watty_material(self):
        return self.watty.get_material_name()

    def watty_front_area(self):
        dimensions = self.watty.get_dimensions()
        front_area = dimensions.get_dimension(
            Dimensions.DimensionType.HEIGHT
        ) * dimensions.get_dimension(Dimensions.DimensionType.WIDTH)
        return f'{front_area / 10000:.2f} m2'

    def __str__(self):  # noqa: DJ012
        return str(self.watty)

    class Meta:  # noqa: DJ012
        verbose_name_plural = 'Customers entries waiting for T03'
        verbose_name = 'Customer entry waiting for T03'


class WaitingListEntryStatus(models.Model):
    status = models.PositiveSmallIntegerField(choices=WaitingListStatus.choices)
    entry = models.ForeignKey(
        WaitingListEntry,
        related_name='statuses',
        on_delete=models.CASCADE,
    )
    creator = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        editable=False,
        on_delete=models.CASCADE,
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name_plural = 'Waiting list entry statuses'
        get_latest_by = 'created_at'
