from django.db import transaction
from rest_framework import serializers

from gallery.enums import FurnitureStatusEnum
from gallery.models import <PERSON><PERSON>
from gallery.serializers import WattySerializer
from waiting_list.choices import WaitingListStatus
from waiting_list.models import WaitingListEntry


class WaitingListEntrySerializer(serializers.ModelSerializer):
    watty = WattySerializer()
    magic_preview = serializers.CharField(write_only=True)
    dna_name = serializers.CharField(write_only=True)

    class Meta:
        model = WaitingListEntry
        fields = [
            'email_address',
            'desired_time',
            'dna_name',
            'magic_preview',
            'watty',
        ]

    @transaction.atomic
    def create(self, validated_data):
        # TODO: refactor it at the same time we'll refactor add_to_cart
        initial_status = WaitingListStatus.NEW
        watty_data = validated_data.pop('watty')
        magic_preview = validated_data.pop('magic_preview')
        dna_name = validated_data.pop('dna_name')
        dna_object = watty_data['configurator_params']['geom_id']
        user = validated_data.pop('user')
        watty = Watty.objects.create(
            owner=user,
            furniture_status=FurnitureStatusEnum.SPECIAL,
            dna_name=dna_name,
            dna_object=dna_object,
            **watty_data,
        )
        watty.set_preview(magic_preview, 'webgl_cart')
        entry = WaitingListEntry.objects.create(
            owner=user,
            watty=watty,
            status=initial_status,
            **validated_data,
        )
        entry.update_status(status=initial_status, creator=user)
        return entry
