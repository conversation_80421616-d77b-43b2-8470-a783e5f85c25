from django.urls import reverse
from rest_framework import status

import pytest

from waiting_list.choices import (
    DesiredTimes,
    WaitingListStatus,
)
from waiting_list.models import WaitingListEntry


def waiting_list_entry_data(email_address=None):
    return {
        'email_address': email_address or '<EMAIL>',
        'magic_preview': (
            'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAA'
            'DUlEQVR42mP8z/C/HgAGgwJ/lK3Q6wAAAABJRU5ErkJggg=='
        ),
        'dna_name': 'dna',
        'watty': {
            'walls': '',
            'backs': '',
            'slabs': '',
            'frame': '',
            'bars': '',
            'drawers': '',
            'doors': '',
            'hinges': '',
            'width': 100,
            'height': 200,
            'depth': 30,
            'configurator_params': {'geom_id': 1},
        },
    }


@pytest.mark.django_db
class TestWaitingListEntryCreateView:
    def test_post_request_creates_waiting_list_entry_with_related_objects(
        self,
        api_client,
        waiting_list_setup_factory,
        user,
    ):
        url = reverse('waiting_list:create')
        email_address = '<EMAIL>'
        data = waiting_list_entry_data(email_address)
        waiting_list_setup_factory(enabled=False)
        api_client.force_authenticate(user)

        response = api_client.post(url, data, format='json')

        entry = WaitingListEntry.objects.first()
        entry_status = entry.statuses.first()
        assert response.status_code == status.HTTP_201_CREATED
        assert entry.token is not None
        assert entry.email_address == email_address
        assert entry.owner == entry.watty.owner == user
        assert entry.statuses.count() == 1
        assert entry.status == entry_status.status == WaitingListStatus.NEW
        assert entry_status.creator == user
        assert entry.desired_time == DesiredTimes.EMPTY

    def test_post_request_returns_error_when_sales_enabled(self, api_client, user):
        url = reverse('waiting_list:create')
        data = waiting_list_entry_data()
        api_client.force_authenticate(user)

        response = api_client.post(url, data, format='json')

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.json()['error_codes'] == 'sales_enabled'
